package com.wanshifu.iop.im.api.resp.seat;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24 17:41
 * @description TODO
 */
@Data
public class ConversationTransferServiceResp {

    /**
     * 分组名称
     * */
    private String groupName;

    /**
     * 分组id
     * */
    private Integer groupId;

    private List<GroupMemberInfo> groupMemberInfoList;

    @Data
    public static class GroupMemberInfo {

        /**
         * 坐席id
         * */
        private Long seatId;

        /**
         * 坐席名称
         * */
        private String seatName;

        /**
         * 坐席最大接线量
         * */
        private Integer maxLineNum;

        /**
         * 坐席当前接线量
         * */
        private Integer currentLineNum;

        /**
         * 坐席状态
         * offline:离线
         * wait_connect_line：待接线
         * online：在线（未达最大接线上线）
         * full_load：满负荷（已达最大接线上线）：
         * busy：不计入工时；
         * */
        private String currentSeatStatusEn;


    }
}
