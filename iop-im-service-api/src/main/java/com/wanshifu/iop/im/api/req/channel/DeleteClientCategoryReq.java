package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.iop.im.api.req.BaseSwitchStatusReq;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 删除端侧草稿
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class DeleteClientCategoryReq extends BaseSwitchStatusReq {
    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;
}
