package com.wanshifu.iop.im.api.resp.leftSidebar;

import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;

import java.util.List;

@Data
public class OnlineSeatInfoResp {

    /**
     * 坐席id
     * */
    private Long seatId;

    /**
     * 坐席名称
     */
    private String seatName;

    /**
     * 坐席头像
     */
    private String faceUrl;

    /**
     * 坐席当前状态
     */
    private String seatStatus;

    /**
     * 坐席当前状态名称
     */
    private String seatStatusName;

    /**
     * 坐席子状态
     */
    private String seatSubStatus;

    /**
     * 坐席子状态名称
     */
    private String seatSubStatusName;

    /**
     * 坐席类型 0-普通 1-组长 2-管理员
     */
    private Integer seatType;

    /**
     * 坐席接线上线
     */
    private Integer maxWiringQuantity;

    /**、
     * 坐席可切换状态
     */
    private List<CommonLabelValueResp> supportSwitchStatusList;
}
