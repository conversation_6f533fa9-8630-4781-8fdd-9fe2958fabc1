package com.wanshifu.iop.im.domain.bo.groupManage;

import lombok.Data;

import java.util.List;

/**
 * 分组坐席列表
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
public class GroupSeatListRespBo {

    /**
     * 坐席id
     */
    private Long seatId;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 坐席名称
     */
    private String seatName;

    /**
     * 坐席工号
     */
    private Long seatNo;

    /**
     * 最大接线量
     */
    private Integer maxWiringQuantity;

    /**
     * 分组id
     */
    private Integer groupId;
}
