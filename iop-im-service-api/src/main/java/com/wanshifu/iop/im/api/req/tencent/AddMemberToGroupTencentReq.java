package com.wanshifu.iop.im.api.req.tencent;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/19 17:06
 * @description TODO
 */
@Data
public class AddMemberToGroupTencentReq {

    /**
     * 群id
     * */
    @JSONField(name = "GroupId")
    private String GroupId;

    /**
     * 需要添加的成员
     * */
    @JSONField(name = "MemberList")
    private List<AddMemberToGroupItem> MemberList;


    @Data
    public static class AddMemberToGroupItem {

        /**
         * 成员
         * */
        @JSONField(name = "Member_Account")
        private String Member_Account;

    }
}
