package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 用户问题分类
 */
@Data
@ToString
@Table(name = "user_problem_class")
public class UserProblemClass {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "problem_id")
    private Long problemId;

    /**
     * 关联渠道id
     */
    @Column(name = "channel_id")
    private Long channelId;

    /**
     * 问题分类名
     */
    @Column(name = "problem_name")
    private String problemName;

    /**
     * 状态：enable：启用， disable：禁用
     */
    @Column(name = "status")
    private String status;

    /**
     * 更新人账号id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 创建人账号id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 删除状态，1：删除，0：正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}