package com.wanshifu.iop.im.api.req.tags;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 获取标签列表
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class CreateTagsReq {

    /**
     * 标签id -编辑用
     */
    private Integer tagId;

    /**
     * 标签名称
     */
    @NotNull(message = "坐席标签不能为空")
    private String tagName;

    /**
     * 标签描述
     */
    @NotNull(message = "标签说明不能为空")
    private String tagDesc;

    /**
     * 使用场景，conversation：会话,seat：坐席
     * */
    private String scene;

    /**
     * 操作人id
     */
    private Long operatorId;
}
