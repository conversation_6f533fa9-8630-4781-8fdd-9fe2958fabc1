package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 流程节点关系表
 */
@Data
@ToString
@Table(name = "flow_transition")
public class FlowTransition {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "transition_id")
    private Long transitionId;

    /**
     * 流程表主键id
     */
    @Column(name = "flow_define_id")
    private Long flowDefineId;

    /**
     * 来源节点id
     */
    @Column(name = "from_node_id")
    private Long fromNodeId;

    /**
     * 下一个节点id
     */
    @Column(name = "to_node_id")
    private Long toNodeId;

    /**
     * 条件表达式，决定是否执行to_node_id节点
     */
    @Column(name = "condition_expression")
    private String conditionExpression;

    /**
     * 删除状态，1：删除，0：正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}