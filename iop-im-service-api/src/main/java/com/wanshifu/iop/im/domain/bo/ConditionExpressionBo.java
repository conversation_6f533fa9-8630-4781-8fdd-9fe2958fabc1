package com.wanshifu.iop.im.domain.bo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/17 13:54
 * @description TODO
 */
@Data
public class ConditionExpressionBo {

    /**
     * 条件类型
     * */
    private String conditionType;

    /**
     * 条件
     * */
    private String condition;

    /**
     * 条件值对象
     * */
    private ConditionExpressionBoItem conditionValue;

    @Data
    public static class ConditionExpressionBoItem {
        /**
         * 星期 1,2,3,4,5
         * */
        private List<Integer> workDays;

        /**
         * 时间范围
         * */
        private List<String> workTime;
    }

}
