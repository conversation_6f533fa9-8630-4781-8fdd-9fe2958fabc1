package com.wanshifu.iop.im.api.req.incomingConfig;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 白名单查询参数
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class UpdateWhiteAndBlackReq {

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    /**
     * 特征id
     */
    @NotNull(message = "id不能为空")
    private Long propertyId;

    /**
     * 特征值
     */
    @NotNull(message = "特征值不能为空")
    private String propertyValue;

    /**
     * 类型 white-白名单 black-黑名单
     */
    @NotNull(message = "类型不能为空")
    @ValueIn("white,black")
    private String controlType;

    /**
     * 状态 0-禁用 1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 操作人
     */
    private Long operatorId;
}
