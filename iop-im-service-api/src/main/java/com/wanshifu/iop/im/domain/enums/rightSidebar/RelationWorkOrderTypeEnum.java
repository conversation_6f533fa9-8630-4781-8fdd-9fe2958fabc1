package com.wanshifu.iop.im.domain.enums.rightSidebar;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 关联工单类型枚举
 * <AUTHOR>
 * @date： 2025-07-25 15:18:03
 */

public enum RelationWorkOrderTypeEnum {

    /**
     * 工单类型枚举 customerOrder-售后工单 secondline-二线工单
     */
    customerOrder("customerOrder", "售后工单"),
    secondline("secondline", "二线工单")

   ;

   public final String type;

   public final String name;

   RelationWorkOrderTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, RelationWorkOrderTypeEnum> mapping = new HashMap<>();

   static {
       for (RelationWorkOrderTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, RelationWorkOrderTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static RelationWorkOrderTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
