package com.wanshifu.iop.im.service.api.backend;

import com.wanshifu.iop.im.api.req.BatchGetEnumsReq;
import com.wanshifu.iop.im.api.req.ConversationThemeConfigRqt;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.*;
import com.wanshifu.iop.im.api.resp.CommonEnumsResp;
import com.wanshifu.iop.im.api.resp.ConversationThemeConfigResp;
import com.wanshifu.iop.im.api.resp.VerifyRepeatNameResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 通用接口
 * <AUTHOR>
 * @date： 2025-06-04 10:49:16
 */
@FeignClient(
        value = "iop-im-service",
        path = "/common", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface CommonServiceApi {

    /**
     * 校验重复名称接口
     */
    @PostMapping("/verifyRepeatName")
    VerifyRepeatNameResp verifyRepeatName(@Valid @RequestBody VerifyRepeatNameReq req);

    /**
     * 启用禁用状态接口
     */
    @PostMapping("/switchStatus")
    Integer switchStatus(@Valid @RequestBody SwitchStatusReq req);

    /**
     * 获取标签枚举接口
     */
    @PostMapping("/getEnums")
    Map<String, List<CommonEnumsResp>> getEnums(@Valid @RequestBody BatchGetEnumsReq req);
    /**
     * 获取主题列表接口
     */
    @PostMapping("/getThemeConfigList")
    Map<String,List<ConversationThemeConfigResp>> getThemeConfigList(@Valid @RequestBody ConversationThemeConfigRqt req);

}
