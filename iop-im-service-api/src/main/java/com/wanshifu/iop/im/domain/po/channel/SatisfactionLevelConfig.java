package com.wanshifu.iop.im.domain.po.channel;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 渠道会话满意度配置表
 */
@Data
@ToString
@Table(name = "satisfaction_level_config")
public class SatisfactionLevelConfig {

    /**
     * 系统自动回复明细配置ID
     */
    @Id
    @Column(name = "satisfaction_level_config_id")
    private Long satisfactionLevelConfigId;

    /**
     * 渠道id
     */
    @Column(name = "channel_id")
    private Long channelId;

    /**
     * 其他字段配置
     */
    @Column(name = "result_callback_form_json")
    private String resultCallbackFormJson;


    /**
     * 启用状态  1：禁用，0：启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}