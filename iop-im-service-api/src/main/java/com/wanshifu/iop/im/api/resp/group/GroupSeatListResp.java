package com.wanshifu.iop.im.api.resp.group;

import com.wanshifu.iop.im.api.req.BasePageReq;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import lombok.Data;

import java.util.List;

/**
 * 分组坐席列表
 * <AUTHOR>
 * @date： 2025-06-30 14:38:34
 */
@Data
public class GroupSeatListResp {

    /**
     * 坐席id
     */
    private Long seatId;

    /**
     * 坐席名称
     */
    private String seatName;

    /**
     * 坐席工号
     */
    private Long seatNo;

    /**
     * 最大接线量
     */
    private Integer maxWiringQuantity;

    /**
     * 坐席所属会话组
     */
    private List<String> groupNameList;
}
