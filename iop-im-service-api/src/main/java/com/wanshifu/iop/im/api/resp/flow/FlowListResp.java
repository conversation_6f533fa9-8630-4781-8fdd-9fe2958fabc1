package com.wanshifu.iop.im.api.resp.flow;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FlowListResp {

    /**
     * 路由id
     * */
    private Long flowId;

    /**
     * 路由导航名称
     * */
    private String flowName;

    /**
     * 关联渠道入口
     * */
    private List<FlowChannelItem> flowChannels;


    /**
     * 创建时间
     * */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     * */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建人id
     * */
    private Long createAccountId;

    /**
     * 修改人id
     * */
    private Long updateAccountId;

    /**
     * 关联渠道入口
     * */
    @Data
    public static class FlowChannelItem {
        private Long channelId;

        private String channelName;
    }

}
