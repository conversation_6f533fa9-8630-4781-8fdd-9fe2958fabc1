package com.wanshifu.iop.im.domain.bo.channel;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SidebarJsonBo {
    /**
     * 侧边栏用户端(访客)配置列表
     */
    private List<UserSidebarBoItem> userSidebarItemList;
    /**
     * 侧边栏坐席端(含内、外部坐席)配置列表
     */
    private List<SeatSidebarBoItem> seatSidebarItemList;

    /**
     * 用户端侧边栏
     */
    @Data
    public static class UserSidebarBoItem {

        /**
         * 渠道ID
         */
        private Long channelId;
        /**
         * 端侧对象类型:  seat-坐席 visitor-访客
         */
        private String clientUserType;
        /**
         * 样式标题
         */
        private String title;
        /**
         * 样式序号
         */
        private Integer sort;

        /**
         * 启用状态 : 1-启用,0-禁用
         */
        private Integer status;

        /**
         * 样式链接
         */
        private String url;


        /**
         * 图标aid
         */
        private Long iconAid;

        /**
         * 图标url
         */
        private Long iconUrl;

        /**
         * 操作人id
         */
        private Long operatorId;

        /**
         * 数据创建时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date createTime;
        /**
         * 数据修改时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date updateTime;

    }

    /**
     * 坐席端侧边栏
     * 用户基础信息 用户订单 用户工单 快捷回复 业务记录 联络动态
     */
    @Data
    public static class SeatSidebarBoItem {

        /**
         * 坐席侧边栏信息标识
         */
        private String seatSidebarBoItemEn;
        /**
         * 坐席侧边栏信息描述         */
        private String seatSidebarBoItemDesc;
        /**
         * 选中状态 : 1-选中,0-未选中
         */
        private Integer checkStatus;
    }

}
