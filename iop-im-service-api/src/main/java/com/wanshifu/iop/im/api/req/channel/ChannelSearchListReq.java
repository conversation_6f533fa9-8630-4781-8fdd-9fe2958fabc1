package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.iop.im.api.req.BaseSwitchStatusReq;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 端侧+渠道列表
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class ChannelSearchListReq extends BaseSwitchStatusReq {
    /**
     * 端侧ID  为空或者0L默认取第一条
     */
    private Long clientCategoryId;
}
