package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 表单信息提交记录表
 */
@Data
@ToString
@Table(name = "from_submit_record")
public class FromSubmitRecord {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "from_submit_record_id")
    private Long fromSubmitRecordId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 坐席id
     */
    @Column(name = "seat_id")
    private Long seatId;

    /**
     * 业务模板id
     */
    @Column(name = "business_from_template_id")
    private Long businessFromTemplateId;

    /**
     * 提交内容json
     */
    @Column(name = "submit_content")
    private String submitContent;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}