package com.wanshifu.iop.im.domain.dto.channel;

import lombok.Data;

import java.util.Date;

/**
 * 端侧+渠道接口
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class ChannelDto {

    private Long channelId;

    /**
     * 渠道中文名
     */
    private String channelName;

    /**
     * 渠道英文名
     */
    private String channelEn;

    /**
     * 渠道描述
     */
    private String channelDesc;

    /**
     * 渠道类型: 用户渠道-user_channel、订单渠道-order_channel
     */
    private String channelType;

    /**
     * 路由导航配置id
     */
    private Long flowDefineId;

    /**
     * 端侧ID
     */
    private Long clientCategoryId;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 预览url模版  https://www.im.wnshifu.com/client?conversationId=xxx
     */
    private String previewUrlTemplate;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据修改时间
     */
    private Date updateTime;
    /**
     * 渠道会话样式列表 json
     */
    private String conversationStyleConfigJson;
    /**
     * 渠道会话满意度配置 json
     */
    private String satisfactionLevelConfigJson;

    /**
     * 渠道自动回复列表 json
     */
    private String conversationAutoReplyConfigListJson;

}
