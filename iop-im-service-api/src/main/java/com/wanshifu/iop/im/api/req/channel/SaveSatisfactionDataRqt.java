package com.wanshifu.iop.im.api.req.channel;

import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author:z<PERSON><PERSON>@wanshifu.com
 * @create:2025-07-28 10:35:35
 * @Description ：
 **/
@Data
public class SaveSatisfactionDataRqt {
    /**
     * 记录ID
     */
    @NotNull
    private Long satisfactionEvaluateRecordId;

    /**
     * 渠道id
     */
    @NotNull
    private Long channelId;

    /**
     * 坐席id
     */
    @NotNull
    private Long seatId;

    /**
     * 外部用户id
     */
    @NotNull
    private Long outUserId;

    /**
     * 星级和对应的描述
     */
    private String guidelineDesc;

    /**
     * 标题
     */
    private String title;

    /**
     * 客服是否解决问题 0-否 1-是
     */
    private Integer hasSolveProblem;

    /**
     * 结果回收表单json
     */
    @NotNull
    private String resultJson;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete=0;
}