package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import com.wanshifu.iop.im.domain.enums.DistributeStrategyEnum;
import lombok.Data;

import java.util.List;

/**
 * 分组分配策略编辑
 * <AUTHOR>
 * @date： 2025-06-30 14:12:59
 */
@Data
public class EditGroupSeatDistributeRuleReq extends BaseTenantReq {

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 坐席id
     */
    private Long seatId;

    /**
     * 规则指标id rule_metric_id
     */
    private Integer ruleMetricId;

    /**
     * 规则配置值
     */
    private List<String> ruleConfigValueList;

    /**
     * 操作人id
     */
    private Long operatorId;

    public void checkParams() {
        if (groupId == null || groupId == 0)  {
            throw new BusinessException("分组id不能为null");
        }
        if (seatId == null || seatId == 0)  {
            throw new BusinessException("坐席id不能为null");
        }
        if (ruleMetricId == null || ruleMetricId == 0)  {
            throw new BusinessException("规则指标id不能为null");
        }
        if (ruleConfigValueList == null || ruleConfigValueList.size() == 0)  {
            throw new BusinessException("规则配置值不能为null");
        }
        if (operatorId == null || operatorId == 0)  {
            throw new BusinessException("操作人id不能为null");
        }
    }
}
