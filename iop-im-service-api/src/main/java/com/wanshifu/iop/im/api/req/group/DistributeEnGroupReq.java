package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import com.wanshifu.iop.im.domain.enums.DistributeStrategyEnum;
import lombok.Data;

/**
 * 分配进组
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
public class DistributeEnGroupReq extends BaseTenantReq {

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 外部用户id
     */
    private String outerUserId;

    /**
     * 会话所在的会话渠道（端侧）
     */
    private Long clientCategoryId;

    /**
     * 会话所在的会话入口（渠道）
     */
    private Long channelId;

    /**
     * 用户填写的咨询类型
     */
    private Long userProblemClassId;

    /**
     * 会话分组id, 转接时使用
     * */
    private Integer groupInfoId;

    public void checkParams() {
        if (conversationId == null || conversationId == 0)  {
            throw new BusinessException("会话id不能为null");
        }
        if (StringUtils.isBlank(outerUserId)) {
            throw new BusinessException("外部用户id不能为空");
        }
        if (clientCategoryId == null || clientCategoryId == 0) {
            throw new BusinessException("会话所在的会话渠道不能为空");
        }
        if (channelId == null || channelId == 0) {
            throw new BusinessException("会话所在的会话入口不能为空");
        }
        if (userProblemClassId == null || userProblemClassId == 0) {
            throw new BusinessException("用户填写的咨询类型不能为空");
        }
    }
}
