package com.wanshifu.iop.im.api.req.seat;

import com.wanshifu.framework.core.BusinessException;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 坐席状态更新
 * <AUTHOR>
 * @date： 2025-07-29 10:29:57
 */
@Data
public class ConversationFlowSeatStatusUpdateReq {

    /**
     * 坐席id
     */
    private Long seatId;

    /**
     * 坐席外部id
     */
    private String seatOuterUserId;

    public void checkParams() {
        if (seatId == null && StringUtils.isBlank(seatOuterUserId)) {
            throw new BusinessException("seatId 和 seatOuterUserId 必须有一个要传值");
        }
    }
}
