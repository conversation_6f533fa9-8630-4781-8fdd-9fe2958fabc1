package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class GetStyleAndSideBarReq {
    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 渠道ID
     */
    @NotNull(message = "渠道ID不能为空")
    @Min(1L)
    private Long channelId;

    /**
     * 会话主题样式ID
     */
    private Long conversationStyleConfigId;

    /**
     * 端侧类型 inner-内部坐席 outer-外部坐席 visitor-访客
     */
    @NotNull(message = "端侧类型不能为空")
    @ValueIn("inner,outer,visitor")
    private String clientUserType;
}
