package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 比较运算符枚举
 * <AUTHOR>
 * @date： 2025-07-01 14:15:07
 */

public enum ComparisonOperatorEnum {

    /**
     * eq-属于 neq-不属于
     */
    EQ("eq", "属于"),

    NEQ("neq", "不属于"),


   ;

   public final String type;

   public final String name;

   ComparisonOperatorEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, ComparisonOperatorEnum> mapping = new HashMap<>();

   static {
       for (ComparisonOperatorEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, ComparisonOperatorEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       if (!mapping.containsKey(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static ComparisonOperatorEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       if (!mapping.containsKey(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
