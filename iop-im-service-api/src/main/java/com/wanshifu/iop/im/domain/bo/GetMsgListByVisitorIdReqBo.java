package com.wanshifu.iop.im.domain.bo;

import com.wanshifu.iop.im.api.req.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 核心会话信息返回参数
 * <AUTHOR>
 * @date： 2025-07-21 20:02:14
 */
@Data
public class GetMsgListByVisitorIdReqBo {

    /**
     * 腾讯群组id
     * */
    private String visitorOutUserId;

    /**
     * 会话消息查询排序
     */
    private String sort = "desc";

    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 页大小
     */
    private Integer pageSize = 20;
}
