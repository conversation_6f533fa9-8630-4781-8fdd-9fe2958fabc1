package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum RegisterStatusEnum {

    SUCCESS("success", "注册成功"),
    FAIL("fail", "注册失败"),


    ;

    public final String type;

    public final String name;

    RegisterStatusEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, RegisterStatusEnum> mapping = new HashMap<>();

    static {
        for (RegisterStatusEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, RegisterStatusEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        RegisterStatusEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static RegisterStatusEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
