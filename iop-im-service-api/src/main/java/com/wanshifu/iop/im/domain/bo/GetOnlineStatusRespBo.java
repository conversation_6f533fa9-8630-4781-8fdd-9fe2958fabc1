package com.wanshifu.iop.im.domain.bo;

import lombok.Data;

import java.util.List;

@Data
public class GetOnlineStatusRespBo {


    /**
     * 消息列表
     * */
    private List<OnlineItem> results;


    @Data
    public static class OnlineItem {

        /**
         * 外部用户id
         * */
        private String outerUserId;

        /**
         * 在线状态，Offline,Online,PushOnline
         * */
        private String status;
    }


}
