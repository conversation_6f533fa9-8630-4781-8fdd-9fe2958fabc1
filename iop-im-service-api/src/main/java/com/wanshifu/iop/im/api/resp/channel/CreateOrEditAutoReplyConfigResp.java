package com.wanshifu.iop.im.api.resp.channel;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 创建或编辑自动回复配置响应结果
 */
@Data
public class CreateOrEditAutoReplyConfigResp {

    /**
     * 返回的自动回复配置ID
     */
    private Long conversationAutoReplyConfigId;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 自动回复配置内容 JSON 字符串
     */
    private AutoReplyJsonBo autoReplyJson;

    /**
     * 启用状态：1-启用，0-未启用
     */
    private Integer status;

    /**
     * 是否删除：1-是，0-否
     */
    private Integer isDelete;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据修改时间
     */
    private Date updateTime;

    @Data
    public static class AutoReplyJsonBo {

        /**
         * 自动回复规则列表
         */
        private List<AutoReplyRuleBo> rules;

        /**
         * 其他配置项
         */
        private String defaultReply;

        /**
         * 是否启用关键词匹配
         */
        private Boolean enableKeywordMatch;
    }

    @Data
    public static class AutoReplyRuleBo {
        private String keyword;
        private String replyContent;
        private Integer priority;
    }
}