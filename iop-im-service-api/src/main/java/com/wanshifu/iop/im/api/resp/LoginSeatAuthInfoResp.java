package com.wanshifu.iop.im.api.resp;

import lombok.Data;

import java.util.List;

/**
 * 登录坐席信息
 * <AUTHOR>
 * @date： 2025-06-04 15:14:56
 */
@Data
public class LoginSeatAuthInfoResp {

    /**
     * 坐席id
     */
    private Long seatId;

    /**
     * 坐席昵称
     */
    private String seatName;

    /**
     * 头像地址
     */
    private String faceUrl;

    /**
     * 坐席工号
     */
    private Long seatNo;

    /**
     * 坐席类型 0-普通 1-组长 2-管理员
     */
    private Integer seatType;

    /**
     * 坐席上线状态
     */
    private String loginSeatStatusEn;

    /**
     * 当前坐席状态
     */
    private String currentSeatStatusEn;

    /**
     * 坐席标签id
     */
    private Integer tagId;

    /**
     * 坐席所在分组
     */
    private List<Integer> groupIdList;

    /**
     * 坐席最大链接数
     */
    private Integer maxWiringQuantity;

}
