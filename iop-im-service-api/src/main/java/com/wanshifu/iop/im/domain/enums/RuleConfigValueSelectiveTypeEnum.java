package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 配置选择器枚举
 * <AUTHOR>
 * @date： 2025-07-01 14:15:07
 */

public enum RuleConfigValueSelectiveTypeEnum {

    /**
     * 配置选择器类型，single-单选，multiple-多选
     */
    SINGLE("single", "单选"),

    MULTIPLE("multiple", "多选");


   ;

   public final String type;

   public final String name;

   RuleConfigValueSelectiveTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, RuleConfigValueSelectiveTypeEnum> mapping = new HashMap<>();

   static {
       for (RuleConfigValueSelectiveTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, RuleConfigValueSelectiveTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       if (!mapping.containsKey(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static RuleConfigValueSelectiveTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       if (!mapping.containsKey(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
