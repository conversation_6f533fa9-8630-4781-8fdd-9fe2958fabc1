package com.wanshifu.iop.im.api.req.tencent;

import lombok.Data;

import java.util.List;

@Data
public class CallbackCreateGroupReq {

    /**
     * 回调命令
     * */
    private String CallbackCommand;

    /**
     * 群id
     * */
    private String GroupId;

    /**
     * 操作者
     * */
    private String Operator_Account;

    /**
     *  群主
     * */
    private String Owner_Account;

    /**
     * 群组类型
     * */
    private String Type;

    /**
     * 群组名称
     * */
    private String Name;

    /**
     * 初始成员列表
     * */
    private List<MemberItem> MemberList;

    @Data
    public static class MemberItem {
        private String Member_Account;
    }
}
