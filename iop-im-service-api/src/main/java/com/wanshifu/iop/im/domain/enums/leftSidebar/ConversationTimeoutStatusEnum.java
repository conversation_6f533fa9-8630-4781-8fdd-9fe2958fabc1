package com.wanshifu.iop.im.domain.enums.leftSidebar;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 会话超时状态
 * */

public enum ConversationTimeoutStatusEnum {
    /**
     * 会话超时状态 not：未超时 approach：临近超时 already：超时
     */
    ALREADY("already", "超时"),
    APPROACH("approach", "临近超时"),
    NOT("not", "未超时"),
    ;

   ;

   public final String type;

   public final String name;

   ConversationTimeoutStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, ConversationTimeoutStatusEnum> mapping = new HashMap<>();

   static {
       for (ConversationTimeoutStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, ConversationTimeoutStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static ConversationTimeoutStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
