package com.wanshifu.iop.im.domain.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @author:z<PERSON><PERSON>@wanshifu.com
 * @create:2025-07-23 11:20:25
 * @Description ：
 *
 **/
@Data
public class ConversationStyleConfigV2Bo {
    /**
     * 会话样式配置ID
     */
    private Long conversationStyleConfigId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 面向端口类型:  web-web端 app-app/小程序
     */
    private String clientPortType;

    /**
     * 面向端口类型:  web-web端 app-app/小程序
     */
    private String port;
    /**
     * 当前选择主题ID
     */
    private Long conversationThemeId;

    /**
     * 订单卡片样式
     */
    private Long orderCardThemeId;

    /**
     * 工单样式id
     */
    private Long workOrderCardThemeId;

    /**
     * 侧边栏配置json对象
     */
    private String sidebarJson;
    /**
     * 其他配置json对象
     */
    private String otherJson;
    /**
     * 端侧ID
     */
    private Long clientCategoryId;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 消息类型
     * */
    private String msgType;

    /**
     * 数据创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 数据修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
}