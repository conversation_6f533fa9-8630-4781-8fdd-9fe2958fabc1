package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 会话日志表
 */
@Data
@ToString
@Table(name = "im_conversation_log")
public class ImConversationLog {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "conversation_log_id")
    private Long conversationLogId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 会话发起人外部用户id
     */
    @Column(name = "from_outer_user_id")
    private String fromOuterUserId;

    /**
     * merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,robot:机器人,seat:坐席
     */
    @Column(name = "from_outer_user_type")
    private String fromOuterUserType;

    /**
     * 会话状态：processing:进行中，complete:已完成
     */
    @Column(name = "conversation_status")
    private String conversationStatus;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    private Long groupId;

    /**
     * 消息接收者id指的是单聊对象虚拟用户id
     */
    @Column(name = "to_outer_user_id")
    private String toOuterUserId;

    /**
     * merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,robot:机器人,seat:坐席
     */
    @Column(name = "to_outer_user_type")
    private String toOuterUserType;

    /**
     * 来源类型：sensitive:敏感词，normal:正常进入（通用入口）
     */
    @Column(name = "from_type")
    private String fromType;

    /**
     * 来源id，敏感词才有值
     */
    @Column(name = "from_id")
    private String fromId;

    /**
     * 来源场景，order_no:订单
     */
    @Column(name = "from_scene")
    private String fromScene;

    /**
     * 场景值，订单编号
     */
    @Column(name = "scene_value")
    private String sceneValue;

    /**
     * 会话标签，多个id组成的json
     */
    @Column(name = "conversation_tag_ids")
    private String conversationTagIds;

    /**
     * 会话完成时间
     */
    @Column(name = "complete_time")
    private Date completeTime;

    /**
     * 代理账号id
     */
    @Column(name = "agent_outer_user_id")
    private String agentOuterUserId;

    /**
     * channel_config主键id
     */
    @Column(name = "channel_config_id")
    private Long channelConfigId;

    /**
     * 会话类型 online-在线会话 leave-留言 history-历史会话
     */
    @Column(name = "conversation_type")
    private String conversationType;

    /**
     * xxljob任务id
     */
    @Column(name = "timeout_xxl_id")
    private Long timeoutXxlId;

    /**
     * 删除状态，0:正常，1:删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}