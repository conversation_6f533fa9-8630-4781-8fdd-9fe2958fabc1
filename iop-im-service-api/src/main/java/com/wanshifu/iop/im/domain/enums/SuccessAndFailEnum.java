package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 账号类型
 * */

public enum SuccessAndFailEnum {

   SUCCESS("success", "成功"),

   FAIL("fail", "失败"),

   ;

   public final String type;

   public final String name;

   SuccessAndFailEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, SuccessAndFailEnum> mapping = new HashMap<>();

   static {
       for (SuccessAndFailEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, SuccessAndFailEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static SuccessAndFailEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
