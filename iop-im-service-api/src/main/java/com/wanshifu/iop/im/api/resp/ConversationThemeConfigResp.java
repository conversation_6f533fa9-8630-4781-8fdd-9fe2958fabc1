package com.wanshifu.iop.im.api.resp;

import lombok.Data;

/**
 * @author:z<PERSON><PERSON>@wanshifu.com
 * @create:2025-07-19 16:56:50
 * @Description ：
 **/
@Data
public class ConversationThemeConfigResp {
    private Long conversationThemeConfigId;

    /**
     * 端侧对象类型:  seat-坐席 visitor-访客
     */
    private String clientUserType;
    /**
     * 面向端口类型:  web-web端 app-app/小程序
     */
    private String clientPortType;
    /**
     * 主题类型:  theme 主题， orderCard 订单主题样式，wordOrderCard 工单主题样式
     */
    private String themeType;
    /**
     *
     */
    private String configJson;
    /**
     * 状态  1：禁用，0：启用
     */
    private Integer isDelete;
}