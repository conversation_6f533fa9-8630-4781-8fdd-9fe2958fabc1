package com.wanshifu.iop.im.domain.bo.queue;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/26 15:35
 * @description TODO
 */
@Data
public class XxlDistributeMqReqBo {

    /**
     * 群组id
     * */
    private Integer groupId;

    /**
     * 会话id
     * */
    private Long conversationId;

    /**
     * 外部用户id
     * */
    private String outerUserId;

    /**
     * 是否是ka用户
     * */
    private Boolean isUserKa;

    /**
     * 组下面的对应坐席的信息
     * */
    private List<GroupSeatInfo> groupSeatInfoList;

    @Data
    public static class GroupSeatInfo{

        /**
         * 坐席id
         * */
        private Long seatId;

        /**
         * 坐席外部id
         * */
        private String seatOuterUserId;

        /**
         * 坐席接待的最大接线量
         * */
        private Integer maxOnlineCount;

        /**
         * 坐席接待的当前接线量
         * */
        private Integer onlineCount;

        /**
         * 最大空闲程度
         * */
        private BigDecimal rate;
    }
}
