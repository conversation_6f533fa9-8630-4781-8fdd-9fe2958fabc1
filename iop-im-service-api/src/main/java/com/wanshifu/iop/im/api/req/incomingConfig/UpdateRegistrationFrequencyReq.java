package com.wanshifu.iop.im.api.req.incomingConfig;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 获取注册频率配置参数
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class UpdateRegistrationFrequencyReq {

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    /**
     * 注册上限，0:不限制
     */
    @NotNull(message = "注册上限不能为空")
    private Integer dimensionIpTimes;

    /**
     * 维度类型：daily日期维度、minute分钟维度
     */
    @NotNull(message = "维度类型不能为空")
    private String dimensionType;

    /**
     * 如果是分钟维度，表示10分钟内的注册次数；如果是日期维度，表示10天内的注册次数
     */
    @NotNull(message = "维度值不能为空")
    private Integer dimensionValue;

    /**
     * 操作人
     */
    private Long operatorId;
}
