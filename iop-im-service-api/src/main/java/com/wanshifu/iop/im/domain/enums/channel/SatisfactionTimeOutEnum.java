package com.wanshifu.iop.im.domain.enums.channel;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 满意度发送时效枚举
 */
public enum SatisfactionTimeOutEnum {
    TODAY("today", "当天失效"),
    ;

    public final String type;

    public final String name;

    SatisfactionTimeOutEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, SatisfactionTimeOutEnum> mapping = new HashMap<>();

    static {
        for (SatisfactionTimeOutEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, SatisfactionTimeOutEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        SatisfactionTimeOutEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static SatisfactionTimeOutEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
