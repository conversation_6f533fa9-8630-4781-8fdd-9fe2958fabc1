package com.wanshifu.iop.im.domain.bo;

import com.wanshifu.framework.core.BusinessException;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@Data
public class ConversationAssignBo {

    /**
     * 外部用户id
     * */
    private String outerUserId;

    /**
     * 会话id
     */
    private Long conversationId;

    public void checkParams() {
        if (conversationId == null) {
            throw new BusinessException("conversationId is null");
        }
        if (StringUtils.isBlank(outerUserId)) {
            throw new BusinessException("outerUserId is null");
        }
    }
}
