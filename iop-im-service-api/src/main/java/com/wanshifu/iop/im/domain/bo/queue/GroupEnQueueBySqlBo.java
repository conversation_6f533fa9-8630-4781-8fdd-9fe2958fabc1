package com.wanshifu.iop.im.domain.bo.queue;

import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * 分组访问规则详情
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
public class GroupEnQueueBySqlBo {

    /**
     * 会话分配id
     */
    private Long conversationDistributeId;

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 队列配置id
     */
    private Integer queueConfigId;

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 外部用户id
     */
    private String outerUserId;

    /**
     * 队列状态 wait-排队中 completed-分配完成 cancel-取消排队  expired-过期
     */
    private String queueStatus;

    /**
     * 排序
     */
    private Long sort;
}
