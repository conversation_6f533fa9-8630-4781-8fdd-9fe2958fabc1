package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 账号使用状态
 * */

public enum GroupStatusEnum {

   NO_USED("no_used", "已解散"),

   USED("used", "使用中"),


   ;

   public final String type;

   public final String name;

   GroupStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, GroupStatusEnum> mapping = new HashMap<>();

   static {
       for (GroupStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, GroupStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static GroupStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
