package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 分组规则表
 */
@Data
@ToString
@Table(name = "group_rule")
public class GroupRule {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "group_rule_id")
    private Long groupRuleId;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    private Integer groupId;

    /**
     * 规则指标id
     */
    @Column(name = "rule_metric_id")
    private Integer ruleMetricId;

    /**
     * 比较运算符: eq-属于, not_eq-不属于
     */
    @Column(name = "comparison_operator")
    private String comparisonOperator;

    /**
     * 规则配置值
     */
    @Column(name = "rule_config_value")
    private String ruleConfigValue;

    /**
     * 操作人账号id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 启用状态: 1-禁用, 0-启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}