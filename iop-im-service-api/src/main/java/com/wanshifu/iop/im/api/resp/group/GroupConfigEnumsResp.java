package com.wanshifu.iop.im.api.resp.group;

import com.wanshifu.iop.im.api.resp.CommonLabelValueLevelResp;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;

import java.util.List;

/**
 * 域名列表查询结果
 * <AUTHOR>
 * @date： 2025-06-04 15:14:56
 */
@Data
public class GroupConfigEnumsResp {

    /**
     * 分组规则下拉枚举
     */
    private List<RuleMetricConfigEnumsOptionResp> groupRuleEnums;

    /**
     * 逻辑操作符枚举
     */
    private List<CommonLabelValueResp> logicalOperatorEnums;

    /**
     * 比较运算符枚举
     */
    private List<CommonLabelValueResp> comparisonOperatorEnums;
}
