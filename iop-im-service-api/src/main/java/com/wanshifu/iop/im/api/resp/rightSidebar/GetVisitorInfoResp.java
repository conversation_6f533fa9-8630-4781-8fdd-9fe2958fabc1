package com.wanshifu.iop.im.api.resp.rightSidebar;

import com.wanshifu.iop.im.api.resp.leftSidebar.ListConversationMsgResp;
import lombok.Data;

import java.util.List;

/**
 * 获取访客信息
 * <AUTHOR>
 * @date： 2025-07-14 20:37:38
 */
@Data
public class GetVisitorInfoResp  {

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 字段英文名
     */
    private String fieldEn;

    /**
     * 字段值类型 single-单个 | multiple-多个
     */
    private String valueType;

    /**
     * 单个字段值
     */
    private String singleValue;

    /**
     * 多个字段值
     */
    private List<String> multipleValue;

}
