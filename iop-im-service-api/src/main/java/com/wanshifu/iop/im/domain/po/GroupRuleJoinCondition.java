package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 分组规则连接条件表
 */
@Data
@ToString
@Table(name = "group_rule_join_condition")
public class GroupRuleJoinCondition {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "join_condition_id")
    private Integer joinConditionId;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    private Integer groupId;

    /**
     * 逻辑运算符 and-且 or-或
     */
    @Column(name = "logical_operator")
    private String logicalOperator;

    /**
     * 操作人账号id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}