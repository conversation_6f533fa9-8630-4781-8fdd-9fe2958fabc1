package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 访问属性配置类型枚举
 * */

public enum PropertyTypeEnum {

    /**
     * 特征类型 user-用户ID master-师傅ID enterprise-总包ID tourist-访客设备id ip-ip地址/范围
     */

    USER("user", "用户ID"),

    MASTER("master", "师傅ID"),

    ENTERPRISE("enterprise", "总包ID"),

    TOURIST("tourist", "访客设备id"),

    IP("ip", "ip地址/范围"),

   ;

   public final String type;

   public final String name;

   PropertyTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, PropertyTypeEnum> mapping = new HashMap<>();

   static {
       for (PropertyTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, PropertyTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       if (!mapping.containsKey(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static PropertyTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       if (!mapping.containsKey(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
