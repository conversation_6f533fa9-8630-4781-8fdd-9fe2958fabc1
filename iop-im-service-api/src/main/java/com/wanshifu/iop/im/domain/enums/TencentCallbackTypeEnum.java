package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;
/**
 * 腾讯回调事件类型和
 * */
public enum TencentCallbackTypeEnum {

    /**
     * 发送单聊消息后回调
     * */
    CALLBACK_AFTER_SEND_MSG("C2C.CallbackAfterSendMsg", "callback_after_send_msg"),

    /**
     * 消息已读后回调-单聊
     * */
    CALLBACK_AFTER_MSG_REPORT("C2C.CallbackAfterMsgReport", "callback_after_msg_report"),

    /**
     * 消息已读后回调-群聊
     * */
    CALLBACK_AFTER_MSG_GROUP_REPORT("Group.CallbackAfterSendMsg", "callback_after_msg_group_report"),

    /**
     * 拉人进群后回调
     * */
    CALLBACK_AFTER_NEW_MEMBER_JOIN("Group.CallbackAfterNewMemberJoin", "callback_after_new_member_join"),

    /**
     * 拉群后回调
     * */
    CALLBACK_AFTER_CREATE_GROUP("Group.CallbackAfterCreateGroup", "callback_after_create_group"),

    /**
     * 用户状态变更
     * */
    CALLBACK_IM_STATE_CHANGE("State.StateChange", "callback_im_state_change"),


    ;

    public final String type;

    public final String beanType;

    TencentCallbackTypeEnum(String type, String beanType) {
        this.type = type;
        this.beanType = beanType;
    }

    private static final Map<String, TencentCallbackTypeEnum> mapping = new HashMap<>();

    static {
        for (TencentCallbackTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, TencentCallbackTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        TencentCallbackTypeEnum tencentCallbackTypeEnum = mapping.get(type);
        if (tencentCallbackTypeEnum == null) {
            return "";
        }

        return tencentCallbackTypeEnum.beanType;
    }

    public static TencentCallbackTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
