package com.wanshifu.iop.im.domain.dto.channel;

import lombok.Data;

import java.util.Date;

@Data
public class ConversationAutoReplyDetailConfigDto {
    /**
     * 系统自动回复明细配置ID
     */
    private Long conversationAutoReplyDetailConfig;

    /**
     * 系统自动回复配置ID
     */
    private Long conversationAutoReplyConfigId;

    /**
     * 提示类型：欢迎语 - welcome_auto_reply、排队提示语、坐席超时回复提醒语 seat_timeout_auto_reply、访客超时回复提醒语 visitor_timeout_auto_reply
     */
    private String msgType;

    /**
     * 系统自动消息接收对象：坐席-seat、访客-visitor
     */
    private String autoReplyReceiveObject;

    /**
     * 自动回复内容（字符串模板）
     */
    private String AutoReplyContent;

    /**
     * 提示语触发事件类型：系统主动 - system_active、 访客回复超时 - visitor_timeout、坐席回复超时 - agent_timeout、访客主动关闭 - visitor_close、客服主动关闭 - agent-close
     */
    private String triggerMode;

    /**
     * 回复超时时长（分钟）
     */
    private String timeoutCostSecond;

    /**
     * 是否插入占位符
     */
    private Integer supportPlaceholder;

    /**
     * 占位符键值对json 格式
     */
    private String extraJson;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据修改时间
     */
    private Date updateTime;
}
