package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 会话转接记录表
 */
@Data
@ToString
@Table(name = "im_conversation_transfer_log")
public class ImConversationTransferLog {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "transfer_log_id")
    private Long transferLogId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 原来的坐席分组id
     */
    @Column(name = "origin_group_info_id")
    private Long originGroupInfoId;

    /**
     * 原来的坐席外部账号id
     */
    @Column(name = "origin_outer_user_id")
    private String originOuterUserId;

    /**
     * 转接的分组分组id
     */
    @Column(name = "new_group_info_id")
    private Long newGroupInfoId;

    /**
     * 转接的坐席外部用户id
     */
    @Column(name = "new_outer_user_id")
    private String newOuterUserId;

    /**
     * 转接时间
     */
    @Column(name = "transfer_time")
    private Date transferTime;

    /**
     * 转接备注
     */
    @Column(name = "note")
    private String note;

    /**
     * 删除状态
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}