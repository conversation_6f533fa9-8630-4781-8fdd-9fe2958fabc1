package com.wanshifu.iop.im.domain.enums.rightSidebar;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 字段属性业务来源类型枚举
 * <AUTHOR>
 * @date： 2025-07-25 15:18:03
 */

public enum FieldAttributeFromTemplateTypeEnum {

    /**
     * 来源模板类型 visitor_info-访客信息  business_from-业务表单
     */
    VISITOR_INFO("visitor_info", "访客信息"),

    BUSINESS_FROM("business_from", "业务表单"),
   ;

   public final String type;

   public final String name;

   FieldAttributeFromTemplateTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, FieldAttributeFromTemplateTypeEnum> mapping = new HashMap<>();

   static {
       for (FieldAttributeFromTemplateTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, FieldAttributeFromTemplateTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static FieldAttributeFromTemplateTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
