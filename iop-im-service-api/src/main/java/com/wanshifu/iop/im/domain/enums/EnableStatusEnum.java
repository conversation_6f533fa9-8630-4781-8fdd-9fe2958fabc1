package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 启用状态
 * */

public enum EnableStatusEnum {

   ENABLE("enable", "启用"),

   DISABLE("disable", "禁用"),

   ;

   public final String type;

   public final String name;

   EnableStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, EnableStatusEnum> mapping = new HashMap<>();

   static {
       for (EnableStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, EnableStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static EnableStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
