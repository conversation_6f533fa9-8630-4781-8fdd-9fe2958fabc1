package com.wanshifu.iop.im.api.req.leftSidebar;

import com.wanshifu.iop.im.domain.enums.leftSidebar.VisitorLabelEnum;
import lombok.Data;

import java.util.List;

/**
 * 会话列表搜索
 * <AUTHOR>
 * @date： 2025-07-14 20:22:55
 */
@Data
public class FormatSearchConversationListReq {

    /**
     * 坐席id
     */
    private Long seatId;

    /**
     * 会话标签 {@link VisitorLabelEnum}
     */
    private List<String> visitorLabelList;

    // ====== 历史会话入参 =========
    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 搜索开始时间
     */
    private String searchStartTime;

    /**
     * 搜索结束时间
     */
    private String searchEndTime;
}
