package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 会话历史消息导入日志表
 */
@Data
@ToString
@Table(name = "im_conversation_message_history_import_log")
public class ImConversationMessageHistoryImportLog {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "conversation_message_history_import_log_id")
    private Long conversationMessageHistoryImportLogId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 导入状态: processing-导入中, complete-导入完成, fail-导入失败
     */
    @Column(name = "import_status")
    private String importStatus;

    /**
     * 导入时间
     */
    @Column(name = "import_time")
    private Date importTime;

    /**
     * 导入备注
     */
    @Column(name = "import_remark")
    private String importRemark;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}