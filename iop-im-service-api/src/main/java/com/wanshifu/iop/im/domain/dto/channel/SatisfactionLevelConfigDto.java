package com.wanshifu.iop.im.domain.dto.channel;

import lombok.Data;

import java.util.Date;

@Data
public class SatisfactionLevelConfigDto {
    /**
     * 系统自动回复明细配置ID
     */
    private Long satisfactionLevelConfigId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 星级和对应的描述json
     */
    private ScoreDescJsonDto scoreDescJsonBo;

    /**
     * 标签按钮文案JSON
     */
    private ResultRecycleButtonDescJsonDto resultRecycleButtonDescJsonBo;
    
    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据修改时间
     */
    private Date updateTime;
}
