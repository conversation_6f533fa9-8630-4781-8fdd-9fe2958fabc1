package com.wanshifu.iop.im.api.req.seat;

import com.wanshifu.iop.im.api.resp.seat.SeatFunctionResp;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 切换标签状态
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class EditSeatStatusReq {

    /**
     * 坐席状态id
     */
    @NotNull(message = "坐席状态id不能为空")
    private Integer seatStatusId;

    /**
     * 是否计入工时 0-否 1-是
     */
    @NotNull(message = "是否计入工时不能为空")
    private Integer isReckonInManHour;

    /**
     * 状态说明
     */
    private String seatStatusDesc;

    /**
     * 编辑坐席功能
     */
    private List<SeatFunctionResp> seatFunctionList;

    /**
     * 操作人
     */
    private Long operatorId;

}
