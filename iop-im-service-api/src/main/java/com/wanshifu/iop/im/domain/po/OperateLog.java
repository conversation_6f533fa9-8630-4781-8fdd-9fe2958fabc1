package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 操作日志表
 */
@Data
@ToString
@Table(name = "operate_log")
public class OperateLog {

    /**
     * 日志id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "operate_log_id")
    private Long operateLogId;

    /**
     * 业务表id
     */
    @Column(name = "from_table_id")
    private Long fromTableId;

    /**
     * 业务表名
     */
    @Column(name = "from_table_name")
    private String fromTableName;

    /**
     * 业务类型（add,modify,delete,changeStatus）
     */
    @Column(name = "from_business")
    private String fromBusiness;

    /**
     * 操作内容
     */
    @Column(name = "operate_content")
    private String operateContent;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}