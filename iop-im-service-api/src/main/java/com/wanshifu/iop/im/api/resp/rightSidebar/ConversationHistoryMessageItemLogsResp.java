package com.wanshifu.iop.im.api.resp.rightSidebar;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.iop.im.domain.bo.HistoryMsgBodyItemBo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 会话访问信息
 * <AUTHOR>
 * @date： 2025-07-14 20:37:38
 */
@Data
public class ConversationHistoryMessageItemLogsResp {

    /**
     * 会话进线时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 消息明细
     */
    private List<HistoryMsgBodyItemBo> msgBodyItemList;

}
