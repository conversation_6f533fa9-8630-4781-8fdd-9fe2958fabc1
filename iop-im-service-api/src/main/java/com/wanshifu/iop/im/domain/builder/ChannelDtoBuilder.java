package com.wanshifu.iop.im.domain.builder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.channel.*;
import com.wanshifu.iop.im.domain.dto.channel.*;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.iop.im.domain.po.channel.ClientCategoryConfig;
import com.wanshifu.iop.im.domain.po.channel.ClientChannelConfigDraft;
import com.wanshifu.iop.im.domain.po.channel.SatisfactionEvaluateRecord;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class ChannelDtoBuilder {
    /************************************** 构建resp或Dto ****************************************/

    public static List<ChannelDto> buildChannelDtoList(CreateChannelReq req) {
        List<ChannelDto> channelDtoList = new ArrayList<>();
        ChannelDto channelDto = new ChannelDto();
        BeanUtils.copyProperties(req, channelDto);
        if(Objects.isNull(channelDto.getCreateTime())) {
            channelDto.setCreateTime(new Date());
        }
        channelDto.setUpdateTime(new Date());
        channelDtoList.add(channelDto);
        return channelDtoList;
    }

    public static List<ChannelDto> buildChannelDtoList(UpdateChannelReq req) {
        List<ChannelDto> channelDtoList = new ArrayList<>();
        ChannelDto channelDto = new ChannelDto();
        BeanUtils.copyProperties(req, channelDto);
        if(Objects.isNull(channelDto.getCreateTime())) {
            channelDto.setCreateTime(new Date());
        }
        channelDto.setUpdateTime(new Date());
        channelDtoList.add(channelDto);
        return channelDtoList;
    }

    public static List<ChannelDto> buildChannelDtoList(List<ChannelConfig> channelConfigList) {
        List<ChannelDto> channelDtoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(channelConfigList)) {
            channelConfigList.forEach(f -> channelDtoList.add(ChannelDtoBuilder.buildChannelDto(f)));
        }
        return channelDtoList;
    }

    public static ChannelDto buildChannelDto(CreateChannelReq req) {
        ChannelDto channelDto = new ChannelDto();
        BeanUtils.copyProperties(req, channelDto);
        if(Objects.isNull(channelDto.getCreateTime())) {
            channelDto.setCreateTime(new Date());
        }
        channelDto.setUpdateTime(new Date());
        return channelDto;
    }

    public static ChannelDto buildChannelDto(ChannelConfig channelConfig) {
        ChannelDto channelDto = new ChannelDto();
        BeanUtils.copyProperties(channelConfig, channelDto);
        if(Objects.isNull(channelDto.getCreateTime())) {
            channelDto.setCreateTime(new Date());
        }
        channelDto.setUpdateTime(new Date());
        return channelDto;
    }

    public static ClientCategoryDto buildClientCategoryConfigDto(ClientCategoryConfig clientCategoryConfig) {
        if(Objects.isNull(clientCategoryConfig)) {
            return null;
        }
        ClientCategoryDto clientCategoryDto = new ClientCategoryDto();
        BeanUtils.copyProperties(clientCategoryConfig, clientCategoryDto);
        if(Objects.isNull(clientCategoryDto.getCreateTime())) {
            clientCategoryDto.setCreateTime(new Date());
        }
        return clientCategoryDto;
    }

    public static ClientCategoryDto buildClientCategoryConfigDto(ClientChannelConfigDraft clientChannelConfigDraft) {
        if(Objects.isNull(clientChannelConfigDraft)) {
            return null;
        }
        ClientCategoryDto clientCategoryDto = new ClientCategoryDto();
        BeanUtils.copyProperties(clientChannelConfigDraft, clientCategoryDto);
        if(Objects.isNull(clientCategoryDto.getCreateTime())) {
            clientCategoryDto.setCreateTime(new Date());
        }
        return clientCategoryDto;
    }

    public static AllConfigJsonDto buildAllConfigJsonDto(String allConfigJson) {
        if(StringUtils.isEmpty(allConfigJson)) {
            return null;
        }
        return JSONObject.parseObject(allConfigJson,  AllConfigJsonDto.class);
    }


    public static List<ClientCategoryDto> buildClientCategoryConfigDtoList(List<ClientChannelConfigDraft> clientChannelConfigDraftList) {
        List<ClientCategoryDto> clientCategoryDtoList = new ArrayList<>();
        if(CollectionUtils.isEmpty(clientChannelConfigDraftList)) {
            return clientCategoryDtoList;
        }
        clientChannelConfigDraftList.forEach(f -> clientCategoryDtoList.add(ChannelDtoBuilder.buildClientCategoryConfigDto(f)));
        return clientCategoryDtoList;
    }

    public static ScoreDescJsonDto buildScoreDescJsonDto(String scoresJson) {
        if (StringUtils.isEmpty(scoresJson)) {
            return null;
        }
        return JSONObject.parseObject(scoresJson, ScoreDescJsonDto.class);
    }
    public static ThemesJsonDto buildThemesJsonDto(String themesJson) {
        if (StringUtils.isEmpty(themesJson)) {
            return null;
        }
        return JSONObject.parseObject(themesJson, ThemesJsonDto.class);
    }

    public static ResultRecycleButtonDescJsonDto buildResultRecycleButtonDescJsonDto(String resultRecycleButtonDescJson) {
        if (StringUtils.isEmpty(resultRecycleButtonDescJson)) {
            return null;
        }
        return JSONObject.parseObject(resultRecycleButtonDescJson, ResultRecycleButtonDescJsonDto.class);
    }

    /************************************** 构建数据库实体 ****************************************/
    public static ClientCategoryConfig buildClientCategoryConfig(ClientCategoryCreateReq req) {
        ClientCategoryConfig clientCategoryConfig = new ClientCategoryConfig();
        ChannelBoBuilder.initBaseFields(req, clientCategoryConfig);
        return clientCategoryConfig;
    }

    public static ChannelConfig buildChannelConfig(CreateStyleAndSideBarReq req) {
        ChannelConfig channelConfig = new ChannelConfig();
        BeanUtils.copyProperties(req, channelConfig);
        channelConfig.setIsDelete(0);
        return channelConfig;
    }

    public static ClientCategoryConfig buildClientCategoryConfig(ClientChannelConfigDraft clientChannelConfigDraft) {
        ClientCategoryConfig clientCategoryConfig = new ClientCategoryConfig();
        BeanUtils.copyProperties(clientChannelConfigDraft, clientCategoryConfig);
        return clientCategoryConfig;
    }

    public static List<ChannelConfig> buildChannelConfigList(List<ChannelDto> channelDtoList) {
        List<ChannelConfig> channelConfigList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(channelDtoList)) {
            return ChannelDtoBuilder.buildChannelConfigList(channelDtoList);
        }
        return channelConfigList;
    }

    public static SatisfactionEvaluateRecord buildSatisfactionEvaluateRecord(SatisfactionLevelConfigDto satisfactionLevelConfigDto) {
        SatisfactionEvaluateRecord satisfactionEvaluateRecord = new SatisfactionEvaluateRecord();
        BeanUtils.copyProperties(satisfactionLevelConfigDto, satisfactionEvaluateRecord);
        if(Objects.isNull(satisfactionEvaluateRecord.getCreateTime())) {
            satisfactionEvaluateRecord.setCreateTime(new Date());
        }
        satisfactionEvaluateRecord.setUpdateTime(new Date());
        return satisfactionEvaluateRecord;
    }

    public static ClientChannelConfigDraft buildClientCategoryDraft(CreateDraftReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(req, clientChannelConfigDraft);
        if(Objects.isNull(clientChannelConfigDraft.getCreateTime())) {
            clientChannelConfigDraft.setCreateTime(new Date());
        }
        clientChannelConfigDraft.setUpdateTime(new Date());
        String allConfigJsonString = JSON.toJSONString(req.getChannelBoList());
        clientChannelConfigDraft.setAllConfigJson(allConfigJsonString);
        return clientChannelConfigDraft;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(ClientCategoryCreateReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(req, clientChannelConfigDraft);
        if(Objects.isNull(clientChannelConfigDraft.getCreateTime())) {
            clientChannelConfigDraft.setCreateTime(new Date());
        }
        clientChannelConfigDraft.setUpdateTime(new Date());
        return clientChannelConfigDraft;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(CreateChannelReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(req, clientChannelConfigDraft);
        if(Objects.isNull(clientChannelConfigDraft.getCreateTime())) {
            clientChannelConfigDraft.setCreateTime(new Date());
        }
        clientChannelConfigDraft.setUpdateTime(new Date());
        return clientChannelConfigDraft;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(UpdateChannelReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(req, clientChannelConfigDraft);
        if(Objects.isNull(clientChannelConfigDraft.getCreateTime())) {
            clientChannelConfigDraft.setCreateTime(new Date());
        }
        clientChannelConfigDraft.setUpdateTime(new Date());
        return clientChannelConfigDraft;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(ChannelDto channelDto) {
        ClientChannelConfigDraft clientChannelConfigDraft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(channelDto, clientChannelConfigDraft);
        if(Objects.isNull(clientChannelConfigDraft.getCreateTime())) {
            clientChannelConfigDraft.setCreateTime(new Date());
        }
        clientChannelConfigDraft.setUpdateTime(new Date());
        return clientChannelConfigDraft;
    }

    public static List<ClientChannelConfigDraft> buildClientChannelConfigDraftList(List<ChannelDto> channelList) {
        List<ClientChannelConfigDraft> clientChannelConfigDraftList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(channelList)) {
            return clientChannelConfigDraftList;
        }
        channelList.forEach(f -> clientChannelConfigDraftList.add(ChannelDtoBuilder.buildClientChannelConfigDraft(f)));
        return clientChannelConfigDraftList;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(SwitchChannelStatusReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(req, clientChannelConfigDraft);
        if(Objects.isNull(clientChannelConfigDraft.getCreateTime())) {
            clientChannelConfigDraft.setCreateTime(new Date());
        }
//        clientChannelConfigDraft.setOperatorId(req.getOperatorId());
//        clientChannelConfigDraft.setStatus(req.getStatus());
        clientChannelConfigDraft.setIsDelete(0);
        clientChannelConfigDraft.setUpdateTime(new Date());
        return clientChannelConfigDraft;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(EditStyleAndSideBarReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(req, clientChannelConfigDraft);
        if(Objects.isNull(clientChannelConfigDraft.getCreateTime())) {
            clientChannelConfigDraft.setCreateTime(new Date());
        }
        if(Objects.nonNull(req.getConversationStyleConfigBo())) {
            String allConfigJson = JSON.toJSONString(req.getConversationStyleConfigBo());
            clientChannelConfigDraft.setAllConfigJson(allConfigJson);
        }
        clientChannelConfigDraft.setIsDelete(0);
        clientChannelConfigDraft.setUpdateTime(new Date());
        return clientChannelConfigDraft;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(CreateStyleAndSideBarReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(req, clientChannelConfigDraft);
        if(Objects.isNull(clientChannelConfigDraft.getCreateTime())) {
            clientChannelConfigDraft.setCreateTime(new Date());
        }
        if(Objects.nonNull(req.getConversationStyleConfigBo())) {
            String allConfigJson = JSON.toJSONString(req.getConversationStyleConfigBo());
            clientChannelConfigDraft.setAllConfigJson(allConfigJson);
        }
        clientChannelConfigDraft.setIsDelete(0);
        clientChannelConfigDraft.setUpdateTime(new Date());
        return clientChannelConfigDraft;
    }

}
