package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 模块类型
 * <AUTHOR>
 * @date： 2025-05-29 15:49:55
 */
public enum ModulesTypeEnum {

    /**
     * seat-坐席
     * tag-标签
     * group-分组
     * clientCategory-会话渠道
     * channelEntry-会话入口
     * problemClass-咨询类型
     */
    SEAT("seat", "坐席"),

    TAGS("tags", "标签"),

    GROUP("group", "分组"),

    CLIENT_CATEGORY("clientCategory", "会话渠道"),

    CHANNEL_ENTRY("channelEntry", "会话入口"),

    PROBLEM_CLASS("problemClass", "咨询类型"),


    ;

    public final String type;

    public final String name;

    ModulesTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, ModulesTypeEnum> mapping = new HashMap<>();

    static {
        for (ModulesTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, ModulesTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        ModulesTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static ModulesTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
