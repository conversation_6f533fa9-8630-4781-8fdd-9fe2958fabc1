package com.wanshifu.iop.im.domain.bo;

import com.wanshifu.iop.im.domain.po.FlowNode;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/14 14:31
 * @description 返回机器人或者坐席
 */
@Data
public class GetConversationToAccountInfoBo {

    /**
     * 机器人对应的虚拟账号id或坐席对应的im用户id
     * */
    private String toAccount;

    /**
     * 类型：robot:机器人 ，  seat:坐席，  system:系统
     * */
    private String toAccountType;

    private FlowNode currentFlowNode;

}
