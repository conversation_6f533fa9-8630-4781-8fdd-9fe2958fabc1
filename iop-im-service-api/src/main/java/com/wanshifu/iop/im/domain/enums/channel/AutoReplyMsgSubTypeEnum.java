package com.wanshifu.iop.im.domain.enums.channel;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统自动回复消息类型枚举
 */
public enum AutoReplyMsgSubTypeEnum {
    /**
     * 会话欢迎配置
     */
    WELCOME_TIP("welcome_tip", "首次进入会话","system","visitor","欢迎语"),
    WELCOME_INTRODUCE("welcome_introduce","首次进入会话","system", "visitor","介绍语"),

    /**
     * 会话排队配置
     */
    QUEUE_UP_TIP("queue_up_tip", "排队","system", "visitor","欢迎语配置"),
    QUEUE_UP_COMFORT("queue_up_comfort", "排队","system", "visitor","欢迎介绍语"),
    /**
     * 会话结束配置
     */
    VISITOR_LEAVE_TIMEOUT_TIP("visitor_leave_timeout_tip", "访客离开超时自动关闭","visitor", "visitor","访客离开超时自动关闭"),
    SEAT_END_TIP("seat_end_tip", "seat_close","seat", "visitor","坐席主动关闭提示语"),

    SEAT_TIMEOUT_VISITOR_COMFORT("seat_timeout_visitor_comfort", "坐席超时未回复","seat", "visitor","坐席超时访客安抚语"),
    SEAT_REPLY_TIMEOUT_REMIND("seat_timeout_seat_tip", "坐席超时未回复","seat", "seat","坐席超时坐席提示语"),

    /**
     * 访客超时未回复
     */
    VISITOR_TIMEOUT_VISITOR_TIP("visitor_timeout_visitor_tip", "访客超时未回复","visitor", "visitor","访客超时访客提示语"),
    VISITOR_TIMEOUT_SEAT_TIP("visitor_timeout_seat_tip", "访客离开超时","visitor", "visitor","访客超时坐席提示"),
    VISITOR_TIMEOUT_CLOSE_VISITOR_TIP("visitor_timeout_close_visitor_tip", "坐席关闭会话","seat", "visitor","访客超时关闭访客提示语"),

    /**
     * 坐席超时未回复&坐席临近超时未回复
     */
//    SEAT_REPLY_ALREADY_TIMEOUT_REMIND("seat_reply_already_timeout_remind", "坐席已超时未回复","seat", "seat","坐席超时未回复提醒"),
//    SEAT_REPLY_NEARLY_TIMEOUT_REMIND("seat_reply_nearly_timeout_remind", "坐席临近超时未回复","seat", "seat","坐席临近超时未回复提醒"),
    /**
     * 会话样式配置
     */
    CONVERSATION_STYLE_CONFIG_TIP("conversation_style_config_tip", "会话样式默认子类型","seat", "seat","会话样式默认子类型"),
    CONVERSATION_STYLE_CONFIG_VISITOR_TIP("conversation_style_config_visitor_tip", "会话样式默认访客子类型","seat", "seat","会话样式默认访客子类型"),
    CONVERSATION_STYLE_CONFIG_SEAT_TIP("conversation_style_config_seat_tip", "会话样式默认坐席子类型","visitor", "visitor","会话样式默认坐席子类型"),

    ;

    public final String type;
    public final String triggerObject;
    public final String triggerEventName;

    public final String receiveObject;


    public final String name;

    AutoReplyMsgSubTypeEnum(String type, String triggerEventName, String triggerObject, String receiveObject, String name) {
        this.type = type;
        this.triggerEventName = triggerEventName;
        this.triggerObject = triggerObject;
        this.receiveObject = receiveObject;
        this.name = name;
    }

    private static final Map<String, AutoReplyMsgSubTypeEnum> mapping = new HashMap<>();

    static {
        for (AutoReplyMsgSubTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, AutoReplyMsgSubTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        AutoReplyMsgSubTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static AutoReplyMsgSubTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
