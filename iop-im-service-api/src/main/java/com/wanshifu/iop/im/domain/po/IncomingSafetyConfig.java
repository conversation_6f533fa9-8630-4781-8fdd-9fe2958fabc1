package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 进线安全配置
 */
@Data
@ToString
@Table(name = "incoming_safety_config")
public class IncomingSafetyConfig {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "safety_id")
    private Long safetyId;

    /**
     * 租户id
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 进线来源url
     */
    @Column(name = "url_froms")
    private String urlFroms;

    /**
     * ip注册上限，0:不限制
     */
    @Column(name = "dimension_ip_times")
    private Integer dimensionIpTimes;

    /**
     * 维度类型：daily日期维度、minute分钟维度
     */
    @Column(name = "dimension_type")
    private String dimensionType;

    /**
     * 如果是分钟维度，表示10分钟内的注册次数；如果是日期维度，表示10天内的注册次数
     */
    @Column(name = "dimension_value")
    private Integer dimensionValue;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 启用状态：1：禁用，0：启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 删除状态：0:正常，1:删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

}