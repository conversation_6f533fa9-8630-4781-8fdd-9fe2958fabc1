package com.wanshifu.iop.im.api.resp.group;

import com.wanshifu.iop.im.domain.bo.groupManage.GroupAccessRuleOptionBo;
import lombok.Data;

import java.util.List;

/**
 * 分组访问规则详情
 * <AUTHOR>
 * @date： 2025-06-30 14:38:34
 */
@Data
public class GroupAccessRuleDetailResp {

    /**
     * 全局逻辑运算符 and， or
     */
    private String globalLogicalOperator;

    /**
     * 分组接入规则集合
     */
    private List<GroupAccessRuleOptionBo> groupAccessRuleOptionList;

}
