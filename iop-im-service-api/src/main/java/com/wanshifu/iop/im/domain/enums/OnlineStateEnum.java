package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户在线状态
 * */

public enum OnlineStateEnum {

   ONLINE("online", "在线"),

   OFFLINE("offline", "离线"),


   ;

   public final String type;

   public final String name;

   OnlineStateEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, OnlineStateEnum> mapping = new HashMap<>();

   static {
       for (OnlineStateEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, OnlineStateEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static OnlineStateEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
