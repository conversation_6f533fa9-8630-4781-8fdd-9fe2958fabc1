package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 虚拟账号使用状态
 * */

public enum VirtualStatusEnum {

   NO_USED("no_used", "空闲"),

   USED("used", "使用中"),


   ;

   public final String type;

   public final String name;

   VirtualStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, VirtualStatusEnum> mapping = new HashMap<>();

   static {
       for (VirtualStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, VirtualStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static VirtualStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
