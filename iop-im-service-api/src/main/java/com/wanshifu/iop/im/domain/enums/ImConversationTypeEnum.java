package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 会话类型
 * */

public enum ImConversationTypeEnum {

    /**
     * 会话类型 online-在线会话 leave-留言 history-历史会话
     */
    online("online", "在线会话"),
    leave("leave", "留言"),
    history("history", "历史会话"),

   ;

   public final String type;

   public final String name;

   ImConversationTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, ImConversationTypeEnum> mapping = new HashMap<>();

   static {
       for (ImConversationTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, ImConversationTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static ImConversationTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
