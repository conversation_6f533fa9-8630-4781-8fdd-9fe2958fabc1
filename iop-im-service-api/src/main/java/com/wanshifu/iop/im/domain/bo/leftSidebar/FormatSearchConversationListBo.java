package com.wanshifu.iop.im.domain.bo.leftSidebar;

import com.wanshifu.iop.im.domain.enums.leftSidebar.VisitorLabelEnum;
import lombok.Data;

import java.util.List;

/**
 * 格式化后的搜索会话列表
 * <AUTHOR>
 * @date： 2025-07-16 16:10:55
 */
@Data
public class FormatSearchConversationListBo {

    /**
     * 坐席id to_outer_user_id
     */
    private String toOuterUserId;

    /**
     * 接受方 to_outer_user_type
     */
    private String toOuterUserType;

    /**
     * 会话类型 online-在线会话 leave-留言 history-历史会话
     */
    private String conversationType;

    /**
     * 打标访客
     */
    private List<String> searchMarkOuterUserIdList;

    /**
     * 全部打标访客
     */
    private List<String> allMarkOuterUserIdList;

    /**
     * 一些特殊情况下，不需要查会话表就知道没有数据，这时候就不需要再查数据库了
     * 是否没有数据 0-有数据 1-没有数据
     */
    private Integer noData = 0;

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 搜索开始时间
     */
    private String searchStartTime;

    /**
     * 搜索结束时间
     */
    private String searchEndTime;
}
