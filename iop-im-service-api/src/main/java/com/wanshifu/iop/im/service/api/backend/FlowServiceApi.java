package com.wanshifu.iop.im.service.api.backend;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.flow.FlowAddReq;
import com.wanshifu.iop.im.api.req.flow.FlowConfigReq;
import com.wanshifu.iop.im.api.req.flow.FlowDelReq;
import com.wanshifu.iop.im.api.req.flow.FlowDetailReq;
import com.wanshifu.iop.im.api.req.flow.FlowListReq;
import com.wanshifu.iop.im.api.req.flow.FlowModifyReq;
import com.wanshifu.iop.im.api.resp.flow.FlowAllListResp;
import com.wanshifu.iop.im.api.resp.flow.FlowDetailResp;
import com.wanshifu.iop.im.api.resp.flow.FlowListResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "iop-im-service",
        path = "/flow", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface FlowServiceApi {

    /**
     * 路由导航列表
     * */
    @PostMapping("/flowList")
    SimplePageInfo<FlowListResp> flowList(@Valid @RequestBody FlowListReq req);

    /**
     * 路由导航删除
     * */
    @PostMapping("/flowDel")
    Integer flowDel(@Valid @RequestBody FlowDelReq req);

    /**
     * 路由导航详情
     * */
    @PostMapping("/flowDetail")
    FlowDetailResp flowDetail(@Valid @RequestBody FlowDetailReq req);

    /**
     * 路由导航编辑
     * */
    @PostMapping("/flowModify")
    Integer flowModify(@Valid @RequestBody FlowModifyReq req);

    /**
     * 路由导航新增
     * */
    @PostMapping("/flowAdd")
    Long flowAdd(@Valid @RequestBody FlowAddReq req);

    /**
     * 路由导航编辑
     * */
    @PostMapping("/flowConfig")
    Integer flowConfig(@Valid @RequestBody FlowConfigReq req);

    /**
     * 路由导航all列表
     * */
    @PostMapping("/flowAllList")
    List<FlowAllListResp> flowAllList();
}
