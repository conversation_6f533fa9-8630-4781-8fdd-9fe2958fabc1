package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 会话访客明细表
 */
@Data
@ToString
@Table(name = "conversation_visitor_detail")
public class ConversationVisitorDetail {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "conversation_visitor_detail_id")
    private Long conversationVisitorDetailId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * ip地址
     */
    @Column(name = "ip")
    private String ip;

    /**
     * 设备id
     */
    @Column(name = "device_Id")
    private String deviceId;

    /**
     * 设备型号 web-网页 android-安卓 ios-苹果
     */
    @Column(name = "device_type")
    private String deviceType;

    /**
     * 设备版本
     */
    @Column(name = "device_version")
    private String deviceVersion;

    /**
     * 手机型号
     */
    @Column(name = "phone_model")
    private String phoneModel;

    /**
     * 会话页地址url
     */
    @Column(name = "conversation_page_url")
    private String conversationPageUrl;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}