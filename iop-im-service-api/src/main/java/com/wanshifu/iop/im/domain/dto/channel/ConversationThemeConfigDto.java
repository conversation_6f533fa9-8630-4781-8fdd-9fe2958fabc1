package com.wanshifu.iop.im.domain.dto.channel;

import lombok.Data;

import java.util.Date;

@Data
public class ConversationThemeConfigDto {
    /**
     * 会话样式配置ID
     */
    private Long conversationThemeConfigId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 端侧对象类型:  seat-坐席 visitor-访客
     */
    private String clientUserType;

    /**
     * 可选主题id列表
     */
    private String themesJson;

    /**
     * 当前选择主题ID
     */
    private Long themeId;

    /**
     * 端侧ID
     */
    private Long clientCategoryId;

    /**
     * 启用状态  1：启用，0：禁用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据修改时间
     */
    private Date updateTime;
}
