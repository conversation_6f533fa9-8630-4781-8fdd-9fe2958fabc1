package com.wanshifu.iop.im.api.req.tencent;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class ImportMsgToSingleReq {

    /**
     * 平滑过渡期间，实时消息导入，消息计入未读计数，且消息会推送到终端
     * */
    @JSONField(name = "SyncFromOldSystem")
    private Integer syncFromOldSystem;

    /**
     * 消息发送方账号
     * */
    @JSONField(name = "From_Account")
    private String fromAccount;

    /**
     * 消息接收方账号
     * */
    @JSONField(name = "To_Account")
    private String toAccount;

    /**
     * 消息序列号
     * */
    @JSONField(name = "MsgSeq")
    private Long msgSeq;

    /**
     * 随机数
     * */
    @JSONField(name = "MsgRandom")
    private Long msgRandom;

    /**
     * 消息时间
     * */
    @JSONField(name = "MsgTimeStamp")
    private Long msgTimeStamp;

    /**
     * 消息体
     * */
    @JSONField(name = "MsgBody")
    private List<MsgBodyItem> msgBody;


    @Data
    public static class MsgBodyItem {

        /**
         * 消息类型
         * */
        @JSONField(name = "MsgType")
        private String MsgType;

        @JSONField(name = "MsgContent")
        private MsgContentItem MsgContent;
    }

    @Data
    public static class MsgContentItem {
        /**
         * 消息内容
         * */
        @JSONField(name = "Text")
        private String Text;

    }

}
