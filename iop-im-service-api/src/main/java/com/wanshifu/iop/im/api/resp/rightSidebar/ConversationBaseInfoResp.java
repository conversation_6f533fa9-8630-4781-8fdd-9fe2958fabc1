package com.wanshifu.iop.im.api.resp.rightSidebar;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 会话访问信息
 * <AUTHOR>
 * @date： 2025-07-14 20:37:38
 */
@Data
public class ConversationBaseInfoResp {

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 会话开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 会话结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
