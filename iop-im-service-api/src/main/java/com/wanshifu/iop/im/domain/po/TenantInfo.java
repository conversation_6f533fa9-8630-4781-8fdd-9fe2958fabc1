package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 租户信息表
 */
@Data
@ToString
@Table(name = "tenant_info")
public class TenantInfo {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 租户名
     */
    @Column(name = "tenant_name")
    private String tenantName;

    /**
     * 描述
     */
    @Column(name = "tencent_desc")
    private String tencentDesc;

    /**
     * 状态：1：删除，0：正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

}