package com.wanshifu.iop.im.api.req.tencent;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/19 17:17
 * @description TODO
 */
@Data
public class DelMemberFromGroupTencentReq {

    /**
     * 群id
     * */
    @JSONField(name = "GroupId")
    private String GroupId;

    /**
     * 需要删除的成员
     * */
    @JSONField(name = "MemberToDel_Account")
    private List<String> MemberToDel_Account;
}
