package com.wanshifu.iop.im.domain.bo.groupManage;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 分组访问规则详情
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
public class GroupAccessRuleOptionBo {

    /**
     * 规则ID
     */
    private Integer ruleMetricId;

    /**
     * 比较运算符 eq，neq
     */
    private String comparisonOperator;

    /**
     * 值
     */
    private List<Long> configValueList = new ArrayList<>();

    /**
     * 子级
     */
    private List<GroupAccessRuleOptionBo> children;
}
