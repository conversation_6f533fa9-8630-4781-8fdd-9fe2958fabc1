package com.wanshifu.iop.im.api.req.leftSidebar;

import com.wanshifu.framework.core.BusinessException;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 访客标签移除
 * <AUTHOR>
 * @date： 2025-07-14 20:22:55
 */
@Data
public class SeatUnmarkForVisitorReq extends BaseOnlineSeatReq {

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 打标内容
     */
    private String removeLabel;

    public void checkParams() {
        super.checkParams();
        if (conversationId == null || conversationId <= 0) {
            throw new BusinessException("conversationId is null");
        }
        if (StringUtils.isBlank(removeLabel)) {
            throw new BusinessException("removeLabel is null");
        }
    }
}
