package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum SeatSidebarTypeEnum {

    CHAT("chat", "聊天"),
    ;

    public final String type;

    public final String name;

    SeatSidebarTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, SeatSidebarTypeEnum> mapping = new HashMap<>();

    static {
        for (SeatSidebarTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, SeatSidebarTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        SeatSidebarTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static SeatSidebarTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
