package com.wanshifu.iop.im.domain.builder;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.channel.*;
import com.wanshifu.iop.im.api.resp.channel.GetAutoReplyConfigResp;
import com.wanshifu.iop.im.api.resp.channel.GetSatisfactionLevelConfigResp;
import com.wanshifu.iop.im.domain.bo.CreateConversationStyleConfigBo;
import com.wanshifu.iop.im.domain.bo.channel.*;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyMsgSubTypeEnum;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyTypeEnum;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.iop.im.domain.po.channel.ClientCategoryConfig;
import com.wanshifu.iop.im.domain.po.channel.ClientChannelConfigDraft;
import com.wanshifu.iop.im.domain.po.channel.SatisfactionEvaluateRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

public class ChannelBoBuilder {

    private static final String CHANNEL_CONFIG_BO_LIST = "channelConfigBoList";
    public static String visitor = "visitor";

    /************************************** 构建resp或Bo ****************************************/

    public static List<ChannelBo> buildChannelBoList(CreateChannelReq req) {
        return Collections.singletonList(buildSingleChannelBo(req));
    }

    public static List<ChannelBo> buildChannelBoList(UpdateChannelReq req) {
        return Collections.singletonList(buildSingleChannelBo(req));
    }

    public static <T> ChannelBo buildSingleChannelBo(T source) {
        ChannelBo channelBo = new ChannelBo();
        BeanUtils.copyProperties(source, channelBo);
        if (Objects.isNull(channelBo.getCreateTime())) {
            channelBo.setCreateTime(new Date());
        }
        channelBo.setUpdateTime(new Date());
        return channelBo;
    }

    public static ClientCategoryBo buildClientCategoryConfigBo(ClientChannelConfigDraft draft) {
        ClientCategoryBo target = new ClientCategoryBo();
        BeanUtils.copyProperties(draft, target);
        if (draft.getClientChannelConfigDraftId() != null) {
            target.setClientCategoryConfigId(draft.getClientChannelConfigDraftId());
        }
        if (target.getCreateTime() == null) {
            target.setCreateTime(new Date());
        }
        return target;
    }

    public static AllConfigJsonBo buildAllConfigJsonBoByFastJson(String allConfigJson) {
        if (StringUtils.isEmpty(allConfigJson)) return null;
        try {
            JSONObject jsonObject = JSON.parseObject(allConfigJson);
            List<ChannelBo> channelBoList = jsonObject.getObject(CHANNEL_CONFIG_BO_LIST, new TypeReference<List<ChannelBo>>() {
            });
            AllConfigJsonBo configJsonBo = new AllConfigJsonBo();

            configJsonBo.setChannelConfigBoList(channelBoList);
            return configJsonBo;
        } catch (Exception e) {
            throw new IllegalArgumentException("JSON 格式解析失败：" + allConfigJson, e);
        }
    }

    public static AllConfigJsonBo buildAllConfigJsonBo(String allConfigJson) {
        if (StringUtils.isEmpty(allConfigJson)) {
            return null;
        }
        try {
            // 使用 Jackson 解析 JSON
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonObject = objectMapper.readTree(allConfigJson);

            // 提取 channelConfigBoList
            JsonNode channelConfigBoListNode = jsonObject.get(CHANNEL_CONFIG_BO_LIST);
            List<ChannelBo> channelBoList = new ArrayList<>();

            if (channelConfigBoListNode != null && channelConfigBoListNode.isArray()) {
                for (JsonNode node : channelConfigBoListNode) {
                    ChannelBo channelBo = objectMapper.treeToValue(node, ChannelBo.class);
                    channelBoList.add(channelBo);
                }
            }

            AllConfigJsonBo configJsonBo = new AllConfigJsonBo();
            configJsonBo.setChannelConfigBoList(channelBoList);
            return configJsonBo;
        } catch (Exception e) {
            throw new IllegalArgumentException("JSON 格式解析失败：" + allConfigJson, e);
        }
    }

    public static GetSatisfactionLevelConfigResp buildGetSatisfactionLevelConfigResp(GetSatisfactionLevelConfigReq req, ClientChannelConfigDraft draft, String satisfactionLevelConfigInitJson, String allConfigInitJson) {
        AllConfigJsonBo beforeAllConfigJsonBo = buildAllConfigJsonBo(draft.getAllConfigJson());
        if (beforeAllConfigJsonBo == null || CollectionUtils.isEmpty(beforeAllConfigJsonBo.getChannelConfigBoList())) {
            beforeAllConfigJsonBo = buildAllConfigJsonBo(allConfigInitJson);
            Assert.notNull(beforeAllConfigJsonBo, "初始化配置JSON格式有误！");
            Assert.notEmpty(beforeAllConfigJsonBo.getChannelConfigBoList(), "初始化配置JSON格式有误！");

            beforeAllConfigJsonBo.getChannelConfigBoList().forEach(channelBo -> {
                // 复制请求属性到 channelBo
                channelBo.setChannelId(req.getChannelId());
                channelBo.setClientCategoryId(req.getClientCategoryId());
                channelBo.setOperatorId(req.getOperatorId());
                channelBo.setStatus(0);
                SatisfactionLevelConfigBo satisfactionLevelConfigBo = channelBo.getSatisfactionLevelConfigBo();
                if(Objects.nonNull(satisfactionLevelConfigBo)) {
                    if(Objects.nonNull(req.getSatisfactionLevelConfigId())) {
                        satisfactionLevelConfigBo.setSatisfactionLevelConfigId(req.getSatisfactionLevelConfigId());
                    }
                    satisfactionLevelConfigBo.setStatus(0);
                }
            });
        }

        ChannelBo channelBo = beforeAllConfigJsonBo.getChannelConfigBoList().stream()
                .filter(f -> f.getChannelId().equals(req.getChannelId()))
                .findFirst()
                .orElse(null);

        GetSatisfactionLevelConfigResp resp = new GetSatisfactionLevelConfigResp();
        if (Objects.nonNull(channelBo)) {
            // 初始化默认字段
            SatisfactionLevelConfigBo satisfactionLevelConfigBo = channelBo.getSatisfactionLevelConfigBo();
            if(Objects.isNull(satisfactionLevelConfigBo)) {
                channelBo.setSatisfactionLevelConfigBo(ChannelBoBuilder.buildSatisfactionLevelConfigBo(req, satisfactionLevelConfigInitJson));
            }else {
                satisfactionLevelConfigBo.setChannelId(req.getChannelId());
                satisfactionLevelConfigBo.setClientCategoryId(req.getClientCategoryId());
                satisfactionLevelConfigBo.setOperatorId(req.getOperatorId());
                satisfactionLevelConfigBo.setUpdateTime(new Date());
            }
            satisfactionLevelConfigBo.setClientCategoryId(req.getClientCategoryId());
            satisfactionLevelConfigBo.setChannelId(req.getChannelId());

            if(Objects.isNull(satisfactionLevelConfigBo.getStatus())) {
                // 满意度状态默认开启
                satisfactionLevelConfigBo.setStatus(0);
            }

            //satisfactionLevelConfigBo.setResultCallbackFormJsonBo(req.getResultCallbackFormJsonBo());
            resp.setSatisfactionLevelConfigBo(satisfactionLevelConfigBo);
        }
        return resp;
    }

//    public static GetAutoReplyConfigResp buildGetAutoReplyConfigResp(GetAutoReplyConfigReq req, ClientChannelConfigDraft clientChannelConfigDraft, String defaultAutoReplyJson, String defaultAllJson) {
//        AllConfigJsonBo beforeAllConfigJsonBo = buildAllConfigJsonBo(clientChannelConfigDraft.getAllConfigJson());
//        if (beforeAllConfigJsonBo == null || CollectionUtils.isEmpty(beforeAllConfigJsonBo.getChannelConfigBoList())) {
//            beforeAllConfigJsonBo = buildAllConfigJsonBo(defaultAllJson);
//            Assert.notNull(beforeAllConfigJsonBo, "初始化配置JSON格式有误！");
//            Assert.notEmpty(beforeAllConfigJsonBo.getChannelConfigBoList(), "初始化配置JSON格式有误！");
//
//            beforeAllConfigJsonBo.getChannelConfigBoList().forEach(channelBo -> {
//                // 复制请求属性到 channelBo
//                BeanUtils.copyProperties(req, channelBo);
//            });
//
//        }
//
//        ChannelBo channelBo = beforeAllConfigJsonBo.getChannelConfigBoList().stream()
//                .filter(f -> f.getChannelId().equals(req.getChannelId()))
//                .findFirst()
//                .orElse(null);
//
//        if(Objects.isNull(channelBo)) {
//            throw new BusinessException("未找到该渠道"+ req.getChannelId());
//        }
//        GetAutoReplyConfigResp resp = new GetAutoReplyConfigResp();
//
//        List<ConversationAutoReplyConfigBo> autoReplyConfigBoList = channelBo.getConversationAutoReplyConfigBoList();
//
//        if (autoReplyConfigBoList == null) {
//            autoReplyConfigBoList = new ArrayList<>();
//        }
//
//        // 设置默认值
//        ConversationAutoReplyConfigBo autoReplyConfigBo = autoReplyConfigBoList.stream()
//                .filter(f -> f.getMsgType().equals(req.getMsgType())).findFirst().orElse(null);
//        if (Objects.isNull(autoReplyConfigBo)) {
//            autoReplyConfigBo = ChannelBoBuilder.buildDefaultConversationAutoReplyConfigBo(defaultAutoReplyJson, req.getMsgType(), req.getChannelId(), req.getClientCategoryId());
//        }
//        //设置会话配置
////        if(AutoReplyTypeEnum.CONVERSATION_STYLE_CONFIG.type.equals(req.getMsgType())){
////            autoReplyConfigBo.setCreateConversationStyleConfigBos(channelBo.getCreateConversationStyleConfigBos());
////        }
//        autoReplyConfigBo.setClientCategoryId(req.getClientCategoryId());
//        autoReplyConfigBo.setChannelId(req.getChannelId());
//        resp.setConversationAutoReplyConfigBo(autoReplyConfigBo);
//        return resp;
//    }
    public static GetAutoReplyConfigResp buildGetAutoReplyConfigRespV2(GetAutoReplyConfigReq req, ClientChannelConfigDraft clientChannelConfigDraft, String defaultAutoReplyJson, String defaultAllJson) {
        AllConfigJsonBo beforeAllConfigJsonBo = buildAllConfigJsonBo(clientChannelConfigDraft.getAllConfigJson());
        if (beforeAllConfigJsonBo == null || CollectionUtils.isEmpty(beforeAllConfigJsonBo.getChannelConfigBoList())) {
            beforeAllConfigJsonBo = buildAllConfigJsonBo(defaultAllJson);
            Assert.notNull(beforeAllConfigJsonBo, "初始化配置JSON格式有误！");
            Assert.notEmpty(beforeAllConfigJsonBo.getChannelConfigBoList(), "初始化配置JSON格式有误！");

            beforeAllConfigJsonBo.getChannelConfigBoList().forEach(channelBo -> {
                // 复制请求属性到 channelBo
                BeanUtils.copyProperties(req, channelBo);
            });

        }

        ChannelBo channelBo = beforeAllConfigJsonBo.getChannelConfigBoList().stream()
                .filter(f -> f.getChannelId().equals(req.getChannelId()))
                .findFirst()
                .orElse(null);

        if(Objects.isNull(channelBo)) {
            throw new BusinessException("未找到该渠道"+ req.getChannelId());
        }
        GetAutoReplyConfigResp resp = new GetAutoReplyConfigResp();

        List<ConversationAutoReplyConfigBo> autoReplyConfigBoList = channelBo.getConversationAutoReplyConfigBoList();

        if (autoReplyConfigBoList == null) {
            autoReplyConfigBoList = new ArrayList<>();
        }

        // 设置默认值
        ConversationAutoReplyConfigBo autoReplyConfigBo = autoReplyConfigBoList.stream()
                .filter(f -> f.getMsgType().equals(req.getMsgType())).findFirst().orElse(null);
        if (Objects.isNull(autoReplyConfigBo)) {
            autoReplyConfigBo = ChannelBoBuilder.buildDefaultConversationAutoReplyConfigBo(defaultAutoReplyJson, req.getMsgType(), req.getChannelId(), req.getClientCategoryId());
        }
        ConversationAutoReplyConfigBoV2 conversationAutoReplyConfigBoV2 = new ConversationAutoReplyConfigBoV2();
        BeanUtils.copyProperties(autoReplyConfigBo, conversationAutoReplyConfigBoV2);
        //设置会话配置
        if(AutoReplyTypeEnum.CONVERSATION_STYLE_CONFIG.type.equals(req.getMsgType())){
            conversationAutoReplyConfigBoV2.getConversationAutoReplyDetailConfigBoList().forEach(createConversationStyleConfigBo -> {
                createConversationStyleConfigBo.setMsgSubType(AutoReplyMsgSubTypeEnum.CONVERSATION_STYLE_CONFIG_SEAT_TIP.type);
                createConversationStyleConfigBo.setTriggerMode(AutoReplyMsgSubTypeEnum.CONVERSATION_STYLE_CONFIG_SEAT_TIP.type);
                //确认目前只有访客需要使用样式
                createConversationStyleConfigBo.setAutoReplyReceiveObject(visitor);
                createConversationStyleConfigBo.setTriggerMode(AutoReplyMsgSubTypeEnum.CONVERSATION_STYLE_CONFIG_TIP.type);
                createConversationStyleConfigBo.setSubTypeShowSeq("1");
            });
            if(CollectionUtils.isEmpty(channelBo.getCreateConversationStyleConfigBos())){
                List<CreateConversationStyleConfigBo> createConversationStyleConfigBos = ChannelBoBuilder.buildConversationStyleConfigBos(defaultAutoReplyJson, req.getMsgType(), req.getChannelId(), req.getClientCategoryId());
                conversationAutoReplyConfigBoV2.setCreateConversationStyleConfigBos(createConversationStyleConfigBos);

            }else {
                conversationAutoReplyConfigBoV2.setCreateConversationStyleConfigBos(channelBo.getCreateConversationStyleConfigBos());
            }

        }
        conversationAutoReplyConfigBoV2.setClientCategoryId(req.getClientCategoryId());
        conversationAutoReplyConfigBoV2.setChannelId(req.getChannelId());
        resp.setConversationAutoReplyConfigBo(conversationAutoReplyConfigBoV2);
        return resp;
    }

    /**
     * 构建默认会话配置对象
     * @param defaultAutoReplyJson
     * @param msgType
     * @param channelId
     * @param clientCategoryId
     * @return
     */
    private static List<CreateConversationStyleConfigBo> buildConversationStyleConfigBos(String defaultAutoReplyJson, String msgType, Long channelId, Long clientCategoryId) {

        List<CreateConversationStyleConfigBo> createConversationStyleConfigBoList = JSONObject.parseArray(defaultAutoReplyJson, CreateConversationStyleConfigBo.class);

        //设置默认值
        for(CreateConversationStyleConfigBo configBo : createConversationStyleConfigBoList){
            configBo.setClientCategoryId(clientCategoryId);
            configBo.setChannelId(channelId);
            configBo.setMsgType(msgType); // 设置传入的 msgType
            configBo.setStatus(0);
            configBo.setIsDelete(0);
            configBo.setOperatorId(0L);
            configBo.setCreateTime(new Date());
            configBo.setUpdateTime(new Date());
        }

        return createConversationStyleConfigBoList;
    }

    private static List<ConversationAutoReplyDetailConfigBo> parseDefaultAutoReplyDetailConfigList(String msgType, String defaultJsonStr) {
        try {
            List<ConversationAutoReplyDetailConfigBo> list = JSON.parseObject(
                    defaultJsonStr,
                    new TypeReference<List<ConversationAutoReplyDetailConfigBo>>() {},
                    Feature.SupportNonPublicField,
                    Feature.DisableFieldSmartMatch
            );

            // 筛选指定 msgType
            if (CollectionUtils.isNotEmpty(list)) {
                list = list.stream()
                        .filter(f -> f.getMsgType().equals(msgType))
                        .collect(Collectors.toList());
            }

            return list;
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse JSON to List<ConversationAutoReplyDetailConfigBo>", e);
        }
    }

    /**
     * 构建默认的自动回复配置 BO，并根据 msgType 过滤配置项
     */
    private static ConversationAutoReplyConfigBo buildDefaultConversationAutoReplyConfigBo(
            String autoReplyConfigInitJson, String msgType, Long channelId, Long clientCategoryId) {
        // 校验消息类型
        if(! AutoReplyTypeEnum.getMapping().containsKey(msgType)){
            throw new BusinessException("未找到该自动回复消息类型不在范围内");
        }
        ConversationAutoReplyConfigBo configBo = new ConversationAutoReplyConfigBo();
        configBo.setConversationAutoReplyConfigId(0L);
        configBo.setClientCategoryId(clientCategoryId);
        configBo.setChannelId(channelId);
        configBo.setMsgType(msgType); // 设置传入的 msgType
        configBo.setStatus(0);
        configBo.setIsDelete(0);
        configBo.setOperatorId(0L);
        configBo.setCreateTime(new Date());
        configBo.setUpdateTime(new Date());

        // 解析传入的 JSON 模板数据
        List<ConversationAutoReplyDetailConfigBo> autoReplyDetailConfigBoList = ChannelBoBuilder.parseDefaultAutoReplyDetailConfigList(msgType, autoReplyConfigInitJson);

        // 过滤出指定 msgType 的配置项
        List<ConversationAutoReplyDetailConfigBo> filteredList = autoReplyDetailConfigBoList.stream()
                .filter(bo -> msgType.equals(bo.getMsgType())).collect(Collectors.toList());

        // 手动解析占位符字段
//        for (ConversationAutoReplyDetailConfigBo bo : filteredList) {
//            bo.parsePlaceholderJson();
//        }

        configBo.setConversationAutoReplyDetailConfigBoList(filteredList);

        return configBo;
    }

    /**
     * 构建并合并自动回复配置到 AllConfigJsonBo 中
     *
     * @param draft 自动回复草稿数据
     * @param req   请求参数
     * @return 更新后的 AllConfigJsonBo 对象
     */
    public static AllConfigJsonBo buildAndMergeAutoReplyConfig(ClientChannelConfigDraft draft, CreateOrEditAutoReplyConfigReq req) {
        // 1. 解析现有 JSON 配置
        AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(draft.getAllConfigJson());

        // 使用 Optional 安全获取 channelConfigBoList
        List<ChannelBo> channelConfigBoList = Optional.ofNullable(allConfigJsonBo)
                .map(AllConfigJsonBo::getChannelConfigBoList)
                .filter(list -> !list.isEmpty())
                .orElseGet(ArrayList::new);

        // 2. 查找或创建对应渠道配置
        ChannelBo channelBo = channelConfigBoList.stream()
                .filter(f -> f.getChannelId().equals(req.getChannelId()))
                .findFirst()
                .orElseThrow(() -> new BusinessException("渠道信息不存在 "+ req.getChannelId()) );

        // 更新渠道层的操作人、更新时间
        channelBo.setOperatorId(req.getOperatorId());
        channelBo.setUpdateTime(new Date());

        if(CollectionUtils.isEmpty(channelBo.getConversationAutoReplyConfigBoList())) {
            channelBo.setConversationAutoReplyConfigBoList(new ArrayList<>());
        }

        ConversationAutoReplyConfigBo conversationAutoReplyConfigBo = channelBo.getConversationAutoReplyConfigBoList().stream().filter(f -> f.getMsgType().equals(req.getMsgType())).findFirst()
                .orElse(null);

        ConversationAutoReplyConfigBo configBo = new ConversationAutoReplyConfigBo();

        // 更新原本就有的消息内容
        if (Objects.nonNull(conversationAutoReplyConfigBo)) {

            if (conversationAutoReplyConfigBo.getMsgType().equals(req.getMsgType())) {
                // 更新
                BeanUtils.copyProperties(req, conversationAutoReplyConfigBo);
                conversationAutoReplyConfigBo.setUpdateTime(new Date());
                if (Objects.isNull(conversationAutoReplyConfigBo.getCreateTime())) {
                    conversationAutoReplyConfigBo.setCreateTime(new Date());
                }
                conversationAutoReplyConfigBo.setOperatorId(req.getOperatorId());
                List<ConversationAutoReplyDetailConfigBo> autoReplyDetailConfigBoList = req.getConversationAutoReplyDetailConfigBoList();
                conversationAutoReplyConfigBo.setConversationAutoReplyDetailConfigBoList(autoReplyDetailConfigBoList);
            } else {
                //新增
                // 4. 更新自动回复配置
                BeanUtils.copyProperties(req, configBo); // 使用 BeanUtils 拷贝基础字段
                configBo.setConversationAutoReplyConfigId(req.getAutoReplyConfigId());
                configBo.setStatus(req.getConversationAutoReplyDetailConfigBoList().get(0).getStatus()); // 确保状态字段正确更新
                configBo.setUpdateTime(new Date()); // 更新时间设置为当前时间
                configBo.setCreateTime(new Date());
                // 如果需要处理 conversationAutoReplyDetailConfigBoList，可以在这里进行解析和赋值
                List<ConversationAutoReplyDetailConfigBo> detailConfigBos = JSON.parseObject(
                        req.getAutoReplyJson(),
                        new TypeReference<List<ConversationAutoReplyDetailConfigBo>>() {
                        },
                        Feature.DisableFieldSmartMatch,
                        Feature.SupportNonPublicField
                );

                configBo.setConversationAutoReplyDetailConfigBoList(detailConfigBos);
            }

        } else { // 新增消息类型
            // 4. 更新自动回复配置
            BeanUtils.copyProperties(req, configBo); // 使用 BeanUtils 拷贝基础字段
            configBo.setConversationAutoReplyConfigId(req.getAutoReplyConfigId());
            configBo.setStatus(req.getConversationAutoReplyDetailConfigBoList().get(0).getStatus()); // 确保状态字段正确更新
            configBo.setUpdateTime(new Date()); // 更新时间设置为当前时间
            configBo.setCreateTime(new Date());
            // 如果需要处理 conversationAutoReplyDetailConfigBoList，可以在这里进行解析和赋值
            List<ConversationAutoReplyDetailConfigBo> detailConfigBos = JSON.parseObject(
                    req.getAutoReplyJson(),
                    new TypeReference<List<ConversationAutoReplyDetailConfigBo>>() {
                    },
                    Feature.DisableFieldSmartMatch,
                    Feature.SupportNonPublicField
            );
            detailConfigBos.stream().filter(f->f.getMsgType().equals(req.getMsgType())).findFirst();

            configBo.setConversationAutoReplyDetailConfigBoList(detailConfigBos);
        }

        // 5. 更新 channelBo
        if(CollectionUtils.isNotEmpty(channelBo.getConversationAutoReplyConfigBoList())) {
            if(Objects.nonNull(configBo.getConversationAutoReplyConfigId())) {
                channelBo.getConversationAutoReplyConfigBoList().add(configBo);
            }
        } else {
            if(Objects.nonNull(configBo.getConversationAutoReplyConfigId())) {
                channelBo.setConversationAutoReplyConfigBoList(new ArrayList<>());
                channelBo.getConversationAutoReplyConfigBoList().add(configBo);
            }

        }
        //设置会话配置
        if(AutoReplyTypeEnum.CONVERSATION_STYLE_CONFIG.type.equals(req.getMsgType())){
            channelBo.setCreateConversationStyleConfigBos(req.getCreateConversationStyleConfigBos());
        }
        // 6. 更新 allConfigJsonBo 的 channelConfigBoList
        assert allConfigJsonBo != null;

        //allConfigJsonBo.setChannelConfigBoList(channelConfigBoList);
        if (allConfigJsonBo.getChannelConfigBoList() == null) {
            allConfigJsonBo.setChannelConfigBoList(new ArrayList<>(Collections.singletonList(channelBo)));
        } else {
            boolean exists = allConfigJsonBo.getChannelConfigBoList().stream()
                    .anyMatch(cb -> cb.getChannelId().equals(channelBo.getChannelId()));

            if (exists) {
                // 如果存在相同 channelId 的 ChannelBo，则替换
                allConfigJsonBo.getChannelConfigBoList().removeIf(cb -> cb.getChannelId().equals(channelBo.getChannelId()));
            }
            // 添加新的或更新的 ChannelBo
            allConfigJsonBo.getChannelConfigBoList().add(channelBo);
        }

        return allConfigJsonBo;
    }

    /************************************** 构建数据库实体 ****************************************/

    public static ClientCategoryConfig buildClientCategoryConfig(DeployClientCategoryAndChannelReq req, ClientChannelConfigDraft draft) {
        if (draft == null) {
            return null;
        }
        ClientCategoryConfig config = new ClientCategoryConfig();
        BeanUtils.copyProperties(draft, config);

        config.setClientCategoryConfigId(draft.getClientChannelConfigDraftId());

        // 初始化默认字段
        Date now = new Date();
        if (config.getCreateTime() == null) {
            config.setCreateTime(now);
        }
        config.setUpdateTime(now);
        if (config.getIsDelete() == null) {
            config.setIsDelete(0); // 默认不删除
        }

        return config;
    }

    public static void initBaseFields(Object source, Object target) {
        BeanUtils.copyProperties(target, source); // 注意方向
        Date now = new Date();

        if (target instanceof ClientCategoryConfig) {
            ClientCategoryConfig config = (ClientCategoryConfig) target;
            config.setIsDelete(0);
            if (config.getCreateTime() == null) {
                config.setCreateTime(now);
            }
            config.setUpdateTime(now);
        } else if (target instanceof ChannelConfig) {
            ChannelConfig config = (ChannelConfig) target;
            config.setIsDelete(0);
            if (config.getCreateTime() == null) {
                config.setCreateTime(now);
            }
            config.setUpdateTime(now);
        } else if (target instanceof ClientChannelConfigDraft) {
            ClientChannelConfigDraft draft = (ClientChannelConfigDraft) target;
            if (draft.getCreateTime() == null) {
                draft.setCreateTime(now);
            }
            draft.setUpdateTime(now);
        }
    }

    public static SatisfactionEvaluateRecord buildSatisfactionEvaluateRecord(SatisfactionLevelConfigBo bo) {
        SatisfactionEvaluateRecord record = new SatisfactionEvaluateRecord();
        BeanUtils.copyProperties(bo, record);
        if (record.getCreateTime() == null) {
            record.setCreateTime(new Date());
        }
        record.setUpdateTime(new Date());
        return record;
    }

    public static ClientChannelConfigDraft buildClientCategoryDraft(CreateDraftReq req) {
        ClientChannelConfigDraft draft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(req, draft);
        if (req.getClientCategoryConfigId() != null) {
            draft.setClientChannelConfigDraftId(req.getClientCategoryConfigId());
        }
        if (draft.getCreateTime() == null) {
            draft.setCreateTime(new Date());
        }
        draft.setUpdateTime(new Date());
        draft.setAllConfigJson(JSON.toJSONString(req.getChannelBoList()));
        return draft;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(ClientCategoryUpdateReq req) {
        return buildGenericClientChannelConfigDraft(req, req.getClientCategoryConfigId());
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(ClientCategoryCreateReq req) {
        return buildGenericClientChannelConfigDraft(req, req.getClientCategoryConfigId());
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(CreateChannelReq req) {
        return buildGenericClientChannelConfigDraft(req, req.getClientCategoryId());
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(EditStyleAndSideBarReq req) {
        return buildGenericClientChannelConfigDraftWithJson(req, req.getClientCategoryId(), req.getConversationStyleConfigBo());
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(CreateStyleAndSideBarReq req) {
        return buildGenericClientChannelConfigDraftWithJson(req, req.getClientCategoryId(), req.getConversationStyleConfigBo());
    }

    private static <T> ClientChannelConfigDraft buildGenericClientChannelConfigDraft(T source, Long clientId) {
        ClientChannelConfigDraft target = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(source, target);
        target.setClientChannelConfigDraftId(clientId);

        if (target.getCreateTime() == null) {
            target.setCreateTime(new Date());
        }
        target.setUpdateTime(new Date());
        target.setIsDelete(0); // 默认值统一设为 0

        return target;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(CreateOrEditSatisfactionLevelConfigReq req, ClientChannelConfigDraft beforeDraft) {
        ClientChannelConfigDraft target = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(beforeDraft, target);
        target.setClientChannelConfigDraftId(req.getClientCategoryId());
        if (target.getCreateTime() == null) {
            target.setCreateTime(new Date());
        }
        if(target.getIsDelete() ==  null) {
            target.setIsDelete(0);
        }
        if(Objects.isNull(target.getCreateTime())) {
            target.setCreateTime(new Date());
        }
        target.setUpdateTime(new Date());
        target.setOperatorId(req.getOperatorId());

        if (req.getSatisfactionLevelConfigBo() != null) {
            // 1. 构建或获取 allConfigJsonBo
            AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(target.getAllConfigJson());
            if(Objects.isNull(allConfigJsonBo)) {
                throw new BusinessException("渠道草稿不存在！");
            }

            // 2. 查找或创建对应的 channelBo
            ChannelBo channelBo = ChannelBoBuilder.fillOrCreateChannelBo(
                    allConfigJsonBo, req);

            channelBo.setOperatorId(req.getOperatorId());
            channelBo.setUpdateTime(new Date());

            // 3. 更新 satisfactionLevelConfigBo
            channelBo.setSatisfactionLevelConfigBo(req.getSatisfactionLevelConfigBo());

            // 4. 将更新后的结构写回 allConfigJsonBo
            if (allConfigJsonBo.getChannelConfigBoList() == null) {
                allConfigJsonBo.setChannelConfigBoList(new ArrayList<>());
            }

            // 确保 channelBo 已存在于列表中（避免重复添加）
            boolean exists = allConfigJsonBo.getChannelConfigBoList().stream()
                    .anyMatch(cb -> cb.getChannelId().equals(channelBo.getChannelId()));

            if (!exists) {
                allConfigJsonBo.getChannelConfigBoList().add(channelBo);
            } else {
                // 已存在，替换掉channelBo
                allConfigJsonBo.getChannelConfigBoList().stream().filter(cb -> cb.getChannelId().equals(channelBo.getChannelId())).forEach(cb -> {
                    cb.setSatisfactionLevelConfigBo(channelBo.getSatisfactionLevelConfigBo());
                    cb.setCreateConversationStyleConfigBos(channelBo.getCreateConversationStyleConfigBos());
                });
            }

            // 5. 最终序列化为 JSON 字符串
            target.setAllConfigJson(JSON.toJSONString(allConfigJsonBo));
        }
        return target;
    }

    /**
     * 查找或创建指定 channelId 的 ChannelBo
     * 设置 满意度
     */
    public static ChannelBo fillOrCreateChannelBo(AllConfigJsonBo allConfigJsonBo, CreateOrEditSatisfactionLevelConfigReq req) {
        List<ChannelBo> channelBoList = Optional.ofNullable(allConfigJsonBo.getChannelConfigBoList())
                .orElseGet(ArrayList::new);

        return  channelBoList.stream()
                .filter(cb -> cb.getChannelId().equals(req.getChannelId()))
                .findFirst().orElseGet(() -> {
                    ChannelBo newChannelBo = new ChannelBo();
                    newChannelBo.setChannelId(req.getChannelId());
                    newChannelBo.setClientCategoryId(req.getClientCategoryId());
                    newChannelBo.setCreateTime(new Date());
                    newChannelBo.setIsDelete(0);
                    newChannelBo.setStatus(0);
                    newChannelBo.setSatisfactionLevelConfigBo(req.getSatisfactionLevelConfigBo());
                    channelBoList.add(newChannelBo);
                    return newChannelBo;
                });
    }


    private static <T> ClientChannelConfigDraft buildGenericClientChannelConfigDraftWithJson(T source, Long clientId, Object jsonContent) {
        ClientChannelConfigDraft draft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(source, draft);
        draft.setClientChannelConfigDraftId(clientId);

        if (draft.getCreateTime() == null) {
            draft.setCreateTime(new Date());
        }
        draft.setUpdateTime(new Date());

        if (jsonContent != null) {
            draft.setAllConfigJson(JSON.toJSONString(jsonContent));
        }

        return draft;
    }


    public static ClientChannelConfigDraft buildClientChannelConfigDraft(UpdateChannelReq req, ClientChannelConfigDraft draftSource) {
        ClientChannelConfigDraft result = buildBaseClientChannelConfigDraft(req, req.getClientCategoryId());

        AllConfigJsonBo allConfigJsonBo = buildAllConfigJsonBo(draftSource.getAllConfigJson());
        Optional.ofNullable(allConfigJsonBo)
                .map(AllConfigJsonBo::getChannelConfigBoList).flatMap(list -> list.stream()
                        .filter(f -> f.getChannelId().equals(req.getChannelId()))
                        .findFirst()).ifPresent(channelBo -> BeanUtils.copyProperties(req, channelBo));

        result.setAllConfigJson(JSONObject.toJSONString(allConfigJsonBo));
        return result;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(DeleteChannelReq req, ClientChannelConfigDraft draftSource) {
        ClientChannelConfigDraft result = buildBaseClientChannelConfigDraft(req, req.getClientCategoryId());

        AllConfigJsonBo allConfigJsonBo = buildAllConfigJsonBo(draftSource.getAllConfigJson());
        if(Objects.nonNull(allConfigJsonBo)) {
            updateChannelIsDelete(allConfigJsonBo, req); // 更新 channelId=123456 的 isDelete 为 1 删除
            result.setAllConfigJson(JSONObject.toJSONString(allConfigJsonBo));
        }
        return result;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(SwitchChannelStatusReq req, ClientChannelConfigDraft draftSource) {
        ClientChannelConfigDraft result = buildBaseClientChannelConfigDraft(req, req.getClientCategoryId());

        AllConfigJsonBo allConfigJsonBo = buildAllConfigJsonBo(draftSource.getAllConfigJson());
        if(Objects.nonNull(allConfigJsonBo)) {
            updateChannelStatus(allConfigJsonBo, req.getOperatorId(), req.getChannelId(), req.getStatus()); // 更新 channelId=123456 的 status 为 1
            result.setAllConfigJson(JSONObject.toJSONString(allConfigJsonBo));
        }
        return result;
    }

    /**
     * 更新渠道删除状态
     * @param allConfigJsonBo 渠道配置对象
     * @param req  删除请求对象
     */
    private static void updateChannelIsDelete(AllConfigJsonBo allConfigJsonBo, DeleteChannelReq req) {
        List<ChannelBo> channelConfigBoList = Optional.ofNullable(allConfigJsonBo.getChannelConfigBoList())
                .orElse(new ArrayList<>());

        channelConfigBoList.stream()
                .filter(channelBo -> channelBo.getChannelId().equals(req.getChannelId()))
                .findFirst()
                .ifPresent(channelBo -> {
                    channelBo.setIsDelete(1);
                    channelBo.setOperatorId(req.getOperatorId());
                    channelBo.setUpdateTime(new Date());
                    System.out.println("更新 channelId=" + req.getChannelId() + " 的 isDelete 为 " + req.getIsDelete());
                });

        allConfigJsonBo.setChannelConfigBoList(channelConfigBoList);
    }

    /**
     * 更新渠道启用状态
     * @param allConfigJsonBo 渠道配置对象
     * @param channelId  待更新的渠道 ID
     * @param newStatus 新的渠道状态
     */
    private static void updateChannelStatus(AllConfigJsonBo allConfigJsonBo, Long operatorId,Long channelId, Integer newStatus) {
        List<ChannelBo> channelConfigBoList = Optional.ofNullable(allConfigJsonBo.getChannelConfigBoList())
                .orElse(new ArrayList<>());

        channelConfigBoList.stream()
                .filter(channelBo -> channelBo.getChannelId().equals(channelId))
                .findFirst()
                .ifPresent(channelBo -> {
                    channelBo.setStatus(newStatus);
                    channelBo.setUpdateTime(new Date());
                    channelBo.setOperatorId(operatorId);
                    System.out.println("更新 channelId=" + channelId + " 的 status 为 " + newStatus);
                });

        allConfigJsonBo.setChannelConfigBoList(channelConfigBoList);
    }

    private static <T> ClientChannelConfigDraft buildBaseClientChannelConfigDraft(T source, Long clientId) {
        ClientChannelConfigDraft draft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(source, draft); // 注意顺序：target, source

        if (clientId != null) {
            draft.setClientChannelConfigDraftId(clientId);
        }

        if (draft.getCreateTime() == null) {
            draft.setCreateTime(new Date());
        }
        draft.setUpdateTime(new Date());
        draft.setIsDelete(0);

        return draft;
    }

    public static ClientChannelConfigDraft buildClientChannelConfigDraft(List<ChannelBo> channelList, ClientChannelConfigDraft draftSource) {
        if (CollectionUtils.isEmpty(channelList)) {
            throw new RuntimeException("编辑的渠道列表为空！");
        }

        AllConfigJsonBo allConfigJsonBo = buildAllConfigJsonBo(draftSource.getAllConfigJson());
        if (allConfigJsonBo == null || CollectionUtils.isEmpty(allConfigJsonBo.getChannelConfigBoList())) {
            throw new RuntimeException("编辑的渠道列表为空！");
        }

        List<ChannelBo> updatedList = reBuildChannelBoList(channelList, allConfigJsonBo.getChannelConfigBoList());
        ClientChannelConfigDraft updatedDraft = new ClientChannelConfigDraft();
        BeanUtils.copyProperties(draftSource, updatedDraft);
        updatedDraft.setUpdateTime(new Date());
        updatedDraft.setAllConfigJson(buildAllConfigJsonString(updatedList));
        return updatedDraft;
    }

    public static SatisfactionLevelConfigBo buildSatisfactionLevelConfigBo(GetSatisfactionLevelConfigReq req, String satisfactionLevelConfigJson ) {
        if (StringUtils.isEmpty(satisfactionLevelConfigJson)) {
            return null;
        }
        SatisfactionLevelConfigBo satisfactionLevelConfigBo = JSON.parseObject(
                satisfactionLevelConfigJson,
                new TypeReference<SatisfactionLevelConfigBo>() {},
                Feature.SupportNonPublicField
        );
        final Long satisfactionLevelConfigId = req.getSatisfactionLevelConfigId();
        satisfactionLevelConfigBo.setSatisfactionLevelConfigId(satisfactionLevelConfigId);

        ResultCallbackFormJsonBo resultCallbackFormJsonBo = satisfactionLevelConfigBo.getResultCallbackFormJsonBo();
        if(Objects.nonNull(resultCallbackFormJsonBo)) {
            List<ResultCallbackFormJsonBo.FiledBo> filedBoList = resultCallbackFormJsonBo.getFiledBoList();
            if(CollectionUtils.isNotEmpty(filedBoList)) {
                // 排序
                filedBoList.sort(Comparator.comparingLong(ResultCallbackFormJsonBo.FiledBo::getFiledId));
                // 设置字段序号
                AtomicLong filedSeq = new AtomicLong(0L);
                filedBoList.forEach(filedBo -> {
                    filedBo.setSatisfactionLevelConfigId(satisfactionLevelConfigId);
                    filedBo.setFiledId(filedSeq.getAndIncrement());
                    // 设置按钮序号
                    if(CollectionUtils.isNotEmpty(filedBo.getOptionBoList())) {
                        AtomicInteger buttonSeq = new AtomicInteger(0);
                        filedBo.getOptionBoList().forEach(optionBo -> {
                            int andIncrement = buttonSeq.getAndIncrement();
                            optionBo.setButtonId(andIncrement);
                            if(CollectionUtils.isNotEmpty(optionBo.getButtonReasonItemList())) {
                                optionBo.getButtonReasonItemList().forEach(item-> item.setButtonId(andIncrement));
                            }
                        });
                    }
                });
            }
        }

        satisfactionLevelConfigBo.setChannelId(req.getChannelId());
        satisfactionLevelConfigBo.setOperatorId(req.getOperatorId());
        satisfactionLevelConfigBo.setCreateTime(new Date());
        satisfactionLevelConfigBo.setUpdateTime(new Date());

        return satisfactionLevelConfigBo;
    }

    public static SatisfactionLevelConfigBo buildSatisfactionLevelConfigBo(CreateOrEditSatisfactionLevelConfigReq req, String satisfactionLevelConfigJson) {
        if (StringUtils.isEmpty(satisfactionLevelConfigJson)) {
            return null;
        }
        SatisfactionLevelConfigBo satisfactionLevelConfigBo = JSON.parseObject(satisfactionLevelConfigJson,
                new TypeReference<SatisfactionLevelConfigBo>() {},
                Feature.SupportNonPublicField);

        satisfactionLevelConfigBo.setSatisfactionLevelConfigId(req.getSatisfactionLevelConfigBo().getSatisfactionLevelConfigId());
        satisfactionLevelConfigBo.setChannelId(req.getChannelId());
        satisfactionLevelConfigBo.setOperatorId(req.getOperatorId());
        satisfactionLevelConfigBo.setStatus(0);
        satisfactionLevelConfigBo.setCreateTime(new Date());
        satisfactionLevelConfigBo.setUpdateTime(new Date());

        return satisfactionLevelConfigBo;
    }


    public static List<ClientCategoryBo> buildClientCategoryConfigBoList(List<ClientChannelConfigDraft> clientChannelConfigDraftList) {
        List<ClientCategoryBo> clientCategoryBoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(clientChannelConfigDraftList)) {
            return clientCategoryBoList;
        }
        for (ClientChannelConfigDraft draft : clientChannelConfigDraftList) {
            ClientCategoryBo bo = buildClientCategoryConfigBo(draft);
            clientCategoryBoList.add(bo);
        }
        return clientCategoryBoList;
    }


    public static List<ChannelBo> reBuildChannelBoList(List<ChannelBo> soucreList, List<ChannelBo> targetList) {
        if (CollectionUtils.isEmpty(soucreList) || CollectionUtils.isEmpty(targetList)) {
            throw new RuntimeException("源或目标渠道列表为空！");
        }

        List<ChannelBo> updatedList = new ArrayList<>();
        for (ChannelBo targetBo : targetList) {
            Optional<ChannelBo> optional = soucreList.stream()
                    .filter(source -> source.getChannelId().equals(targetBo.getChannelId()))
                    .findFirst();

            optional.ifPresent(item -> {
                if (targetBo.getSatisfactionLevelConfigBo() != null) {
                    item.setSatisfactionLevelConfigBo(targetBo.getSatisfactionLevelConfigBo());
                }
                if (CollectionUtils.isNotEmpty(targetBo.getConversationStyleConfigBoList())) {
                    item.setConversationStyleConfigBoList(targetBo.getConversationStyleConfigBoList());
                }
                if (CollectionUtils.isNotEmpty(targetBo.getConversationAutoReplyConfigBoList())) {
                    item.setConversationAutoReplyConfigBoList(targetBo.getConversationAutoReplyConfigBoList());
                }
                if (CollectionUtils.isNotEmpty(targetBo.getCreateConversationStyleConfigBos())) {
                    item.setCreateConversationStyleConfigBos(targetBo.getCreateConversationStyleConfigBos());
                }
                BeanUtils.copyProperties(item, targetBo);
            });
            updatedList.add(targetBo);
        }

        return updatedList.stream()
                .sorted(Comparator.comparing(ChannelBo::getChannelSeq))
                .collect(Collectors.toList());
    }

    /********************************* JSON 工具方法 ******************************************/

    public static String buildAllConfigJsonString(List<ChannelBo> channelBoList) {
        Map<String, Object> wrapper = new HashMap<>();
        wrapper.put(CHANNEL_CONFIG_BO_LIST, channelBoList);
        return JSON.toJSONString(wrapper);
    }

}
