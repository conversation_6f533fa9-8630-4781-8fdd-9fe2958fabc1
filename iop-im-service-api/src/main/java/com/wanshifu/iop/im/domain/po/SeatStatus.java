package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 坐席状态表
 */
@Data
@ToString
@Table(name = "seat_status")
public class SeatStatus {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seat_status_id")
    private Integer seatStatusId;

    /**
     * 坐席状态名称
     */
    @Column(name = "seat_status_cn")
    private String seatStatusCn;

    /**
     * 坐席状态英文名称
     */
    @Column(name = "seat_status_en")
    private String seatStatusEn;

    /**
     * 状态说明
     */
    @Column(name = "seat_status_desc")
    private String seatStatusDesc;

    /**
     * 默认值类型 0-无 1-离线默认值 2-在线默认值
     */
    @Column(name = "default_type")
    private Integer defaultType;

    /**
     * 是否计入工时 0-否 1-是
     */
    @Column(name = "is_reckon_in_man_hour")
    private Integer isReckonInManHour;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}