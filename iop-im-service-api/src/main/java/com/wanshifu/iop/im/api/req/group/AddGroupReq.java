package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import lombok.Data;

import java.util.List;

/**
 * 添加分组
 * <AUTHOR>
 * @date： 2025-06-30 14:12:59
 */
@Data
public class AddGroupReq extends BaseTenantReq {

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 关联渠道
     */
    private Long clientCategoryId;

    /**
     * 关联入口
     */
    private List<Long> channelIdList;

    /**
     * 关联咨询类型
     */
    private List<Long> problemClassIdList;

    /**
     * 操作人
     */
    private Long operatorId;

    public void checkParams() {
        if (StringUtils.isEmpty(groupName)) {
            throw new BusinessException("分组名称不能为空");
        }
        if (groupName.length() > 10) {
            throw new BusinessException("分组名称不能超过10个字");
        }
        if (clientCategoryId == null || clientCategoryId == 0) {
            throw new BusinessException("关联渠道不能为空");
        }
        if (CollectionUtils.isEmpty(channelIdList)) {
            throw new BusinessException("关联入口不能为空");
        }
        if (CollectionUtils.isEmpty(problemClassIdList)) {
            throw new BusinessException("关联咨询类型不能为空");
        }
        if ( operatorId == null || operatorId == 0 ) {
            throw new BusinessException("操作人不能为空");
        }
    }
}
