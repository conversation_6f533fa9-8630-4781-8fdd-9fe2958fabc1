package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.iop.im.domain.bo.channel.ChannelBo;
import com.wanshifu.iop.im.domain.bo.channel.ClientCategoryBo;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CreateDraftReq {

    /**
     * 当前的端侧ID
     */
    private Long clientCategoryConfigId;

    @NotNull(message = "端侧信息不能为空")
    private ClientCategoryBo  clientCategoryBo;
    @NotNull(message = "渠道列表对象不能为空")
    private List<ChannelBo> channelBoList;

    /**
     * 操作人id
     */
    @NotNull(message = "操作人不能为空")
    @Min(1)
    private Long operatorId;

}
