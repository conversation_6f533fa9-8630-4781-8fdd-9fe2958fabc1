package com.wanshifu.iop.im.api.resp.channel;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

/**
 * @author:<PERSON><PERSON><PERSON>@wanshifu.com
 * @create:2025-07-23 22:38:20
 * @Description ：
 **/
@Data
public class GetConversationThemeInfoResp {
    /**
     * 会话样式配置ID
     */
    private Long conversationStyleConfigId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 面向端口类型:  web-web端 app-app/小程序
     */
    private String clientPortType;

    /**
     * 当前选择主题ID
     */
    private Long conversationThemeId;

    /**
     * 侧边栏配置json对象
     */
    private String sidebarJson;
    /**
     * 其他配置json对象
     */
    private String otherJson;
    /**
     * 端侧ID
     */
    private Long clientCategoryId;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 数据修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
}