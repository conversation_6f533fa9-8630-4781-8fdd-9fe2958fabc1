package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 分配策略枚举
 * <AUTHOR>
 * @date： 2025-07-02 20:46:49
 */

public enum DistributeStrategyEnum {

    /**
     * 分配策略 balanced_saturation-饱和度均衡
     */
    BALANCED_SATURATION("balanced_saturation", "饱和度均衡"),

   ;

   public final String type;

   public final String name;

   DistributeStrategyEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, DistributeStrategyEnum> mapping = new HashMap<>();

   static {
       for (DistributeStrategyEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, DistributeStrategyEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static DistributeStrategyEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
