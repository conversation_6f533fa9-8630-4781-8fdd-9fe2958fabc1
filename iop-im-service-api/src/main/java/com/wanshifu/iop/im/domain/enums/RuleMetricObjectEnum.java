package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 规则指标对象
 * <AUTHOR>
 * @date： 2025-07-01 14:15:07
 */

public enum RuleMetricObjectEnum {

    /**
     * 规则对象 group:分组规则 seat:坐席规则
     */
    GROUP("group", "分组规则", "groupRule"),

    SEAT("seat", "坐席规则", "seatRule"),

   ;

   public final String type;

   public final String name;

   public final String ruleType;

   RuleMetricObjectEnum(String type, String name, String ruleType) {
       this.type = type;
       this.name = name;
       this.ruleType = ruleType;
   }

   private static final Map<String, RuleMetricObjectEnum> mapping = new HashMap<>();

   static {
       for (RuleMetricObjectEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, RuleMetricObjectEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       if (!mapping.containsKey(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static RuleMetricObjectEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       if (!mapping.containsKey(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
