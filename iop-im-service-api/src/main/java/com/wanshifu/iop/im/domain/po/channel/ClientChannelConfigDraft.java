package com.wanshifu.iop.im.domain.po.channel;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 端侧渠道草稿记录表
 */
@Data
@ToString
@Table(name = "client_channel_config_draft")
public class ClientChannelConfigDraft {

    /**
     * 端侧ID
     */
    @Id
    @Column(name = "client_channel_config_draft_id")
    private Long clientChannelConfigDraftId;

    /**
     * 端侧名
     */
    @Column(name = "client_category_name")
    private String clientCategoryName;

    /**
     * 端侧类型: 内部-inner、外部-outer、游客-visitor
     */
    @Column(name = "client_category_type")
    private String clientCategoryType;

    /**
     * 端侧标识
     */
    @Column(name = "client_category_en")
    private String clientCategoryEn;

    /**
     * 分配规则英文名
     */
    @Column(name = "rule_indicators_config_en")
    private String ruleIndicatorsConfigEn;

    /**
     * 腾讯应用配置ID
     */
    @Column(name = "um_id")
    private Long umId;

    /**
     * 端侧+渠道 全部配置的json字符串
     */
    @Column(name = "all_config_json")
    private String allConfigJson;

    /**
     * 端侧上线状态  0-未上线  1-已上线
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}