package com.wanshifu.iop.im.domain.bo.conversation;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/17 20:06
 * @description TODO
 */
@Data
public class FormCustomerMsgContentBo {

    @JSONField(name = "Data")
    private String data;

    @Data
    public static class FromData{
        private String businessID;

        private String msgId;

        private List<FormCustomerMsgContent> problemList;

        @Data
        public static class FormCustomerMsgContent{
            private String problemName;

            private Long problemId;
        }

        private Integer submit;

        private ProblemItemResult result;

    }


    @Data
    public static class ProblemItemResult{

        private Long problemId;
        private String problemName;
        private String problemDesc;


    }

}
