package com.wanshifu.iop.im.api.req.flow;


import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FlowModifyReq {

    /**
     * 路由导航id
     * */
    @NotNull(message = "路由导航id不能为空")
    private Long flowId;

    /**
     * 更新人id
     * */
    @NotNull(message = "更新人id不能为空")
    private Long updateAccountId;

    /**
     * 路由导航名
     * */
    @NotNull(message = "路由导航名不能为空")
    private String flowName;


}
