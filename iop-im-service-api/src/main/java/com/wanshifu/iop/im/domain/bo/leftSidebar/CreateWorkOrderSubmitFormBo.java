package com.wanshifu.iop.im.domain.bo.leftSidebar;

import lombok.Data;

import java.util.List;

@Data
public class CreateWorkOrderSubmitFormBo {

    /**
     * 工单类型
     */
    private String workType;

    /**
     * 来源账户类型
     */
    private String fromAccountType;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 来源账户ID
     */
    private Long fromAccountId;

    /**
     * 目标账户ID
     */
    private Long toAccountId;

    /**
     * 创建问题ID
     */
    private Integer createProblemId;

    /**
     * 工单描述
     */
    private String workDesc;

    /**
     * 工单需求
     */
    private String workDemand;

    /**
     * 是否保护客户信息标志
     */
    private Boolean protectCustomerInfoFlag;

    /**
     * 创建退款意愿
     */
    private Integer createRefundWill;

    /**
     * 创建退款金额
     */
    private Integer createRefundAmount;

    /**
     * 工单自定义字段数据
     */
    private WorkCustomFieldData workCustomFieldData;

    /**
     * 二线工单 - 反馈对象
     */
    private String feedbackObject;

    @Data
    public static class WorkCustomFieldData {
        private List<MediaItem> image;
        private List<MediaItem> video;
        private List<MediaItem> audio;
    }

    @Data
    public static class MediaItem {
        private String name;
        private String url;
        private Long aid;
        private String hash;
        private String key;
    }
}
