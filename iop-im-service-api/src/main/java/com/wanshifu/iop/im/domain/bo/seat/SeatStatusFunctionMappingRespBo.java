package com.wanshifu.iop.im.domain.bo.seat;

import com.wanshifu.iop.im.api.req.seat.SeatSearchListReq;
import lombok.Data;

import javax.persistence.Column;

/**
 * 坐席状态功能映射查询返回参数
 * <AUTHOR>
 * @date： 2025-06-04 09:49:32
 */
@Data
public class SeatStatusFunctionMappingRespBo{

    /**
     * 坐席状态id
     */
    private Integer seatStatusId;

    /**
     * 坐席状态英文名称
     */
    private String seatStatusEn;

    /**
     * 坐席状态中文名称
     */
    private String seatStatusCn;

    /**
     * 坐席功能id
     */
    private Integer seatFunctionId;

    /**
     * 坐席功能英文名称
     */
    private String seatFunctionEn;

    /**
     * 坐席功能类型 support-支持 restrict-限制
     */
    private String seatFunctionType;

}
