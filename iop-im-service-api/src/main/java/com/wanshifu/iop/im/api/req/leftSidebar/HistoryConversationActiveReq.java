package com.wanshifu.iop.im.api.req.leftSidebar;

import com.wanshifu.framework.core.BusinessException;
import lombok.Data;

/**
 * 历史会话激活
 * <AUTHOR>
 * @date： 2025-07-14 20:22:55
 */
@Data
public class HistoryConversationActiveReq extends BaseOnlineSeatReq {

    /**
     * 会话id
     */
    private Long conversationId;

    public void checkParams() {
        super.checkParams();
        if (conversationId == null) {
            throw new BusinessException("历史会话id不能为空");
        }
    }
}
