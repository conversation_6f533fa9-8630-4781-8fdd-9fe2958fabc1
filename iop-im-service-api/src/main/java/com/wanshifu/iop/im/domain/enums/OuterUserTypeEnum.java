package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账号类型
 * */

public enum OuterUserTypeEnum {

    MERCHANT("merchant", "商家"),

    MASTER("master", "师傅"),

    ENTERPRISE("enterprise", "总包"),

    CLIENT("client", "家庭用户"),

    VIRTUAL("virtual", "虚拟账号"),

    ROBOT("robot", "机器人"),

    SEAT("seat", "坐席"),

    SYSTEM("system", "系统"),

   ;

   public final String type;

   public final String name;

   public static final List<String> SINGLE_USER_TYPE = Arrays.asList(
           MERCHANT.type, MASTER.type, ENTERPRISE.type, CLIENT.type);

   OuterUserTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, OuterUserTypeEnum> mapping = new HashMap<>();

   static {
       for (OuterUserTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, OuterUserTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static OuterUserTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
