package com.wanshifu.iop.im.domain.bo.channel;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanshifu.iop.im.domain.bo.CreateConversationStyleConfigBo;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class ConversationAutoReplyConfigBoV2 {

    /**
     * 系统自动回复配置ID
     */
    private Long conversationAutoReplyConfigId;

    /**
     * 端侧ID
     */
    private Long clientCategoryId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 提示类型：welcome_auto_reply 排队提示语 - wait_auto_reply 超时回复提醒语 timeout_auto_reply 会话结束语 end_auto_reply
     */
    private String msgType;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 数据修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;


    /**
     * 系统自动回复消息明细列表
     */
    @JSONField(name = "conversationAutoReplyDetailConfigBoList")
    private List<ConversationAutoReplyDetailConfigBo> conversationAutoReplyDetailConfigBoList;
    /**
     * 会话配置对象
     */
    @JSONField(name = "createConversationStyleConfigBos")
    private List<CreateConversationStyleConfigBo> createConversationStyleConfigBos;
}
