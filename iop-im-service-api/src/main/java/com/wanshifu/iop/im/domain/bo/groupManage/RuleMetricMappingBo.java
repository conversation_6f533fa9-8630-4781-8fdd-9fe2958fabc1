package com.wanshifu.iop.im.domain.bo.groupManage;

import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;

import java.util.List;

/**
 * 分组访问规则详情
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
public class RuleMetricMappingBo {

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 分组配置id
     */
    private Long groupRuleId;

    /**
     * 规则指标英文名称
     */
    private String ruleMetricEn;

    /**
     * 源表关联数据
     */
    private List<CommonLabelValueResp> mappingRelationList;
}
