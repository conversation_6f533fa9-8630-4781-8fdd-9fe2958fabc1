package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 业务用户账号映射表
 */
@Data
@ToString
@Table(name = "business_user_account_mapping")
public class BusinessUserAccountMapping {

    /**
     * 业务用户账号映射id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "business_user_account_mapping_id")
    private Long businessUserAccountMappingId;

    /**
     * 业务用户id(外部用户id，如外部总包id)
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户类型：outer_enterprise:外部总包
     */
    @Column(name = "user_type")
    private String userType;

    /**
     * 账号中台id
     */
    @Column(name = "account_id")
    private Long accountId;

    /**
     * 启用状态：1：禁用，0：启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}