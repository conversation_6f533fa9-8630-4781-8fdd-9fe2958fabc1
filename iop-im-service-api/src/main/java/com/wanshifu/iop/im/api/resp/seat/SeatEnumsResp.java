package com.wanshifu.iop.im.api.resp.seat;

import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;

import java.util.List;

/**
 * 坐席列表枚举
 * <AUTHOR>
 * @date： 2025-06-03 16:54:14
 */
@Data
public class SeatEnumsResp {

    /**
     * 坐席类型
     */
    private List<SeatTypeMappingEnumsResp> seatTypeMappingList;

    /**
     * 坐席标签
     */
//    private List<CommonLabelValueResp> tagMappingList;

    /**
     * 坐席启用禁用状态
     */
    private List<CommonLabelValueResp> statusMappingList;

    /**
     * 账号枚举
     */
    private List<CommonLabelValueResp> accountMappingList;

    /**
     * 分组枚举
     */
//    private List<CommonLabelValueResp> groupMappingList;

    /**
     * 坐席状态
     */
    private List<CommonLabelValueResp> canSelectSeatStatusMappingList;

}
