package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 群聊成员关系
 */
@Data
@ToString
@Table(name = "im_group_user_relation")
public class ImGroupUserRelation {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "im_user_relation_id")
    private Long imUserRelationId;

    /**
     * 内部群组id
     */
    @Column(name = "im_group_id")
    private Long imGroupId;

    /**
     * 外部用户id
     */
    @Column(name = "outer_user_id")
    private String outerUserId;

    /**
     * 用户类型，virtual:虚拟账号，seat:坐席账号
     */
    @Column(name = "outer_class_type")
    private String outerClassType;

    /**
     * 成员状态，in:是群成员，out:已退出
     */
    @Column(name = "member_status")
    private String memberStatus;

    /**
     * 删除状态，0:正常，1:删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}