package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import lombok.Data;

/**
 * 分组介入规则详情入参
 * <AUTHOR>
 * @date： 2025-06-30 14:12:59
 */
@Data
public class GroupAccessRuleDetailReq extends BaseTenantReq {

    /**
     * 分组id
     */
    private Integer groupId;

    public void checkParams() {
        if (groupId == null) {
            throw new BusinessException("分组id不能为null");
        }
    }
}
