package com.wanshifu.iop.im.api.resp.tags;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * 获取标签列表
 * <AUTHOR>
 * @date： 2025-05-29 15:43:44
 */
@Data
public class GetTagsListResp {

    /**
     * 标签id
     */
    private Integer tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签描述
     */
    private String tagDesc;

    /**
     * 启用状态：1：禁用，0：启用
     */
    private Integer status;
}
