package com.wanshifu.iop.im.api.req.leftSidebar;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import lombok.Data;

/**
 * 获取坐席信息
 * <AUTHOR>
 * @date： 2025-07-14 20:22:55
 */
@Data
public class BaseOnlineSeatReq extends BaseTenantReq {

    /**
     * 坐席id
     */
    private Long seatId;

    public void checkParams() {
        if (seatId == null || seatId <= 0) {
            throw new BusinessException("seatId不能为空");
        }
    }
}
