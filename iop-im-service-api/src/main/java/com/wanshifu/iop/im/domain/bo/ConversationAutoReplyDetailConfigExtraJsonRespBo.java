package com.wanshifu.iop.im.domain.bo;

import com.wanshifu.iop.im.domain.enums.channel.AutoReplyMsgSubTypeEnum;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyTypeEnum;
import lombok.Data;

/**
 * 查询会话自动回复详情配置
 * <AUTHOR>
 * @date： 2025-07-17 15:42:38
 */
@Data
public class ConversationAutoReplyDetailConfigExtraJsonRespBo {

    /**
     * 响应即将超时提醒 immediately_response_timeout_reminder
     * */
    private Long immediatelyResponseTimeoutReminder;

    /**
     * 响应超时提醒 response_timeout_reminder
     * */
    private Long responseTimeoutReminder;

}
