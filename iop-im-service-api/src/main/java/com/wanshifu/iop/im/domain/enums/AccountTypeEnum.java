package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

 /**
  * 账号类型
  * */

public enum AccountTypeEnum {

    USER("merchant", "商家"),

    ENTERPRISE("enterprise", "总包"),

    ENTERPRISE_CUSTOMER("enterprise_customer", "总包客服"),

    CUSTOMTER("customer", "客服"),

    MASTER("master", "师傅"),

    SYSTEM("system", "系统"),

    CLIENT("client", "客户"),

    ;

    public final String type;

    public final String name;

    AccountTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, AccountTypeEnum> mapping = new HashMap<>();

    static {
        for (AccountTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, AccountTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        return mapping.get(type).name;
    }

    public static AccountTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }
}
