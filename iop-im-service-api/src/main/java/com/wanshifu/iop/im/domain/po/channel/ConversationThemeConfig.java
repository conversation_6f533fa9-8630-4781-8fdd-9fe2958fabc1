package com.wanshifu.iop.im.domain.po.channel;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 渠道会话主题配置表
 */
@Data
@ToString
@Table(name = "conversation_theme_config")
public class ConversationThemeConfig {

    /**
     * 主题配置ID
     */
    @Id
    @Column(name = "conversation_theme_config_id")
    private Long conversationThemeConfigId;

    /**
     * 端侧对象类型:  seat-坐席 visitor-访客
     */
    @Column(name = "client_user_type")
    private String clientUserType;
    /**
     * 面向端口类型:  web-web端 app-app/小程序
     */
    @Column(name = "client_port_type")
    private String clientPortType;
    /**
     * 主题类型:  theme 主题， orderCard 订单主题样式，wordOrderCard 工单主题样式
     */
    @Column(name = "theme_type")
    private String themeType;
    /**
     * 配置json
     */
    @Column(name = "config_json")
    private String configJson;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}