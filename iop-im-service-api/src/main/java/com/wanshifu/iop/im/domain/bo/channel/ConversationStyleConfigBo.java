package com.wanshifu.iop.im.domain.bo.channel;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ConversationStyleConfigBo {
    /**
     * 会话样式配置ID
     */
    private Long conversationStyleConfigId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 端侧对象类型:  seat-坐席 visitor-访客
     */
    private String clientUserType;

    /**
     * 可选主题列表
     */
    private ThemesJsonBo themesJsonBo;

    /**
     * 当前选择主题ID
     */
    private Long conversationThemeId;

    /**
     * 侧边栏配置json对象
     */
    private SidebarJsonBo sidebarJsonBo;

    /**
     * 端侧ID
     */
    private Long clientCategoryId;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 数据修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;

    /**
     * 主题列表
     */
    @JSONField(name = "conversationThemeConfigBoList")
    private List<ConversationThemeConfigBo> conversationThemeConfigBoList;
}
