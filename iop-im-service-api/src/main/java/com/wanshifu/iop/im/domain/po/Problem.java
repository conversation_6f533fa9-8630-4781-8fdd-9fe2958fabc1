package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 问题类型表
 */
@Data
@ToString
@Table(name = "problem")
public class Problem {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "problem_id")
    private Long problemId;

    /**
     * 问题类型名称
     */
    @Column(name = "problem_name")
    private String problemName;

    /**
     * 问题类型描述
     */
    @Column(name = "problem_desc")
    private String problemDesc;

    /**
     * 父级id
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 等级
     */
    @Column(name = "level")
    private Integer level;

    /**
     * 扩展信息
     */
    @Column(name = "problem_extra")
    private String problemExtra;

    /**
     * 租户id
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 启用状态: 1-禁用, 0-启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}