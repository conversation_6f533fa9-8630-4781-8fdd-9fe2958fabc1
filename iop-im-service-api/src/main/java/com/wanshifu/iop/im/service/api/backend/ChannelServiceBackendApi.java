package com.wanshifu.iop.im.service.api.backend;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.channel.*;
import com.wanshifu.iop.im.api.resp.ImgUploadResp;
import com.wanshifu.iop.im.api.resp.channel.*;
import com.wanshifu.iop.im.domain.bo.channel.ChannelBo;
import com.wanshifu.iop.im.domain.po.channel.ClientChannelConfigDraft;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 端侧+渠道后台管理系统接口
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@FeignClient(
        value = "iop-im-service",
        path = "/channel", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface ChannelServiceBackendApi {

    /**
     * 端侧+渠道枚举
     */
    @PostMapping("/enums")
    ChannelEnumsResp enums(@Valid @RequestBody ChannelEnumsReq req);

    /**
     * 端侧+渠道列表
     */
    @PostMapping("/searchList")
    ChannelSearchListResp searchList(@Valid @RequestBody ChannelSearchListReq req);

    /**
     * 新增端侧
     */
    @PostMapping("/createClientCategory")
    Long createClientCategory(@Valid @RequestBody ClientCategoryCreateReq req);

    /**
     * 编辑端侧
     */
    @PostMapping("/editClientCategory")
    Long editClientCategory(@Valid @RequestBody ClientCategoryUpdateReq req);

    /**
     * 通过端侧ID查询渠道列表
     */
    @PostMapping("/channelList")
    List<ChannelBo> channelList(@Valid @RequestBody ChannelSearchListReq req);

    /**
     * 删除端侧
     */
    @PostMapping("/deleteClientCategory")
    Long deleteClientCategory(@Valid @RequestBody DeleteClientCategoryReq req);

    /**
     * 新增渠道
     */
    @PostMapping("/createChannel")
    Long createChannel(@Valid @RequestBody CreateChannelReq req);


    /**
     * 编辑渠道
     */
    @PostMapping("/editChannel")
    Long editChannel(@Valid @RequestBody UpdateChannelReq req);


    /**
     * 删除渠道
     */
    @PostMapping("/deleteChannel")
    Long deleteChannel(@Valid @RequestBody DeleteChannelReq req);

    /**
     * 批量编辑渠道
     */
    @PostMapping("/batchEditChannel")
    Integer batchEditChannel(@Valid @RequestBody BatchEditChannelReq req);

    /**
     * 修改渠道状态
     */
    @PostMapping("/switchChannelStatus")
    SwitchChannelStatusResp switchChannelStatus(@Valid @RequestBody SwitchChannelStatusReq req);


    /**
     * 预览端侧+渠道
     */
    @PostMapping("/previewDraft")
    PreviewClientCategoryAndChannelResp previewDraft(@Valid @RequestBody PreviewClientCategoryAndChannelReq req);

    /**
     * 发布端侧+渠道
     */
    @PostMapping("/deployConfig")
    Long deployConfig(@Valid @RequestBody DeployClientCategoryAndChannelReq req);

    /**
     * 保存端侧+渠道草稿
     */
    @PostMapping("/saveConfigDraft")
    Long saveConfigDraft(@Valid @RequestBody CreateDraftReq req);

    /**
     * 会话主题样式和侧边栏查询
     */
    @PostMapping("/getStyleAndSideBar")
    GetStyleAndSideBarResp getStyleAndSideBar(@Valid @RequestBody GetStyleAndSideBarReq req);

    /**
     * 编辑会话主题样式和侧边栏
     */
    @PostMapping("/editStyleAndSideBar")
    Long editStyleAndSideBar(@Valid @RequestBody UpdateConversationStyleConfigRqt req);

    /**
     * 新增会话主题样式和侧边栏
     */
    @PostMapping("/createStyleAndSideBar")
    Long createStyleAndSideBar(@Valid @RequestBody CreateConversationStyleConfigRqt req);


    /**
     * 会话满意度配置查询
     */
    @PostMapping("/getSatisfactionLevelConfig")
    GetSatisfactionLevelConfigResp getSatisfactionLevelConfig(@Valid @RequestBody GetSatisfactionLevelConfigReq req);

    /**
     * 新增或编辑会话满意度配置
     */
    @PostMapping("/createOrEditSatisfactionLevelConfig")
    Long createOrEditSatisfactionLevelConfig(@Valid @RequestBody CreateOrEditSatisfactionLevelConfigReq req);


    /**
     * 会话系统自动回复消息配置查询
     */
    @PostMapping("/getAutoReplyConfig")
    GetAutoReplyConfigResp getAutoReplyConfig(@Valid @RequestBody GetAutoReplyConfigReq req);

    /**
     * 编辑会话系统自动回复消息配置
     */
    @PostMapping("/createOrEditAutoReplyConfig")
    Long createOrEditAutoReplyConfig(@Valid @RequestBody CreateOrEditAutoReplyConfigReq req);

    /**
     * 按钮图标上传
     *
     */
    @PostMapping("/iconUpload")
    ImgUploadResp iconUpload(@RequestParam("satisfactionLevelConfigId") Long satisfactionLevelConfigId, @RequestParam("buttonName") String buttonName, @RequestParam("file") MultipartFile file);


    /**
     * 用户咨询问题列表
     * */
    @PostMapping("/userProblemClassList")
    SimplePageInfo<ChannelUserProblemResp> userProblemClassList(@Valid @RequestBody ChannelUserProblemClassReq req);

    /**
     * 用户咨询问题分类-删除
     * */
    @PostMapping("/userProblemClassDel")
    Integer userProblemClassDel(@Valid @RequestBody ChannelUserProblemDelReq req);

    /**
     * 用户咨询问题分类-修改状态
     * */
    @PostMapping("/userProblemClassChangeStatus")
    Integer userProblemClassChangeStatus(@Valid @RequestBody ChannelUserProblemChangeStatusReq req);

    /**
     * 用户咨询问题分类-新增
     * */
    @PostMapping("/userProblemClassAdd")
    Long userProblemClassAdd(@Valid @RequestBody ChannelUserProblemAddReq req);
    /**
     * 端侧渠道列表数据返回
     * */
    @PostMapping("/getChannelConfigDraftList")
    List<GetChannelConfigDraftListResp> getChannelConfigDraftList(@Valid @RequestBody GetChannelConfigDraftListRqt req);
}
