package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 绑定状态
 * */

public enum BindStatusEnum {

   BINDING("binding", "绑定中"),

   OVER("over", "绑定结束"),


   ;

   public final String type;

   public final String name;

   BindStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, BindStatusEnum> mapping = new HashMap<>();

   static {
       for (BindStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, BindStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static BindStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
