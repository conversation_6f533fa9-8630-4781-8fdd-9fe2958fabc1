package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.im.api.req.BaseSwitchStatusReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class GetAutoReplyConfigReq extends BaseSwitchStatusReq {
    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 渠道id
     */
    @NotNull(message = "渠道id不能为空")
    @Min(1L)
    private Long channelId;

    /**
     * 系统自动回复消息类型
     * 欢迎语 - welcome_auto_reply
     * 排队提示语 - wait_auto_reply
     * 超时回复提醒语 timeout_auto_reply
     * 会话结束语 end_auto_reply
     * 会话样式配置 conversation_style_config
     */
    @ValueIn("welcome_auto_reply,wait_auto_reply,timeout_auto_reply,end_auto_reply,conversation_style_config")
    @NotNull(message = "系统自动回复消息类型不能为空")
    private String msgType;

}
