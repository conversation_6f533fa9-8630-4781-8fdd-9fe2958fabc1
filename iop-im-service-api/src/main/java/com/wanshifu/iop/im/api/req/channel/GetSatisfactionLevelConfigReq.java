package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.iop.im.api.req.BaseSwitchStatusReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class GetSatisfactionLevelConfigReq extends BaseSwitchStatusReq {
    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 渠道id
     */
    @NotNull(message = "渠道id不能为空")
    @Min(1L)
    private Long channelId;

    /**
     * 满意度配置ID
     */
    private Long  satisfactionLevelConfigId;
}
