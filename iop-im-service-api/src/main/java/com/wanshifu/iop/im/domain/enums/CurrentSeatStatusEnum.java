package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 当前坐席状态
 * */

public enum CurrentSeatStatusEnum {

   OFFLINE("offline", "离线"),

   BUSY("busy", "忙碌"),

   ONLINE("online", "在线"),
   WAIT_CONNECT_LINE("wait_connect_line", "待接线"),
   FULL_LOAD("full_load", "满负荷"),

   ;

   public final String type;

   public final String name;

   CurrentSeatStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, CurrentSeatStatusEnum> mapping = new HashMap<>();

   static {
       for (CurrentSeatStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, CurrentSeatStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static CurrentSeatStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
