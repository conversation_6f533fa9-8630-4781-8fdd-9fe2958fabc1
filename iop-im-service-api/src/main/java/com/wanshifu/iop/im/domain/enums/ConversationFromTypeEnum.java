package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 会话来源
 * */

public enum ConversationFromTypeEnum {

   SENSITIVE("sensitive", "敏感词"),

   NORMAL("normal", "正常进入"),
   ;

   public final String type;

   public final String name;

   ConversationFromTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, ConversationFromTypeEnum> mapping = new HashMap<>();

   static {
       for (ConversationFromTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, ConversationFromTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static ConversationFromTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
