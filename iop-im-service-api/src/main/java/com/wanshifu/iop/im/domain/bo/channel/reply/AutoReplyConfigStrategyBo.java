package com.wanshifu.iop.im.domain.bo.channel.reply;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 创建或编辑自动回复配置请求参数
 */
@Data
public class AutoReplyConfigStrategyBo {
    /**
     * 系统自动回复消息类型
     * 欢迎语 - welcome_auto_reply
     * 排队提示语 - wait_auto_reply
     * 超时回复提醒语 timeout_auto_reply
     * 会话结束语 end_auto_reply
     * 会话样式配置 conversation_style_config
     */
    @ValueIn("welcome_auto_reply,wait_auto_reply,timeout_auto_reply,end_auto_reply,conversation_style_config")
    @NotNull(message = "系统自动回复消息类型不能为空")
    private String msgType;

    /**
     * 自动回复配置内容 JSON 字符串
     */
    private String autoReplyJson;

}
