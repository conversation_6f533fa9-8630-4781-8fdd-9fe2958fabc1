package com.wanshifu.iop.im.api.resp;

import com.wanshifu.iop.im.domain.enums.ErrorCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date Created in 2023/4/10 9:16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResultResp<T> {
    private Integer code;

    private String message;

    private T data;

    /**
     * 只返回data
     *
     * @param data
     * @param <T>
     * @return
     */
    protected static <T> ResultResp<T> build(T data) {
        ResultResp<T> ResultResp = new ResultResp<>();
        if (data != null) {
            ResultResp.setData(data);
        }
        return ResultResp;
    }

    /**
     * 返回错误信息枚举值code
     *
     * @param body
     * @param errorCodeEnum
     * @param <T>
     * @return
     */
    public static <T> ResultResp<T> build(T body, ErrorCodeEnum errorCodeEnum) {
        ResultResp<T> ResultResp = build(body);
        ResultResp.setCode(errorCodeEnum.code);
        ResultResp.setMessage(errorCodeEnum.desc);
        return ResultResp;
    }

    /**
     * 返回错误信息
     *
     * @param message
     * @param body
     * @param errorCodeEnum
     * @param <T>
     * @return
     */
    private static <T> ResultResp<T> build(String message, T body, ErrorCodeEnum errorCodeEnum) {
        ResultResp<T> ResultResp = build(body);
        ResultResp.setCode(errorCodeEnum.code);
        ResultResp.setMessage(message);
        return ResultResp;
    }

    /**
     * 只返回成功标识
     *
     * @param <T>
     * @return
     */
    public static <T> ResultResp<T> success() {
        return ResultResp.success(ErrorCodeEnum.SUCCESS_CODE);
    }

    /**
     * 操作成功
     *
     * @param data
     * @param <T>
     * @return
     */
    public static <T> ResultResp<T> success(T data) {
        return build(data, ErrorCodeEnum.SUCCESS_CODE);
    }

    /**
     * 操作成功--业务异常
     *
     * @param <T>
     * @return
     */
    public static <T> ResultResp<T> success(ErrorCodeEnum errorCodeEnum) {
        return build(null, errorCodeEnum);
    }

    public static <T> ResultResp<T> fail() {
        return ResultResp.build(null);
    }

    public static <T> ResultResp<T> fail(String message, ErrorCodeEnum errorCodeEnum) {
        return ResultResp.build(message, null, errorCodeEnum);
    }


    public static <T> ResultResp<T> fail(String message, T body, ErrorCodeEnum errorCodeEnum) {
        return ResultResp.build(message, body, errorCodeEnum);
    }


    /**
     * 操作失败
     *
     * @param data
     * @param <T>
     * @return
     */
    public static <T> ResultResp<T> fail(T data) {
        return build(data, ErrorCodeEnum.FAIL);
    }

    /**
     * 操作失败
     *
     * @param <T>
     * @return
     */
    public static <T> ResultResp<T> fail(ErrorCodeEnum errorCodeEnum) {
        return build(null, errorCodeEnum);
    }

    public ResultResp<T> message(String msg) {
        this.setMessage(msg);
        return this;
    }

    public ResultResp<T> code(Integer code) {
        this.setCode(code);
        return this;
    }

    public boolean isSuccess() {
        if (this.getCode().intValue() == ErrorCodeEnum.SUCCESS_CODE.code.intValue()) {
            return true;
        }
        return false;
    }
}
