package com.wanshifu.iop.im.domain.bo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class HistoryMsgBodyItemBo {

    /**
     * 消息发送方账号
     * */
    private String fromAccount;

    /**
     * 消息发送方名称
     */
    private String fromAccountName;

    /**
     * 消息发送方头像
     */
    private String fromAccountFaceUrl;

    /**
     * 消息接收方账号
     * */
    private String toAccount;

    /**
     * 消息接收方名称
     */
    private String toAccountName;

    /**
     * 消息接收方头像
     */
    private String toAccountFaceUrl;

    /**
     * 消息体
     * */
    @JSONField(name = "MsgBody")
    private List<MsgBodyItem> msgBody;


    @Data
    public static class MsgBodyItem {

        /**
         * 消息类型
         * */
        @JSONField(name = "MsgType")
        private String MsgType;

        @JSONField(name = "MsgContent")
        private MsgContentCustomElemBo MsgContent;
    }

}
