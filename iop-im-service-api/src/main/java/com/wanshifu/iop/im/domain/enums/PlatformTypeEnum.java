package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 平台类型枚举
 * <AUTHOR>
 * @date： 2025-05-29 15:49:55
 */
public enum PlatformTypeEnum {

    /**
     * kf:ams:客服系统
     * enterprise:enterprise:总包系统
     */

    KF("kf", "ams", "客服系统"),

    ENTERPRISE("enterprise", "enterprise", "总包系统"),

    ;

    public final String type;

    public final String accountType;

    public final String name;

    PlatformTypeEnum(String type, String accountType, String name) {
        this.type = type;
        this.accountType = accountType;
        this.name = name;
    }

    private static final Map<String, PlatformTypeEnum> mapping = new HashMap<>();

    static {
        for (PlatformTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, PlatformTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        PlatformTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static PlatformTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

    public static String getAccountTypeByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type).accountType;
    }

}
