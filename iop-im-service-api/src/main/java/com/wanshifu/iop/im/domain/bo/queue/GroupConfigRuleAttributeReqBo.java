package com.wanshifu.iop.im.domain.bo.queue;

import com.wanshifu.iop.im.domain.po.GroupRule;
import com.wanshifu.iop.im.domain.po.RuleMetricConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GroupConfigRuleAttributeReqBo {

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 分组规则
     */
    private List<GroupRule> groupRuleList;

    /**
     * 分组规则指标
     */
    private List<RuleMetricConfig> ruleMetricConfigs;
}
