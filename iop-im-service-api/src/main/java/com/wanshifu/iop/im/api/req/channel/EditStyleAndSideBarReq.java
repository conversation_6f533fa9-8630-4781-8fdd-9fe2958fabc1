package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.im.domain.bo.channel.ConversationStyleConfigBo;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class EditStyleAndSideBarReq {
    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 会话主题样式ID
     */
    @NotNull(message = "会话主题样式ID不能为空")
    @Min(1L)
    private Long conversationStyleConfigId;

    /**
     * 端侧类型 inner-内部坐席 outer-外部坐席 visitor-访客
     */
    @NotNull(message = "端侧类型不能为空")
    @ValueIn("inner,outer,visitor")
    private String clientUserType;

    /**
     * 端侧ID
     */
    private Long clientChannelConfigDraftId;

    /**
     * 端侧名
     */
    private String clientCategoryName;

    /**
     * 端侧类型: 内部-inner、外部-outer、游客-visitor
     */
    private String clientCategoryType;

    /**
     * 分配规则英文名称
     */
    private String clientCategoryEn;

    /**
     * 腾讯应用配置ID
     */
    private Long umId;

    /**
     * 端侧上线状态  0-未上线  1-已上线
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据修改时间
     */
    private Date updateTime;

    /**
     * 会话主题样式
     */
    private ConversationStyleConfigBo conversationStyleConfigBo;
}
