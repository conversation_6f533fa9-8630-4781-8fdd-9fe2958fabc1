package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 业务表单模板表
 */
@Data
@ToString
@Table(name = "business_from_template")
public class BusinessFromTemplate {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "business_from_template_id")
    private Long businessFromTemplateId;

    /**
     * 业务模板英文名称
     */
    @Column(name = "business_template_en")
    private String businessTemplateEn;

    /**
     * 业务模板中文名称
     */
    @Column(name = "business_template_cn")
    private String businessTemplateCn;

    /**
     * 访客类型 user-用户 master-师傅 visitor-访客
     */
    @Column(name = "user_class")
    private String userClass;

    /**
     * 模板问题类型一级id
     */
    @Column(name = "problem_id")
    private Long problemId;

    /**
     * 业务模板描述
     */
    @Column(name = "business_template_desc")
    private String businessTemplateDesc;

    /**
     * 租户id
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 启用状态: 1-禁用, 0-启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}