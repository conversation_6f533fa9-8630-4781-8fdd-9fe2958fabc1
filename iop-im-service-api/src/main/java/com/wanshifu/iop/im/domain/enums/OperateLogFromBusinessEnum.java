package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志业务场景枚举
 * <AUTHOR>
 * @date： 2025-05-29 15:49:55
 */
public enum OperateLogFromBusinessEnum {

    /**
     * add,modify,delete,changeStatus,rearrangement
     */

    ADD("add", "新增"),
    MODIFY("modify", "修改"),
    DELETE("delete", "删除"),
    CHANGE_STATUS("changeStatus", "修改状态"),
    REARRANGEMENT("rearrangement", "重新排序"),

    ;

    public final String type;

    public final String name;

    OperateLogFromBusinessEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, OperateLogFromBusinessEnum> mapping = new HashMap<>();

    static {
        for (OperateLogFromBusinessEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, OperateLogFromBusinessEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        OperateLogFromBusinessEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static OperateLogFromBusinessEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
