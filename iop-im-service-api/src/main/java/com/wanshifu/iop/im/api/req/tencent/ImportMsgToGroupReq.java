package com.wanshifu.iop.im.api.req.tencent;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class ImportMsgToGroupReq {
    /**
     * 群id
     * */
    @JSONField(name = "GroupId")
    private String groupId;

    /**
     * 触发会话更新
     * */
    @JSONField(name = "RecentContactFlag")
    private Integer RecentContactFlag;

    /**
     * 消息列表
     * */
    @JSONField(name = "MsgList")
    private List<MsgListItem> MsgList;


    @Data
    public static class MsgListItem {
        /**
         * 发送者用户ID
         * */
        @JSONField(name = "From_Account")
        private String From_Account;

        /**
         * 发送时间戳
         * */
        @JSONField(name = "SendTime")
        private Long SendTime;

        /**
         * 消息随机数，非必填
         * */
        @JSONField(name = "Random")
        private Long Random;

        /**
         * 消息内容
         * */
        @JSONField(name = "MsgBody")
        private List<MsgBodyItem> MsgBody;
    }

    @Data
    public static class MsgBodyItem {

        /**
         * 消息类型
         * */
        @JSONField(name = "MsgType")
        private String MsgType;

        /**
         * 消息内容
         * */
        @JSONField(name = "MsgContent")
        private MsgContentItem MsgContent;
    }

    @Data
    public static class MsgContentItem {
        /**
         * 消息内容
         * */
        @JSONField(name = "Text")
        private String Text;

    }

}
