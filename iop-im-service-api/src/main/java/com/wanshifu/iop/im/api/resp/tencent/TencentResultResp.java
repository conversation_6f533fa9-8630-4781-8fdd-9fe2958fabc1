package com.wanshifu.iop.im.api.resp.tencent;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class TencentResultResp {

    /**
     * 请求结果状态，OK：处理成功
     * FAIL: 失败
     * */
    @JSONField(name = "ActionStatus")
    private String ActionStatus;

    @JSONField(name = "ErrorInfo")
    private String ErrorInfo;

    @JSONField(name = "ErrorCode")
    private Integer ErrorCode;

    /**
     * 群id
     * */
    @JSONField(name = "GroupId")
    private String GroupId;


    /**
     * 消息时间戳
     * */
    @JSONField(name = "MsgTime")
    private Integer MsgTime;

    /**
    * 消息唯一标识，用户撤回
    * */
    @JSONField(name = "MsgKey")
    private String MsgKey;

    /**
     * 消息在客户端的唯一标识
     * */
    @JSONField(name = "MsgId")
    private String MsgId;


    /**
     * 消息序列号，唯一标识一条信息，可用户已读消息查询
     * */
    @JSONField(name = "MsgSeq")
    private Integer MsgSeq;

    /**
     * 消息丢弃原因；默认为空，否则表明消息被丢弃原因，当前只支持消息频控丢弃
     * */
    @JSONField(name = "MsgDropReason")
    private String MsgDropReason;

    @JSONField(name = "ImportMsgResult")
    private List<ImportMsgResultItem> ImportMsgResult;

    @Data
    public static class ImportMsgResultItem {

        /**
         * 消息序列号，唯一标示一条消息
         * */
        @JSONField(name = "MsgSeq")
        private Integer MsgSeq;

        /**
         * 消息的时间戳
         * */
        @JSONField(name = "MsgTime")
        private Integer MsgTime;

        /**
         * 导入结果
         * */
        @JSONField(name = "Result")
        private Integer Result;
    }

    @JSONField(name = "QueryResult")
    private List<QueryResultItem> queryResult;

    /**
     * 账号在线状态
     * */
    @Data
    public static class QueryResultItem{

        @JSONField(name = "To_Account")
        private String toAccount;

        /**
         * Online,PushOnline, Offline
         * */
        @JSONField(name = "Status")
        private String status;

    }

}
