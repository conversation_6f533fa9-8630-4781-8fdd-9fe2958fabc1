package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 规则指标来源类型
 * <AUTHOR>
 * @date： 2025-07-01 14:15:07
 */

public enum RuleMetricFromTypeEnum {

    /**
     * 规则来源 kf-客服系统 bigdata-大数据
     */

    KF("kf", "客服系统"),

    BIGDATA("bigdata", "大数据"),

   ;

   public final String type;

   public final String name;

   RuleMetricFromTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, RuleMetricFromTypeEnum> mapping = new HashMap<>();

   static {
       for (RuleMetricFromTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, RuleMetricFromTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       if (!mapping.containsKey(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static RuleMetricFromTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       if (!mapping.containsKey(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
