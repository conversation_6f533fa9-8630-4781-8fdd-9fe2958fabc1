package com.wanshifu.iop.im.api.req.tencent;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/29 11:37
 * @description TODO
 */
@Data
public class DeleteFriendShipReq {

    @J<PERSON>NField(name = "From_Account")
    private String From_Account;

    @JSONField(name = "To_Account")
    private List<String> To_Account;

    /**
     * 固定  Delete_Type_Single
     * */
    @JSONField(name = "DeleteType")
    private String DeleteType;
}
