package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 自动回复触发类型
 * */

public enum TriggerModeEnum {

   VISITOR_ENTER("visitor_enter", "访客提交转人工表单"),

   SEAT_ENTER("seat_enter", "坐席接线"),

   SYSTEM_ACTIVE("system_active", "系统回复"),

   SEAT_TIMEOUT("seat_timeout", "坐席超时"),

   VISITOR_TIMEOUT("visitor_timeout", "访客超时"),

   VISITOR_LEAVE("visitor_leave", "访客离开"),

    SEAT_END("seat_end", "会话结束"),

   ;

   public final String type;

   public final String name;

   TriggerModeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, TriggerModeEnum> mapping = new HashMap<>();

   static {
       for (TriggerModeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, TriggerModeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static TriggerModeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
