package com.wanshifu.iop.im.domain.bo;

import com.wanshifu.framework.core.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 核心会话信息返回参数
 * <AUTHOR>
 * @date： 2025-07-21 20:02:14
 */
@Data
@AllArgsConstructor
public class CoreActiveConversationRespBo {

    /**
     * 新会话id
     */
    private Long newConversationId;

    /**
     * 腾讯群组id
     * */
    private String outGroupId;

    /**
     * 虚拟账号id
     * */
    private String virtualUserId;

    /**
     * 访客id
     * */
    private String fromOutUserId;

    /**
     * 坐席外部id
     */
    private String toOutUserId;

    /**
     * 会话成员关系
     */
    List<HistoryConversationItemSenderInfoRespBo> historySender = new ArrayList<>();

    public void checkParams(){
        if (newConversationId == null){
            throw new BusinessException("newConversationId is null");
        }
    }
}
