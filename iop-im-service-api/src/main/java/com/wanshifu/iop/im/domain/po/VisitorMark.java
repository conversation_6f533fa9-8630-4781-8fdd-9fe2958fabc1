package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 访客打标表
 */
@Data
@ToString
@Table(name = "visitor_mark")
public class VisitorMark {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "visitor_mark_id")
    private Long visitorMarkId;

    /**
     * 打标人id
     */
    @Column(name = "account_id")
    private Long accountId;

    /**
     * 打标人类型 seat-坐席打标
     */
    @Column(name = "account_class")
    private String accountClass;

    /**
     * 外部用户id
     */
    @Column(name = "outer_user_id")
    private String outerUserId;

    /**
     * 标签值 alreadyFeedback-已反馈 needFeedback-需反馈  needLeave-需留言
     */
    @Column(name = "label_value")
    private String labelValue;

    /**
     * 标签名称 alreadyFeedback-已反馈 needFeedback-需反馈  needLeave-需留言
     */
    @Column(name = "label_name")
    private String labelName;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}