package com.wanshifu.iop.im.api.req.channel;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.iop.im.api.req.BaseSwitchStatusReq;
import com.wanshifu.iop.im.domain.bo.CreateConversationStyleConfigBo;
import com.wanshifu.iop.im.domain.bo.channel.ConversationStyleConfigBo;
import com.wanshifu.iop.im.domain.bo.channel.SatisfactionLevelConfigBo;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class CreateOrEditSatisfactionLevelConfigReq extends BaseSwitchStatusReq {
    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 渠道id
     */
    @NotNull(message = "渠道id不能为空")
    @Min(1L)
    private Long channelId;

    /**
     * 会话满意度不能为空
     */
    @NotNull(message = "渠道id不能为空")
    private SatisfactionLevelConfigBo satisfactionLevelConfigBo;
    /**
     * 会话配置对象
     */
    private List<CreateConversationStyleConfigBo> createConversationStyleConfigBos;
}
