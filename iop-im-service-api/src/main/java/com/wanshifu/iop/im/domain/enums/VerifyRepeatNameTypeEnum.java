package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 校验重复名称
 * <AUTHOR>
 * @date： 2025-05-29 15:49:55
 */
public enum VerifyRepeatNameTypeEnum {

    /**
     * seat-坐席
     * tag-标签
     * group-分组
     */
    SEAT("seat", "坐席"),

    TAGS("tags", "标签"),

    GROUP("group", "分组");

    ;

    public final String type;

    public final String name;

    VerifyRepeatNameTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, VerifyRepeatNameTypeEnum> mapping = new HashMap<>();

    static {
        for (VerifyRepeatNameTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, VerifyRepeatNameTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        VerifyRepeatNameTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static VerifyRepeatNameTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
