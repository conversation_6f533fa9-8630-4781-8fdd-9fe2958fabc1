package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;

import java.math.BigInteger;
import java.util.Date;
import javax.persistence.*;


/**
 * 在线坐席表
 */
@Data
@ToString
@Table(name = "seat_info")
public class SeatInfo {

    /**
     * 坐席id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seat_id")
    private Long seatId;

    /**
     * 坐席昵称
     */
    @Column(name = "seat_name")
    private String seatName;

    /**
     * 头像地址
     */
    @Column(name = "face_url")
    private String faceUrl;

    /**
     * 客服账号id
     */
    @Column(name = "account_id")
    private Long accountId;

    /**
     * 账号类型：ams:客服账号，包含外包，enterprise:总包账号
     */
    @Column(name = "account_type")
    private String accountType;

    /**
     * 租户id
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 坐席工号
     */
     @Column(name = "seat_no")
     private Long seatNo;


    /**
     * 坐席类型 0-普通 1-组长 2-管理员
     */
    @Column(name = "seat_type")
    private Integer seatType;

    /**
     * 关联标签id
     */
    @Column(name = "tag_id")
    private Integer tagId;

    /**
     * 当前坐席状态
     */
    @Column(name = "current_seat_status_en")
    private String currentSeatStatusEn;

    /**
     * 坐席上线状态
     */
    @Column(name = "login_seat_status_en")
    private String loginSeatStatusEn;

    /**
     * 坐席最大链接数
     */
    @Column(name = "max_wiring_quantity")
    private Integer maxWiringQuantity;

    /**
     * 坐席扩展字段
     */
    @Column(name = "seat_extra_json")
    private String seatExtraJson;

    /**
     * 启用状态：1：禁用，0：启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}