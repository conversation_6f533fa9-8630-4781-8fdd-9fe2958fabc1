package com.wanshifu.iop.im.domain.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 坐席状态默认值类型
 * <AUTHOR>
 * @date： 2025-05-29 15:49:55
 */
public enum SeatStatusDefaultTypeEnum {

    /**
     * 默认值类型 0-无 1-离线默认值 2-在线默认值
     */

    NO(0, "无"),

    OFFLINE(1, "离线默认值"),
    ONLINE(2, "在线默认值")
    ;

    public final Integer type;

    public final String name;

    SeatStatusDefaultTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<Integer, SeatStatusDefaultTypeEnum> mapping = new HashMap<>();

    static {
        for (SeatStatusDefaultTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<Integer, SeatStatusDefaultTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(Integer type) {
        if (Objects.isNull(type)) {
            return "";
        }
        SeatStatusDefaultTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static SeatStatusDefaultTypeEnum getEnumByType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
