package com.wanshifu.iop.im.api.req.leftSidebar;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import lombok.Data;

import java.util.List;

/**
 * 坐席给访客打标
 * <AUTHOR>
 * @date： 2025-07-14 20:22:55
 */
@Data
public class SeatMarkForVisitorReq extends BaseOnlineSeatReq {

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 打标内容
     */
    private List<String> markLabelList;

    public void checkParams() {
        super.checkParams();
        if (conversationId == null) {
            throw new BusinessException("会话id不能为空");
        }
        if ( CollectionUtils.isEmpty( markLabelList )) {
            throw new BusinessException("打标内容不能为空");
        }
    }
}
