package com.wanshifu.iop.im.domain.bo.socketBase;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 发送未读消息数 socket消息bo
 * <AUTHOR>
 * @date： 2025-07-24 10:38:49
 */
@Data
public class SendWaitConversationUpdateMessageBo extends BaseMessageBo {

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 最新消息时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date latestMsgTime;

    /**、
     * 最新消息内容
     */
    private String latestMsgContent;

    /**
     * 最新消息发送人
     */
    private String latestMsgSender;

    /**
     * 未读消息数
     */
    private Long unreadCount;

    /**
     * 会话超时状态 not：未超时 approach：临近超时 already：超时
     */
    private String timeoutStatus = "not";
}
