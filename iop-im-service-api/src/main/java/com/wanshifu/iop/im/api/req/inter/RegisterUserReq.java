package com.wanshifu.iop.im.api.req.inter;

import lombok.Data;

import javax.validation.constraints.NotNull;

/***
 * 注册用户入参
 * */

@Data
public class RegisterUserReq {


    /**
     * 用户ID
     * */
    private Long userId;

    /**
     * 用户类型
     * */
    @NotNull
    private String userClass;

    /**
     * 用户设备id, 用户id，必填
     * */
    @NotNull
    private String deviceId;

    /**
     * 系统来源url，验证注册合法性
     * */
    @NotNull
    private String systemFromUrl;


    /**
     * ip
     * */
    private String ip;

}
