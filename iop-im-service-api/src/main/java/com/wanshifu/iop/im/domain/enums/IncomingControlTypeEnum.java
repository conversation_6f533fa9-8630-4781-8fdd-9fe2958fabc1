package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 访问属性配置类型枚举
 * */

public enum IncomingControlTypeEnum {

    /**
     * white:白名单，black:黑名单
     */

    white("white", "白名单"),

    black("black", "黑名单"),

   ;

   public final String type;

   public final String name;

   IncomingControlTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, IncomingControlTypeEnum> mapping = new HashMap<>();

   static {
       for (IncomingControlTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, IncomingControlTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       if (!mapping.containsKey(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static IncomingControlTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       if (!mapping.containsKey(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
