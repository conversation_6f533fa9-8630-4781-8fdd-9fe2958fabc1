package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 规则指标枚举
 * <AUTHOR>
 * @date： 2025-07-01 14:15:07
 */

public enum RuleMetricConfigEnum {

    /**
     * 会话渠道-conversationChannel group
     * 会话入口-conversationEntry  group
     * 咨询类型-problemClass  group
     */
    CONVERSATION_CHANNEL("conversationChannel", "会话渠道", "group", "clientCategory"),
    CONVERSATION_ENTRY("conversationEntry", "会话入口", "group", "channelEntry"),
    PROBLEM_CLASS("problemClass", "咨询类型", "group", "problemClass"),

   ;

   public final String type;

   public final String name;

    public final String object;

    public final String action;

   RuleMetricConfigEnum(String type, String name, String object, String action) {
       this.type = type;
       this.name = name;
       this.object = object;
       this.action = action;
   }

   private static final Map<String, RuleMetricConfigEnum> mapping = new HashMap<>();

   static {
       for (RuleMetricConfigEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, RuleMetricConfigEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       if (!mapping.containsKey(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static RuleMetricConfigEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       if (!mapping.containsKey(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
