package com.wanshifu.iop.im.api.req.incomingConfig;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 白名单查询参数
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class AddWhiteAndBlackReq {

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    /**
     * 类型 white-白名单 black-黑名单
     */
    @NotNull(message = "类型不能为空")
    @ValueIn("white,black")
    private String controlType;

    /**
     * 特征类型 user-用户ID master-师傅ID enterprise-总包ID tourist-访客设备id ip-ip地址/范围
     */
    @NotNull(message = "特征类型不能为空")
    private String propertyType;

    /**
     * 特征值
     */
    @NotNull(message = "特征值不能为空")
    private String propertyValue;

    /**
     * 启用状态：1：禁用，0：启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 操作人
     */
    private Long operatorId;
}
