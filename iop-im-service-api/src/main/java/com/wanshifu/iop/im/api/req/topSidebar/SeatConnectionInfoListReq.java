package com.wanshifu.iop.im.api.req.topSidebar;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.iop.im.api.req.leftSidebar.BaseOnlineSeatReq;
import lombok.Data;

/**
 * 坐席连接信息
 * <AUTHOR>
 * @date： 2025-07-24 13:55:14
 */
@Data
public class SeatConnectionInfoListReq extends BaseOnlineSeatReq {

    /**
     * 查询类型 0-全部 1-在线
     */
    private Integer queryType;

    public void checkParams() {
        super.checkParams();

        if (queryType == null) {
            throw new BusinessException("查询类型不能为空");
        }
    }
}
