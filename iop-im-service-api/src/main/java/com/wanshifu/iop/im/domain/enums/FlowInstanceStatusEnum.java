package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 状态
 * */

public enum FlowInstanceStatusEnum {

   RUNNING("running", "进行中"),

   COMPLETED("completed", "已完成"),

   FAIL("fail", "失败"),


   ;

   public final String type;

   public final String name;

   FlowInstanceStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, FlowInstanceStatusEnum> mapping = new HashMap<>();

   static {
       for (FlowInstanceStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, FlowInstanceStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static FlowInstanceStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
