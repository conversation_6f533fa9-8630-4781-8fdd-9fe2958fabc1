package com.wanshifu.iop.im.api.req.inter;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class GetVirtualUserInfoReq {

    /**
     * 会话id
     * */
    private Long cId;

    /**
     * 关联渠道入口标识
     * */
    @NotNull
    private String channelEn;

    /**
     * 发起方
     * */
    @NotNull
    private OuterUserInfoIem from;

    /**
     * 发起方
     * */
    private OuterUserInfoIem to;

    /**
     * 渠道id  todo  前端新增channelId
     * */
    private Long channelId;

    @Data
    public static class OuterUserInfoIem {

        /**
         * 用户类型：merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,robot:机器人,seat:坐席
         * */
        @NotNull
        private String outerUserType;

        /**
         * 用户id 只有类型是坐席时，才不是真实用户id
         * */
        private String outerUserId;
    }



}
