package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 会话消息明细表
 */
@Data
@ToString
@Table(name = "im_conversation_item")
public class ImConversationItem {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "conversation_item_id")
    private Long conversationItemId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 消息发送方外部用户id
     */
    @Column(name = "from_outer_user_id")
    private String fromOuterUserId;

    /**
     * merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,robot:机器人,seat:坐席
     */
    @Column(name = "from_outer_user_type")
    private String fromOuterUserType;

    /**
     * 接收方外部用户id
     */
    @Column(name = "to_outer_user_id")
    private String toOuterUserId;

    /**
     * merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,robot:机器人,seat:坐席
     */
    @Column(name = "to_outer_user_type")
    private String toOuterUserType;

    /**
     * 群聊接待人外部用户id
     */
    @Column(name = "response_outer_user_id")
    private String responseOuterUserId;

    /**
     * 群聊接待人类型robot:机器人,seat:坐席
     */
    @Column(name = "response_outer_user_type")
    private String responseOuterUserType;


    /**
     * 消息发送时间
     */
    @Column(name = "msg_send_time")
    private Date msgSendTime;

    /**
     * 接收方是否已读消息
     */
    @Column(name = "has_read")
    private Integer hasRead;

    /**
     * 消息读取时间
     */
    @Column(name = "msg_read_time")
    private Date msgReadTime;

    /**
     * 消息类型，TIMTextElem（文本消息）,TIMLocationElem（位置消息）,TIMFaceElem（表情消息）,TIMCustomElem（自定义消息）,TIMSoundElem（语音消息）,TIMImageElem（图像消息）,TIMFileElem（文件消息）,TIMVideoFileElem（视频消息）
     */
    @Column(name = "msg_type")
    private String msgType;

    /**
     * 消息内容
     */
    @Column(name = "msg_content")
    private String msgContent;

    /**
     * 消息序列号
     */
    @Column(name = "msg_seq")
    private Long msgSeq;

    /**
     * 消息唯一标识，可用于撤回
     */
    @Column(name = "msg_key")
    private String msgKey;

    /**
     * 消息在客户端上的唯一标识
     */
    @Column(name = "msg_id")
    private String msgId;

    /**
     * 删除状态，0:正常，1:删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 消息标签 online-在线会话 leave-留言 history-历史会话
     */
    @Column(name = "msg_label")
    private String msgLabel;

}