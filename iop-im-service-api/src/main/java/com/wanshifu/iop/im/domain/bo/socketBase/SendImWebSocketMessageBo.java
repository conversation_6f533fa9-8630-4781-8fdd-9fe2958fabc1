package com.wanshifu.iop.im.domain.bo.socketBase;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.framework.core.BusinessException;
import lombok.Data;

import java.util.Date;

/**
 * im异步mq发送消息
 * <AUTHOR>
 * @date： 2025-07-24 10:38:49
 */
@Data
public class SendImWebSocketMessageBo {

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 坐席id
     */
    private Long seatId;

    /**
     * 业务类型
     */
    private String businessType;

    public void checkParams() {
        if (conversationId == null && seatId== null) {
            throw new BusinessException(" 会话id和坐席id不能同时为空 ");
        }
        if (businessType == null) {
            throw new BusinessException("businessType is null");
        }
    }
}
