package com.wanshifu.iop.im.api.req.tags;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 切换标签状态
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class SwitchTagsStatusReq {

    /**
     * 标签id
     */
    @NotNull(message = "标签id不能为空")
    private Integer tagId;

    /**
     * 启用状态：1：禁用，0：启用
     */
    private Integer status;

    /**
     * 操作人id
     */
    private Long operatorId;
}
