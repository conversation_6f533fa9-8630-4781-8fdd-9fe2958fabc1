package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 流程实例表
 */
@Data
@ToString
@Table(name = "flow_instance")
public class FlowInstance {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "instance_id")
    private Long instanceId;

    /**
     * 流程表主键id
     */
    @Column(name = "flow_define_id")
    private Long flowDefineId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 外部用户id
     */
    @Column(name = "outer_user_id")
    private String outerUserId;

    /**
     * 当前节点id
     */
    @Column(name = "current_node_id")
    private Long currentNodeId;

    /**
     * running:进行中，completed:已完成，fail:失败
     */
    @Column(name = "status")
    private String status;

    /**
     * 删除状态，1：删除，0：正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}