package com.wanshifu.iop.im.domain.bo.channel;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanshifu.iop.im.domain.bo.CreateConversationStyleConfigBo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 端侧+渠道接口
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class ChannelBo {
    /**
     * 渠道中ID
     */
    private Long channelId;

    /**
     * 渠道在端侧的序号
     */
    private Integer channelSeq;

    /**
     * 渠道中文名
     */
    private String channelName;

    /**
     * 渠道英文名
     */
    private String channelEn;

    /**
     * 渠道描述
     */
    private String channelDesc;

    /**
     * 渠道类型: 用户渠道-user_channel、订单渠道-order_channel
     */
    private String channelType;

    /**
     * 路由导航配置id
     */
    private Long flowDefineId;

    /**
     * 路由导航名称
     * */
    private String flowName;

    /**
     * 端侧ID
     */
    private Long clientCategoryId;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 预览url模版  https://www.im.wnshifu.com/client?conversationId=xxx
     */
    private String previewUrlTemplate;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 数据创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 数据修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
    /**
     * 渠道会话样式列表
     */
    @JSONField(name = "conversationStyleConfigBoList")
    private List<ConversationStyleConfigBo> conversationStyleConfigBoList;
    /**
     * 渠道会话满意度配置
     */
    @JSONField(name = "satisfactionLevelConfigBo")
    private SatisfactionLevelConfigBo satisfactionLevelConfigBo;

    /**
     * 渠道自动回复列表
     */
    @JSONField(name = "conversationAutoReplyConfigBoList")
    private List<ConversationAutoReplyConfigBo> conversationAutoReplyConfigBoList;
    /**
     * 会话配置对象
     */
    @JSONField(name = "createConversationStyleConfigBos")
    private List<CreateConversationStyleConfigBo> createConversationStyleConfigBos;
    /**
     * 更新时覆盖属性
     */
    public void overlayChannelBo(ChannelBo paramChannelBo) {
        this.setChannelId(paramChannelBo.getChannelId());
        this.setChannelName(paramChannelBo.getChannelName());
        this.setChannelEn(paramChannelBo.getChannelEn());
        this.setChannelDesc(paramChannelBo.getChannelDesc());
        this.setChannelType(paramChannelBo.getChannelType());
        this.setFlowDefineId(paramChannelBo.getFlowDefineId());
        this.setClientCategoryId(paramChannelBo.getClientCategoryId());
        this.setStatus(paramChannelBo.getStatus());
        this.setPreviewUrlTemplate(paramChannelBo.getPreviewUrlTemplate());
        this.setIsDelete(paramChannelBo.getIsDelete());
        this.setOperatorId(paramChannelBo.getOperatorId());
        this.setCreateTime(paramChannelBo.getCreateTime());
        this.setUpdateTime(new Date());
    }
}


