package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 在线状态
 * */

public enum OnlineStatusEnum {

    /**
     * 客户端登录后和即时通信 IM 后台有长连接。
     * */
   ON_LINE("Online", "前台运行状态"),

    /**
     * iOS 和 Android 进程被 kill 或因网络问题掉线，进入 PushOnline 状态，此时仍然可以接收消息的离线推送。客户端切到后台，但是进程未被手机操作系统 kill 掉时，此时状态仍是 Online。
     * */
   PUSH_ONLINE("PushOnline", "后台运行状态"),

    /**
     * 客户端主动退出登录或者客户端自上一次登录起7天之内未登录过。
     * 如果用户是多终端登录，则只要有一个终端的状态是 Online ，该字段值就是 Online。
     * */
   OFF_LINE("Offline", "离线状态"),
   ;

   public final String type;

   public final String name;

   OnlineStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, OnlineStatusEnum> mapping = new HashMap<>();

   static {
       for (OnlineStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, OnlineStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static OnlineStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
