package com.wanshifu.iop.im.domain.enums.channel;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 满意度字段类型类型枚举
 */
public enum FiledTypeEnum {
    TEXT("text", "文本"),
    BUTTON("button", "按钮"),
    RADIO("radio", "单选"),
    CHECKBOX("checkbox", "多选"),
    COMPOSITE("composite", "复合"),

    ;

    public final String type;

    public final String name;

    FiledTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, FiledTypeEnum> mapping = new HashMap<>();

    static {
        for (FiledTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, FiledTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        FiledTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static FiledTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
