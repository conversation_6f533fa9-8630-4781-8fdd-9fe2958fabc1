package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.iop.im.api.req.BasePageReq;
import lombok.Data;

/**
 * 坐席分配规则；列表
 * <AUTHOR>
 * @date： 2025-06-30 14:12:59
 */
@Data
public class SeatPriorityDistributeRuleDetailReq extends BasePageReq {

    /**
     * 分组id
     */
    private Integer groupId;

    public void checkParams() {
        if (groupId == null) {
            throw new BusinessException("分组id不能为null");
        }
    }
}
