package com.wanshifu.iop.im.domain.bo.topSidebar;

import com.wanshifu.iop.im.domain.po.GroupInfo;
import com.wanshifu.iop.im.domain.po.SeatGroupMapping;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 分组坐席合并信息
 * <AUTHOR>
 * @date： 2025-07-24 14:21:31
 */
@Data
public class GroupSeatMergeInfoRespBo {

    /**
     * 分组信息
     */
    private List<GroupInfo> groupInfoList = new ArrayList<>();

    /**
     * 坐席信息
     */
    private List<SeatInfo> seatInfoList = new ArrayList<>();

    /**
     * 坐席注册信息
     */
    private List<UserRegisterInfo> seatRegisterInfoList = new ArrayList<>();

    /**
     * 坐席分组管理关系
     */
    private List<SeatGroupMapping> seatGroupMappingList = new ArrayList<>();
}
