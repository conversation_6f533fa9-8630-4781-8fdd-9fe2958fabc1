package com.wanshifu.iop.im.api.req.tencent;

import lombok.Data;

import java.util.List;

/**
 * 单聊/群聊发送消息后回调
 * */
@Data
public class CallbackSingleSendMsgAfterReq {
    /**
     * 回调命令
     * */
    private String CallbackCommand;

    /**
     * 群组id，如果是群聊的回调有值，其他没值
     * */
    private String GroupId;

    /**
     * 发送者
     * */
    private String From_Account;

    /**
     * 接收者
     * */
    private String To_Account;

    /**
     * 消息序号
     * */

    private Long MsgSeq;

    /**
     * 消息随机数
     * */
    private Long MsgRandom;

    /**
     * 消息的发送时间戳，单位为秒
     * */
    private Long MsgTime;

    /**
     * 消息的唯一标识，可用于 REST API 撤回单聊消息
     * */
    private String MsgKey;

    /**
     * 消息在客户端上的唯一标识
     * */
    private String MsgId;

    /**
     * 是否仅发送给在线用户标识。1代表仅发送给在线用户，否则为0；
     * */
    private int OnlineOnlyFlag;

    /**
     * 该条消息的下发结果
     * */
    private int SendMsgResult;

    /**
     * 该条消息下发失败的错误信息，若消息发送成功，则为"send msg succeed"
     * */
    private String ErrorInfo;

    /**
     * 消息体，目前只支持文本类型
     * */
    private List<MsgBodyItem> MsgBody;

    /**
     * 自定义消息
     * */
    private String CloudCustomData;

    /**
     * 毫秒级别，事件触发时间戳
     * */
    private Long EventTime;

    // Getters and Setters
    @Data
    public static class MsgBodyItem {
        private String MsgType;
        private Object MsgContent;
    }

    // Getters and Setters
    @Data
    public static class MsgContent {
        private String Text;

        // Getters and Setters
    }
}
