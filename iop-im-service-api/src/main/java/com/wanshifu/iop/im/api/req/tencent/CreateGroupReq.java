package com.wanshifu.iop.im.api.req.tencent;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class CreateGroupReq {

    /**
     * 群名
     * */
    @JSONField(name = "Name")
    private String Name;

    /**
     * 群类型  固定 Public
     * */
    @JSONField(name = "Type")
    private String Type;

    @JSONField(name = "MemberList")
    private List<CreateGroupItem> MemberList;


    @Data
    public static class CreateGroupItem {
        /**
         * 外部用户id
         * */
        @JSONField(name = "Member_Account")
        private String Member_Account;

        /**
         * 角色类型，只有管理员才有，群成员不需要
         * */
        @JSONField(name = "Role")
        private String Role;

    }
}
