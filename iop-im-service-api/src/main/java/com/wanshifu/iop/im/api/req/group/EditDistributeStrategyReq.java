package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.BasePageReq;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import com.wanshifu.iop.im.domain.enums.DistributeStrategyEnum;
import lombok.Data;

/**
 * 分组分配策略编辑
 * <AUTHOR>
 * @date： 2025-06-30 14:12:59
 */
@Data
public class EditDistributeStrategyReq extends BaseTenantReq {

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 分组分配策略
     */
    private String distributeStrategy;

    /**
     * 操作人
     */
    private Long operatorId;

    public void checkParams() {
        if (groupId == null) {
            throw new BusinessException("分组id不能为null");
        }
        if (StringUtils.isBlank(distributeStrategy)) {
            throw new BusinessException("分配策略不能为null");
        }
        if (operatorId == null || operatorId == 0) {
            throw new BusinessException("操作人不能为null");
        }
        DistributeStrategyEnum enumByType = DistributeStrategyEnum.getEnumByType(distributeStrategy);
        if (enumByType == null) {
            throw new BusinessException("不存在的分配策略");
        }
    }
}
