package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ChannelUserProblemChangeStatusReq {

    /**
     * 入口id
     * */
    @NotNull
    private Long channelId;

    /**
     * 问题分类id
     * */
    @NotNull
    private Long problemId;

    /**
     * 状态，enable:启用，disable:禁用
     * */
    @NotNull
    @ValueIn("enable,disable")
    private String status;

    /**
     * 操作人id
     * */
    @NotNull
    private Long accountId;

}
