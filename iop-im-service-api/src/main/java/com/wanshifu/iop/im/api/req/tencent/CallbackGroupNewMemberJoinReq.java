package com.wanshifu.iop.im.api.req.tencent;

import lombok.Data;

import java.util.List;

@Data
public class CallbackGroupNewMemberJoinReq {

    /**
     * 回调命令类型
     * */
    private String CallbackCommand;

    /**
     * 群id
     * */
    private String GroupId;

    /**
     * 群组类型
     * */
    private String Type;

    /**
     * 入群方式：Apply（申请入群）；Invited（邀请入群）
     * */
    private String JoinType;

    /**
     * 操作者成员
     * */
    private String Operator_Account;

    /**
     * 新入群成员列表
     * */
    private List<NewMember> NewMemberList;
    /**
     * 事件发生时间戳，单位：毫秒
     * */
    private Long EventTime;

    public static class NewMember {
        private String Member_Account;
    }
}
