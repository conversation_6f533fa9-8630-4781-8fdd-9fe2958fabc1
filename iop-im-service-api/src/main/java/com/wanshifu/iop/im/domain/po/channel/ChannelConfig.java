package com.wanshifu.iop.im.domain.po.channel;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 渠道配置表
 */
@Data
@ToString
@Table(name = "channel_config")
public class ChannelConfig {

    /**
     * 渠道ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "channel_id")
    private Long channelId;

    /**
     * 渠道序号
     */
    @Column(name = "channel_seq")
    private Integer channelSeq;

    /**
     * 渠道中文名
     */
    @Column(name = "channel_name")
    private String channelName;

    /**
     * 渠道英文名
     */
    @Column(name = "channel_en")
    private String channelEn;

    /**
     * 渠道描述
     */
    @Column(name = "channel_desc")
    private String channelDesc;

    /**
     * 渠道类型: 用户渠道-user_channel、订单渠道-order_channel
     */
    @Column(name = "channel_type")
    private String channelType;

    /**
     * 路由导航配置id
     */
    @Column(name = "flow_define_id")
    private Long flowDefineId;

    /**
     * 端侧ID
     */
    @Column(name = "client_category_id")
    private Long clientCategoryId;

    /**
     * 启用状态  1：禁用，0：启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 预览url模版  https://www.im.wnshifu.com/client?conversationId=xxx
     */
    @Column(name = "preview_url_template")
    private String previewUrlTemplate;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}