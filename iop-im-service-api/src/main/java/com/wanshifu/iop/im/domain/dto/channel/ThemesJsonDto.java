package com.wanshifu.iop.im.domain.dto.channel;

import lombok.Data;

import java.util.List;

@Data
public class ThemesJsonDto {

    /**
     * 主题样式列表
     */
    private List<ThemesJsonBoItem> themesJsonBoItemList;

    @Data
    public static class ThemesJsonBoItem {
        /**
         * 主题样式ID
         */
        private Long conversationStyleConfigId;

        /**
         * 星级
         */
        private Long themeId;
        /**
         * 主题名 exp: 主题一 主题二  主题三
         */
        private String themeName;

        /**
         * 主题预览url
         */
        private String themeUrl;
        /**
         * 主题预览选定状态 0-未选定 1-选定
         */
        private Integer checkStatus;
    }
}
