package com.wanshifu.iop.im.service.api.outer;

import com.wanshifu.iop.im.api.req.channel.GetConversationThemeInfoRqt;
import com.wanshifu.iop.im.api.req.channel.PreviewClientCategoryAndChannelReq;
import com.wanshifu.iop.im.api.req.channel.SaveSatisfactionDataRqt;
import com.wanshifu.iop.im.api.resp.channel.GetConversationThemeInfoResp;
import com.wanshifu.iop.im.api.resp.channel.PreviewClientCategoryAndChannelResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 端侧+渠道前台其他系统使用接口
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@FeignClient(
        value = "iop-im-service",
        path = "/outer/channel", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface ChannelServiceApi {

    @PostMapping("/previewDraft")
    PreviewClientCategoryAndChannelResp previewDraft(@Valid @RequestBody PreviewClientCategoryAndChannelReq req);

    /**
     * 获取会话主题样式配置
     * */
    @PostMapping("/getConversationThemeInfo")
    GetConversationThemeInfoResp getConversationThemeInfo(@Valid @RequestBody GetConversationThemeInfoRqt req);
    /**
     * 保存评价满意度数据
     * */
    @PostMapping("/saveSatisfactionData")
    Integer saveSatisfactionData(@Valid @RequestBody SaveSatisfactionDataRqt req);

}
