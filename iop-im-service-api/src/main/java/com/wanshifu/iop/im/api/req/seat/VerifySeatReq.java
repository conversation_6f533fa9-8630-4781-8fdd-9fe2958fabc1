package com.wanshifu.iop.im.api.req.seat;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建坐席
 * <AUTHOR>
 * @date： 2025-06-03 17:16:34
 */
@Data
public class VerifySeatReq {

    /**
     * 所属会话组
     */
    @Size(min = 1)
    private List<Integer> groupIdList;

    /**
     * 坐席标签
     */
    private Integer tagId;

}
