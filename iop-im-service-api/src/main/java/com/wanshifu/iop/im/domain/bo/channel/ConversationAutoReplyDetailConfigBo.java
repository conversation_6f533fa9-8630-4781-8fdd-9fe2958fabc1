package com.wanshifu.iop.im.domain.bo.channel;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ConversationAutoReplyDetailConfigBo {
    /**
     * 系统自动回复明细配置ID
     */
    private Long conversationAutoReplyDetailConfigId;

    /**
     * 系统自动回复配置ID
     */
    private Long conversationAutoReplyConfigId;

    /**
     * 提示类型：欢迎语 - welcome_auto_reply、排队提示语、坐席超时回复提醒语 seat_timeout_auto_reply、访客超时回复提醒语 visitor_timeout_auto_reply end_auto_reply-会话结束语
     */
    private String msgType;
    /**
     * 提示子类型
     *   welcome_tip 欢迎语  welcome_introduce 欢迎介绍语 ,  queue_up_tip 排队提示语 queue_up_comfort 排队安抚语 , visitor_leave_timeout_close_tip- 访客离开超时关闭提示语 seat_active_close-坐席主动关闭提示语 seat_reply_timeout_comfort-坐席回复超时提示语 seat_reply_timeout_remind-坐席回复超时提醒语, reply_timeout_visitor_tip-访客超时回复关闭提示语 reply_timeout_seat_tip-坐席超时回复关闭提示语 reply_timeout_close_tip-访客离开超时关闭提示语
     * welcome_auto_reply:
     *  welcome_tip 欢迎语  welcome_introduce 欢迎介绍语
     * seat_timeout_auto_reply:
     *  queue_up_tip 排队提示语 queue_up_comfort 排队安抚语
     * visitor_timeout_auto_reply:
     *  visitor_leave_timeout_close_tip- 访客离开超时关闭提示语 seat_active_close-坐席主动关闭提示语 seat_reply_timeout_comfort-坐席回复超时提示语 seat_reply_timeout_remind-坐席回复超时提醒语
     * end_auto_reply:
     *   reply_timeout_visitor_tip-访客超时回复关闭提示语 reply_timeout_seat_tip-坐席超时回复关闭提示语 reply_timeout_close_tip-访客离开超时关闭提示语
     */
    private String msgSubType;

    /**
     * 提示子类型展示序号
     */
    private String subTypeShowSeq;

    /**
     * 系统自动消息接收对象：坐席-seat、访客-visitor
     */
    private String autoReplyReceiveObject;

    /**
     * 自动回复内容（字符串模板）
     */
    private String autoReplyContent;

    /**
     * 其他提示类型:  countdown - 倒计时
     */
    private String otherTipType;

    /**
     * 提示语触发事件类型：系统主动 - system_active、 访客回复超时 - visitor_timeout、坐席回复超时 - agent_timeout、访客主动关闭 - visitor_close、客服主动关闭 - agent-close
     */
    private String triggerMode;

    /**
     * 回复超时时长（分钟）
     */
    private String timeoutCostSecond;

    /**
     * 是否展示超时时间 0-否 1-是
     */
    private Integer timeoutShow;

    /**
     * 是否插入占位符
     */
    private Integer supportPlaceholder;

    /**
     * 扩展字符串
     */
    @JSONField(name = "extraJson")
    @JsonProperty("extraJson")
    private String extraJsonBo;

    /**
     * 介绍语开启方式 msgSubType = introduction 时必填
     * checkBox  - 多选框选框
     */
    private String introductionMode;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;
    /**
     * 扩展字段1
     */
    private String extendVal1;

    /**
     * 数据创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 数据修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
}
