package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 坐席状态功能关联表
 */
@Data
@ToString
@Table(name = "seat_status_function_mapping")
public class SeatStatusFunctionMapping {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seat_status_function_mapping_id")
    private Integer seatStatusFunctionMappingId;

    /**
     * 坐席状态id
     */
    @Column(name = "seat_status_id")
    private Integer seatStatusId;

    /**
     * 坐席状态英文名称
     */
    @Column(name = "seat_status_en")
    private String seatStatusEn;

    /**
     * 坐席功能id
     */
    @Column(name = "seat_function_id")
    private Integer seatFunctionId;

    /**
     * 坐席功能英文名称
     */
    @Column(name = "seat_function_en")
    private String seatFunctionEn;

    /**
     * 坐席功能类型 support-支持 restrict-限制
     */
    @Column(name = "seat_function_type")
    private String seatFunctionType;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}