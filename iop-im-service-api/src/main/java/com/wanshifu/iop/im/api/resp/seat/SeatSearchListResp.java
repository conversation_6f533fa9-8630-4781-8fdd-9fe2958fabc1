package com.wanshifu.iop.im.api.resp.seat;

import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * 坐席 列表
 * <AUTHOR>
 * @date： 2025-05-29 15:43:44
 */
@Data
public class SeatSearchListResp {

    /**
     * 坐席id
     */
    private Long seatId;

    /**
     * 坐席姓名 （关联账号姓名）
     */
    private String username;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 坐席昵称
     */
    private String seatName;

    /**
     * 头像地址
     */
    private String faceUrl;

    /**
     * 坐席工号
     */
    private Long seatNo;

    /**
     * 坐席类型 0-普通 1-组长 2-管理员
     */
    private Integer seatType;

    /**
     * 坐席类型 0-普通 1-组长 2-管理员
     */
    private String seatTypeName;

    /**
     * 坐席上线状态
     */
    private String loginSeatStatusEn;

    /**
     * 坐席上线状态中文名称
     */
    private String loginSeatStatusCn;

    /**
     * 当前坐席状态
     */
    private String currentSeatStatusEn;

    /**
     * 当前坐席状态中文名称
     */
    private String currentSeatStatusCn;

    /**
     * 启用状态：1：禁用，0：启用
     */
    private Integer status;

    /**
     * 启用状态名称
     */
    private String statusName;

    /**
     * 坐席标签id
     */
    private Integer tagId;

    /**
     * 坐席所在分组
     */
     private List<CommonLabelValueResp> groupMappingList;

    /**
     * 坐席标签id
     */
    private String tagName;

    /**
     * 坐席最大链接数
     */
    private Integer maxWiringQuantity;
}
