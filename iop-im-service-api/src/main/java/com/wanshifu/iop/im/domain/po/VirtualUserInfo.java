package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 虚拟用户注册信息表
 */
@Data
@ToString
@Table(name = "virtual_user_info")
public class VirtualUserInfo {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "virtual_info_id")
    private Long virtualInfoId;


    /**
     * 应用ID
     */
    @Column(name = "im_id")
    private Long imId;

    /**
     * 外部用户id
     */
    @Column(name = "outer_user_id")
    private String outerUserId;

    /**
     * 注册时间
     */
    @Column(name = "register_time")
    private Date registerTime;

    /**
     * 用户签名
     */
    @Column(name = "user_sign")
    private String userSign;

    /**
     * 签名过期时间
     */
    @Column(name = "user_sign_expire_time")
    private Date userSignExpireTime;

    /**
     * 账号状态，no_used:空闲,used:使用中
     */
    @Column(name = "status")
    private String status;

    /**
     * 删除状态，1:已删除，0:正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

}