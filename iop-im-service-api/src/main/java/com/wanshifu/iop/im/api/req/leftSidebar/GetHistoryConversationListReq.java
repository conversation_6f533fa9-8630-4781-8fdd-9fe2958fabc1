package com.wanshifu.iop.im.api.req.leftSidebar;

import lombok.Data;

import java.util.List;

/**
 * 获取历史会话列表
 * <AUTHOR>
 * @date： 2025-07-14 20:22:55
 */
@Data
public class GetHistoryConversationListReq extends BaseOnlineSeatReq {

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 会话标签
     */
    private List<String> visitorLabelList;

    /**
     * 搜索开始时间
     */
    private String searchStartTime;

    /**
     * 搜索结束时间
     */
    private String searchEndTime;

    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 页大小
     */
    private Integer pageSize = 10;
}
