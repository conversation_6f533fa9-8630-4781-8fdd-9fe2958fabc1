package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 端侧+渠道接口
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class ChannelEnumsReq {

    /**
     * 枚举类型 0：列表枚举 1：创建枚举
     */
    @NotNull(message = "enumType不能为空")
    @ValueIn("0,1")
    private Integer enumType;
}
