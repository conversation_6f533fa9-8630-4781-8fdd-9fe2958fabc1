package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 进线安全配置日期维度配置
 * */

public enum IncomingSafetyDimensionTypeEnum {

    /**
     * 维度类型：daily日期维度、minute分钟维度
     */

    DAILY("daily", "日期维度"),
    MINUTE("minute", "分钟维度"),

   ;

   public final String type;

   public final String name;

   IncomingSafetyDimensionTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, IncomingSafetyDimensionTypeEnum> mapping = new HashMap<>();

   static {
       for (IncomingSafetyDimensionTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, IncomingSafetyDimensionTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       if (!mapping.containsKey(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static IncomingSafetyDimensionTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       if (!mapping.containsKey(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
