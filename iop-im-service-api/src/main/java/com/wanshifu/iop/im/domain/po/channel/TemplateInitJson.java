package com.wanshifu.iop.im.domain.po.channel;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 端侧渠道初始化json配置表
 */
@Data
@ToString
@Table(name = "template_init_json")
public class TemplateInitJson {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "template_init_json_id")
    private Long templateInitJsonId;

    /**
     * 关联表名 如 client_channel_config_draft
     */
    @Column(name = "table_type_name")
    private String tableTypeName;

    /**
     * 关联字段名 如 all_config_json
     */
    @Column(name = "column_type_name")
    private String columnTypeName;

    /**
     * 初始化JSON类型:  如会话满意度：satisfaction, 会话样式主题：style_theme,系统自动回复- auto_reply
     */
    @Column(name = "init_json_type")
    private String initJsonType;

    /**
     * 初始化JSON内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}