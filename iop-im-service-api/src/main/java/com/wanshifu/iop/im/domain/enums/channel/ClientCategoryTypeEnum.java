package com.wanshifu.iop.im.domain.enums.channel;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 端侧类型枚举
 */
public enum ClientCategoryTypeEnum {
    INNER("inner", "内部渠道"),
    VISITOR("visitor", "游客渠道"),
    OUTER("outer", "外部渠道"),

    ;

    public final String type;

    public final String name;

    ClientCategoryTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, ClientCategoryTypeEnum> mapping = new HashMap<>();

    static {
        for (ClientCategoryTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, ClientCategoryTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        ClientCategoryTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static ClientCategoryTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
