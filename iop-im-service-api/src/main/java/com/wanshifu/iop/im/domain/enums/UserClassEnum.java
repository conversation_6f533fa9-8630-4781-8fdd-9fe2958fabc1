package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 账号类型
 * */

public enum UserClassEnum {

   USER("merchant", "商家"),

   ENTERPRISE("enterprise", "总包"),

   ENTERPRISE_CUSTOMER("enterprise_customer", "总包客服"),

   CUSTOMTER("customer", "客服"),

   MASTER("master", "师傅"),

   SYSTEM("system", "系统"),

   TOURIST("tourist", "游客"),

   CLIENT("client", "客户"),

    SEAT("seat", "座席"),

   ;

   public final String type;

   public final String name;

   UserClassEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, UserClassEnum> mapping = new HashMap<>();

   static {
       for (UserClassEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, UserClassEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static UserClassEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
