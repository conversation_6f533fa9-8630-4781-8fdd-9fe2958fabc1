package com.wanshifu.iop.im.api.req.leftSidebar;

import com.wanshifu.framework.core.BusinessException;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 获取坐席信息
 * <AUTHOR>
 * @date： 2025-07-14 20:22:55
 */
@Data
public class OnlineSeatInfoReq {

    /**
     * imToken
     */
    private String imToken;

    public void checkParams() {
        if (StringUtils.isBlank(imToken)) {
            throw new BusinessException("imToken不能为空");
        }
    }
}
