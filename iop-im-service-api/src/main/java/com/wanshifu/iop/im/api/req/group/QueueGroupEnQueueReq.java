package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import com.wanshifu.iop.im.domain.po.QueueConfig;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;
import java.util.Objects;
import java.util.Queue;

/**
 * 进入分组队列入参
 * <AUTHOR>
 * @date： 2025-06-30 14:12:59
 */
@Data
public class QueueGroupEnQueueReq extends BaseTenantReq {

    /**
     * 会话分配id
     */
    private Long conversationDistributeId;

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 队列配置id
     */
    private QueueConfig queueConfig;

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 外部用户id
     */
    private String outerUserId;

    public void checkParams() {
        if (conversationDistributeId == null || conversationDistributeId == 0) {
            throw new BusinessException("conversationDistributeId is required");
        }
        if (groupId == null || groupId == 0) {
            throw new BusinessException("groupId is required");
        }
        if ( Objects.isNull(queueConfig) ){
            throw new BusinessException("queueConfig is required");
        }
        if (conversationId == null || conversationId == 0) {
            throw new BusinessException("conversationId is required");
        }
        if (StringUtils.isEmpty(outerUserId)) {
            throw new BusinessException("outerUserId is required");
        }
    }
}
