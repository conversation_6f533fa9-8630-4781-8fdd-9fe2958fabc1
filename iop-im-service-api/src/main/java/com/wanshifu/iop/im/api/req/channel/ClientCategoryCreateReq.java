package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 端侧创建
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class ClientCategoryCreateReq {

    /**
     * 端侧名
     */
    @NotNull(message = "端侧名不能为空")
    private String clientCategoryName;

    /**
     * 端侧标识
     */
    @NotNull(message = "端侧标识不能为空")
    private String clientCategoryEn;

    /**
     * 端侧类型 inner-内部坐席 outer-外部坐席 visitor-访客
     */
    @NotNull(message = "端侧类型不能为空")
    @ValueIn("inner,outer,visitor")
    private String clientCategoryType;

    /**
     * 端侧ID
     */
    private Long clientCategoryConfigId;

    /**
     * 操作人id
     */
    private Long operatorId;
}
