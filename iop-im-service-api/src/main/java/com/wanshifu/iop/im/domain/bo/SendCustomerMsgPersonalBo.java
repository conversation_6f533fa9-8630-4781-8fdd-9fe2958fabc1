package com.wanshifu.iop.im.domain.bo;

import lombok.Data;

import java.util.List;

@Data
public class SendCustomerMsgPersonalBo {

    /**
     * 租户id，必填
     * */
    private Long tenantId;

    /**
     * im场景类型，必填
     * {@link com.wanshifu.iop.im.domain.enums.ImTypeEnum}
     * */
    private String imType;

    /**
     * 消息发送者 外部用户id
     * */
    private String fromAccountId;

    /**
     * 消息接收者 外部用户id
     * */
    private String toAccountId;

    private List<MsgContentItem> msgContentItemList;

    @Data
    public static class MsgContentItem{
        /**
         * 消息类型
         * TIMTextElem（文本消息）
         * TIMLocationElem（位置消息）
         * TIMFaceElem（表情消息）
         * TIMCustomElem（自定义消息）
         * TIMSoundElem（语音消息）
         * TIMImageElem（图像消息）
         * TIMFileElem（文件消息）
         * TIMVideoFileElem（视频消息）
         * */
        private String msgType;

        /**
         * 消息内容，如果是非文本需要json格式化数据
         * */
        private Object content;

    }


    /**
     * 消息是否需要回执，默认需要
     * */
    private Integer IsNeedReadReceipt;



}
