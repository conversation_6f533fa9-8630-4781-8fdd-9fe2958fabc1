package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum ImTypeEnum {

    CHAT("chat", "聊天"),


    ;

    public final String type;

    public final String name;

    ImTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, ImTypeEnum> mapping = new HashMap<>();

    static {
        for (ImTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, ImTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        ImTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static ImTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
