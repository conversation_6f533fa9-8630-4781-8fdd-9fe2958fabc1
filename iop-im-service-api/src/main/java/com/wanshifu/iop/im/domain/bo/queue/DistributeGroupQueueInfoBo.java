package com.wanshifu.iop.im.domain.bo.queue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分组队列信息
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
public class DistributeGroupQueueInfoBo {

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 是否需要排队
     */
    private Boolean needQueue = false;

    /**
     * 队列长度
     */
    private Long queueLength = 0L;
}
