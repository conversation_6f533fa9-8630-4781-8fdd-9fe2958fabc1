package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 会话激活日志表
 */
@Data
@ToString
@Table(name = "im_conversation_active_log")
public class ImConversationActiveLog {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "conversation_active_log_id")
    private Long conversationActiveLogId;

    /**
     * 激活来源会话id
     */
    @Column(name = "from_conversation_id")
    private Long fromConversationId;

    /**
     * 被激活的会话id
     */
    @Column(name = "to_conversation_id")
    private Long toConversationId;

    /**
     * 激活场景 history-历史会话  leave-留言
     */
    @Column(name = "activate_scene")
    private String activateScene;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}