package com.wanshifu.iop.im.api.resp.rightSidebar;

import com.wanshifu.iop.im.api.resp.CommonLabelValueLevelResp;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.api.resp.leftSidebar.ListConversationMsgResp;
import lombok.Data;

import java.util.List;

/**
 * 获取表单枚举
 * <AUTHOR>
 * @date： 2025-07-14 20:37:38
 */
@Data
public class GetFromEnumsResp  {

    /**
     * 业务小结模板
     */
    private List<CommonLabelValueResp> businessSummaryTemplateEnums;

    /**
     * 用户问题类型
     */
    private List<CommonLabelValueLevelResp> userProblemTypeEnums;

    /**
     * 问题是否已解决 0-未解决 1-已解决
     */
    private List<CommonLabelValueResp> problemIsSolvedEnums;

    /**
     * 创建工单类型枚举 customerOrder-售后工单 secondline-二线工单
     */
    private List<CommonLabelValueResp> createWorkOrderTypeEnums;
}
