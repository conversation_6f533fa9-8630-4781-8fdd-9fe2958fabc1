package com.wanshifu.iop.im.domain.bo.groupManage;

import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;

import java.util.List;

/**
 * 坐席规则选项
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
public class SeatPriorityDistributeRuleOptionBo {

    /**
     * 规则指标id rule_metric_id
     */
    private Integer ruleMetricId;

    /**
     * 规则中文名称 KA用户
     */
    private String ruleMetricCn;

    /**
     * 规则配置枚举列表
     * [
     *      {
     *          "label": "ka",
     *          "value": 1
     *      },
     *      {
     *          "label": "非ka",
     *          "value": 0
     *      },
     * ]
     */
    private List<CommonLabelValueResp> ruleConfigEnums;

}
