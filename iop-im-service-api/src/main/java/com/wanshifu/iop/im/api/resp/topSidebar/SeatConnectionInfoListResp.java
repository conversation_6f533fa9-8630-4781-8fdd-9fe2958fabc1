package com.wanshifu.iop.im.api.resp.topSidebar;

import lombok.Data;

import java.util.List;

/**
 * 坐席接线情况
 * <AUTHOR>
 * @date： 2025-07-24 13:57:54
 */
@Data
public class SeatConnectionInfoListResp {

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 空闲坐席数
     */
    private Long freeSeatCount;

    /**
     * 总坐席数
     */
    private Integer totalSeatCount;

    /**
     * 分组坐席信息
     */
    private List<SeatConnectionInfoItemResp> seatConnectionInfoItem;

}
