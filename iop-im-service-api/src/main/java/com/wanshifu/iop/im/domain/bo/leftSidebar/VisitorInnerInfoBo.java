package com.wanshifu.iop.im.domain.bo.leftSidebar;

import lombok.Data;

import java.util.List;

/**
 * 访客内部信息
 * <AUTHOR>
 * @date： 2025-07-16 16:10:55
 */
@Data
public class VisitorInnerInfoBo {

    /**
     * 外部用户id
     */
    private String outerUserId;

    /**
     * 内部用户id
     */
    private Long innerUserId;

    /**
     * 用户类型
     */
    private String userClass;

    /**
     * 访客名称
     */
    private String userName;

    /**
     * 访客头像
     * */
    private String userFaceUrl;

    /**
     * 访客在线状态 online:在线，offline:离线
     */
    private String onlineState;
}
