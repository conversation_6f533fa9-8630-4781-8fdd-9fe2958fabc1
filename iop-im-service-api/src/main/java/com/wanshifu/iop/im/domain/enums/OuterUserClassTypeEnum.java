package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 外部用户类型
 * */

public enum OuterUserClassTypeEnum {

   OUTER_USER_ID("outerUserId", "外部用户id"),

   VIRTU<PERSON>("virtual", "虚拟账号"),

   SEAT("seat", "坐席账号"),

   ROBOT("robot", "机器人"),


   ;

   public final String type;

   public final String name;

   OuterUserClassTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, OuterUserClassTypeEnum> mapping = new HashMap<>();

   static {
       for (OuterUserClassTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, OuterUserClassTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static OuterUserClassTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
