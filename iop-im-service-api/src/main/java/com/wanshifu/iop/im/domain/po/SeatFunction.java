package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 坐席功能表
 */
@Data
@ToString
@Table(name = "seat_function")
public class SeatFunction {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seat_function_id")
    private Integer seatFunctionId;

    /**
     * 坐席功能名称
     */
    @Column(name = "seat_function_cn")
    private String seatFunctionCn;

    /**
     * 坐席功能英文名称
     */
    @Column(name = "seat_function_en")
    private String seatFunctionEn;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}