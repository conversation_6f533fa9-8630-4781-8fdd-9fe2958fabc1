package com.wanshifu.iop.im.domain.dto.channel;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ConversationStyleConfigDto {
    /**
     * 会话样式配置ID
     */
    private Long conversationStyleConfigId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 端侧对象类型:  seat-坐席 visitor-访客
     */
    private String clientUserType;

    /**
     * 可选主题列表
     */
    private ThemesJsonDto themesJsonBo;

    /**
     * 可选主题列表字符串
     */
    private String themesJson;

    /**
     * 当前选择主题ID
     */
    private Long conversationThemeId;

    /**
     * 侧边栏配置json对象
     */
    private SidebarJsonDto sidebarJsonBo;

    /**
     * 侧边栏配置json
     */
    private String sidebarJson;

    /**
     * 端侧ID
     */
    private Long clientCategoryId;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据修改时间
     */
    private Date updateTime;

    /**
     * 主题列表
     */
    private List<ConversationThemeConfigDto> conversationThemeConfigBoList;
}
