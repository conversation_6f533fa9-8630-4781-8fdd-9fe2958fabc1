package com.wanshifu.iop.im.domain.bo;

import lombok.Data;


import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/2 19:21
 * @description TODO
 */
@Data
public class FlowNodeBo implements Comparable<FlowNodeBo>{

    /**
     *
     */
    private Long flowNodeId;

    /**
     * 流程表主键id
     */
    private Long flowDefineId;

    /**
     * 节点名
     */
    private String nodeName;

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 顺序，越小越靠前
     */
    private Integer sort;

    /**
     * 节点配置json
     */
    private String configJson;

    /**
     * 删除状态，1：删除，0：正常
     */
    private Integer isDelete;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    @Override
    public int compareTo(FlowNodeBo o) {
        return Integer.compare(o.getSort(),this.sort);
    }
}
