package com.wanshifu.iop.im.api.req.seat;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.im.api.resp.seat.SeatFunctionResp;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建坐席
 * <AUTHOR>
 * @date： 2025-06-03 17:16:34
 */
@Data
public class SeatCreateReq {

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    /**
     * 账号
     */
    @NotNull(message = "账号不能为空")
    private Long accountId;

    /**
     * 坐席昵称
     */
    @NotNull(message = "坐席昵称不能为空")
    private String seatName;

    /**
     * 头像地址
     */
    @NotNull(message = "头像地址不能为空")
    private String faceUrl;

    /**
     * 所属会话组
     */
    @Size(min = 1)
    private List<Integer> groupIdList;

    /**
     * 坐席标签
     */
    private Integer tagId;

    /**
     * 坐席最大接线数 0~100
     */
    @NotNull(message = "坐席最大接线数不能为空")
    private Integer maxWiringQuantity;

    /**
     * 坐席类型
     */
    @NotNull(message = "坐席类型不能为空")
    @ValueIn("0,1,2")
    private Integer seatType;

    /**
     * 坐席启用状态
     */
    @NotNull(message = "坐席启用状态不能为空")
    @ValueIn("0,1")
    private Integer status;

    /**
     * 坐席上线状态
     */
    @NotNull(message = "坐席上线状态不能为空")
    private String loginSeatStatusEn;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private Long operatorId;

    public void checkParams() {
        if (this.seatName.length() > 20) {
            throw new BusinessException("坐席名称不能超过20个字符");
        }

        if (this.maxWiringQuantity < 0 || this.maxWiringQuantity > 100) {
            throw new BusinessException("坐席最大接线数范围0~100");
        }
    }
}
