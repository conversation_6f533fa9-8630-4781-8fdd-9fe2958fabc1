package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 流程定义表
 */
@Data
@ToString
@Table(name = "flow_define")
public class FlowDefine {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "flow_define_id")
    private Long flowDefineId;

    /**
     * 路由导航名称
     */
    @Column(name = "flow_name")
    private String flowName;

    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 状态：enable：启用， disable：禁用
     */
    @Column(name = "status")
    private String status;

    /**
     * 删除状态，1：删除，0：正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}