package com.wanshifu.iop.im.domain.bo.channel;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
@Data
public class ResultCallbackFormJsonBo {

    /**
     * 满意度字段列表
     */
    @JSONField(name = "filedBoList")
    private List<FiledBo> filedBoList;

    @Data
    public static class FiledBo {
        /**
         * 满意度配置id
         */
        private Long satisfactionLevelConfigId;
        /**
         * 字段序号
         */
        private Long filedId;
        /**
         * 字段名
         */
        private String filedName;

        /**
         * 字段描述
         */
        private String filedDesc;
        /**
         * 字段类型 @see FiledTypeEnum
         */
        private String filedType;

        /**
         * text类型 文本内容
         */
        private String textContent;
        /**
         * 字段分组  base -基础设置 style_setting 样式设置
         */
        private String filedGroup;
        /**
         * 启用状态 默认1  0-否  1-是
         */
        private Integer status;
        /**
         * 必填状态 0-非必填 1-必填
         */
        private Integer isRequired;

        /**
         * 字段选项列表
         */
        private List<OptionBo> optionBoList;
    }
}
