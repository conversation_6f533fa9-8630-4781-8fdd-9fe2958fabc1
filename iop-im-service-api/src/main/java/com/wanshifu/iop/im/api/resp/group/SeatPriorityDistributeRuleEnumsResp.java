package com.wanshifu.iop.im.api.resp.group;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.domain.bo.groupManage.SeatPriorityDistributeRuleOptionBo;
import lombok.Data;

import java.util.List;

/**
 * 坐席分配规则列表
 * <AUTHOR>
 * @date： 2025-06-30 14:38:34
 */
@Data
public class SeatPriorityDistributeRuleEnumsResp {

    /**
     * 坐席分配规则枚举 ka
     */
    private List<SeatPriorityDistributeRuleOptionBo> seatPriorityDistributeRuleOptionEnums;

    /**
     * 分配策略 balanced_saturation-饱和度均衡
     */
    private List<CommonLabelValueResp> distributeStrategyEnums;
}
