package com.wanshifu.iop.im.api.resp.topSidebar;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 坐席接线情况
 * <AUTHOR>
 * @date： 2025-07-24 13:57:54
 */
@Data
public class SeatConnectionInfoItemResp {

    /**
     * 坐席id
     */
    private Long seatId;

    /**
     * 坐席名称
     */
    private String seatName;

    /**
     * 坐席当前状态
     */
    private String seatStatus;

    /**
     * 坐席当前状态名称
     */
    private String seatStatusName;

    /**
     * 坐席当前状态持续时间 秒
     */
    private Long seatStatusDuration;

    /**
     * 坐席当前接线量
     */
    private Integer currentWiringQuantity = 0;

    /**
     * 坐席最大接线量
     */
    private Integer maxWiringQuantity = 0;

    /**
     * 排序比例
     */
    private BigDecimal sortRate = BigDecimal.ZERO;
}
