package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum ActionStatusEnum {

    OK("OK", "成功"),


    ;

    public final String type;

    public final String name;

    ActionStatusEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, ActionStatusEnum> mapping = new HashMap<>();

    static {
        for (ActionStatusEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, ActionStatusEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        ActionStatusEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static ActionStatusEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
