package com.wanshifu.iop.im.domain.enums.channel;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 满意度发送条件枚举
 */
public enum SendConditionEnum {
    SEAT_SEND("seat_send", "坐席主动发送"),
    SEAT_CLOSE("seat_close", "坐席关闭会话"),
    VISITOR_END("visitor_end", "访客结束会话（超时未回复+超时离开）"),

    ;

    public final String type;

    public final String name;

    SendConditionEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, SendConditionEnum> mapping = new HashMap<>();

    static {
        for (SendConditionEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, SendConditionEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        SendConditionEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static SendConditionEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
