package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum TencentInterFaceTypeEnum {

    ACCOUNT_IMPORT("account_import", "注册用户"),
    CREATE_GROUP("create_group", "创建群聊"),
    SEND_MSG_PERSONAL("send_msg_personal", "向个人发送消息"),
    SEND_CUSTOMER_MSG_PERSONAL("send_customer_msg_personal", "发送自定义消息"),
    SEND_MSG_GROUP("send_msg_group", "向群内发送消息"),
    IMPORT_MSG_TO_GROUP("import_msg_to_group", "向群内导入消息"),
    DESTROY_GROUP("destroy_group", "解散群聊"),
    ONLINE_STATUS("online_status", "查询用户在线状态"),
    IMPORT_MSG_TO_SINGLE("import_msg_to_single", "向单聊内导入消息"),
    ADD_MEMBER_TO_GROUP("add_member_to_group", "向群中添加用户"),
    DEL_MEMBER_FROM_GROUP("del_member_from_group", "从群中删除用户"),
    MODIFY_MSG_SINGLE("modify_msg_single", "修改单聊历史消息"),
    DELETE_FRIEND_SHIP("delete_friend_ship", "删除好友关系-单向删除"),


    ;

    public final String type;

    public final String name;

    TencentInterFaceTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, TencentInterFaceTypeEnum> mapping = new HashMap<>();

    static {
        for (TencentInterFaceTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, TencentInterFaceTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        TencentInterFaceTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static TencentInterFaceTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
