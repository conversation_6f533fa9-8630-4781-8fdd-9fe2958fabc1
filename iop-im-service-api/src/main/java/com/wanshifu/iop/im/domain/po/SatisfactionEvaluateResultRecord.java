package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import lombok.Data;
import java.util.Date;


/**
 * 渠道会话满意度评价记录表
 */
@Data
@ToString
@Table(name = "satisfaction_evaluate_result_record")
public class SatisfactionEvaluateResultRecord {

    /**
     * 会话满意度评价结果记录表id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "satisfaction_evaluate_result_record_id")
    private Long satisfactionEvaluateResultRecordId;

    /**
     * 记录ID
     */
    @Column(name = "satisfaction_evaluate_record_id")
    private Long satisfactionEvaluateRecordId;

    /**
     * 渠道id
     */
    @Column(name = "channel_id")
    private Long channelId;

    /**
     * 坐席id
     */
    @Column(name = "seat_id")
    private Long seatId;

    /**
     * 外部用户id
     */
    @Column(name = "out_user_id")
    private Long outUserId;

    /**
     * 星级和对应的描述
     */
    @Column(name = "guideline_desc")
    private String guidelineDesc;

    /**
     * 标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 客服是否解决问题 0-否 1-是 
     */
    @Column(name = "has_solve_problem")
    private Integer hasSolveProblem;

    /**
     * 结果回收表单配置字段json
     */
    @Column(name = "result_callback_form_json")
    private String resultCallbackFormJson;

    /**
     * 结果回收表单json
     */
    @Column(name = "result_json")
    private String resultJson;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}