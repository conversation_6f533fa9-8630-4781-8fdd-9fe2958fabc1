package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 会话历史消息导入状态
 * */

public enum ConversationMessageHistoryImportStatusEnum {
    /**
     * 导入状态: processing-导入中, complete-导入完成, fail-导入失败
     */

    PROCESSING("processing", "导入中"),
    COMPLETE("complete", "导入完成"),
    FAIL("fail", "导入失败"),
   ;

   public final String type;

   public final String name;

   ConversationMessageHistoryImportStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, ConversationMessageHistoryImportStatusEnum> mapping = new HashMap<>();

   static {
       for (ConversationMessageHistoryImportStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, ConversationMessageHistoryImportStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static ConversationMessageHistoryImportStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
