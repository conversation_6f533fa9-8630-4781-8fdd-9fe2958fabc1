package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 流程节点表
 */
@Data
@ToString
@Table(name = "flow_node")
public class FlowNode {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "flow_node_id")
    private Long flowNodeId;

    /**
     * 流程表主键id
     */
    @Column(name = "flow_define_id")
    private Long flowDefineId;

    /**
     * 节点名
     */
    @Column(name = "node_name")
    private String nodeName;

    /**
     * 节点类型
     */
    @Column(name = "node_type")
    private String nodeType;

    /**
     * 顺序，越小越靠前
     */
    @Column(name = "sort")
    private Integer sort;

    /**
     * 节点配置json
     */
    @Column(name = "config_json")
    private String configJson;

    /**
     * 删除状态，1：删除，0：正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}