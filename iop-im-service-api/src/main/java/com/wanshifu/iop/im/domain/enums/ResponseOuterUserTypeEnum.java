package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 账号类型
 * */

public enum ResponseOuterUserTypeEnum {

    ROBOT("robot", "机器人"),

    SEAT("seat", "坐席"),

   ;

   public final String type;

   public final String name;

   ResponseOuterUserTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, ResponseOuterUserTypeEnum> mapping = new HashMap<>();

   static {
       for (ResponseOuterUserTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, ResponseOuterUserTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static ResponseOuterUserTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
