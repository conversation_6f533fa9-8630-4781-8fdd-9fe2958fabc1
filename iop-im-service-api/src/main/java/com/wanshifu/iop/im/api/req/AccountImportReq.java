package com.wanshifu.iop.im.api.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入用户入参
 * */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountImportReq {

    /**
     * 用户id
     * */
    @JSONField(name = "UserID")
    private String userID;

    /**
     * 用户昵称
     * */
    @JSONField(name = "<PERSON>")
    private String nick;

    /**
     * 用户头像url
     * */
    @JSONField(name = "FaceUrl")
    private String faceUrl;
}
