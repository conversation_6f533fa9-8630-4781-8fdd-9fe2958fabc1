package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;
/**
 * 消息类型
 * TIMTextElem（文本消息）
 * TIMLocationElem（位置消息）
 * TIMFaceElem（表情消息）
 * TIMCustomElem（自定义消息）
 * TIMSoundElem（语音消息）
 * TIMImageElem（图像消息）
 * TIMFileElem（文件消息）
 * TIMVideoFileElem（视频消息）
 * */
public enum MsgTypeEnum {

    TEXT("TIMTextElem", "文本消息"),
    LOCATION("TIMLocationElem", "位置消息"),
    FACE("TIMFaceElem", "表情消息"),
    CUSTOMER("TIMCustomElem", "自定义消息"),
    SOUND("TIMSoundElem", "语音消息"),
    IMAGE("TIMImageElem", "图像消息"),
    FILE("TIMFileElem", "文件消息"),
    VIDEO_FILE("TIMVideoFileElem", "视频消息"),


    ;

    public final String type;

    public final String name;

    MsgTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, MsgTypeEnum> mapping = new HashMap<>();

    static {
        for (MsgTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, MsgTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        MsgTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static MsgTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
