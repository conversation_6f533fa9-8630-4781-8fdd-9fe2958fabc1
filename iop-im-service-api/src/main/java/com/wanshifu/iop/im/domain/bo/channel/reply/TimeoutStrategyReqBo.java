package com.wanshifu.iop.im.domain.bo.channel.reply;

import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyDetailConfig;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/23 16:02
 * @description TODO
 */
@Data
public class TimeoutStrategyReqBo {
    /**
     * 会话对象 必填
     * */
    private ImConversation conversation;

    /**
     * 最后一条会话 用户会话记录
     * */
    private ImConversationItem fromConversationItem;

    /**
     * 最后一条会话 坐席会话记录
     * */
    private ImConversationItem seatConversationItem;

    /**
     * 最后一条会话 机器人或者系统会话记录
     * */
    private ImConversationItem robotConversationItem;

    /**
     * 自动回复详细配置
     * */
    private ConversationAutoReplyDetailConfig conversationAutoReplyDetailConfig;

}
