package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 逻辑操作符枚举
 * <AUTHOR>
 * @date： 2025-07-01 14:15:07
 */

public enum LogicalOperatorEnum {

    /**
     * and-且 or-或
     */

    AND("and", "且"),

    OR("or", "或"),

   ;

   public final String type;

   public final String name;

   LogicalOperatorEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, LogicalOperatorEnum> mapping = new HashMap<>();

   static {
       for (LogicalOperatorEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, LogicalOperatorEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       if (!mapping.containsKey(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static LogicalOperatorEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       if (!mapping.containsKey(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
