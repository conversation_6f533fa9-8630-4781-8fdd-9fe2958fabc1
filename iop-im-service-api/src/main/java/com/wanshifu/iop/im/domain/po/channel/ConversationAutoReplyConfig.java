package com.wanshifu.iop.im.domain.po.channel;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 系统自动回复消息配置表
 */
@Data
@ToString
@Table(name = "conversation_auto_reply_config")
public class ConversationAutoReplyConfig {

    /**
     * 系统自动回复配置ID
     */
    @Id
    @Column(name = "conversation_auto_reply_config_id")
    private Long conversationAutoReplyConfigId;

    /**
     * 渠道id
     */
    @Column(name = "channel_id")
    private Long channelId;

    /**
     * 提示类型：欢迎语 - welcome_auto_reply、排队提示语、坐席超时回复提醒语 seat_timeout_auto_reply、访客超时回复提醒语 visitor_timeout_auto_reply
     */
    @Column(name = "msg_type")
    private String msgType;

    /**
     * 启用状态  1：禁用，0：启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}