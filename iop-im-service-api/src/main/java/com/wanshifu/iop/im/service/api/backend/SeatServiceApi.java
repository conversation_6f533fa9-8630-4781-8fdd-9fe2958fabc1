package com.wanshifu.iop.im.service.api.backend;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.seat.*;
import com.wanshifu.iop.im.api.req.tags.CreateTagsReq;
import com.wanshifu.iop.im.api.req.tags.SwitchTagsStatusReq;
import com.wanshifu.iop.im.api.resp.seat.ConversationTransferServiceResp;
import com.wanshifu.iop.im.api.resp.seat.GetSeatStatusListResp;
import com.wanshifu.iop.im.api.resp.seat.SeatEnumsResp;
import com.wanshifu.iop.im.api.resp.seat.SeatFunctionResp;
import com.wanshifu.iop.im.api.resp.seat.SeatSearchListResp;
import com.wanshifu.iop.im.api.resp.tags.GetTagsListResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 坐席接口
 * <AUTHOR>
 * @date： 2025-06-04 10:49:16
 */
@FeignClient(
        value = "iop-im-service",
        path = "/seat", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface SeatServiceApi {

    /**
     * 坐席列表枚举
     */
    @PostMapping("/enums")
    SeatEnumsResp enums(@Valid @RequestBody SeatEnumsReq req);

    /**
     * 坐席列表
     */
    @PostMapping("/searchList")
    SimplePageInfo<SeatSearchListResp> searchList(@Valid @RequestBody SeatSearchListReq req);

    /**
     * 新增坐席
     */
    @PostMapping("/create")
    Integer create(@Valid @RequestBody SeatCreateReq req);

    /**
     * 编辑坐席
     */
    @PostMapping("/edit")
    Integer edit(@Valid @RequestBody SeatEditReq req);

    /**
     * 获取标签列表
     */
    @PostMapping("/tagsList")
    List<GetTagsListResp> tagsList();

    /**
     * 新增标签
     */
    @PostMapping("/createTags")
    Integer createTags(@Valid @RequestBody CreateTagsReq req);

    /**
     * 编辑标签
     */
    @PostMapping("/editTags")
    Integer editTags(@Valid @RequestBody CreateTagsReq req);

    /**
     * 修改标签状态
     */
    @PostMapping("/switchTagsStatus")
    Integer switchTagsStatus(@Valid @RequestBody SwitchTagsStatusReq req);

    /**
     * 获取坐席功能枚举
     */
    @PostMapping("/seatFunctionEnums")
    List<SeatFunctionResp> seatFunctionEnums();

    /**
     * 坐席状态列表
     */
    @PostMapping("/seatStatusList")
    List<GetSeatStatusListResp> seatStatusList();

    /**
     * 修改坐席状态
     */
    @PostMapping("/editSeatStatus")
    Integer editSeatStatus(@Valid @RequestBody EditSeatStatusReq req);

    /**
     * 关闭会话
     * */
    @PostMapping("/closeConversation")
    Integer closeConversation(@Valid @RequestBody CloseConversationReq req);

    /**
     * 坐席列表-转接坐席列表
     * */
    @PostMapping("/transferConversation")
    List<ConversationTransferServiceResp> transferConversation();

    /**
     * 会话转接
     * */
    @PostMapping("/transfer")
    Integer transfer(@Valid @RequestBody TransferServiceReq req);
}
