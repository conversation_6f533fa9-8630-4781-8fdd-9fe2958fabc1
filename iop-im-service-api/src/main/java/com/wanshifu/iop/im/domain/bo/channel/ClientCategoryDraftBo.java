package com.wanshifu.iop.im.domain.bo.channel;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 端侧+渠道接口
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class ClientCategoryDraftBo {

    /**
     * 端侧ID 取值: client_channel_config_draft_id
     */
    private Long clientCategoryConfigId;

    /**
     * 端侧名
     */
    private String clientCategoryName;

    /**
     * 端侧类型: 内部-inner、外部-outer、游客-visitor
     */
    private String clientCategoryType;

    /**
     * 分配规则英文名称
     */
    private String ruleIndicatorsConfigEn;

    /**
     * 腾讯应用配置ID
     */
    private Long umId;

    /**
     * 端侧+渠道 全部配置的json字符串
     */
    private AllConfigJsonBo allConfigJson;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 数据修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
