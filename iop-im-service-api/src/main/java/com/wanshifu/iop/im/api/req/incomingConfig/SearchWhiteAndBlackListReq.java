package com.wanshifu.iop.im.api.req.incomingConfig;

import com.wanshifu.iop.im.api.req.BasePageReq;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 白名单查询参数
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class SearchWhiteAndBlackListReq extends BasePageReq {

    /**
     * 类型 white-白名单 black-黑名单
     */
    private List<String> controlTypeList;

    /**
     * 特征类型 user-用户ID master-师傅ID enterprise-总包ID tourist-访客设备id ip-ip地址/范围
     */
    private List<String> propertyTypeList;

    /**
     * 启用状态：1：禁用，0：启用
     */
    private List<Integer> statusList;
}
