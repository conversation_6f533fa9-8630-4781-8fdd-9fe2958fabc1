package com.wanshifu.iop.im.api.req.seat;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.im.api.resp.seat.SeatFunctionResp;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 创建坐席
 * <AUTHOR>
 * @date： 2025-06-03 17:16:34
 */
@Data
public class SeatEnumsReq {

    /**
     * 枚举类型 0：列表枚举 1：创建枚举
     */
    @NotNull(message = "enumType不能为空")
    @ValueIn("0,1")
    private Integer enumType;
}
