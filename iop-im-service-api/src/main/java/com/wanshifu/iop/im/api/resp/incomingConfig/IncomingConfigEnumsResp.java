package com.wanshifu.iop.im.api.resp.incomingConfig;

import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;

import java.util.List;

/**
 * 域名列表查询结果
 * <AUTHOR>
 * @date： 2025-06-04 15:14:56
 */
@Data
public class IncomingConfigEnumsResp {

    /**
     * 类型枚举
     */
    private List<CommonLabelValueResp> controlTypeEnums;

    /**
     * 特征类型枚举
     */
    private List<CommonLabelValueResp> propertyTypeEnums;

    /**
     * 日期维度类型
     */
    private List<CommonLabelValueResp> dimensionTypeEnums;
}
