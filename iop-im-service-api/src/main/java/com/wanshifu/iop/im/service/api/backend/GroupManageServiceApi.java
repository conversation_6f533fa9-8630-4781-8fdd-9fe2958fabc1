package com.wanshifu.iop.im.service.api.backend;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.group.*;
import com.wanshifu.iop.im.api.req.incomingConfig.*;
import com.wanshifu.iop.im.api.resp.group.*;
import com.wanshifu.iop.im.api.resp.incomingConfig.GetRegistrationFrequencyConfigResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.IncomingConfigEnumsResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchDomainListResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchWhiteAndBlackListResp;
import com.wanshifu.iop.im.domain.po.GroupInfo;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 进线配置接口
 * <AUTHOR>
 * @date： 2025-06-04 10:49:16
 */
@FeignClient(
        value = "iop-im-service",
        path = "/groupManage", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface GroupManageServiceApi {

    /**
     * 分组配置枚举
     * @return 分组配置枚举
     */
    @PostMapping("/enums")
    GroupConfigEnumsResp enums();

    /**
     * 分组列表
     * @param req
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<GroupListResp> list(@Valid @RequestBody GroupListReq req);

    /**
     * 新增分组
     * @param req
     * @return
     */
    @PostMapping("/add")
    Long add(@Valid @RequestBody AddGroupReq req);

    /**
     * 分组排序
     * @param req
     * @return
     */
    @PostMapping("/rearrangement")
    Integer rearrangement(@Valid @RequestBody GroupRearrangementReq req);

    /**
     * 分组详情
     * @param req
     * @return
     */
    @PostMapping("/seatList")
    SimplePageInfo<GroupSeatListResp> seatList(@Valid @RequestBody GroupSeatListReq req);

    /**
     * 新增分组坐席
     * @param req
     * @return
     */
    @PostMapping("/addSeat")
    Long addSeat(@Valid @RequestBody AddGroupSeatReq req);

    /**
     * 移除分组坐席
     * @param req
     * @return
     */
    @PostMapping("/removeSeat")
    Integer removeSeat(@Valid @RequestBody RemoveGroupSeatReq req);

    /**
     * 分组接入规则详情
     * @param req
     * @return
     */
    @PostMapping("/ruleDetail")
    GroupAccessRuleDetailResp ruleDetail(@Valid @RequestBody GroupAccessRuleDetailReq req);

    /**
     * 编辑分组接入规则
     * @param req
     * @return
     */
    @PostMapping("/editRule")
    Integer editRule(@Valid @RequestBody EditGroupAccessRuleReq req);

    /**
     * 分组坐席优先级分配规则枚举
     * @param req
     * @return
     */
    @PostMapping("/seatPriorityDistributeRuleEnums")
    SeatPriorityDistributeRuleEnumsResp seatPriorityDistributeRuleEnums(@Valid @RequestBody GroupAccessRuleDetailReq req);

    /**
     * 分组坐席优先级分配规则列表
     * @param req
     * @return
     */
    @PostMapping("/seatPriorityDistributeRuleList")
    SeatPriorityDistributeRuleDetailResp seatPriorityDistributeRuleList(@Valid @RequestBody SeatPriorityDistributeRuleDetailReq req);

    /**
     * 编辑分组分配策略
     * @param req
     * @return
     */
    @PostMapping("/editDistributeStrategy")
    Integer editDistributeStrategy(@Valid @RequestBody EditDistributeStrategyReq req);

    /**
     * 编辑分组坐席分配规则
     * @param req
     * @return
     */
    @PostMapping("/editSeatDistributeRule")
    Integer editSeatDistributeRule(@Valid @RequestBody EditGroupSeatDistributeRuleReq req);
}