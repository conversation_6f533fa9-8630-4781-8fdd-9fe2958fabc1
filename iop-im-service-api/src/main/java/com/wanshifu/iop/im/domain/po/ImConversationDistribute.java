package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 会话分配表
 */
@Data
@ToString
@Table(name = "im_conversation_distribute")
public class ImConversationDistribute {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "conversation_distribute_id")
    private Long conversationDistributeId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    private Integer groupId;

    /**
     * 坐席id
     */
    @Column(name = "seat_id")
    private Long seatId;

    /**
     * 外部用户id
     */
    @Column(name = "outer_user_id")
    private String outerUserId;

    /**
     * 分组队列id
     */
    @Column(name = "group_queue_id")
    private Long groupQueueId;

    /**
     * 分配规则json
     */
    @Column(name = "distribute_rule_json")
    private String distributeRuleJson;

    /**
     * 分配状态 engroup-进组 completed-分配完成 cancel-取消分配
     */
    @Column(name = "distribute_status")
    private String distributeStatus;

    /**
     * 进组时间
     */
    @Column(name = "distribute_group_time")
    private Date distributeGroupTime;

    /**
     * 分配坐席时间
     */
    @Column(name = "distribute_seat_time")
    private Date distributeSeatTime;

    /**
     * 取消时间
     */
    @Column(name = "cancel_time")
    private Date cancelTime;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}