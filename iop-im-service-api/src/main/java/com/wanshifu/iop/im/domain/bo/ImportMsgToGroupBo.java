package com.wanshifu.iop.im.domain.bo;

import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

@Data
public class ImportMsgToGroupBo {

    /**
     * 租户id，必填
     * */
    private Long tenantId;

    /**
     * im场景类型，必填
     * {@link com.wanshifu.iop.im.domain.enums.ImTypeEnum}
     * */
    private String imType;



    /**
     * 群id
     * */
    private String groupId;

    /**
     * 触发会话更新
     * */
    private Integer RecentContactFlag=1;

    /**
     * 消息列表
     * */
    @Size(min = 1,max = 7)
    private List<MsgListItem> MsgList;


    @Data
    public static class MsgListItem {
        /**
         * 发送者用户ID
         * */
        private String From_Account;

        /**
         * 发送时间戳
         * */
        private Long SendTime;

        /**
         * 消息随机数，非必填
         * */
        private Long Random;

        /**
         * 消息序列号
         * */
        private Long MsgSeq;

        /**
         * 消息内容
         * */
        private List<MsgBodyItem> MsgBody;
    }

    @Data
    public static class MsgBodyItem {

        /**
         * 消息类型
         * */
        private String MsgType;

        private MsgContentItem MsgContent;
    }

    @Data
    public static class MsgContentItem {
        /**
         * 消息内容
         * */
        private String Text;

    }

}
