package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 坐席状态切换日志表
 */
@Data
@ToString
@Table(name = "seat_status_switch_log")
public class SeatStatusSwitchLog {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seat_status_switch_log_id")
    private Long seatStatusSwitchLogId;

    /**
     * 坐席id
     */
    @Column(name = "seat_id")
    private Long seatId;

    /**
     * 切换前状态英文名
     */
    @Column(name = "seat_status_en_before")
    private String seatStatusEnBefore;

    /**
     * 切换后状态英文名
     */
    @Column(name = "seat_status_en_after")
    private String seatStatusEnAfter;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 切换到在线的首次时间，这个字段用于控制在线和满负荷之间多次切换的时候，最开始的时间
     */
    @Column(name = "first_online_time")
    private Date firstOnlineTime;

    /**
     * 满负荷秒数，这个字段用户记录本次满负荷回到在线的时长，单位秒
     */
    @Column(name = "full_load_seconds")
    private Long fullLoadSeconds;
}