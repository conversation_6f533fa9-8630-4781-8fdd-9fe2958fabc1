package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 坐席功能类型
 * <AUTHOR>
 * @date： 2025-05-29 15:49:55
 */
public enum SeatFunctionTypeEnum {

    /**
     * 坐席功能类型 support-支持 restrict-限制
     */

    SUPPORT("support", "可以"),
    RESTRICT("restrict", "不可以"),


    ;

    public final String type;

    public final String name;

    SeatFunctionTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, SeatFunctionTypeEnum> mapping = new HashMap<>();

    static {
        for (SeatFunctionTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, SeatFunctionTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        SeatFunctionTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static SeatFunctionTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
