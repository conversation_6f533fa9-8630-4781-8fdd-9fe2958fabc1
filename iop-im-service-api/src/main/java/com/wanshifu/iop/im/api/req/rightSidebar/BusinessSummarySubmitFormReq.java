package com.wanshifu.iop.im.api.req.rightSidebar;

import com.wanshifu.iop.im.domain.bo.leftSidebar.CreateWorkOrderSubmitFormBo;
import lombok.Data;

/**
 * 提交表单
 * <AUTHOR>
 * @date： 2025-07-14 20:22:55
 */
@Data
public class BusinessSummarySubmitFormReq extends BaseOnlineConversationReq {

    /**
     * 业务小结模板
     */
    private String businessSummaryTemplate;

    /**
     * 三级问题场景
     */
    private String Lv3ProblemId;

    /**
     * 业务小结内容/客户建议内容
     */
    private String businessSummaryContent;

    /**
     * 问题是否解决 0-未解决 1-已解决
     */
    private Integer isSolved;

    /**
     * 创建工单类型 customerOrder-售后工单 secondline-二线工单
     */
    private String createWorkOrderType;

    /**
     * 提交创建工单表单
     */
    public CreateWorkOrderSubmitFormBo submitWorkOrderForm;
}
