package com.wanshifu.iop.im.domain.enums.channel;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统自动回复消息类型枚举
 *
 */
public enum AutoReplyTypeEnum {
    WELCOME_AUTO_REPLY("welcome_auto_reply", "欢迎语"),

    WAIT_AUTO_REPLY("wait_auto_reply", "排队提示语"),
    TIMEOUT_AUTO_REPLY("timeout_auto_reply", "超时回复提醒语"),
    END_AUTO_REPLY("end_auto_reply", "会话结束语"),
    // timeout_auto_remind
    TIMEOUT_AUTO_REMIND("timeout_auto_remind", "超时回复提示"),
    CONVERSATION_STYLE_CONFIG("conversation_style_config", "会话接入配置"),

    ;

    public final String type;

    public final String name;

    AutoReplyTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, AutoReplyTypeEnum> mapping = new HashMap<>();

    static {
        for (AutoReplyTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, AutoReplyTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        AutoReplyTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static AutoReplyTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
