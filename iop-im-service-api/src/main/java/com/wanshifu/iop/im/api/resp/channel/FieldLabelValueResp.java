package com.wanshifu.iop.im.api.resp.channel;

import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class FieldLabelValueResp extends CommonLabelValueResp {
    /**
     * 是否必填
     */
    private Integer isRequired;
    /**
     * 是否选中
     */
    private Integer  isSelected;
    /**
     * 超时截止时间
     */
    private String timeOutEndTime;
    /**
     * 展示辩题
     */
    private String fieldShowTitle;

    /**
     * 图标aid composite 或 button类型才有
     */
    private Long iconAid;

    /**
     * 按钮id button类型才有
     */
    private Integer buttonId;
    /**
     * 按钮原因列表
     */
    private List<ButtonReasonItem> buttonReasonItemList;

    @Data
    public static class ButtonReasonItem {
        /**
         * 按钮id
         */
        private Long buttonId;
        /**
         * 按钮原因
         */
        private String buttonReason;
        /**
         * 勾选状态 0-未勾选 1-已勾选
         */
        private Integer checkStatus;
    }
}
