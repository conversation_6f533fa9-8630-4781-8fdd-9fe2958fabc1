package com.wanshifu.iop.im.api.req.seat;

import com.wanshifu.iop.im.api.req.BasePageReq;
import com.wanshifu.iop.im.api.resp.seat.SeatFunctionResp;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 坐席列表
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class SeatSearchListReq extends BasePageReq {

    /**
     * 坐席姓名 （客服账号名称）
     */
    private String username;

    /**
     * 坐席工号
     */
    private Integer seatNo;

    /**
     * 坐席类型
     */
    private List<Integer> seatTypeList;

    /**
     * 坐席标签
     */
    private List<Integer> tagIdList;

    /**
     * 坐席启用状态
     */
    private List<Integer> statusList;

}
