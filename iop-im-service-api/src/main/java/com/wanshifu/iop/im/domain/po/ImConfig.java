package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 
 */
@Data
@ToString
@Table(name = "im_config")
public class ImConfig {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "im_id")
    private Long imId;

    /**
     * 租户id
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 应用名
     */
    @Column(name = "im_name")
    private String imName;

    /**
     * 应用使用场景标识
     */
    @Column(name = "im_Type")
    private String imType;

    /**
     * 第三方应用id
     */
    @Column(name = "im_third_id")
    private String imThirdId;

    /**
     * 第三方应用密钥
     */
    @Column(name = "im_third_secret")
    private String imThirdSecret;

    /**
     * 业务说明
     */
    @Column(name = "im_desc")
    private String imDesc;

    /**
     * 状态：1：删除，0：正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

}