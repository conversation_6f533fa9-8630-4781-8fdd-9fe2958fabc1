package com.wanshifu.iop.im.api.req.tencent;

import lombok.Data;

import java.util.List;

/**
 * 单聊/群聊发送消息后回调
 * */
@Data
public class CallbackGroupSendMsgAfterReq {
    /**
     * 回调命令
     * */
    private String CallbackCommand;
    
    /**
     * 群组id，如果是群聊的回调有值，其他没值
     * */
    private String GroupId;

    /**
     * 群组类型
     * */
    private String Type;

    /**
     * 发送者
     * */
    private String From_Account;

    /**
     * 请求的发起者
     * */
    private String Operator_Account;

    /**
     * 消息随机数
     * */
    private Long Random;

    /**
     * 消息序号
     * */
    private Long MsgSeq;

    /**
     * 消息在客户端上的唯一标识
     * */
    private String MsgId;

    /**
     * 消息的发送时间戳，单位为秒
     * */
    private Long MsgTime;

    /**
     * 是否仅发送给在线用户标识。1代表仅发送给在线用户，否则为0；
     * */
    private int OnlineOnlyFlag;

    /**
     * 自定义消息
     * */
    private String CloudCustomData;

    /**
     * 毫秒级别，事件触发时间戳
     * */
    private Long EventTime;


    /**
     * 消息体，目前只支持文本类型
     * */
    private List<MsgBodyItem> MsgBody;

    // Getters and Setters
    @Data
    public static class MsgBodyItem {
        private String MsgType;
        private Object MsgContent;
    }

    // Getters and Setters
    @Data
    public static class MsgContent {
        private String Text;
    }
}
