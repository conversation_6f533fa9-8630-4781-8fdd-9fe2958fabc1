package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import lombok.Data;

import java.util.List;

/**
 * 添加分组坐席
 * <AUTHOR>
 * @date： 2025-06-30 14:12:59
 */
@Data
public class AddGroupSeatReq extends BaseTenantReq {

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 添加坐席
     */
    private List<Long> seatIdList;

    /**
     * 操作人
     */
    private Long operatorId;

    public void checkParams(){
        // 校验入参
        if (groupId == null ){
            throw new BusinessException("分组id为null");
        }
        if (CollectionUtils.isEmpty(seatIdList)){
            throw new BusinessException("选择关联坐席为null");
        }
        if ( operatorId == null || operatorId == 0 ){
            throw new BusinessException("操作人id为null");
        }
    }
}
