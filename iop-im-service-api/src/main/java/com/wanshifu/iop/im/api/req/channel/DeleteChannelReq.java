package com.wanshifu.iop.im.api.req.channel;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 渠道删除
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class DeleteChannelReq {

    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 渠道ID
     */
    @NotNull(message = "渠道ID不能为空")
    @Min(1L)
    private Long channelId;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

}
