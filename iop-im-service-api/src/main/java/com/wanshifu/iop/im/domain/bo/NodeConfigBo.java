package com.wanshifu.iop.im.domain.bo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/17 10:22
 * @description TODO
 */
@Data
public class NodeConfigBo {

    /**
     * 时间
     * */
    private List<String> workTime;

    /**
     * 工作日
     * */
    private List<Integer> workDays;

    /**
     * 自动回复内容
     * */
    private String autoReplyContent;

    /**
     * 节点key
     * */
    private NodeConfigDataItem nodeData;

    @Data
    public static class NodeConfigDataItem {
        private String id;
    }
}
