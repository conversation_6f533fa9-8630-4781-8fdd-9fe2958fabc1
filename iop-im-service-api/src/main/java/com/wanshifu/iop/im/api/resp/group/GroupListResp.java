package com.wanshifu.iop.im.api.resp.group;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 域名列表查询结果
 * <AUTHOR>
 * @date： 2025-06-04 15:14:56
 */
@Data
public class GroupListResp {

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 关联渠道
     */
    private List<String> clientCategoryNameList;

    /**
     * 关联入口
     */
    private List<String> channelNameList;

    /**
     * 关联咨询类型
     */
    private List<String> problemClassNameList;

    /**
     * 会话组坐席数
     */
    private Integer seatCount = 0;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 启用状态：1：禁用，0：启用
     */
    private Integer status;

    /**
     * 启用状态名称
     */
    private String statusName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否是保底分组
     */
    private Integer isGuaranteedGroup = 0;
}
