package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import lombok.Data;

import java.util.List;

/**
 * 分组排序
 * <AUTHOR>
 * @date： 2025-06-30 14:12:59
 */
@Data
public class GroupRearrangementReq extends BaseTenantReq {

    /**
     * 调整字段
     */
    private List<GroupRearrangementField> rearrangementField;

    /**
     * 操作人
     */
    private Long operatorId;

    @Data
    public static class GroupRearrangementField {
        /**
         * 分组 id
         */
        private Integer groupId;
        /**
         * 排序
         */
        private Integer sort;
    }

    public void checkParam() {
        if (rearrangementField == null || rearrangementField.size() == 0) {
            throw new BusinessException("分组排序字段不能为空");
        }
        if (operatorId == null || operatorId == 0) {
            throw new BusinessException("操作人不能为空");
        }
    }
}
