package com.wanshifu.iop.im.api.req.tencent;

import lombok.Data;

import java.util.List;

/**
 * 单聊发送消息后已读回调
 * */
@Data
public class CallbackAfterMsgReportReq {
    /**
     * 回调命令
     * */
    private String CallbackCommand;

    /**
     * 已读上报方
     * */
    private String Report_Account;

    /**
     * 消息发送方
     * */
    private String Peer_Account;

    /**
     * 已读时间
     * */
    private Long LastReadTime;


    /**
     * 毫秒级别，事件触发时间戳
     * */
    private Long EventTime;

}
