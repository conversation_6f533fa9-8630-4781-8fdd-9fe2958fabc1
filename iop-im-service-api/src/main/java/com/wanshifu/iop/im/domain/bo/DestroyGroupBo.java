package com.wanshifu.iop.im.domain.bo;

import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

@Data
public class DestroyGroupBo {

    /**
     * 租户id，必填
     * */
    private Long tenantId;

    /**
     * im场景类型，必填
     * {@link com.wanshifu.iop.im.domain.enums.ImTypeEnum}
     * */
    private String imType;

    /**
     * 群id
     * */
    private String groupId;


}
