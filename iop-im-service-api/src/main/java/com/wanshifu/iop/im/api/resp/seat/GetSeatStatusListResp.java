package com.wanshifu.iop.im.api.resp.seat;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.List;

/**
 * 获取坐席状态列表
 * <AUTHOR>
 * @date： 2025-05-29 15:43:44
 */
@Data
public class GetSeatStatusListResp {

    /**
     * 主键id
     */
    private Integer seatStatusId;

    /**
     * 坐席状态英文名称
     */
    private String seatStatusEn;

    /**
     * 坐席状态名称
     */
    private String seatStatusCn;

    /**
     * 状态说明
     */
    private String seatStatusDesc;

    /**
     * 是否计入工时 0-否 1-是
     */
    private Integer isReckonInManHour;

    /**
     * 坐席功能
     */
    private List<SeatFunctionResp> seatFunctionList;
}
