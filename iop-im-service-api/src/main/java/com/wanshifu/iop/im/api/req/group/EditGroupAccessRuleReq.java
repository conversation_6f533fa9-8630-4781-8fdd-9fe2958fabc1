package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.BaseTenantReq;
import com.wanshifu.iop.im.domain.bo.groupManage.GroupAccessRuleOptionBo;
import com.wanshifu.iop.im.domain.enums.LogicalOperatorEnum;
import lombok.Data;

import java.util.List;

/**
 * 分组介入规则详情入参
 * <AUTHOR>
 * @date： 2025-06-30 14:12:59
 */
@Data
public class EditGroupAccessRuleReq extends BaseTenantReq {

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 全局逻辑运算符 and， or
     */
    private String globalLogicalOperator;

    /**
     * 分组接入规则集合
     */
    private List<GroupAccessRuleOptionBo> groupAccessRuleOptionList;

    /**
     * 操作人
     */
    private Long operatorId;

    public void checkParams() {
        if (groupId == null) {
            throw new BusinessException("分组id不能为null");
        }
        if ( StringUtils.isBlank(globalLogicalOperator) ) {
            throw new BusinessException("逻辑运算符不能为null");
        }
        if (LogicalOperatorEnum.getEnumByType(globalLogicalOperator) == null){
            throw new BusinessException("逻辑运算符不合法");
        }
        if (groupAccessRuleOptionList == null || groupAccessRuleOptionList.size() == 0) {
            throw new BusinessException("分组接入规则集合不能为空");
        }
        if (operatorId == null || operatorId == 0) {
            throw new BusinessException("操作人不能为null");
        }
    }
}
