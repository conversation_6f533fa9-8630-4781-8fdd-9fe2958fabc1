package com.wanshifu.iop.im.domain.enums.channel;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 渠道类型枚举
 */
public enum TemplateInitJsonEnum {

    // 枚举示例
    ALL("all", "全部", "client_channel_config_draft", "all_config_json"),
    SATISFACTION("satisfaction", "满意度模板", "client_channel_config_draft", "all_config_json"),
    STYLE_THEME("style_theme", "样式主题模板", "client_channel_config_draft", "all_config_json"),
    AUTO_REPLY("auto_reply", "自动回复模板", "client_channel_config_draft", "all_config_json"),
    CONVERSATION_GLOBAL_CONFIG("conversation_global_config", "会话全局配置模板", "client_channel_config_draft", "all_config_json"),
    CREATE_CONVERSATION_STYLE_CONFIG("createConversationStyleConfig", "会话样式配置模板", "client_channel_config_draft", "all_config_json");

    public final String type;
    public final String name;
    public final String tableTypeName;
    public final String columnTypeName;

    TemplateInitJsonEnum(String type, String name, String tableTypeName, String columnTypeName) {
        this.type = type;
        this.name = name;
        this.tableTypeName = tableTypeName;
        this.columnTypeName = columnTypeName;
    }
    private static final Map<String, TemplateInitJsonEnum> mapping = new HashMap<>();

    static {
        for (TemplateInitJsonEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, TemplateInitJsonEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        TemplateInitJsonEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static TemplateInitJsonEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}

