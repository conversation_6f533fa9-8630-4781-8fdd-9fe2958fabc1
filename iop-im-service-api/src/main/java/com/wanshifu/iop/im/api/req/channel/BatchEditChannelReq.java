package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.iop.im.domain.bo.channel.ChannelBo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量编辑渠道
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class BatchEditChannelReq {
    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空")
    @Min(1L)
    private Long operationId;

    /**
     * 编辑提交渠道列表
     */
    @NotEmpty(message = "编辑提交的渠道列表不能为空")
    private List<ChannelBo> channelBoList;

}
