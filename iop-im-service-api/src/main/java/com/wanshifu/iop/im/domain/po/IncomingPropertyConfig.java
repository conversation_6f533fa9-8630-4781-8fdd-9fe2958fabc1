package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 进线属性配置
 */
@Data
@ToString
@Table(name = "incoming_property_config")
public class IncomingPropertyConfig {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "property_id")
    private Long propertyId;

    /**
     * 租户id
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 特征值，ip/用户id/师傅id/总包id/游客设备id
     */
    @Column(name = "property_value")
    private String propertyValue;

    /**
     * 特征类型，user/master/enterprise/tourist/ip
     */
    @Column(name = "property_type")
    private String propertyType;

    /**
     * ip类型，white:白名单，black:黑名单
     */
    @Column(name = "control_type")
    private String controlType;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 启用状态：1：禁用，0：启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 删除状态：0:正常，1:删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}