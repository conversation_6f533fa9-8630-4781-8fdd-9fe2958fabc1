package com.wanshifu.iop.im.api.resp.seat;

import lombok.Data;

import javax.persistence.Column;

/**
 * 坐席功能
 * <AUTHOR>
 * @date： 2025-05-29 15:43:44
 */
@Data
public class SeatFunctionResp {

    /**
     * 主键id
     */
    private Integer seatFunctionId;

    /**
     * 坐席功能名称
     */
    private String seatFunctionCn;

    /**
     * 坐席功能英文名称
     */
    private String seatFunctionEn;

    /**
     * 坐席功能类型 support-支持 restrict-限制
     */
    private String seatFunctionType;

    /**
     * 坐席功能类型名称
     */
    private String seatFunctionTypeName;

}
