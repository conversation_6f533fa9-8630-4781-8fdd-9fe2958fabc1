package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 应用管理员账号信息
 */
@Data
@ToString
@Table(name = "im_admin")
public class ImAdmin {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "im_admin_id")
    private Long imAdminId;

    /**
     * 应用id
     */
    @Column(name = "im_id")
    private Long imId;

    /**
     * 管理员账号
     */
    @Column(name = "admin_account_id")
    private String adminAccountId;

    /**
     * 状态：1：删除，0：正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

}