package com.wanshifu.iop.im.domain.po.channel;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 端侧配置表
 */
@Data
@ToString
@Table(name = "client_category_config")
public class ClientCategoryConfig {

    /**
     * 端侧ID 取值: client_channel_config_draft_id
     */
    @Id
    @Column(name = "client_category_config_id")
    private Long clientCategoryConfigId;

    /**
     * 端侧名
     */
    @Column(name = "client_category_name")
    private String clientCategoryName;

    /**
     * 端侧类型: 内部-inner、外部-outer、游客-visitor
     */
    @Column(name = "client_category_type")
    private String clientCategoryType;

    /**
     * 分配规则英文名称
     */
    @Column(name = "client_category_en")
    private String clientCategoryEn;

    /**
     * 分配规则英文名
     */
    @Column(name = "rule_indicators_config_en")
    private String ruleIndicatorsConfigEn;

    /**
     * 腾讯应用配置ID
     */
    @Column(name = "um_id")
    private Long umId;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}