package com.wanshifu.iop.im.domain.bo;

import com.wanshifu.iop.im.domain.enums.channel.AutoReplyMsgSubTypeEnum;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 查询会话自动回复详情配置
 * <AUTHOR>
 * @date： 2025-07-17 15:42:38
 */
@Data
public class SelectConversationAutoReplyDetailConfigRespBo {

    /**
     * 渠道id
     * */
    private Long channelId;

    /**
     * 消息类型 {@link AutoReplyTypeEnum}
     * */
    private String msgType;

    /**
     * 消息字类型 {@link AutoReplyMsgSubTypeEnum}
     * */
    private String msgSubType;

    /**
     * 回复超时时长
     */
    private Long timeoutCostSecond = 0L;

    /**
     * 扩展信息
     */
    private String extraJson;

}
