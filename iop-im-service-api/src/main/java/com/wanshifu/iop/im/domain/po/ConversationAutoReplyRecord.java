package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 定时任务执行记录表
 */
@Data
@ToString
@Table(name = "conversation_auto_reply_record")
public class ConversationAutoReplyRecord {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "record_id")
    private Long recordId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 会话记录id
     */
    @Column(name = "conversation_item_id")
    private Long conversationItemId;

    /**
     * 自动回复配置明细id
     */
    @Column(name = "conversation_auto_reply_detail_config_id")
    private Long conversationAutoReplyDetailConfigId;

    /**
     * 超时类型
     */
    @Column(name = "msg_sub_type")
    private String msgSubType;

    /**
     * 发送状态：success:执行成功，fail:执行失败
     */
    @Column(name = "record_status")
    private String recordStatus;

    /**
     * 发送内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 删除状态，1:删除，0:未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}