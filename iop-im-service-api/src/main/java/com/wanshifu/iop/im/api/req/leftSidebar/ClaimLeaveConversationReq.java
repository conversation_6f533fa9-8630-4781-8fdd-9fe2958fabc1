package com.wanshifu.iop.im.api.req.leftSidebar;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import lombok.Data;

import java.util.List;

/**
 * 认领留言
 * <AUTHOR>
 * @date： 2025-07-14 20:22:55
 */
@Data
public class ClaimLeaveConversationReq extends BaseOnlineSeatReq {

    /**
     * 认领留言
     */
    private List<Long> conversationIdList;

    public void checkParams() {
        super.checkParams();
        if (CollectionUtils.isEmpty(conversationIdList)) {
            throw new BusinessException("认领留言不能为空");
        }
    }
}
