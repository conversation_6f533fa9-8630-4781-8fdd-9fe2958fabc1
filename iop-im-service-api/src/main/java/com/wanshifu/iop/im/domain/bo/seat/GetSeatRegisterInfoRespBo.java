package com.wanshifu.iop.im.domain.bo.seat;

import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 获取坐席注册信息响应
 * <AUTHOR>
 * @date： 2025-06-04 09:49:32
 */
@Data
@AllArgsConstructor
public class GetSeatRegisterInfoRespBo {

    /**
     * 坐席信息
     */
    private SeatInfo seatInfo;

    /**
     * 坐席注册信息
     */
    private UserRegisterInfo userRegisterInfo;
}
