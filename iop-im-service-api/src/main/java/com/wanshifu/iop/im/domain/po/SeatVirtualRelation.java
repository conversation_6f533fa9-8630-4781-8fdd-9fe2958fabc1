package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 坐席虚拟账号绑定关系
 */
@Data
@ToString
@Table(name = "seat_virtual_relation")
public class SeatVirtualRelation {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seat_virtual_relation_id")
    private Long seatVirtualRelationId;

    /**
     * 坐席id
     */
    @Column(name = "seat_id")
    private Long seatId;

    /**
     * 外部用户id
     */
    @Column(name = "outer_user_id")
    private String outerUserId;

    /**
     * 虚拟账号用户id
     */
    @Column(name = "virtual_user_id")
    private String virtualUserId;

    /**
     * 绑定时间
     */
    @Column(name = "bind_time")
    private Date bindTime;

    /**
     * 解绑时间
     */
    @Column(name = "over_bind_time")
    private Date overBindTime;

    /**
     * 绑定状态，binding:绑定中，over:绑定结束
     */
    @Column(name = "bind_status")
    private String bindStatus;

    /**
     * 删除状态：0:正常，1:删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}