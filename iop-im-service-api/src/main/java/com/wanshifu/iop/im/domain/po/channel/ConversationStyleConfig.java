package com.wanshifu.iop.im.domain.po.channel;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 渠道会话样式配置表
 */
@Data
@ToString
@Table(name = "conversation_style_config")
public class ConversationStyleConfig {

    /**
     * 会话样式配置ID
     */
    @Id
    @Column(name = "conversation_style_config_id")
    private Long conversationStyleConfigId;

    /**
     * 渠道id
     */
    @Column(name = "channel_id")
    private Long channelId;

    /**
     * 面向端口类型:  web-web端 app-app/小程序
     */
    @Column(name = "client_port_type")
    private String clientPortType;

    /**
     * 当前选择主题ID
     */
    @Column(name = "conversation_theme_id")
    private Long conversationThemeId;

    /**
     * 侧边栏配置json
     */
    @Column(name = "sidebar_json")
    private String sidebarJson;

    /**
     * 其他配置json
     */
    @Column(name = "other_json")
    private String otherJson;
    /**
     * 端侧ID
     */
    @Column(name = "client_category_id")
    private Long clientCategoryId;

    /**
     * 启用状态  1：禁用，0：启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}