package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 分组队列表
 */
@Data
@ToString
@Table(name = "group_queue")
public class GroupQueue {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "group_queue_id")
    private Long groupQueueId;

    /**
     * 会话分配id
     */
    @Column(name = "conversation_distribute_id")
    private Long conversationDistributeId;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    private Integer groupId;

    /**
     * 队列配置id
     */
    @Column(name = "queue_config_id")
    private Integer queueConfigId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 外部用户id
     */
    @Column(name = "outer_user_id")
    private String outerUserId;

    /**
     * 队列状态 wait-排队中 completed-分配完成 cancel-取消排队  expired-过期 
     */
    @Column(name = "queue_status")
    private String queueStatus;

    /**
     * 出队时间
     */
    @Column(name = "release_time")
    private Date releaseTime;

    /**
     * 取消时间
     */
    @Column(name = "cancel_time")
    private Date cancelTime;

    /**
     * 排序
     */
    @Column(name = "sort")
    private Long sort;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}