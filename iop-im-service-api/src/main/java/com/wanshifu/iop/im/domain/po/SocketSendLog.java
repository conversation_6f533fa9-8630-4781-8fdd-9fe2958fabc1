package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 订阅消息发送日志表
 */
@Data
@ToString
@Table(name = "socket_send_log")
public class SocketSendLog {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "socket_send_log_id")
    private Long socketSendLogId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 订阅人类型：seat-坐席 user-用户 master-师傅 enterprise-总包
     */
    @Column(name = "subscriber_class")
    private String subscriberClass;

    /**
     * 订阅人id
     */
    @Column(name = "subscriber_id")
    private String subscriberId;

    /**
     * 业务类型 msg_unread-消息未读  timeout_without-超时未回复 abount_to_timeout_without-临近超时未回复 conversation_distribute-会话分配 conversation_close-会话关闭
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 消息内容json
     */
    @Column(name = "message_content")
    private String messageContent;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}