package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 分组坐席分配规则表
 */
@Data
@ToString
@Table(name = "seat_group_distribute_rule")
public class SeatGroupDistributeRule {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seat_group_distribute_rule_id")
    private Integer seatGroupDistributeRuleId;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    private Integer groupId;

    /**
     * 坐席id
     */
    @Column(name = "seat_id")
    private Long seatId;

    /**
     * 规则指标id
     */
    @Column(name = "rule_metric_id")
    private Integer ruleMetricId;

    /**
     * 规则配置值
     */
    @Column(name = "rule_config_value")
    private String ruleConfigValue;

    /**
     * 操作人账号id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}