package com.wanshifu.iop.im.domain.bo;

import lombok.Data;

import java.util.List;

@Data
public class CreateGroupReqBo {

    /**
     * 租户id，必填
     * */
    private Long tenantId;

    /**
     * im场景类型，必填
     * {@link com.wanshifu.iop.im.domain.enums.ImTypeEnum}
     * */
    private String imType;

    /**
     * 群名称
     * */
    private String groupName;

    /**
     * 群管理员id，并不是应用管理员
     * */
    private String adminOuterUserId;



    /**
     * 会话id
     * */
    private Long conversationId;

    /**
     * 用户类型
     * {link OuterUserClassTypeEnum}
     * */
    private String outerClassType;

    /**
     * 群聊人员信息，包含群主
     * */
    private List<CreateGroupReqBoItem> groupMembers;

    @Data
    public static class CreateGroupReqBoItem {
        private String outerUserId;
        private String outerClassType;
    }


}
