package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 会话状态
 * */

public enum ConversationStatusEnum {

   PROCESSING("processing", "进行中"),

   COMPLETE("complete", "已完成"),
   ;

   public final String type;

   public final String name;

   ConversationStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, ConversationStatusEnum> mapping = new HashMap<>();

   static {
       for (ConversationStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, ConversationStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static ConversationStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
