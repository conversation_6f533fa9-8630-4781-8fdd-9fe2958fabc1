package com.wanshifu.iop.im.service.api.workbench;

import com.wanshifu.iop.im.api.req.topSidebar.SeatConnectionInfoListReq;
import com.wanshifu.iop.im.api.resp.topSidebar.SeatConnectionInfoListResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 顶边栏接口
 * <AUTHOR>
 * @date： 2025-06-04 10:49:16
 */
@FeignClient(
        value = "iop-im-service",
        path = "/topSidebar", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface TopSidebarServiceApi {

    /**
     * 在线坐席接线情况列表
     * @param req
     * @return
     */
    @PostMapping("seatConnectionInfoList")
    List<SeatConnectionInfoListResp> seatConnectionInfoList(@Valid @RequestBody SeatConnectionInfoListReq req);
}