package com.wanshifu.iop.im.api.resp.incomingConfig;

import lombok.Data;

/**
 * 注册频率查询结果
 * <AUTHOR>
 * @date： 2025-06-04 15:14:56
 */
@Data
public class GetRegistrationFrequencyConfigResp {

    /**
     * 册上限，0:不限制
     */
    private Integer dimensionIpTimes;

    /**
     * 维度类型：daily日期维度、minute分钟维度
     */
    private String dimensionType;

    /**
     * 维度类型名称
     */
    private String dimensionTypeName;

    /**
     * 如果是分钟维度，表示10分钟内的注册次数；如果是日期维度，表示10天内的注册次数
     */
    private Integer dimensionValue;
}
