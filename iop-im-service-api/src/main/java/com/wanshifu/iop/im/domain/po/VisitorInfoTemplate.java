package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 访客信息模板表
 */
@Data
@ToString
@Table(name = "visitor_info_template")
public class VisitorInfoTemplate {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "visitor_info_template_id")
    private Long visitorInfoTemplateId;

    /**
     * 访客类型英文名称 user-用户 master-师傅
     */
    @Column(name = "visitor_class_en")
    private String visitorClassEn;

    /**
     * 访客类型中文名称 user-用户 master-师傅
     */
    @Column(name = "visitor_class_cn")
    private String visitorClassCn;

    /**
     * 租户id
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}