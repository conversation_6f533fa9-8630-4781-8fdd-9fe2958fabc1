package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 用户注册信息表
 */
@Data
@ToString
@Table(name = "user_register_info")
public class UserRegisterInfo {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "register_info_id")
    private Long registerInfoId;

    /**
     * im应用配置ID
     */
    @Column(name = "im_id")
    private Long imId;

    /**
     * 内部用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户类型(merchant/master/customer:客服)
     */
    @Column(name = "user_class")
    private String userClass;

    /**
     * 外部用户ID
     */
    @Column(name = "outer_user_id")
    private String outerUserId;

    /**
     * 注册状态：processing:进行中，success:注册成功，fail:注册是吧
     */
    @Column(name = "register_status")
    private String registerStatus;

    /**
     * 注册时间
     */
    @Column(name = "register_time")
    private Date registerTime;

    /**
     * 用户签名
     */
    @Column(name = "user_sign")
    private String userSign;

    /**
     * 签名过期时间
     */
    @Column(name = "user_sign_expire_time")
    private Date userSignExpireTime;

    /**
     * 设备id
     */
    @Column(name = "device_id")
    private String deviceId;

    /**
     * 账号状态，0:启用，1:禁用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 删除状态，0:正常状态，1:删除状态
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 用户在线状态，online:在线，offline:离线
     */
    @Column(name = "online_state")
    private String onlineState;

    /**
     * 用户状态变更时间
     */
    @Column(name = "online_state_change_time")
    private Date onlineStateChangeTime;

}