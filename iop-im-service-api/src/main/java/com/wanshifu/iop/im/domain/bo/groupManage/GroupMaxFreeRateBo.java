package com.wanshifu.iop.im.domain.bo.groupManage;

import lombok.Data;

import java.util.List;

/**
 * 分组最大空闲程度
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
public class GroupMaxFreeRateBo {

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 分组id
     */
    private Long rate;

    /**
     * 分组序号
     */
    private Integer sort;

    /**
     * 是否需要排队
     */
    private Boolean needQueue = false;
}
