package com.wanshifu.iop.im.api.resp.channel;

import com.wanshifu.iop.im.domain.bo.CreateConversationStyleConfigBo;
import com.wanshifu.iop.im.domain.bo.channel.ConversationStyleConfigBo;
import lombok.Data;

import java.util.List;

/**
 * 渠道主题+样式
 */
@Data
public class PreviewClientCategoryAndChannelResp {
    /**
     * 渠道样式配置
     */
    private List<ConversationStyleConfigBo> conversationStyleConfigBoList;
    /**
     * 会话配置对象
     */
    private List<CreateConversationStyleConfigBo> createConversationStyleConfigBos;
}
