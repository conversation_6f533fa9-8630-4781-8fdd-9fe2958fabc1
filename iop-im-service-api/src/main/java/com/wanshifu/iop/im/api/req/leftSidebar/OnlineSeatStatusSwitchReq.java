package com.wanshifu.iop.im.api.req.leftSidebar;

import com.wanshifu.framework.core.BusinessException;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 获取坐席信息
 * <AUTHOR>
 * @date： 2025-07-14 20:22:55
 */
@Data
public class OnlineSeatStatusSwitchReq extends BaseOnlineSeatReq {

    /**
     * 切换状态
     */
    private String switchStatus;

    public void checkParams() {
        super.checkParams();
        if (StringUtils.isBlank(switchStatus)) {
            throw new BusinessException("switchStatus不能为空");
        }
    }
}
