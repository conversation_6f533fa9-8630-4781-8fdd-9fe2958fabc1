package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 群组表
 */
@Data
@ToString
@Table(name = "im_group")
public class ImGroup {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "im_group_id")
    private Long imGroupId;

    /**
     * 应用id
     */
    @Column(name = "im_id")
    private Long imId;

    /**
     * 外部群组id
     */
    @Column(name = "outer_group_id")
    private String outerGroupId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 群组状态，used:使用中，no_used:已解散
     */
    @Column(name = "group_status")
    private String groupStatus;

    /**
     * 删除状态，0:正常，1:删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}