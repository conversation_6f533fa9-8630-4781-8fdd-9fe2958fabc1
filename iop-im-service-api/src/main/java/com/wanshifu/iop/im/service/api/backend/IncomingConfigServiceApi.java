package com.wanshifu.iop.im.service.api.backend;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.incomingConfig.*;
import com.wanshifu.iop.im.api.resp.incomingConfig.GetRegistrationFrequencyConfigResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.IncomingConfigEnumsResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchDomainListResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchWhiteAndBlackListResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 进线配置接口
 * <AUTHOR>
 * @date： 2025-06-04 10:49:16
 */
@FeignClient(
        value = "iop-im-service",
        path = "/incomingConfig", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface IncomingConfigServiceApi {

    /**
     * 获取进线配置相关枚举
     * @return 进线配置枚举响应对象
     */
    @PostMapping("/enums")
    IncomingConfigEnumsResp enums();

    /**
     * 获取域名白名单列表
     * @param req 域名查询请求参数
     * @return 域名白名单列表
     */
    @PostMapping("/domainList")
    List<SearchDomainListResp> domainList(@Valid @RequestBody SearchDomainListReq req);

    /**
     * 添加域名到白名单
     * @param req 域名添加请求参数
     * @return 影响行数
     */
    @PostMapping("/addDomain")
    Integer addDomain(@Valid @RequestBody AddDomainReq req);

    /**
     * 从白名单中删除域名
     * @param req 域名删除请求参数
     * @return 影响行数
     */
    @PostMapping("/deleteDomain")
    Integer deleteDomain(@Valid @RequestBody DeleteDomainReq req);

    /**
     * 查询访客白名单/黑名单列表
     * @param req 查询请求参数
     * @return 访客名单列表
     */
    @PostMapping("/whiteAndBlackList")
    SimplePageInfo<SearchWhiteAndBlackListResp> whiteAndBlackList(@Valid @RequestBody SearchWhiteAndBlackListReq req);

    /**
     * 添加访客白名单/黑名单
     * @param req 添加请求参数
     * @return 影响行数
     */
    @PostMapping("/addWhiteAndBlack")
    Integer addWhiteAndBlack(@Valid @RequestBody AddWhiteAndBlackReq req);

    /**
     * 修改访客白名单/黑名单
     * @param req 修改请求参数
     * @return 影响行数
     */
    @PostMapping("/updateWhiteAndBlack")
    Integer updateWhiteAndBlack(@Valid @RequestBody UpdateWhiteAndBlackReq req);

    /**
     * 获取注册频率限制配置
     * @param req 查询请求参数
     * @return 注册频率配置信息
     */
    @PostMapping("/getRegistrationFrequencyConfig")
    GetRegistrationFrequencyConfigResp getRegistrationFrequencyConfig(@Valid @RequestBody GetRegistrationFrequencyConfigReq req);

    /**
     * 更新注册频率限制配置
     * @param req 更新请求参数
     * @return 影响行数
     */
    @PostMapping("/updateRegistrationFrequency")
    Integer updateRegistrationFrequency(@Valid @RequestBody UpdateRegistrationFrequencyReq req);
}