package com.wanshifu.iop.im.domain.enums.channel;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 渠道类型枚举
 */
public enum ChannelTypeEnum {
    USER_CHANNEL("user_channel", "用户入口"),

    ORDER_CHANNEL("order_channel", "订单入口"),

    ;

    public final String type;

    public final String name;

    ChannelTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, ChannelTypeEnum> mapping = new HashMap<>();

    static {
        for (ChannelTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, ChannelTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        ChannelTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static ChannelTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
