package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 规则指标表
 */
@Data
@ToString
@Table(name = "rule_metric_config")
public class RuleMetricConfig {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "rule_metric_id")
    private Integer ruleMetricId;

    /**
     * 规则中文名称
     */
    @Column(name = "rule_metric_cn")
    private String ruleMetricCn;

    /**
     * 规则英文名称
     */
    @Column(name = "rule_metric_en")
    private String ruleMetricEn;

    /**
     * 规则对象 group:分组规则 seat:坐席规则
     */
    @Column(name = "rule_metric_object")
    private String ruleMetricObject;

    /**
     * 规则描述
     */
    @Column(name = "rule_metric_desc")
    private String ruleMetricDesc;

    /**
     * 规则扩展json（存默认枚举值，规则的来源，渠道，入口，咨询类型，用户ka标签等）
     */
    @Column(name = "rule_metric_extra_json")
    private String ruleMetricExtraJson;

    /**
     * 规则来源 kf-客服系统 bigdata-大数据
     */
    @Column(name = "from_type")
    private String fromType;

    /**
     * 操作人账号id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}