package com.wanshifu.iop.im.api.resp.channel;

import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;

import java.util.List;

/**
 * 端侧列表枚举
 * <AUTHOR>
 * @date： 2025-06-03 16:54:14
 */
@Data
public class ChannelEnumsResp {
    /**
     * 端侧类型枚举
     */
    private List<CommonLabelValueResp> clientCategoryTypeEnumList;
    /**
     * 渠道类型枚举
     */
    private List<CommonLabelValueResp> channelTypeEnumList;

    /**
     * 启用状态类型枚举
     */
    private List<CommonLabelValueResp> statusEnumList;
}
