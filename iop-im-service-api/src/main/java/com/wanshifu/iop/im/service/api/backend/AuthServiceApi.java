package com.wanshifu.iop.im.service.api.backend;

import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.BatchLogoutSeatReq;
import com.wanshifu.iop.im.api.req.seat.DisableSeatStatusReq;
import com.wanshifu.iop.im.api.req.seat.SetLoginAuthReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.SetLoginAuthResp;
import com.wanshifu.iop.im.api.resp.VerifyRepeatNameResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 鉴权接口
 * <AUTHOR>
 * @date： 2025-06-04 10:49:16
 */
@FeignClient(
        value = "iop-im-service",
        path = "/auth", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface AuthServiceApi {

    /**
     * 登录坐席
     */
    @FeignTimeout(connectTimeoutMillis = 500, readTimeoutMillis = 500)
    @PostMapping("/loginSeat")
    SetLoginAuthResp loginSeat(@Valid @RequestBody SetLoginAuthReq req);

    /**
     * 批量登出坐席
     */
    @FeignTimeout(connectTimeoutMillis = 500, readTimeoutMillis = 500)
    @PostMapping("/batchLogoutSeat")
    Integer batchLogoutSeat(@Valid @RequestBody BatchLogoutSeatReq req);

    /**
     * 禁用坐席状态
     */
    @FeignTimeout(connectTimeoutMillis = 500, readTimeoutMillis = 500)
    @PostMapping("/disableSeatStatus")
    Integer disableSeatStatus(@Valid @RequestBody DisableSeatStatusReq req);
}
