package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.im.domain.bo.CreateConversationStyleConfigBo;
import com.wanshifu.iop.im.domain.bo.channel.ConversationAutoReplyDetailConfigBo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 创建或编辑自动回复配置请求参数
 */
@Data
public class CreateOrEditAutoReplyConfigReq {

    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 渠道id
     */
    @NotNull(message = "渠道id不能为空")
    @Min(1L)
    private Long channelId;

    /**
     * 系统自动回复消息类型
     * 欢迎语 - welcome_auto_reply
     * 排队提示语 - wait_auto_reply
     * 超时回复提醒语 timeout_auto_reply
     * 会话结束语 end_auto_reply
     * 会话样式配置 conversation_style_config
     */
    @ValueIn("welcome_auto_reply,wait_auto_reply,timeout_auto_reply,end_auto_reply,conversation_style_config")
    @NotNull(message = "系统自动回复消息类型不能为空")
    private String msgType;

    /**
     * 自动回复配置ID（为空则为新增，非空为编辑）
     */
    private Long autoReplyConfigId;

    /**
     * 自动回复配置内容 JSON 字符串
     */
    private String autoReplyJson;

    /**
     * 启用状态：1-启用，0-未启用
     */
    private Integer status;

    /**
     * 是否删除：1-是，0-否
     */
    private Integer isDelete;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 数据创建时间（可选，默认由服务端设置）
     */
    private Date createTime;

    /**
     * 数据修改时间（可选，默认由服务端设置）
     */
    private Date updateTime;

    /**
     * 系统自动回复消息明细列表
     */
    @NotEmpty(message="系统自动回复消息明细列表不能为空")
    private List<ConversationAutoReplyDetailConfigBo> conversationAutoReplyDetailConfigBoList;
    /**
     * 会话配置对象
     */
    private List<CreateConversationStyleConfigBo> createConversationStyleConfigBos;

}
