package com.wanshifu.iop.im.domain.dto.channel;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ConversationAutoReplyConfigDto {

    /**
     * 系统自动回复配置ID
     */
    private Long conversationAutoReplyConfigId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 提示类型：欢迎语 - welcome_auto_reply、排队提示语、坐席超时回复提醒语 seat_timeout_auto_reply、访客超时回复提醒语 visitor_timeout_auto_reply
     */
    private String msgType;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据修改时间
     */
    private Date updateTime;


    /**
     * 系统自动回复消息明细列表
     */
    private List<ConversationAutoReplyDetailConfigDto> conversationAutoReplyDetailConfigBoList;

}
