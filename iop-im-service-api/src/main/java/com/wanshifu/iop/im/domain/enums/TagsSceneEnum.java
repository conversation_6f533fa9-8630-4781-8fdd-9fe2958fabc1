package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 标签场景枚举
 * <AUTHOR>
 * @date： 2025-05-29 15:49:55
 */
public enum TagsSceneEnum {

    /**
     * conversation-会话场景
     * seat-坐席场景
     */

    CONVERSATION("conversation", "会话场景"),

    SEAT("seat", "坐席场景"),


    ;

    public final String type;

    public final String name;

    TagsSceneEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    private static final Map<String, TagsSceneEnum> mapping = new HashMap<>();

    static {
        for (TagsSceneEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<String, TagsSceneEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return "";
        }
        TagsSceneEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static TagsSceneEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
