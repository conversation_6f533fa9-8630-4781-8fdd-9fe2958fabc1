package com.wanshifu.iop.im.api.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 切换状态
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class SwitchStatusReq {

    /**
     * 切换id
     */
    private Long switchId;

    /**
     * 切换类型
     */
    private String switchType;

    /**
     * 启用状态：1：禁用，0：启用
     */
    private Integer status;

    /**
     * 操作人id
     */
    private Long operatorId;
}
