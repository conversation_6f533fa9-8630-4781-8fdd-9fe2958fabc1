package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 队列状态枚举
 * */

public enum QueueStatusEnum {

    /**
     * 队列状态 wait-排队中 completed-分配完成 cancel-取消排队  expired-过期
     */
    WAIT("wait", "排队中"),
    COMPLETED("completed", "分配完成"),
    CANCEL("cancel", "取消排队"),
    EXPIRED("expired", "过期"),

   ;

   public final String type;

   public final String name;

   QueueStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, QueueStatusEnum> mapping = new HashMap<>();

   static {
       for (QueueStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, QueueStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static QueueStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
