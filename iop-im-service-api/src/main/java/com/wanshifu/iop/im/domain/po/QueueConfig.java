package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 队列配置表
 */
@Data
@ToString
@Table(name = "queue_config")
public class QueueConfig {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "queue_config_id")
    private Integer queueConfigId;

    /**
     * 队列来源id group-group_id
     */
    @Column(name = "from_id")
    private Long fromId;

    /**
     * 队列类型 group-分组队列
     */
    @Column(name = "from_type")
    private String fromType;

    /**
     * 队列最大长度
     */
    @Column(name = "max_length")
    private Long maxLength;

    /**
     * 队列成员间隔分钟
     */
    @Column(name = "member_interval_minute")
    private Long memberIntervalMinute;

    /**
     * 队列生命周期（秒）
     */
    @Column(name = "msg_ttl")
    private Long msgTtl;

    /**
     * 操作人账号id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}