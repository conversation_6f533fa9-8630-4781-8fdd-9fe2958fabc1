package com.wanshifu.iop.im.domain.bo;

import com.wanshifu.iop.im.domain.enums.channel.AutoReplyTypeEnum;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyMsgSubTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 查询会话自动回复详情配置
 * <AUTHOR>
 * @date： 2025-07-17 15:42:38
 */
@Data
public class SelectConversationAutoReplyDetailConfigReqBo {

    /**
     * 渠道id
     * */
    private List<Long> channelIdList;

    /**
     * 消息类型 {@link AutoReplyTypeEnum}
     * */
    private List<String> msgTypeList;

    /**
     * 消息字类型 {@link AutoReplyMsgSubTypeEnum}
     * */
    private List<String> msgSubTypeList;

}
