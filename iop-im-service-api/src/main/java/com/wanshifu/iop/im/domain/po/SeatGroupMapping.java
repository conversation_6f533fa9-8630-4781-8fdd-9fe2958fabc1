package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 坐席分组关联表
 */
@Data
@ToString
@Table(name = "seat_group_mapping")
public class SeatGroupMapping {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seat_group_mapping_id")
    private Integer seatGroupMappingId;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    private Integer groupId;

    /**
     * 坐席id
     */
    @Column(name = "seat_id")
    private Long seatId;

    /**
     * 启用（分配）状态：1：禁用，0：启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}