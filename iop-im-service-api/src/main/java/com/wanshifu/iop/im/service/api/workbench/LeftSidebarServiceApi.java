package com.wanshifu.iop.im.service.api.workbench;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.incomingConfig.*;
import com.wanshifu.iop.im.api.req.leftSidebar.*;
import com.wanshifu.iop.im.api.resp.incomingConfig.GetRegistrationFrequencyConfigResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.IncomingConfigEnumsResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchDomainListResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchWhiteAndBlackListResp;
import com.wanshifu.iop.im.api.resp.leftSidebar.*;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 左侧边栏接口
 * <AUTHOR>
 * @date： 2025-06-04 10:49:16
 */
@FeignClient(
        value = "iop-im-service",
        path = "/leftSidebar", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface LeftSidebarServiceApi {

    /**
     * 公共枚举接口
     * @return
     */
    @PostMapping("/enums")
    LeftSidebarEnumsResp enums();

    /**
     * 坐席基本信息
     * @param req
     * @return
     */
    @PostMapping("/onlineSeatInfo")
    OnlineSeatInfoResp onlineSeatInfo(@Valid @RequestBody OnlineSeatInfoReq req);

    /**
     * 坐席状态切换
     * @param req
     * @return
     */
    @PostMapping("/onlineSeatStatusSwitch")
    Integer onlineSeatStatusSwitch(@Valid @RequestBody OnlineSeatStatusSwitchReq req);

    /**
     * 待处理列表
     * @param req
     * @return
     */
    @PostMapping("/waitProcessConversationList")
    List<GetWaitProcessConversationListResp> waitProcessConversationList(@Valid @RequestBody GetWaitProcessConversationListReq req);

    /**
     * 访客人工打标
     * @param req
     * @return
     */
    @PostMapping("/seatMarkForVisitor")
    Integer seatMarkForVisitor(@Valid @RequestBody SeatMarkForVisitorReq req);

    /**
     * 访客标签移除
     * @param req
     * @return
     */
    @PostMapping("/seatUnmarkForVisitor")
    Integer seatUnmarkForVisitor(@Valid @RequestBody SeatUnmarkForVisitorReq req);

    /**
     * 留言会话列表
     * @param req
     * @return
     */
    @PostMapping("/leaveConversationList")
    SimplePageInfo<GetLeaveConversationListResp> leaveConversationList(@Valid @RequestBody GetLeaveConversationListReq req);

    /**
     * 认领留言
     * @param req
     * @return
     */
    @PostMapping("/claimLeaveConversation")
    Integer claimLeaveConversation(@Valid @RequestBody ClaimLeaveConversationReq req);

    /**
     * 历史会话列表
     * @param req
     * @return
     */
    @PostMapping("/historyConversationList")
    SimplePageInfo<GetHistoryConversationListResp> historyConversationList(@Valid @RequestBody GetHistoryConversationListReq req);

    /**
     * 历史会话激活
     * @param req
     * @return
     */
    @PostMapping("/historyConversationActive")
    Integer historyConversationActive(@Valid @RequestBody HistoryConversationActiveReq req);

    /**
     * 历史会话消息记录
     * @param req
     * @return
     */
    @PostMapping("/getHistoryConversationItemLogs")
    SimplePageInfo<GetHistoryConversationItemResp> getHistoryConversationItemLogs(@Valid @RequestBody GetHistoryConversationItemListReq req);
}