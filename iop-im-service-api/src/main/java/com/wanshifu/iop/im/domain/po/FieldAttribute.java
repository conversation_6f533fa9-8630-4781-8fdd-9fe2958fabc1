package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 字段属性表
 */
@Data
@ToString
@Table(name = "field_attribute")
public class FieldAttribute {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "field_attribute_id")
    private Long fieldAttributeId;

    /**
     * 来源模板id
     */
    @Column(name = "from_template_id")
    private Long fromTemplateId;

    /**
     * 来源模板类型 visitor_info-访客信息  business_from-业务表单
     */
    @Column(name = "from_template_type")
    private String fromTemplateType;

    /**
     * 字段英文名称
     */
    @Column(name = "field_en")
    private String fieldEn;

    /**
     * 字段中文名称
     */
    @Column(name = "field_cn")
    private String fieldCn;

    /**
     * 字段场景 info-信息 from-表单
     */
    @Column(name = "field_scene")
    private String fieldScene;

    /**
     * 字段录入类型:text-文本,input-单行文本,textara-文本框,select-下拉列表,compositeSelect-多级复合下拉列表,picture-图片,video-视频,audio-音频
     */
    @Column(name = "field_type")
    private String fieldType;

    /**
     * 字段说明
     */
    @Column(name = "field_desc")
    private String fieldDesc;

    /**
     * 字段扩展信息
     */
    @Column(name = "field_extra")
    private String fieldExtra;

    /**
     * 启用状态: 1-禁用, 0-启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}