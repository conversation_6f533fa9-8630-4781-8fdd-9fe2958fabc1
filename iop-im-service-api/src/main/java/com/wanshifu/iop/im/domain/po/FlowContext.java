package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 流程上下文表
 */
@Data
@ToString
@Table(name = "flow_context")
public class FlowContext {

    /**
     * 
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "context_id")
    private Long contextId;

    /**
     * 流程实例id
     */
    @Column(name = "instance_id")
    private Long instanceId;

    /**
     * 外部用户id
     */
    @Column(name = "outer_user_id")
    private String outerUserId;

    /**
     * 上下文内容
     */
    @Column(name = "context_value")
    private String contextValue;

    /**
     * 删除状态，1：删除，0：正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "create_time")
    private Date createTime;

}