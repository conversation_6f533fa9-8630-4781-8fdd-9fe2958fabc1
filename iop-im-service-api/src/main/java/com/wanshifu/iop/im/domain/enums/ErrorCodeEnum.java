package com.wanshifu.iop.im.domain.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public enum ErrorCodeEnum {
    FAIL(0, ErrorCodeTypeEnum.BIZ_ERROR.code, "失败"),
    SUCCESS_CODE(1, ErrorCodeTypeEnum.SUCCESS_ERROR.code, "成功"),
    DUPLICATE_KEY_CODE(2, ErrorCodeTypeEnum.BIZ_ERROR.code, "重复幂等校验 直接返回成功！"),
    ACCOUNT_ABNORMAL(35,ErrorCodeTypeEnum.BIZ_ERROR.code,"账号不存在或已注销"),
    SYSTEM_EXCEPTION_ERROR(41, ErrorCodeTypeEnum.SYSTEM_ERROR.code, "系统错误");

    public final Integer code;
    public final Integer bizType;
    public final String desc;

    private static final Map<Integer, ErrorCodeEnum> mapping = new HashMap<>();

    static {
        for (ErrorCodeEnum value : values()) {
            mapping.put(value.code, value);
        }
    }

    ErrorCodeEnum(int code, int bizType, String desc) {
        this.code = code;
        this.bizType = bizType;
        this.desc = desc;
    }

    public static ErrorCodeEnum fromType(Integer code) {
        return mapping.get(code);
    }

    /**
     * 判断值是否在枚举内
     */
    public static Boolean include(Integer code) {
        if (null != code) {
            return mapping.containsKey(code);
        }
        return false;
    }

    /**
     * 获取枚举内的值
     */
    public static Integer getUserTypeTxt(Integer code) {
        if (null != code) {
            return mapping.get(code).code;
        }
        return null;
    }
}
