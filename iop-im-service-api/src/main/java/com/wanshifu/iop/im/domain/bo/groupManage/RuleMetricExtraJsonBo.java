package com.wanshifu.iop.im.domain.bo.groupManage;

import lombok.Data;

import java.util.List;

/**
 * 规则指标配置项json
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
public class RuleMetricExtraJsonBo {

    /**
     * {
     * "database":"iop_im_service",
     * "fromTable":"client_category_config",
     * "selectiveType":"",
     * "fromConditions":null,
     * "defaultEnums":null
     * }
     */

    /**
     * 数据库名
     */
    private String database;

    /**
     * 表名
     */
    private String fromTable;

    /**
     * 选择类型 single-单选 multiple-多选
     */
    private String selectiveType;

    /**
     * 查询条件
     */
    private List<String> fromConditions;

    /**
     * 默认枚举
     */
    private List<String> defaultEnums;
}
