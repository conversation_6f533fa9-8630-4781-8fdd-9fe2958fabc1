package com.wanshifu.iop.im.domain.enums.leftSidebar;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 访客标签枚举
 * */

public enum VisitorLabelEnum {

    /**
     * 访客标签枚举 alreadyFeedback-已反馈 needFeedback- 需反馈  needLeave-需留言
     */
    notMark("notMark", "为空"),
    alreadyFeedback("alreadyFeedback", "已反馈"),
    needFeedback("needFeedback", "需反馈"),
    needLeave("needLeave", "需留言"),
    ;

   ;

   public final String type;

   public final String name;

   VisitorLabelEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, VisitorLabelEnum> mapping = new HashMap<>();

   static {
       for (VisitorLabelEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, VisitorLabelEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static VisitorLabelEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
