package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 群成员状态
 * */

public enum MemberStatusEnum {

   IN("in", "群成员"),

   OUT("out", "非群成员"),

   ;

   public final String type;

   public final String name;

   MemberStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, MemberStatusEnum> mapping = new HashMap<>();

   static {
       for (MemberStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, MemberStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static MemberStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
