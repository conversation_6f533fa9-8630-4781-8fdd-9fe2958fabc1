package com.wanshifu.iop.im.api.req.tencent;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class SendMsgGroupReq {

    /**
     * 群id
     * */
    @JSONField(name = "GroupId")
    private String GroupId;
    /**
     * 消息发送方账号id
     * 外部用户Id
     * */
    @JSONField(name = "From_Account")
    private String From_Account;


    /**
     * 消息随机数
     * */
    @JSONField(name = "Random")
    private Long Random;

    /**
     * 消息体
     * */
    @JSONField(name = "MsgBody")
    private List<MsgBodyItem> MsgBody;


    @Data
    public static class MsgBodyItem{
        /**
         * 消息类型
         * TIMTextElem（文本消息）
         * TIMLocationElem（位置消息）
         * TIMFaceElem（表情消息）
         * TIMCustomElem（自定义消息）
         * TIMSoundElem（语音消息）
         * TIMImageElem（图像消息）
         * TIMFileElem（文件消息）
         * TIMVideoFileElem（视频消息）
         * */
        @JSONField(name = "MsgType")
        private String MsgType;

        /**
         * 消息对象
         * */
        @JSONField(name = "MsgContent")
        private Object MsgContent;
    }

    @Data
    public static class MsgBodyContent{
        @JSONField(name = "Text")
        private String Text;
    }
}
