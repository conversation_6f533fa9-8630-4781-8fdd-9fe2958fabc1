package com.wanshifu.iop.im.api.req.seat;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/7/26 17:44
 * @description 会话转接
 */
@Data
public class TransferServiceReq {

    @NotNull
    private String outerGroupId;

    /**
     * 分组id
     * */
    @NotNull
    private Integer groupId;

    /**
     * 坐席id
     * */
    private Long seatId;

    /**
     * 转接备注
     * */
    private String note;

    /**
     * 操作人账号
     * */
    @NotNull
    private Long accountId;

}
