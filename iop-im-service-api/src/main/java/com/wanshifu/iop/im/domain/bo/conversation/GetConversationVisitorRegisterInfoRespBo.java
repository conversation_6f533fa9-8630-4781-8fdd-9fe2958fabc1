package com.wanshifu.iop.im.domain.bo.conversation;

import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 获取会话访客注册信息响应
 * <AUTHOR>
 * @date： 2025-06-04 09:49:32
 */
@Data
@AllArgsConstructor
public class GetConversationVisitorRegisterInfoRespBo {

    /**
     * 会话信息
     */
    private ImConversation imConversation;

    /**
     * 会话访客注册信息
     */
    private UserRegisterInfo userRegisterInfo;
}
