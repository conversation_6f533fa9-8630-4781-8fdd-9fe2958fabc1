package com.wanshifu.iop.im.domain.bo.socketBase;

import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;

import java.util.List;

/**
 * 刷新坐席的访客标签
 * socket消息bo
 * <AUTHOR>
 * @date： 2025-07-24 10:38:49
 */
@Data
public class SendRefreshSeatVisitorMarkMessageBo extends BaseMessageBo {

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 访客标签
     */
    private List<CommonLabelValueResp> visitorLabelNameList;

}
