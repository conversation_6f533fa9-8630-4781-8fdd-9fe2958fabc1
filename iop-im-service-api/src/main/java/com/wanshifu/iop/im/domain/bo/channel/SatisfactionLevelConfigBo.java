package com.wanshifu.iop.im.domain.bo.channel;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class SatisfactionLevelConfigBo {
    /**
     * 端侧ID
     */
    private Long clientCategoryId;
    /**
     * 系统自动回复明细配置ID
     */
    private Long satisfactionLevelConfigId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 其他字段配置
     */
    @JSONField(name = "resultCallbackFormJsonBo")
    private ResultCallbackFormJsonBo resultCallbackFormJsonBo;

    /**
     * 其他字段字符串配置
     */
    private String resultCallbackFormJson;
    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 数据修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
}
