package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 渠道更新
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class UpdateChannelReq {

    /**
     * 渠道ID
     */
    @NotNull(message = "渠道ID不能为空")
    @Min(1L)
    private Long channelId;

    /**
     * 渠道中文名
     */
    @NotNull(message = "渠道名不能为空")
    private String channelName;

    /**
     * 渠道英文名
     */
    @NotNull(message = "渠道标识不能为空")
    private String channelEn;

    /**
     * 渠道描述
     */
    private String channelDesc;

    /**
     * 渠道类型: 用户渠道-user_channel、订单渠道-order_channel
     */
    @NotNull(message = "渠道类型不能为空")
    @ValueIn("user_channel,order_channel")
    private String channelType;


    /**
     * 全部配置json字符串 后端自行设置
     */
    private String allConfigJson;

    /**
     * 路由导航配置id
     */
    private Long flowDefineId;

    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 启用状态  1：禁用，0：启用
     */
    private Integer status;

    /**
     * 预览url模版  https://www.im.wnshifu.com/client?conversationId=xxx
     */
    private String previewUrlTemplate;

    /**
     * 是否删除:1-是,0-否
     */
    private Integer isDelete;

    /**
     * 操作人id
     */
    private Long operatorId;

}
