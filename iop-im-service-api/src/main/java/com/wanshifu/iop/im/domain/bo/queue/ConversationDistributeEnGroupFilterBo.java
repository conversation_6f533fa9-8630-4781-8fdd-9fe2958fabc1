package com.wanshifu.iop.im.domain.bo.queue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date： 2025-07-01 10:58:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConversationDistributeEnGroupFilterBo {

    /**
     * 会话所在的会话渠道（端侧）
     */
    private Long clientCategoryId;

    /**
     * 会话所在的会话入口（渠道）
     */
    private Long channelId;

    /**
     * 用户填写的咨询类型
     */
    private Long userProblemClassId;
}
