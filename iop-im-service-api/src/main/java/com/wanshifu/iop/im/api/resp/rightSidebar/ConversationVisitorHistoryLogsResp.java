package com.wanshifu.iop.im.api.resp.rightSidebar;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 联络动态
 * <AUTHOR>
 * @date： 2025-07-14 20:37:38
 */
@Data
public class ConversationVisitorHistoryLogsResp {

    /**
     * 历史会话id
     */
    private Long historyConversationId;

    /**
     * 渠道名称 client_category_name
     */
    private String clientCategoryName;

    /**
     * 入口名称 channel_name
     */
    private String channelName;

    /**
     * 会话进线时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 最近关联工单id
     */
    private Long workOrderId;

    /**
     * 最近关联工单类型
     */
    private String workOrderTypeName;

    /**
     * 最近关联工单状态
     */
    private String workOrderStatusName;
}
