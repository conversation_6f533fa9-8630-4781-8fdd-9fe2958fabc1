package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.iop.im.api.req.BaseSwitchStatusReq;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class DeployClientCategoryAndChannelReq extends BaseSwitchStatusReq {

    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 渠道ID
     */
    @NotNull(message = "渠道ID不能为空")
    @Min(1L)
    private Long channelId;

}
