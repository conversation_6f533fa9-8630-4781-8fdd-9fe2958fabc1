package com.wanshifu.iop.im.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImConversationBeanBo {

    private String fromAccount;

    private String fromAccountType;

    private String toAccount;

    private String toAccountType;

    /**
     * 群聊接待人类型robot:机器人,seat:坐席
     * */
    private String responseAccount;

    /**
     * 群聊接待人外部用户id
     * */
    private String responseAccountType;

    /**
     * 消息的唯一标识，可用于 REST API 撤回单聊消息
     * */
    private String MsgKey;

    /**
     * 消息在客户端上的唯一标识
     * */
    private String MsgId;

    /**
     * 消息序号
     * */
    private Long MsgSeq;


    /**
     * 消息内容json
     * */
    private String msgContentJson;

    /**
     * 消息列表
     * */
    private List<MsgContentItem> msgContentItemList;

    @Data
    public static class MsgContentItem{

        /**
         * 消息类型
         * */
        private String msgType;

        /**
         * 消息内容
         * */
        private Object msgContent;
    }
}
