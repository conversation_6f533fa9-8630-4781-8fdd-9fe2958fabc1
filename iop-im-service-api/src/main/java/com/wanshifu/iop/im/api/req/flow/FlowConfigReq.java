package com.wanshifu.iop.im.api.req.flow;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FlowConfigReq {

    /**
     * 路由导航id
     * */
    private Long flowId;

    /**
     * 更新人id
     * */
    private Long updateAccountId;

    private List<FlowDetailNode> flowNodeList;

    @Data
    public static class FlowDetailNode {

        /**
         * 节点类型
         * 节点类型：
         * robot:机器人，
         * time:时间，
         * system_msg:系统消息，
         * artificial：转人工，
         * system_reply:系统自动回复，
         * leave_word:留言
         * */
        private String nodeType;

        /**
         * 节点名称
         * */
        private String nodeName;


        /**
         * 节点内容
         * */
        private String nodeConfigJsonStr;

        /**
         * 节点排序号
         * */
        private Integer sort;

        /**
         * 父级节点
         * */
        private String fromNodeId;

        /**
         * 自身节点id
         * */
        private String nodeId;


    }


}
