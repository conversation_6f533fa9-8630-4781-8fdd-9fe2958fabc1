package com.wanshifu.iop.im.api.req.tencent;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/7/19 17:17
 * @description 修改历史会话消息
 */
@Data
public class ModifyMsgSingleTencentReq {

    /**
     * 消息发送者
     * */
    @JSONField(name = "From_Account")
    private String From_Account;

    /**
     * 消息接收者
     * */
    @JSONField(name = "To_Account")
    private String To_Account;

    /**
     *
     * */
    @JSONField(name = "Msg<PERSON><PERSON>")
    private String MsgKey;

    @JSONField(name = "MsgBody")
    private List<ModifyMsgSingleTencentReqItem> MsgBody;

    @Data
    public static class ModifyMsgSingleTencentReqItem {

        @JSONField(name = "MsgType")
        private String MsgType;

        @JSONField(name = "MsgContent")
        private Object MsgContent;

    }
}
