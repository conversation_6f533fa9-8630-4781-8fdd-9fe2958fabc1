package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 节点类型
 *
 * <AUTHOR>
 * */

public enum NodeTypeEnum {

   START("start", "开始"),
   ROBOT("robot", "机器人"),

   TIME("time", "时间判断"),

   SYSTEM_MSG("system_msg", "系统消息"),

   ARTIFICIAL("artificial", "转人工"),

   SYSTEM_REPLY("system_reply", "系统自动回复"),

   LEAVE_WORD("leave_word", "留言"),

   ;

   public final String type;

   public final String name;

   NodeTypeEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, NodeTypeEnum> mapping = new HashMap<>();

   static {
       for (NodeTypeEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, NodeTypeEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static NodeTypeEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
