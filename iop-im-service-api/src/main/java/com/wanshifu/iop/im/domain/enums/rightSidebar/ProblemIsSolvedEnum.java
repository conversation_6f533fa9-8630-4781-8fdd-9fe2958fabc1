package com.wanshifu.iop.im.domain.enums.rightSidebar;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 问题是否已解决
 * <AUTHOR>
 * @date： 2025-07-25 15:18:03
 */

public enum ProblemIsSolvedEnum {

    /**
     * 问题是否已解决 0-未解决 1-已解决
     */
    NO(0, "未解决"),
    YES(1, "已解决"),
   ;

   public final Integer type;

   public final String name;

   ProblemIsSolvedEnum(Integer type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<Integer, ProblemIsSolvedEnum> mapping = new HashMap<>();

   static {
       for (ProblemIsSolvedEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<Integer, ProblemIsSolvedEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(Integer type) {
       if (Objects.isNull(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static ProblemIsSolvedEnum getEnumByType(Integer type) {
       if (Objects.isNull(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
