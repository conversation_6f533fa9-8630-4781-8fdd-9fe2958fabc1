package com.wanshifu.iop.im.domain.po.channel;

import lombok.ToString;
import lombok.Data;
import java.util.Date;
import javax.persistence.*;


/**
 * 系统自动回复消息明细配置表
 */
@Data
@ToString
@Table(name = "conversation_auto_reply_detail_config")
public class ConversationAutoReplyDetailConfig {

    /**
     * 系统自动回复明细配置ID
     */
    @Id
    @Column(name = "conversation_auto_reply_detail_config_id")
    private Long conversationAutoReplyDetailConfig;

    /**
     * 系统自动回复配置ID
     */
    @Column(name = "conversation_auto_reply_config_id")
    private Long conversationAutoReplyConfigId;

    /**
     * 提示类型：欢迎语 - welcome_auto_reply、排队提示语、坐席超时回复提醒语 seat_timeout_auto_reply、访客超时回复提醒语 visitor_timeout_auto_reply
     */
    @Column(name = "msg_type")
    private String msgType;

    /**
     * 提示子类型
     */
    @Column(name = "msg_sub_type")
    private String msgSubType;

    /**
     * 提示子类型展示序号
     */
    @Column(name = "sub_type_show_seq")
    private String subTypeShowSeq;
    /**
     * 系统自动消息接收对象：坐席-seat、访客-visitor
     */
    @Column(name = "autoReply_receive_object")
    private String autoReplyReceiveObject;

    /**
     * 自动回复内容（字符串模板）
     */
    @Column(name = "autoReply_content")
    private String autoReplyContent;

    /**
     * 提示语触发事件类型：系统主动 - system_active、 访客回复超时 - visitor_timeout、坐席回复超时 - agent_timeout、访客主动关闭 - visitor_close、客服主动关闭 - agent-close
     */
    @Column(name = "trigger_mode")
    private String triggerMode;

    /**
     * 回复超时时长（分钟）
     */
    @Column(name = "timeout_cost_second")
    private String timeoutCostSecond;

    /**
     * 是否插入占位符
     */
    @Column(name = "support_placeholder")
    private Integer supportPlaceholder;

    /**
     * 占位符键值对json 格式
     */
    @Column(name = "extra_json")
    private String extraJson;

    /**
     * 启用状态  1：禁用，0：启用
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 是否删除:1-是,0-否
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 数据创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}