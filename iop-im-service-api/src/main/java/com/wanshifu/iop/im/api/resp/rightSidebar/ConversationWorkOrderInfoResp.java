package com.wanshifu.iop.im.api.resp.rightSidebar;

import lombok.Data;

import java.util.List;

/**
 * 会话工单信息
 * <AUTHOR>
 * @date： 2025-07-14 20:37:38
 */
@Data
public class ConversationWorkOrderInfoResp {

    /**
     * 工单tab页信息
     */
    private List<WorkTabInfo> workTabList;

    /**
     * 工单id
     */
    private Long workId;

    /**
     * 工单状态
     */
    private String workStatusName;

    /**
     * 师傅违规信息
     */
    private String masterViolationShow;

    /**
     * 退费信息
     */
    private String refundShow;

    /**
     * 处理客服
     */
    private String chargeCustomerName;

    @Data
    public static class WorkTabInfo {
        /**
         * 工单tab页名称
         */
        private String workTabName;

        /**
         * 工单id
         */
        private Long workId;
    }
}
