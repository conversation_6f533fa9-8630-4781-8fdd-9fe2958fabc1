package com.wanshifu.iop.im.api.resp.leftSidebar;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 列表会话消息
 * <AUTHOR>
 * @date： 2025-07-14 20:37:38
 */
@Data
public class ListConversationMsgResp {

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     * 访客头像
     * */
    private String visitorFaceUrl;

    /**
     * 访客名称
     */
    private String visitorName;

    /**
     * 访客在线状态 online:在线，offline:离线
     */
    private String visitorOnlineState;

    /**
     * 访客标签
     */
    private List<CommonLabelValueResp> visitorLabelNameList;

    /**
     * 最新消息时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date latestMsgTime;

    /**、
     * 最新消息内容
     */
    private String latestMsgContent;

    /**
     * 最新消息发送人
     */
    private String latestMsgSender;
}
