package com.wanshifu.iop.im.service.api.inter;


import com.wanshifu.iop.im.api.req.inter.GetGroupListReq;
import com.wanshifu.iop.im.api.req.inter.GetSeatImInfoReq;
import com.wanshifu.iop.im.api.req.inter.GetVirtualUserInfoReq;
import com.wanshifu.iop.im.api.req.inter.RegisterUserReq;
import com.wanshifu.iop.im.api.req.inter.ToArtificialServiceReq;
import com.wanshifu.iop.im.api.resp.inter.GetGroupListResp;
import com.wanshifu.iop.im.api.resp.inter.GetVirtualUserInfoResp;
import com.wanshifu.iop.im.api.resp.inter.RegisterUserResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 工作台使用
 */
@FeignClient(
        value = "iop-im-service",
        path = "/inter/im", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-im-service.url}"
)
public interface ImInterServiceApi {

    /**
     * 用户注册
     * */
    @PostMapping("/registerUser")
    RegisterUserResp registerUser(@Valid @RequestBody RegisterUserReq req);

    /**
     * 作用：获取虚拟用户信息，绑定关系，系统自动发送欢迎语消息
     * */
    @PostMapping("/getVirtualInfo")
    GetVirtualUserInfoResp getVirtualInfo(@Valid @RequestBody GetVirtualUserInfoReq req);

    /**
     * 客服初始化 坐席信息
     * */
    @PostMapping("/seatImInfo")
    RegisterUserResp getSeatImInfo(@Valid @RequestBody GetSeatImInfoReq req);

    /**
     * 坐席端-获取群列表
     * */
    @PostMapping("/getGroupList")
    List<GetGroupListResp> getGroupList(@Valid @RequestBody GetGroupListReq req);

    /**
     * 转人工，提交表单数据
     * */
    @PostMapping("/toArtificial")
    Integer toArtificial(@Valid @RequestBody ToArtificialServiceReq req);
}
