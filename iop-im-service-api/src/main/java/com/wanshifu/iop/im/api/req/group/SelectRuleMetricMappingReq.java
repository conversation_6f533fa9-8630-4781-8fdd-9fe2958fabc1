package com.wanshifu.iop.im.api.req.group;

import com.wanshifu.iop.im.domain.enums.RuleMetricConfigEnum;
import com.wanshifu.iop.im.domain.po.GroupRule;
import com.wanshifu.iop.im.domain.po.RuleMetricConfig;
import com.wanshifu.iop.im.domain.po.SeatGroupDistributeRule;
import lombok.Data;

import java.util.List;

/**
 * 切换标签状态
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class SelectRuleMetricMappingReq {

    /**
     * 获取的规则
     */
    private RuleMetricConfigEnum ruleMetricConfigEnum;

    /**
     * 分组规则
     */
    private List<GroupRule> groupRulesList;

    /**
     * 坐席规则
     */
    private List<SeatGroupDistributeRule> seatGroupDistributeRulesList;

    /**
     * 规则指标配置
     */
    private List<RuleMetricConfig> ruleMetricConfigList;
}
