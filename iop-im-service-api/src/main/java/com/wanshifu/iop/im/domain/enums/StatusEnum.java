package com.wanshifu.iop.im.domain.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 启用禁用
 * <AUTHOR>
 * @date： 2025-05-29 15:49:55
 */
public enum StatusEnum {

    /**
     * 启用状态：1：禁用，0：启用
     */

    ENABLE(0, "启用", "enable"),

    DISABLE(1, "禁用", "disable"),

    ;

    public final Integer type;

    public final String name;

    public final String en;

    StatusEnum(Integer type, String name, String en) {
        this.type = type;
        this.name = name;
        this.en = en;
    }

    private static final Map<Integer, StatusEnum> mapping = new HashMap<>();

    static {
        for (StatusEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<Integer, StatusEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(Integer type) {
        if (Objects.isNull(type)) {
            return "";
        }
        StatusEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static StatusEnum getEnumByType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        if (!mapping.containsKey(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
