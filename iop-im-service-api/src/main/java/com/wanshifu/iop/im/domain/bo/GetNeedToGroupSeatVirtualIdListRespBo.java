package com.wanshifu.iop.im.domain.bo;

import com.wanshifu.iop.im.domain.po.SeatVirtualRelation;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 核心会话信息返回参数
 * <AUTHOR>
 * @date： 2025-07-21 20:02:14
 */
@Data
public class GetNeedToGroupSeatVirtualIdListRespBo {

    /**
     * 需要加入群组坐席虚拟id列表
     */
    private List<String> needSeatVirtualIdList = new ArrayList<>();

    /**
     * 坐席虚拟账号关联关系
     */
    private List<SeatVirtualRelation> seatVirtualRelations;

    /**
     * 是否存在机器人
     */
    private Boolean hasRobot = false;

    /**
     * 机器人虚拟id
     */
    private String robotVirtualId;
}
