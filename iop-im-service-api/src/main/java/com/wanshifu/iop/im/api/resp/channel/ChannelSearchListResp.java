package com.wanshifu.iop.im.api.resp.channel;

import com.wanshifu.iop.im.domain.bo.channel.ChannelBo;
import com.wanshifu.iop.im.domain.bo.channel.ClientCategoryBo;
import lombok.Data;

import java.util.List;

/**
 * 端侧+渠道接口
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Data
public class ChannelSearchListResp {

    /**
     * 端侧列表
     */
    private List<ClientCategoryBo> clientCategoryBoList;
    /**
     * 选中的端侧ID，初始化时为首个端侧ID
     */
    private Long clientCategoryId;
    /**
     * 选中端侧的渠道列表-初始化是首个端侧下的渠道列表
     */
    private List<ChannelBo> channelBoList;


}
