package com.wanshifu.iop.im.domain.po;

import lombok.ToString;
import javax.persistence.*;
import java.util.Date;
import lombok.Data;


/**
 * 业务表单提交关联表
 */
@Data
@ToString
@Table(name = "business_from_submit_relaiton")
public class BusinessFromSubmitRelaiton {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "business_from_submit_relaiton_id")
    private Long businessFromSubmitRelaitonId;

    /**
     * 表单信息提交记录id
     */
    @Column(name = "from_submit_record_id")
    private Long fromSubmitRecordId;

    /**
     * 会话id
     */
    @Column(name = "conversation_id")
    private Long conversationId;

    /**
     * 业务来源id customerOrder-售后工单id secondline-二线工单id
     */
    @Column(name = "business_from_id")
    private Long businessFromId;

    /**
     * 业务来源类型 customerOrder-售后工单 secondline-二线工单
     */
    @Column(name = "business_from_type")
    private String businessFromType;

    /**
     * 删除状态: 1-删除, 0-正常
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}