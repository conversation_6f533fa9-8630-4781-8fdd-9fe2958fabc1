package com.wanshifu.iop.im.api.req.channel;

import com.wanshifu.iop.im.api.req.BaseSwitchStatusReq;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 切换标签状态
 * <AUTHOR>
 * @date： 2025-05-29 15:34:18
 */
@Data
public class SwitchChannelStatusReq extends BaseSwitchStatusReq {

    /**
     * 端侧ID
     */
    @NotNull(message = "端侧ID不能为空")
    @Min(1L)
    private Long clientCategoryId;

    /**
     * 渠道id
     */
    @NotNull(message = "渠道id不能为空")
    @Min(1L)
    private Long channelId;


}
