package com.wanshifu.iop.im.domain.enums;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 坐席数据类型
 * <AUTHOR>
 * @date： 2025-05-29 15:49:55
 */
public enum SeatTypeEnum {

    /**
     * 坐席类型 0-普通 1-组长 2-管理员
     */

    NORMAL(0, "普通坐席", null),

    GROUP_LEADER(1, "组长坐席", "坐席类型控制数据权限，普通坐席仅能查看本人的会话数据、满意度数据，组长坐席可查看本会话组内的所有数据，管理员坐席可查看本主账号内的所有数据；"),

    ADMIN(2, "管理员坐席", "坐席类型控制数据权限，普通坐席仅能查看本人的会话数据、满意度数据，组长坐席可查看本会话组内的所有数据，管理员坐席可查看本主账号内的所有数据；");

    ;

    public final Integer type;

    public final String name;

    public final String desc;

    SeatTypeEnum(Integer type, String name, String desc) {
        this.type = type;
        this.name = name;
        this.desc = desc;
    }

    private static final Map<Integer, SeatTypeEnum> mapping = new HashMap<>();

    static {
        for (SeatTypeEnum value : values()) {
            mapping.put(value.type, value);
        }
    }

    public static Map<Integer, SeatTypeEnum> getMapping() {
        return mapping;
    }

    public static String getNameByType(Integer type) {
        if (Objects.isNull(type)) {
            return "";
        }
        SeatTypeEnum imTypeEnum = mapping.get(type);
        if (imTypeEnum == null) {
            return "";
        }

        return imTypeEnum.name;
    }

    public static SeatTypeEnum getEnumByType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        return mapping.get(type);
    }

}
