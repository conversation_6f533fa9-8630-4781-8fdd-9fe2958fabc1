package com.wanshifu.iop.im.api.resp.leftSidebar;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 待处理列表
 * <AUTHOR>
 * @date： 2025-07-14 20:37:38
 */
@Data
public class GetWaitProcessConversationListResp extends ListConversationMsgResp {

    /**
     * im群组id
     */
    private String imOuterGroupId;

    /**
     * 消息未读数
     */
    private Long unreadCount = 0L;

    /**
     * 是否有未读消息
     */
    private Integer hasUnread = 0;

    /**
     * 会话超时状态 not：未超时 approach：临近超时 already：超时
     */
    private String timeoutStatus;
}
