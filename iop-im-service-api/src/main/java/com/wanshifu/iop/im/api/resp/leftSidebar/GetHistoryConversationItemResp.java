package com.wanshifu.iop.im.api.resp.leftSidebar;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.iop.im.domain.bo.MsgBodyItemBo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 历史消息item
 * <AUTHOR>
 * @date： 2025-07-14 20:37:38
 */
@Data
public class GetHistoryConversationItemResp {

    /**
     * 消息发送方 访客-visitor 坐席-seat
     */
    private String sendObjectFrom;

    /**
     * 消息发送方
     */
    private String sendOutUserId;

    /**
     * 消息发送方类型
     */
    private String sendOutUserType;

    /**
     * 消息发送方名称
     */
    private String sendOutUserName;

    /**
     * 消息发送方头像
     */
    private String sendOutUserFaceUrl;

    /**
     * 消息发送时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 消息发送方内容
     */
    private List<MsgBodyItemBo> MsgBody;
}
