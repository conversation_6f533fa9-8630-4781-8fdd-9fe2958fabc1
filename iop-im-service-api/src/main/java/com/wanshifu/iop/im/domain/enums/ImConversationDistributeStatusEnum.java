package com.wanshifu.iop.im.domain.enums;


import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 会话分配状态
 * <AUTHOR>
 * @date： 2025-07-03 15:40:54
 */

public enum ImConversationDistributeStatusEnum {

    /**
     * 分配状态 engroup-进组 completed-分配完成 cancel-取消分配
     */
    ENGROUP("engroup", "进组"),
    COMPLETED("completed", "分配完成"),
    CANCEL("cancel", "取消分配"),

   ;

   public final String type;

   public final String name;

   ImConversationDistributeStatusEnum(String type, String name) {
       this.type = type;
       this.name = name;
   }

   private static final Map<String, ImConversationDistributeStatusEnum> mapping = new HashMap<>();

   static {
       for (ImConversationDistributeStatusEnum value : values()) {
           mapping.put(value.type, value);
       }
   }

   public static Map<String, ImConversationDistributeStatusEnum> getMapping() {
       return mapping;
   }

   public static String getNameByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return "";
       }
       return mapping.get(type).name;
   }

   public static ImConversationDistributeStatusEnum getEnumByType(String type) {
       if (StringUtils.isEmpty(type)) {
           return null;
       }
       return mapping.get(type);
   }
}
