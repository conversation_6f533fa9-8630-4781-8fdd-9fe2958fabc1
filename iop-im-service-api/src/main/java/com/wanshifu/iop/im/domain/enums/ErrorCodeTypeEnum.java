package com.wanshifu.iop.im.domain.enums;

import com.wanshifu.framework.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 错误信息类型枚举
 * <AUTHOR>
 */
public enum ErrorCodeTypeEnum {
    SUCCESS_ERROR(1, "success", "成功"),
    BIZ_ERROR(2, "biz", "业务逻辑错误"),
    SYSTEM_ERROR(3, "system", "系统错误"),
    OTHER_ERROR(4, "other", "其他错误");

    public final Integer code;
    public final String type;
    public final String desc;

    private static final Map<Integer, ErrorCodeTypeEnum> mapping = new HashMap<>();

    static {
        for (ErrorCodeTypeEnum value : values()) {
            mapping.put(value.code, value);
        }
    }

    ErrorCodeTypeEnum(Integer code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    public static ErrorCodeTypeEnum fromType(String type) {
        return mapping.get(type);
    }

    public static ErrorCodeTypeEnum fromTypeByCode(Integer code) {
        return mapping.get(code);
    }

    /**
     * 判断值是否在枚举内
     */
    public static Boolean include(String type) {
        if (StringUtils.isNotEmpty(type)) {
            return mapping.containsKey(type);
        }
        return false;
    }

    /**
     * 获取枚举内的值
     */
    public static String getUserTypeTxt(String type) {
        if (StringUtils.isNotEmpty(type)) {
            return mapping.get(type).type;
        }
        return null;
    }
}
