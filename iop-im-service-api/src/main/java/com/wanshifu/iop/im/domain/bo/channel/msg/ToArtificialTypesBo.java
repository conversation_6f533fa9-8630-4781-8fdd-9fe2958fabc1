package com.wanshifu.iop.im.domain.bo.channel.msg;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/7/22 13:57
 * @description TODO
 */
@Data
public class ToArtificialTypesBo {

    /**
     * 会话id
     * */
    private Long imConversationId;

    /**
     * 触发类型
     * */
    @NotNull
    private String triggerMode;

    /**
     * 坐席外部用户id
     * triggerMode = SEAT_ENTER 才有
     * */
    private String seatOuterUserId;

    /**
     * triggerMode = SEAT_ENTER 才有
     * 上一个接待类型
     * robot seat
     * */
    private String preToOuterUserType;

}
