package com.wanshifu.iop.im.domain.bo;

import lombok.Data;

@Data
public class RegisterUserReqBo {

    /**
     * 租户id，必填
     * */
    private Long tenantId;

    /**
     * im场景类型，必填
     * {@link com.wanshifu.iop.im.domain.enums.ImTypeEnum}
     * */
    private String imType;


    /**
     * 如果传outerUserId，标识修改
     * */
    private String outerUserId;

    /**
     * 用户昵称
     * */
    private String Nick;

    /**
     * 用户头像url
     * */
    private String FaceUrl;



}
