# IOP IM Service

## 项目概述

IOP IM Service 是万师傅智能运营平台（Intelligent Operation Platform）的即时通讯服务模块，提供完整的客服系统解决方案。该服务支持用户注册、消息收发、坐席管理、智能分配、AI机器人集成等核心功能，为企业提供高效的客户服务体验。

## 技术栈

- **Java**: 1.8
- **Spring Boot**: 基于万师傅微服务框架
- **Spring Cloud**: 微服务架构，支持Feign客户端
- **MyBatis**: 数据持久化框架
- **Maven**: 项目构建工具
- **Apollo**: 配置中心
- **RocketMQ**: 消息队列
- **Tencent IM**: 腾讯即时通讯SDK
- **FastJSON**: JSON处理
- **Lombok**: 代码简化工具

## 项目结构

```
iop-im-service/
├── iop-im-service-api/          # API接口模块
│   ├── src/main/java/
│   │   └── com/wanshifu/iop/im/
│   │       ├── api/             # API请求响应对象
│   │       ├── domain/          # 领域对象
│   │       └── service/         # Feign客户端接口
│   └── pom.xml
├── iop-im-service-web/          # Web服务模块
│   ├── src/main/java/
│   │   └── com/wanshifu/
│   │       ├── controller/      # 控制器层
│   │       ├── service/         # 业务服务层
│   │       ├── manager/         # 管理器层
│   │       ├── repository/      # 数据访问层
│   │       ├── mapper/          # MyBatis映射器
│   │       ├── mq/             # 消息队列
│   │       ├── config/         # 配置类
│   │       ├── utils/          # 工具类
│   │       └── sdk/            # 第三方SDK
│   ├── src/main/resources/
│   │   ├── config/             # 配置文件
│   │   └── mapper/             # MyBatis映射文件
│   └── pom.xml
└── pom.xml                      # 父级POM文件
```

## 主要功能

### 核心服务功能

1. **用户管理**
   - 用户注册与认证
   - 虚拟用户管理
   - 用户设备关系管理

2. **即时通讯**
   - 单聊消息处理
   - 群聊消息处理
   - 消息历史导入
   - 实时消息推送

3. **客服系统**
   - 坐席管理与状态切换
   - 智能分配策略
   - 排队机制
   - 转人工服务

4. **AI集成**
   - AI机器人对话
   - 智能工作流执行
   - 自动回复配置

5. **业务流程**
   - 询前表单处理
   - 满意度评价
   - 工单集成
   - 访客信息管理

### API接口

#### 内部接口 (ImInterServiceApi)
- `POST /registerUser` - 用户注册
- `POST /getVirtualInfo` - 获取虚拟用户信息
- `POST /seatImInfo` - 坐席信息初始化
- `POST /getGroupList` - 获取群列表
- `POST /toArtificial` - 转人工服务

#### 开放接口
- 支持第三方系统集成
- 提供标准化API接口
- 支持多渠道接入

## 环境要求

- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 5.7+
- **Redis**: 3.0+
- **RocketMQ**: 4.0+
- **Apollo配置中心**

## 快速开始

### 1. 克隆项目

```bash
git clone http://git.wanshifu.com/intelligeint-operation-platform/iop-im-service.git
cd iop-im-service
```

### 2. 配置环境

#### 数据库配置
在Apollo配置中心配置数据库连接信息：
```properties
spring.datasource.url=*************************************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

#### Redis配置
```properties
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=your_password
```

#### RocketMQ配置
```properties
wanshifu.rocketMQ.iop.im.service.general.topic=iop_im_general
wanshifu.rocketMQ.iop.im.service.delay.topic=iop_im_delay
```

### 3. 构建项目

```bash
# 编译整个项目
mvn clean compile

# 打包项目
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests
```

### 4. 运行服务

```bash
# 运行Web服务
cd iop-im-service-web
mvn spring-boot:run
```

或者运行打包后的JAR文件：
```bash
java -jar iop-im-service-web/target/iop-im-service-web-1.0.1-SNAPSHOT.jar
```

### 5. 验证服务

服务启动后，可以通过以下方式验证：
- 健康检查：`http://localhost:8080/actuator/health`
- API文档：根据实际配置访问Swagger文档

## 配置说明

### 应用配置
主要配置文件位于 `iop-im-service-web/src/main/resources/config/application.properties`

```properties
# 应用名称
spring.application.name=iop-im-service

# 环境配置
spring.profiles.active=@deploy.env@

# 日志配置
logging.config=classpath:logback-spring.xml
logging.path=/data/servicesLog/tomcat-iop-im-service/logs

# 监控配置
management.security.enabled=false
```

### 环境配置
支持多环境配置：
- `dev` - 开发环境
- `test` - 测试环境  
- `prod` - 生产环境

## 部署指南

### Maven发布

项目支持自动化发布到Nexus仓库：

```bash
# 发布SNAPSHOT版本
mvn clean deploy

# 发布Release版本
mvn release:prepare
mvn release:perform
```

### Docker部署

```dockerfile
FROM openjdk:8-jre-alpine
COPY iop-im-service-web/target/iop-im-service-web-1.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理
- 规范日志输出

### 数据库规范
- 使用MyBatis进行数据访问
- Repository层封装数据操作
- 支持分页查询
- 事务管理

### 消息队列使用
- 使用RocketMQ进行异步处理
- 支持延时消息
- 消息重试机制
- 死信队列处理

## 监控与运维

- 集成Actuator健康检查
- 支持JavaMelody性能监控
- 日志集中管理
- 链路追踪支持

## 版本历史

- **1.0.1-SNAPSHOT** - 当前开发版本
- 基于万师傅微服务框架2.18.Jacoco版本

## 联系方式

- 项目仓库：http://git.wanshifu.com/intelligeint-operation-platform/iop-im-service
- 技术支持：万师傅技术团队

## 许可证

本项目为万师傅内部项目，版权所有。
