package com.wanshifu.utils;

import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadLocalRandom;

@Component
public class MathUtil {

    /**
     * 线程安全
     * 生成指定长度的字符串
     * */
    public static Long generateCustomerLength(int length){
        if (length <= 0 || length > 32) {
            throw new IllegalArgumentException("入参错误，最大32位，最小1");
        }

        long max = (1L << length) - 1;
        return ThreadLocalRandom.current().nextLong(max + 1);
    }

}
