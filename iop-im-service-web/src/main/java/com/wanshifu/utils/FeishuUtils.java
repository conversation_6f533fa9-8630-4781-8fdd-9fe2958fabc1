package com.wanshifu.utils;


import com.wanshifu.iop.im.domain.bo.ErrorInfoBo;
import com.wanshifu.sdk.SendErrorMessageApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * @author: k<PERSON><PERSON><PERSON>
 * @time:2022/9/21 0021 10:46
 * @desc 飞书提醒
 */
@Component
public class FeishuUtils {

    @Resource
    private SendErrorMessageApi sendErrorMessageApi;

    public void sendErrorInfoFeishu(String errorInfo) {
        ErrorInfoBo errorInfoBo = new ErrorInfoBo();
        errorInfoBo.setMsg_type("text");
        ErrorInfoBo.Item item = new ErrorInfoBo.Item();
        item.setText(errorInfo);
        errorInfoBo.setContent(item);
        //异步发送消息
        CompletableFuture.supplyAsync(() -> {
            sendErrorMessageApi.sendError(errorInfoBo);
            return null;
        });
    }
}
