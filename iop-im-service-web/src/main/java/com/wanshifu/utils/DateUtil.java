package com.wanshifu.utils;

import com.wanshifu.framework.core.BusException;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/26 16:09
 */

public class DateUtil {

    public static String dateToString(Date dateStr){
        if(dateStr==null){
            return "";
        }
        // 创建一个SimpleDateFormat对象并设置日期时间格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 使用SimpleDateFormat对象将Date对象转换为字符串
        return sdf.format(dateStr);
    }

    public static String dateToStr(Date dateStr){
        if(dateStr==null){
            return "";
        }
        // 创建一个SimpleDateFormat对象并设置日期时间格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm");

        // 使用SimpleDateFormat对象将Date对象转换为字符串
        return sdf.format(dateStr);
    }

    public static Date strToDate(String strDate){
        // 定义格式化模式
        SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try {
            // 解析字符串为 Date
            return sdfYMD.parse(strDate);
        } catch (ParseException e) {
            throw new BusException("解析日期失败: " + e.getMessage());
        }
    }
}
