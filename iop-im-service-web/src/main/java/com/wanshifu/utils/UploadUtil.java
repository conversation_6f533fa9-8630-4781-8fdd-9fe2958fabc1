package com.wanshifu.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.core.BusException;

import com.wanshifu.iop.im.api.resp.ImgUploadResp;
import io.itit.itf.okhttp.FastHttpClient;
import io.itit.itf.okhttp.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 上传工具类
 * <AUTHOR>
 * @date 2024/8/28
 */

@Slf4j
@Component
public class UploadUtil {

    @Value("${wanshifu.image-service.url:http://image-service.wanshifu.com?action=getUrls}")
    private String imageServiceUrl;

    public ImgUploadResp uploadFile(MultipartFile file){
        // 后缀名
        String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);

        ImgUploadResp uploadFileResult  = new ImgUploadResp();
        try{
            uploadFileResult  = uploadFileToCdn(file, file.getInputStream());
            uploadFileResult.setType(suffix);
            uploadFileResult.setName(file.getOriginalFilename());
        }catch (Exception e) {
            throw new BusException(e.getMessage());
        }

        return uploadFileResult;
    }

    /**
     * 上传文件
     //     */
    public ImgUploadResp uploadFileToCdn(MultipartFile file, InputStream inputStream) {
        System.out.println(imageServiceUrl);
        try {
            // 校验上传文件
            String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
            if (!Arrays.asList("jpg","gif","png","bmp","jpeg","mp4", "mov", "avi", "mp3", "m4a", "amr").contains(suffix.toLowerCase())) {
                log.error("不支持该类型！目前只支持的类型有：jpg、gif、png、bmp、jpeg、mp4、mov、avi、mp3、m4a、amr suffix:{}", file.getName());
                log.error("不支持该类型！目前只支持的类型有：jpg、gif、png、bmp、jpeg、mp4、mov、avi、mp3、m4a、amr suffix:{}", file.getContentType());
                log.error("不支持该类型！目前只支持的类型有：jpg、gif、png、bmp、jpeg、mp4、mov、avi、mp3、m4a、amr suffix:{}", file.getOriginalFilename());
                throw new BusException("不支持该类型！目前只支持的类型有：jpg、gif、png、bmp、jpeg、mp4、mov、avi、mp3、m4a、amr");
            }

            ImgUploadResp imgUploadResp = new ImgUploadResp();
            Response response = FastHttpClient.newBuilder()
                    .connectTimeout(300, TimeUnit.SECONDS)
                    .writeTimeout(300, TimeUnit.SECONDS)
                    .readTimeout(300, TimeUnit.SECONDS)
                    .build().post()
                    .url(getImageUploadFileUrl(suffix))
                    .addFile("file", file.getOriginalFilename(), inputStream)
                    .build().execute();
            String string = response.body().string();
            Map<String, Object> resultMap = JSON.parseObject(string, Map.class);
            if ("S".equals(String.valueOf(resultMap.get("status")))) {
                JSONObject data = (JSONObject) resultMap.get("data");
                if (Objects.nonNull(data)) {
                    imgUploadResp.setPath(String.valueOf(data.get("img_url")));
                    imgUploadResp.setIid(String.valueOf(data.get("iid")));
                }
            }
            if (Objects.nonNull(response.body())) {
                response.body().close();
            }
            return imgUploadResp;
        } catch (Exception e) {
            log.error("upload file error!  e:{}", e);
            throw new BusException("上传失败uploadFileToCdn:" + e.getMessage());
        }
    }

    private String getImageUploadFileUrl(String suffix){
        if (Arrays.asList("jpg","gif","png","bmp","jpeg").contains(suffix.toLowerCase())){
            // 上传图片
            return imageServiceUrl.replace("getUrls", "upload");
        }else{
            // 上传文件
            return imageServiceUrl.replace("getUrls", "uploadFile");
        }
    }

}
