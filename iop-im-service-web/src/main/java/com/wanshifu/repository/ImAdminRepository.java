package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.iop.im.domain.po.ImAdmin;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class ImAdminRepository extends BaseRepository<ImAdmin> {

    /**
     * 根据imId 查询im管理员信息
     * */
    public List<ImAdmin> getImAdminByImId(Long imId) {
        Condition condition = new Condition(ImAdmin.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("imId", imId);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

}