package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.GroupQueue;
import com.wanshifu.mapper.GroupQueueMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

@Repository
public class GroupQueueRepository extends BaseRepository<GroupQueue> {

    @Resource
    private GroupQueueMapper groupQueueMapper;

    /**
     * 查询队列信息
     * @param conversationDistributeId
     * @return
     */
    public GroupQueue selectInfoByConversationDistributeId(Long conversationDistributeId) {
        Condition condition = new Condition(GroupQueue.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationDistributeId", conversationDistributeId);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}