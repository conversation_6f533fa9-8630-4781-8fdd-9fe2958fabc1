package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.ImConfig;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Repository
public class ImConfigRepository extends BaseRepository<ImConfig> {

    /**
     * 根据租户信息+im类型查询im信息
     * */
    public ImConfig getImConfigInfo(Long tenantId, String imType){
        Condition condition = new Condition(ImConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andEqualTo("imType", imType);
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

}