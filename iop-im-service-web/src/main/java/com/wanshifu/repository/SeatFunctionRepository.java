package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.SeatFunction;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Repository
public class SeatFunctionRepository extends BaseRepository<SeatFunction> {

    /**
     * 批量查询座席功能信息
     * @param idList
     * @return
     */
    public List<SeatFunction> batchGetInfoByseatFunctionIdList(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return new ArrayList<>();
        }

        Condition condition = new Condition(SeatFunction.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("seatFunctionId", idList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 获取所有座席功能信息
     * @return
     */
    public List<SeatFunction> getAll() {
        Condition condition = new Condition(SeatFunction.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }
}