package com.wanshifu.repository.channel;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.po.channel.TemplateInitJson;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class TemplateInitJsonRepository extends BaseRepository<TemplateInitJson> {
    public TemplateInitJson selectTemplateInitJsonByIndex(String tableTypeName, String columnTypeName, String initJsonType) {
        Condition condition = new Condition(TemplateInitJson.class);
        Example.Criteria criteria = condition.createCriteria();
        if(StringUtils.isNotEmpty(tableTypeName)) {
            criteria.andEqualTo("tableTypeName", tableTypeName);
        }
        if(StringUtils.isNotEmpty(columnTypeName)) {
            criteria.andEqualTo("columnTypeName", columnTypeName);
        }
        if(StringUtils.isNotEmpty(initJsonType)) {
            criteria.andEqualTo("initJsonType", initJsonType);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    public List<TemplateInitJson> selectAllTemplateInitJson() {
        Condition condition = new Condition(TemplateInitJson.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return this.selectByCondition(condition);
    }

}