package com.wanshifu.repository.channel;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.channel.ClientCategoryCreateReq;
import com.wanshifu.iop.im.domain.po.channel.ClientChannelConfigDraft;
import com.wanshifu.mapper.channel.ClientChannelConfigDraftMapper;
import com.wanshifu.utils.BaseUtil;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class ClientChannelConfigDraftRepository extends BaseRepository<ClientChannelConfigDraft> {

    @Resource
    private ClientChannelConfigDraftMapper clientChannelConfigDraftMapper;

    /**
     * 校验端侧名
     */
    public ClientChannelConfigDraft checkClientChannelConfigName(Long clientCategoryConfigId, String clientCategoryName) {
        Condition condition = new Condition(ClientChannelConfigDraft.class);
        Example.Criteria criteria = condition.createCriteria();
        if(BaseUtil.hasLongValue(clientCategoryConfigId)) {
            criteria.andEqualTo("clientChannelConfigDraftId", clientCategoryConfigId);
        }
        criteria.andEqualTo("clientCategoryName", clientCategoryName);

        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 校验端侧标识
     */
    public ClientChannelConfigDraft checkClientChannelConfigEn(Long clientCategoryConfigId, String clientCategoryEn) {
        Condition condition = new Condition(ClientChannelConfigDraft.class);
        Example.Criteria criteria = condition.createCriteria();
        if(BaseUtil.hasLongValue(clientCategoryConfigId)) {
            criteria.andEqualTo("clientChannelConfigDraftId", clientCategoryConfigId);
        }
        criteria.andEqualTo("clientCategoryEn", clientCategoryEn);

        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    public ClientChannelConfigDraft selectClientChannelConfigDraft(Long clientCategoryId) {
        Condition condition = new Condition(ClientChannelConfigDraft.class);
        Example.Criteria criteria = condition.createCriteria();
        if(BaseUtil.hasLongValue(clientCategoryId)) {
            criteria.andEqualTo("clientChannelConfigDraftId", clientCategoryId);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }


    /**
     * 查询默认的端侧列表
     */
    public List<ClientChannelConfigDraft> selectClientChannelConfigDraftList() {
        Condition condition = new Condition(ClientChannelConfigDraft.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        //condition.setOrderByClause("create_time desc");
        return this.selectByCondition(condition);
    }

    /**
     * 查询端侧列表
     */
    public List<ClientChannelConfigDraft> selectClientChannelConfigDraftList(Long clientCategoryId) {
        Condition condition = new Condition(ClientChannelConfigDraft.class);
        Example.Criteria criteria = condition.createCriteria();
        if(BaseUtil.hasLongValue(clientCategoryId)) {
            criteria.andEqualTo("clientChannelConfigDraftId", clientCategoryId);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        //condition.setOrderByClause("create_time desc");
        return this.selectByCondition(condition);
    }

    public Long insertOrUpdateClientCategoryDraft(ClientChannelConfigDraft clientChannelConfigDraft) {
        clientChannelConfigDraft.setIsDelete(CommonConstant.ZERO);
        clientChannelConfigDraftMapper.insertOrUpdateClientCategoryDraft(clientChannelConfigDraft);
        return clientChannelConfigDraft.getClientChannelConfigDraftId();
    }

    public Integer batchUpdateClientChannelConfigDraft(List<ClientChannelConfigDraft> clientChannelConfigDraftList) {
        if(CollectionUtils.isEmpty(clientChannelConfigDraftList)) {
            return CommonConstant.ZERO;
        }
        return clientChannelConfigDraftMapper.batchUpdateClientChannelConfigDraft(clientChannelConfigDraftList);
    }
}