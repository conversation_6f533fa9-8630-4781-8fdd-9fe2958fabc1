package com.wanshifu.repository.channel;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.iop.im.api.req.ConversationThemeConfigRqt;
import com.wanshifu.iop.im.domain.po.channel.ConversationThemeConfig;
import com.wanshifu.mapper.channel.ConversationThemeConfigMapper;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Repository
public class ConversationThemeConfigRepository extends BaseRepository<ConversationThemeConfig> {
    @Resource
    private ConversationThemeConfigMapper conversationThemeConfigMapper;

    public List<ConversationThemeConfig> selectListByTenantIdAndClientUserTypeAndClientPortType(ConversationThemeConfigRqt req) {
        Condition condition = new Condition(ConversationThemeConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        if(StringUtils.isNotEmpty(req.getClientPortType())){
            criteria.andEqualTo("clientPortType", req.getClientPortType());
        }
        criteria.andEqualTo("isDelete", 0);

        return this.selectByCondition(condition);
    }
}