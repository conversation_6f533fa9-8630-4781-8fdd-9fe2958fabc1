package com.wanshifu.repository.channel;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.iop.im.domain.bo.channel.SatisfactionLevelConfigBo;
import com.wanshifu.iop.im.domain.po.channel.SatisfactionLevelConfig;
import com.wanshifu.mapper.channel.SatisfactionLevelConfigMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class SatisfactionLevelConfigRepository extends BaseRepository<SatisfactionLevelConfig> {

    @Resource
    private SatisfactionLevelConfigMapper satisfactionLevelConfigMapper;
    public Integer insertOrUpdateSatisfactionLevelConfigBo(SatisfactionLevelConfigBo satisfactionLevelConfigBo) {
        return satisfactionLevelConfigMapper.insertOrUpdateSatisfactionLevelConfigBo(satisfactionLevelConfigBo);
    }
}