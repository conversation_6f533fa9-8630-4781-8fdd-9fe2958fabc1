package com.wanshifu.repository.channel;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.resp.channel.GetChannelConfigDraftListResp;
import com.wanshifu.iop.im.domain.bo.channel.ChannelBo;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.mapper.channel.ChannelConfigMapper;
import com.wanshifu.utils.BaseUtil;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Repository
public class ChannelConfigRepository extends BaseRepository<ChannelConfig> {

    @Resource
    private ChannelConfigMapper channelConfigMapper;

    /**
     * 查询端侧列表
     */
    public List<ChannelConfig> selectChannelConfigList(Long clientCategoryId) {
        Condition condition = new Condition(ChannelConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        if(BaseUtil.hasLongValue(clientCategoryId)) {
            criteria.andEqualTo("clientCategoryId", clientCategoryId);
        }
        criteria.andEqualTo("status", CommonConstant.ZERO);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time");
        return this.selectByCondition(condition);
    }

    public Integer batchUpdateChannelConfig(List<ChannelConfig> channelConfigList) {
        AtomicInteger count = new AtomicInteger();
        if(CollectionUtils.isNotEmpty(channelConfigList)) {
            channelConfigList.forEach(f -> {
                count.addAndGet(this.updateByPrimaryKeySelective(f));
            });
        }
        return count.get();
    }

    public Integer insertOrUpdateChannelConfig(ChannelConfig channelConfig) {
        return channelConfigMapper.insertOrUpdateChannelConfig(channelConfig);
    }

    public Integer batchInsertOrUpdateChannelConfig(List<ChannelConfig> channelConfigList) {
        return channelConfigMapper.batchInsertOrUpdateChannelConfig(channelConfigList);
    }

    public Integer batchInsertOrUpdateChannelBo(List<ChannelBo> channelBoList) {
        return channelConfigMapper.batchInsertOrUpdateChannelBo(channelBoList);
    }

    /**
     * 根据id列表查询
     * @param channelIdList
     * @return
     */
    public List<ChannelConfig> selectByIdList(List<Long> channelIdList) {
        if ( CollectionUtils.isEmpty(channelIdList) ){
            return new ArrayList<>();
        }
        Condition condition = new Condition(ChannelConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("channelId", channelIdList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 通过会话渠道获取入口信息
     * @param req
     * @return
     */
    public List<ChannelConfig> getEnums(GetEnumsReq req) {
        return channelConfigMapper.getEnums(req);
    }


    /**
     * 根据路由导航ids查询渠道配置
     * */
    public List<ChannelConfig> getChannelConfigByFlowIds(List<Long> flowIds) {
        if(CollectionUtils.isEmpty(flowIds)) {
            return new ArrayList<>();
        }
        Condition condition = new Condition(ChannelConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("flowDefineId", flowIds);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 检查是否存在
     * @param req
     * @return
     */
    public List<Long> checkNonexistent(CheckNonexistentReq req) {
        return channelConfigMapper.checkNonexistent(req);
    }


    /**
     * 通过channel_en查询渠道信息
     * */
    public ChannelConfig getChannelConfigByChannelEn(String channelEn) {
        Condition condition = new Condition(ChannelConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("channelEn", channelEn);
        criteria.andEqualTo("status", CommonConstant.ZERO);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
    /**
     * 获取所有的渠道信息列表数据
     * @param
     * @return
     */
    public List<GetChannelConfigDraftListResp> getChannelConfigDraftList() {
        return channelConfigMapper.getChannelConfigDraftList();
    }


}