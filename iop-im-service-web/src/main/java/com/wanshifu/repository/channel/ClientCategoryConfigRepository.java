package com.wanshifu.repository.channel;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.domain.po.channel.ClientCategoryConfig;
import com.wanshifu.mapper.channel.ClientCategoryConfigMapper;
import com.wanshifu.utils.BaseUtil;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class ClientCategoryConfigRepository extends BaseRepository<ClientCategoryConfig> {

    @Resource
    private ClientCategoryConfigMapper clientCategoryConfigMapper;
    /**
     * 查询端侧列表
     */
    public List<ClientCategoryConfig> selectCategoryConfigList(Long clientCategoryId) {
        Condition condition = new Condition(ClientCategoryConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        if(BaseUtil.hasLongValue(clientCategoryId)) {
            criteria.andEqualTo("clientCategoryId", clientCategoryId);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return this.selectByCondition(condition);
    }

    /**
     * 查询端侧名和标识
     */
    public ClientCategoryConfig checkCategoryConfig(ClientCategoryConfig clientCategoryConfig) {
        Condition condition = new Condition(ClientCategoryConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        if(BaseUtil.hasLongValue(clientCategoryConfig.getClientCategoryConfigId())) {
            criteria.andEqualTo("clientCategoryId", clientCategoryConfig.getClientCategoryConfigId());
        }
        if(StringUtils.isNotEmpty(clientCategoryConfig.getClientCategoryEn())) {
            criteria.andEqualTo("clientCategoryEn", clientCategoryConfig.getClientCategoryEn());
        }
        if(StringUtils.isNotEmpty(clientCategoryConfig.getClientCategoryName())) {
            criteria.andEqualTo("clientCategoryName", clientCategoryConfig.getClientCategoryName());
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 新增或更新端侧信息
     * @param clientCategoryConfig 数据表对象实体
     */
    public Integer insertOrUpdateClientCategoryConfig(ClientCategoryConfig  clientCategoryConfig) {
        return clientCategoryConfigMapper.insertOrUpdateClientCategoryConfig(clientCategoryConfig);
    }

    /**
     * 查询端侧信息
     * @param clientCategoryConfigIdList
     * @return
     */
    public List<ClientCategoryConfig> selectByIdList(List<Long> clientCategoryConfigIdList) {
        if ( CollectionUtils.isEmpty(clientCategoryConfigIdList) ){
            return new ArrayList<>();
        }

        Condition condition = new Condition(ClientCategoryConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("clientCategoryConfigId", clientCategoryConfigIdList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 获取枚举
     * @return
     */
    public List<ClientCategoryConfig> getEnums(GetEnumsReq req) {
        return clientCategoryConfigMapper.getEnums(req);
    }

    /**
     * 检查端侧是否存在
     * @param req
     * @return
     */
    public List<Long> checkNonexistent(CheckNonexistentReq req) {
        return clientCategoryConfigMapper.checkNonexistent(req);
    }
}