package com.wanshifu.repository.channel;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.bo.ConversationStyleConfigV2Bo;
import com.wanshifu.iop.im.domain.bo.channel.ConversationStyleConfigBo;
import com.wanshifu.iop.im.domain.po.channel.ConversationStyleConfig;
import com.wanshifu.iop.im.domain.po.channel.TemplateInitJson;
import com.wanshifu.mapper.channel.ConversationStyleConfigMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Repository
public class ConversationStyleConfigRepository extends BaseRepository<ConversationStyleConfig> {

    @Resource
    private ConversationStyleConfigMapper conversationStyleConfigMapper;
    public Integer batchInsertOrUpdateConversationStyleConfig(List<ConversationStyleConfigBo> conversationStyleConfigBoList) {
        return conversationStyleConfigMapper.batchInsertOrUpdateConversationStyleConfig(conversationStyleConfigBoList);
    }
    public Integer batchInsertOrUpdateConversationStyleConfigV2(List<ConversationStyleConfigV2Bo> conversationStyleConfigBoList) {
        return conversationStyleConfigMapper.batchInsertOrUpdateConversationStyleConfigV2(conversationStyleConfigBoList);
    }
    public Integer insertOrUpdateConversationStyleConfig(ConversationStyleConfigV2Bo conversationStyleConfigBo) {
        return conversationStyleConfigMapper.insertOrUpdateConversationStyleConfig(conversationStyleConfigBo);
    }

    public ConversationStyleConfig selectByUniqueCondition(Long channelId, String clientPortType) {
        Condition condition = new Condition(ConversationStyleConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("channelId", channelId);
        criteria.andEqualTo("clientPortType", clientPortType);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        criteria.andEqualTo("status", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}