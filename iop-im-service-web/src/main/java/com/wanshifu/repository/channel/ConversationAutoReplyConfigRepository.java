package com.wanshifu.repository.channel;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyConfig;
import com.wanshifu.mapper.channel.ConversationAutoReplyConfigMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Repository
public class ConversationAutoReplyConfigRepository extends BaseRepository<ConversationAutoReplyConfig> {

    @Resource
    private ConversationAutoReplyConfigMapper conversationAutoReplyConfigMapper;

    public Integer insertOrUpdateConversationAutoReplyConfig(ConversationAutoReplyConfig conversationAutoReplyConfig) {
        return conversationAutoReplyConfigMapper.insertOrUpdateConversationAutoReplyConfig(conversationAutoReplyConfig);
    }

    /**
     * 根据渠道id查询自动回复配置信息
     * */
    public List<ConversationAutoReplyConfig> getConfigListByChannelIds(List<Long> channelIds) {
        Condition condition = new Condition(ConversationAutoReplyConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("channelId", channelIds);
        criteria.andEqualTo("status", CommonConstant.ZERO);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 根据渠道id查询自动回复配置信息
     * */
    public ConversationAutoReplyConfig selectByChannelId(Long channelId) {
        Condition condition = new Condition(ConversationAutoReplyConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("channelId", channelId);
        criteria.andEqualTo("status", CommonConstant.ZERO);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}