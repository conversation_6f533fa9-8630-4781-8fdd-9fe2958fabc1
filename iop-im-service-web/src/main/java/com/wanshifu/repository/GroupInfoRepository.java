package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.group.GroupListReq;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.po.GroupInfo;
import com.wanshifu.mapper.GroupInfoMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Repository
public class GroupInfoRepository extends BaseRepository<GroupInfo> {

    @Resource
    private GroupInfoMapper groupInfoMapper;

    /**
     * 批量查询分组信息
     * @param groupIdList
     * @return
     */
    public List<GroupInfo> batchSelectInfoByGroupIdList(List<Integer> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)){
            return new ArrayList<>();
        }

        Condition condition = new Condition(GroupInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("groupId", groupIdList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time");
        return this.selectByCondition(condition);
    }

    /**
     * 批量查询启用分组信息
     * @param groupIdList
     * @return
     */
    public List<GroupInfo> batchSelectEnableStatusByGroupIdList(List<Integer> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)){
            return new ArrayList<>();
        }

        Condition condition = new Condition(GroupInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("groupId", groupIdList);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time");
        return this.selectByCondition(condition);
    }

    /**
     * 批量查询分组信息
     * @param groupIdList
     * @return
     */
    public Integer selectEnableCountByGroupIdList(List<Integer> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)){
            return 0;
        }
        return groupInfoMapper.selectEnableCountByGroupIdList(groupIdList);
    }

    /**
     * 获取所有
     * @param status
     * @return
     */
    public List<GroupInfo> getAll(Integer status) {
        Condition condition = new Condition(GroupInfo.class);
        Example.Criteria criteria = condition.createCriteria();

        if ( status != null ){
            criteria.andEqualTo("status", status);
        }

        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("sort");
        return this.selectByCondition(condition);
    }

    /**
     * 获取所有
     *
     * @param includeGroupIdList
     * @param tenantId
     * @return
     */
    public List<GroupInfo> getEnums(List<Long> includeGroupIdList, Long tenantId) {
        return groupInfoMapper.getEnums(includeGroupIdList, tenantId);
    }

    /**
     * 根据分组名称查询分组信息
     * @param groupName
     * @return
     */
    public GroupInfo selectInfoByName(String groupName,  Integer editExcludeGroupId) {
        Condition condition = new Condition(GroupInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("groupName", groupName);

        if (editExcludeGroupId != null) {
            criteria.andNotEqualTo("group_id", editExcludeGroupId);
        }

        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 分组列表
     * @param req
     * @return
     */
    public List<GroupInfo> selectList(GroupListReq req) {
        Condition condition = new Condition(GroupInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", req.getTenantId());
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("sort");
        return this.selectByCondition(condition);
    }

    /**
     * 获取最大排序
     * @param tenantId
     * @return
     */
    public Integer selectMaxSortByTenantId(Long tenantId) {
        return groupInfoMapper.selectMaxSortByTenantId(tenantId);
    }

    /**
     * 查询所有启用分组
     * @param tenantId
     * @return
     */
    public List<GroupInfo> selectAllByEnable(Long tenantId, Integer excludeGroupId) {
        Condition condition = new Condition(GroupInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        if ( excludeGroupId != null ){
            criteria.andNotEqualTo("groupId", excludeGroupId);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time");
        return this.selectByCondition(condition);
    }

    /**
     * 更新分组更新时间
     * @param groupId
     */
    public Integer updateNowTime(Integer groupId, Long operatorId) {
        return groupInfoMapper.updateNowTime(groupId, operatorId);
    }

    /**
     * 分组列表
     */
    public List<GroupInfo> groupInfoList(Long tenantId) {
        Condition condition = new Condition(GroupInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 根据分组id查询分组信息
     * @param seatId
     * @return
     */
    public List<GroupInfo> selectEnableBySeatId(Long seatId) {
        return groupInfoMapper.selectEnableBySeatId(seatId);
    }
}