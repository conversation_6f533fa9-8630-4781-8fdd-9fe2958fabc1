package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.UserVirtualRelation;
import com.wanshifu.mapper.UserVirtualRelationMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class UserVirtualRelationRepository extends BaseRepository<UserVirtualRelation> {

    @Resource
    private UserVirtualRelationMapper userVirtualRelationMapper;

    public void closeConversationUpdateBind(String outerUserId, String virtualUserId){
        userVirtualRelationMapper.closeConversationUpdateBind(outerUserId, virtualUserId, new Date());
    }

    /**
     * 查询是否有绑定关系
     * */
    public UserVirtualRelation getRelationByOuterUserId(String outerUserId, String bindStatus) {
        Condition condition = new Condition(UserVirtualRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("outerUserId", outerUserId);
        criteria.andEqualTo("bindStatus", bindStatus);
        criteria.andEqualTo("isDelete",0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }


    /**
     * 查询是否有绑定关系
     * */
    public UserVirtualRelation getRelationByVirtualUserId(String virtualUserId, String bindStatus) {
        Condition condition = new Condition(UserVirtualRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("virtualUserId", virtualUserId);
        criteria.andEqualTo("bindStatus", bindStatus);
        criteria.andEqualTo("isDelete",0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 查询是否有绑定关系
     * */
    public UserVirtualRelation getRelationByOuterUserIdAndVirtualUserId(String outerUserId, String virtualUserId, String bindStatus) {
        Condition condition = new Condition(UserVirtualRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("outerUserId", outerUserId);
        criteria.andEqualTo("virtualUserId", virtualUserId);
        criteria.andEqualTo("bindStatus", bindStatus);
        criteria.andEqualTo("isDelete",0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

}