package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.GroupRule;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Repository
public class GroupRuleRepository extends BaseRepository<GroupRule> {

    /**
     * 根据群组id列表查询
     * @param groupId
     * @return
     */
    public List<GroupRule> selectByGroupId(Integer groupId) {
        Condition condition = new Condition(GroupRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("groupId", groupId);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 根据群组id和指标id  查询单个渠道配置规则
     * @param groupId
     * @return
     */
    public GroupRule selectByGroupIdAndRuleMetricId(Integer groupId, Integer ruleMetricId) {
        Condition condition = new Condition(GroupRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("groupId", groupId);
        criteria.andEqualTo("ruleMetricId", ruleMetricId);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 根据群组id和指标id  查询单个渠道配置规则
     * @param groupIdList
     * @param ruleMetricId
     * @return
     */
    public List<GroupRule> selectByGroupIdListAndRuleMetricId(List<Integer> groupIdList, Integer ruleMetricId) {
        if ( CollectionUtils.isEmpty(groupIdList) ){
            return new ArrayList<>();
        }

        Condition condition = new Condition(GroupRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("groupId", groupIdList);
        criteria.andEqualTo("ruleMetricId", ruleMetricId);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 根据群组id列表查询
     * @param groupIdList
     * @return
     */
    public List<GroupRule> selectByGroupIdList(List<Integer> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)){
            return new ArrayList<>();
        }

        Condition condition = new Condition(GroupRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("groupId", groupIdList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }
}