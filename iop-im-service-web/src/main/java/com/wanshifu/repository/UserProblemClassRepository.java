package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.domain.po.UserProblemClass;
import com.wanshifu.mapper.UserProblemClassMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class UserProblemClassRepository extends BaseRepository<UserProblemClass> {

    @Resource
    private UserProblemClassMapper userProblemClassMapper;

    /**
     * 批量查询
     * @param problemIdList
     * @return
     */
    public List<UserProblemClass> selectByIdList(List<Long> problemIdList) {
        if ( CollectionUtils.isEmpty(problemIdList) ){
            return new ArrayList<>();
        }
        Condition condition = new Condition(UserProblemClass.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("problemId", problemIdList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 根据渠道ID查询问题分类
     * */
    public List<UserProblemClass> getUserProblemClassByChannelId(Long channelId){
        Condition condition = new Condition(UserProblemClass.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("channelId", channelId);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

    /**
     * 根据渠道ID查询问题分类
     * */
    public List<UserProblemClass> getUserProblemClassByChannelIdAndStatus(Long channelId, String status){
        Condition condition = new Condition(UserProblemClass.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("channelId", channelId);
        criteria.andEqualTo("status", status);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 查询是否有启用的重名用户问题分类
     * */
    public List<UserProblemClass> getUserProblemClassByName(String problemName, Long channelId){
        Condition condition = new Condition(UserProblemClass.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("problemName", problemName);
        criteria.andEqualTo("channelId", channelId);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

    /**
     * 获取咨询类型枚举
     * @param req
     * @return
     */
    public List<UserProblemClass> getEnums(GetEnumsReq req) {
        return userProblemClassMapper.getEnums(req);
    }

    /**
     * 检查是否存在
     * @param req
     * @return
     */
    public List<Long> checkNonexistent(CheckNonexistentReq req) {
        return userProblemClassMapper.checkNonexistent(req);
    }
}