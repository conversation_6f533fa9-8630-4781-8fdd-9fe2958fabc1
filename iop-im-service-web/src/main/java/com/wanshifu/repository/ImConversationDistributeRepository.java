package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.po.ImConversationDistribute;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class ImConversationDistributeRepository extends BaseRepository<ImConversationDistribute> {

    /**
     * 根据会话ID查询会话分配信息
     * @param conversationId
     * @param distributeStatus
     * @return
     */
    public ImConversationDistribute selectInfoByConversationIdAndDistributeStatus(Long conversationId, String distributeStatus) {
        Condition condition = new Condition(ImConversationDistribute.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationId", conversationId);
        if ( StringUtils.isNotBlank(distributeStatus) ){
            criteria.andEqualTo("distributeStatus", distributeStatus);
        }
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 查询会话状态
     * @param conversationId 会话ID
     * @param outerUserId   IM用户id
     * @return ImConversationDistribute
     * */
    public ImConversationDistribute selectInfoByConversationId(Long conversationId, String outerUserId, List<String> distributeStatusList) {
        Condition condition = new Condition(ImConversationDistribute.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationId", conversationId);
        criteria.andEqualTo("outerUserId", outerUserId);
        if(CollectionUtils.isNotEmpty(distributeStatusList)){
            criteria.andIn("distributeStatus", distributeStatusList);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}