package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.po.IncomingPropertyConfig;
import com.wanshifu.iop.im.domain.po.IncomingSafetyConfig;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class IncomingPropertyConfigRepository extends BaseRepository<IncomingPropertyConfig> {

    /**
     * 批量查询
     * @param tenantId
     * @param controlTypeList
     * @param propertyTypeList
     * @return
     */
    public List<IncomingPropertyConfig> batchSelectInfoByTenantId(Long tenantId, List<String> controlTypeList, List<String> propertyTypeList, List<Integer> statusList) {
        Condition condition = new Condition(IncomingPropertyConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);

        if (CollectionUtils.isNotEmpty(controlTypeList)){
            criteria.andIn("controlType", controlTypeList);
        }
        if (CollectionUtils.isNotEmpty(propertyTypeList)){
            criteria.andIn("propertyType", propertyTypeList);
        }
        if (CollectionUtils.isNotEmpty(statusList)){
            criteria.andIn("status", statusList);
        }

        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return this.selectByCondition(condition);
    }

    /**
     * 查询
     * @param tenantId
     * @param propertyType
     * @param propertyValue
     * @return
     */
    public IncomingPropertyConfig selectInfoByTenantIdAndProperty(Long tenantId, String propertyType, String propertyValue) {
        Condition condition = new Condition(IncomingPropertyConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andEqualTo("propertyType", propertyType);
        criteria.andEqualTo("propertyValue", propertyValue);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 查询id
     * @param tenantId
     * @param propertyId
     * @return
     */
    public IncomingPropertyConfig selectInfoByPropertyId(Long tenantId, Long propertyId) {
        Condition condition = new Condition(IncomingPropertyConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andEqualTo("propertyId", propertyId);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 进线属性查询，查询条件（ip/设备id/用户id）
     * */
    public List<IncomingPropertyConfig> getPropertyConfigList(Long tenantId,List<String> values, List<String> types){
        Condition condition = new Condition(IncomingPropertyConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andIn("propertyValue", values);
        if(CollectionUtils.isNotEmpty(types)){
            criteria.andIn("propertyType", types);
        }
        criteria.andEqualTo("status", CommonConstant.ZERO);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }


}