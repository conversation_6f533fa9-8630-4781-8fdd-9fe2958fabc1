package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.Problem;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Repository
public class ProblemRepository extends BaseRepository<Problem> {

    /**
     * 根据查询子类型
     * @param parentIdList
     * @return
     */
    public List<Problem> selectAllByParentId(List<Long> parentIdList) {
        if (CollectionUtils.isEmpty(parentIdList)){
            return new ArrayList<>();
        }
        Condition condition = new Condition(Problem.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("parentId", parentIdList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }
}