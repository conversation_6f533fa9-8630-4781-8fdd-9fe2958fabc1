package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.po.ImGroupUserRelation;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Repository
public class ImGroupUserRelationRepository extends BaseRepository<ImGroupUserRelation> {


    /**
     * 查询群里的虚拟账号，并且是成员
     * */
    public List<ImGroupUserRelation> getGroupUserRelationsByImGroupId(Long imGroupId, String outerUserType) {
        Condition condition = new Condition(ImGroupUserRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("imGroupId", imGroupId);
        if(StringUtils.isNotEmpty(outerUserType)) {
            criteria.andEqualTo("outerClassType", outerUserType);
        }
        criteria.andEqualTo("isDelete", 0);
        criteria.andEqualTo("memberStatus","in");
        return this.selectByCondition(condition);
    }

    /**
     * 查询群里的成员
     * @param imGroupIdList
     * @param outerClassType
     * @return
     */
    public List<ImGroupUserRelation> batchSelectAllByGroupIdListAndOuterClassType(List<Long> imGroupIdList, String outerClassType) {
        if ( CollectionUtils.isEmpty(imGroupIdList) ){
            return new ArrayList<>();
        }

        Condition condition = new Condition(ImGroupUserRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("imGroupId", imGroupIdList);
        if(StringUtils.isNotEmpty(outerClassType)) {
            criteria.andEqualTo("outerClassType", outerClassType);
        }
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }
}