package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.po.BusinessFromTemplate;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class BusinessFromTemplateRepository extends BaseRepository<BusinessFromTemplate> {

    /**
     * 根据租户id查询所有业务表单
     *
     * @param tenantId
     * @param userClass
     * @return
     */
    public List<BusinessFromTemplate> selectAllByTenantIdAndUserClass(Long tenantId, String userClass) {
        Condition condition = new Condition(BusinessFromTemplate.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andEqualTo("userClass", userClass);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }
}