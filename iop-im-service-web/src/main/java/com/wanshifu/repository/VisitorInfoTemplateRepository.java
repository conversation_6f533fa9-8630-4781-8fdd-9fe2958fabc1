package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.po.VisitorInfoTemplate;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Repository
public class VisitorInfoTemplateRepository extends BaseRepository<VisitorInfoTemplate> {


    /**
     * 获取访客信息模板
     * @param userClass
     * @return
     */
    public VisitorInfoTemplate selectByVisitorClassEn(String userClass, Long tenantId) {
        Condition condition = new Condition(VisitorInfoTemplate.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("visitorClassEn", userClass);
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}