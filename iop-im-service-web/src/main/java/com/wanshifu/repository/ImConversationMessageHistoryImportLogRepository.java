package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.enums.ConversationMessageHistoryImportStatusEnum;
import com.wanshifu.iop.im.domain.po.ImConversationMessageHistoryImportLog;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;

@Repository
public class ImConversationMessageHistoryImportLogRepository extends BaseRepository<ImConversationMessageHistoryImportLog> {

    /**
     * 通过会话id查询导入日志
     * @param conversationId
     * @return
     */
    public ImConversationMessageHistoryImportLog selectByConversationId(Long conversationId) {
        Condition condition = new Condition(ImConversationMessageHistoryImportLog.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationId", conversationId);
        criteria.andEqualTo("isDelete",0);
        condition.setOrderByClause("create_time desc limit 1");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 初始化导入日志
     * @param conversationId
     * @param importStatus
     * @return
     */
    public ImConversationMessageHistoryImportLog insertInit(Long conversationId, String importStatus) {
        ImConversationMessageHistoryImportLog log = new ImConversationMessageHistoryImportLog();
        log.setConversationId(conversationId);
        log.setImportStatus(importStatus);
        this.insertSelective(log);
        return log;
    }

    /**
     * 更新导入日志
     * @param log
     * @return
     */
    public Integer updateImportStatus(Long id, String importStatus, String importRemark)  {
        ImConversationMessageHistoryImportLog update = new ImConversationMessageHistoryImportLog();
        update.setConversationMessageHistoryImportLogId(id);
        update.setImportStatus(importStatus);
        update.setImportRemark(importRemark);
        if ( ConversationMessageHistoryImportStatusEnum.COMPLETE.type.equals(importStatus) ){
            update.setImportTime(new Date());
        }
        return this.updateByPrimaryKeySelective(update);
    }
}