package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.QueueConfig;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Repository
public class QueueConfigRepository extends BaseRepository<QueueConfig> {

    /**
     * 获取队列配置
     * @param fromId
     * @param fromType
     * @return
     */
    public QueueConfig selectConfigByFromId(Integer fromId, String fromType) {
        Condition condition = new Condition(QueueConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("fromId", fromId);
        criteria.andEqualTo("fromType", fromType);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}