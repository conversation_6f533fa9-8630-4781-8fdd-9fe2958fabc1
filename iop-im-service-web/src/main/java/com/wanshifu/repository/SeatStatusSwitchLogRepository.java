package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.SeatStatusSwitchLog;
import com.wanshifu.mapper.SeatStatusSwitchLogMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class SeatStatusSwitchLogRepository extends BaseRepository<SeatStatusSwitchLog> {

    @Resource
    private SeatStatusSwitchLogMapper seatStatusSwitchLogMapper;


    /**
     * 批量坐席id查询最后一条切换状态记录
     * @param seatIdList
     */
    public List<SeatStatusSwitchLog> selectLastSwitchStatusBySeatIdList(List<Long> seatIdList) {
        if (CollectionUtils.isEmpty(seatIdList)){
            return new ArrayList<>();
        }
        return seatStatusSwitchLogMapper.selectLastSwitchStatusBySeatIdList(seatIdList);
    }

    /**
     * 通过坐席id查询最后一条切换状态记录
     * @param seatId
     * @return
     */
    public SeatStatusSwitchLog selectLastBySeatId(Long seatId) {
        Condition condition = new Condition(SeatStatusSwitchLog.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc, seat_status_switch_log_id desc limit 1");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}