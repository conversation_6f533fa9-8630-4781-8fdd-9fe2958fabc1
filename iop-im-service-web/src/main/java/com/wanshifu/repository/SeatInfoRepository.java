package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.domain.bo.groupManage.GroupSeatListRespBo;
import com.wanshifu.iop.im.domain.bo.seat.MaxOnlineCountByGroupIdListRespBo;
import com.wanshifu.iop.im.domain.bo.seat.SeatSearchListReqBo;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.mapper.SeatInfoMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class SeatInfoRepository extends BaseRepository<SeatInfo> {

    @Resource
    private SeatInfoMapper seatInfoMapper;

    /**
     * 搜索列表
     * @param searchListReqBo
     * @return
     */
    public List<SeatInfo> searchList(SeatSearchListReqBo searchListReqBo) {
        Condition condition = new Condition(SeatInfo.class);
        Example.Criteria criteria = condition.createCriteria();

        if ( searchListReqBo.getAccountId() != null ){
            criteria.andEqualTo("accountId", searchListReqBo.getAccountId());
        }

        if ( searchListReqBo.getSeatNo() != null ){
            criteria.andEqualTo("seatNo", searchListReqBo.getSeatNo());
        }

        if (CollectionUtils.isNotEmpty(searchListReqBo.getSeatTypeList())){
            criteria.andIn("seatType", searchListReqBo.getSeatTypeList());
        }

         if (CollectionUtils.isNotEmpty(searchListReqBo.getTagIdList())){
            criteria.andIn("tagId", searchListReqBo.getTagIdList());
        }

         if (CollectionUtils.isNotEmpty(searchListReqBo.getStatusList())){
            criteria.andIn("status", searchListReqBo.getStatusList());
        }

        criteria.andEqualTo("tenantId",  searchListReqBo.getTenantId());
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return this.selectByCondition(condition);
    }

    /**
     * 获取所有座席的账号id
     * @return
     */
    public List<Long> getAccountIdByAllSeatInfo() {
        return seatInfoMapper.getAccountIdByAllSeatInfo();
    }

    /**
     * 获取所有座席的账号id
     * @param accountId
     * @return
     */
    public SeatInfo selectInfoByAccountId(Long accountId, Long editSeatId) {
        Condition condition = new Condition(SeatInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("accountId", accountId);
        if (editSeatId != null) {
            criteria.andNotEqualTo("seatId", editSeatId);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 获取账号下座席名称
     * @param seatName
     * @param editExcludeSeatId
     * @return
     */
    public SeatInfo selectInfoByName(String seatName, Long editExcludeSeatId) {
        Condition condition = new Condition(SeatInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("seatName", seatName);
        if (editExcludeSeatId != null) {
            criteria.andNotEqualTo("seatId", editExcludeSeatId);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 根据账号类型获取最新工号
     * @param tenantId
     * @return
     */
    public Integer getLastSeatNo(Long tenantId) {
        return seatInfoMapper.getLastSeatNo(tenantId);
    }

    /**
     * 通过id获取启用的坐席信息
     * @param accountId
     * @return
     */
    public SeatInfo selectEnableInfoByAccountId(Long accountId) {
        Condition condition = new Condition(SeatInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("accountId", accountId);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 更新坐席上线
     * @return
     */
    public Integer updateSeatOnlineStatus(Long seatId) {
        return seatInfoMapper.updateSeatOnlineStatus(seatId);
    }

    /**
     * 获取所有启用的座席账号id
     * @return
     */
    public List<Long> selectAccountIdListEnableSeatInfo() {
        return seatInfoMapper.selectAccountIdListEnableSeatInfo();
    }

    /**
     * 批量更新坐席下线
     * @param seatStatusEn
     * @param seatIdList
     */
    public Integer updateSeatOfflineStatus(String seatStatusEn, List<Long> seatIdList) {
        if ( CollectionUtils.isEmpty(seatIdList) ){
            return 0;
        }
        return seatInfoMapper.updateSeatOfflineStatus(seatStatusEn, seatIdList);
    }

    /**
     * 查询指定状态的坐席
     * */
    public List<SeatInfo> getSeatInfoByStatus(String seatStatus){
        Condition condition = new Condition(SeatInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("currentSeatStatusEn", seatStatus);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 通过坐席id查询坐席信息
     * @param seatIdList
     * @return
     */
    public List<SeatInfo> selectListBySeatIdList(List<Long> seatIdList) {
        if (CollectionUtils.isEmpty(seatIdList)) {
            return new ArrayList<>();
        }

        Condition condition = new Condition(SeatInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("seatId", seatIdList);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 通过坐席id查询坐席信息
     * @param seatIdList
     * @return
     */
    public List<SeatInfo> selectListBySeatIdListAndCurrentSeatStatusEn(List<Long> seatIdList, List<String> currentSeatStatusEnList) {
        if (CollectionUtils.isEmpty(seatIdList)) {
            return new ArrayList<>();
        }

        Condition condition = new Condition(SeatInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("seatId", seatIdList);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        if (CollectionUtils.isNotEmpty(currentSeatStatusEnList)){
            criteria.andIn("currentSeatStatusEn", currentSeatStatusEnList);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 获取坐席枚举
     *
     * @param req
     * @return
     */
    public List<SeatInfo> getEnums(GetEnumsReq req) {
        return seatInfoMapper.getEnums(req);
    }

    /**
     * 查询分组所有坐席的最大接线量
     * @param enGroupIdList
     *
     */
    public List<MaxOnlineCountByGroupIdListRespBo> selectMaxOnlineCountByGroupIdList(List<Integer> enGroupIdList) {
        return seatInfoMapper.selectMaxOnlineCountByGroupIdList(enGroupIdList);
    }

    /**
     * 查询分组所有坐席
     * @param groupId
     * @return
     */
    public List<GroupSeatListRespBo> selectSeatInfoByGroupId(Integer groupId) {
        return seatInfoMapper.selectSeatInfoByGroupId(groupId);
    }

    /**
     * 查询坐席
     * @param seatIdList
     * @param seatStatusList
     * @return
     */
    public List<SeatInfo> selectListBySeatIdListAndStatusList(List<Long> seatIdList, List<String> seatStatusList) {
        if (CollectionUtils.isEmpty(seatIdList) || CollectionUtils.isEmpty(seatStatusList)) {
            return new ArrayList<>();
        }

        Condition condition = new Condition(SeatInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("seatId", seatIdList);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andIn("currentSeatStatusEn", seatStatusList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 获取当前不是离线的账号id
     * @param accountIdList
     * @return
     */
    public List<SeatInfo> batchSelectNotOfflineListByAccountIdList(List<Long> accountIdList) {
        if (CollectionUtils.isEmpty(accountIdList)) {
            return new ArrayList<>();
        }

        Condition condition = new Condition(SeatInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("accountId", accountIdList);
        criteria.andEqualTo("tenantId", CommonConstant.DEFAULT_TENANT_ID);
        criteria.andNotEqualTo("currentSeatStatusEn", CommonConstant.DEFAULT_OFFLINE_SEAT_STATUS);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }
}