package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.FlowTransition;
import com.wanshifu.mapper.FlowTransitionMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class FlowTransitionRepository extends BaseRepository<FlowTransition> {


    @Resource
    private FlowTransitionMapper flowTransitionMapper;


    public void updateSetIsDeleteDefineTransition(Long flowDefineId){
        flowTransitionMapper.updateSetIsDeleteDefineTransition(flowDefineId);
    }

    /**
     * 查询to_node_id and flow_define_id
     * */
    public FlowTransition getFlowTransitionByToNodeId( Long flowDefineId, Long toNodeId) {
        Example example = new Example(FlowTransition.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("toNodeId", toNodeId);
        criteria.andEqualTo("flowDefineId", flowDefineId);
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(example));
    }

    /**
     * 根据from_node_id查询下一个节点
     * */
    public List<FlowTransition> getFlowTransitionByFromNodeId(Long flowDefineId, Long fromNodeId) {
        Example example = new Example(FlowTransition.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("fromNodeId", fromNodeId);
        criteria.andEqualTo("flowDefineId", flowDefineId);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(example);
    }


}