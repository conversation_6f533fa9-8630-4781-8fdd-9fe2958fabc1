package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.enums.VirtualStatusEnum;
import com.wanshifu.iop.im.domain.po.VirtualUserInfo;
import com.wanshifu.mapper.VirtualUserInfoMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class VirtualUserInfoRepository extends BaseRepository<VirtualUserInfo> {

    @Resource
    private VirtualUserInfoMapper virtualUserInfoMapper;


    /**
     * 查询当前未使用的虚拟用户一个
     * */
    public VirtualUserInfo getNotUsedVirtualInfo(){
        Condition condition = new Condition(VirtualUserInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("status","no_used");
        criteria.andEqualTo("isDelete",0);
        criteria.andGreaterThan("userSignExpireTime", new Date());
        //限制查询一条
        condition.setOrderByClause("virtual_info_id desc limit 1");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 查询当前未使用的虚拟用户
     * */
    public List<VirtualUserInfo> batchGetNotUsedVirtualInfo(Integer number){
        Condition condition = new Condition(VirtualUserInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("status","no_used");
        criteria.andEqualTo("isDelete",0);
        criteria.andGreaterThan("userSignExpireTime", new Date());
        //限制查询一条
        condition.setOrderByClause("virtual_info_id desc limit "+number);
        return this.selectByCondition(condition);
    }

    /**
     * 更新虚拟用户状态
     * */
    public void updateVirtualStatus(Long virtualInfoId, String status){
        VirtualUserInfo virtualUserInfo = new VirtualUserInfo();
        virtualUserInfo.setVirtualInfoId(virtualInfoId);
        virtualUserInfo.setStatus(status);
        this.updateByPrimaryKeySelective(virtualUserInfo);
    }

    /**
     * 查询当前用户id
     * */
    public VirtualUserInfo getVirtualInfoById(String outerUserId){
        Condition condition = new Condition(VirtualUserInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("outerUserId",outerUserId);
        criteria.andEqualTo("status", VirtualStatusEnum.USED.type);
        criteria.andEqualTo("isDelete",0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 查询多个虚拟账号
     * */
    public List<VirtualUserInfo> getVirtualInfoByOuterUserIds(List<String> outerUserIds){
        Condition condition = new Condition(VirtualUserInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("outerUserId",outerUserIds);
        criteria.andEqualTo("status", VirtualStatusEnum.USED.type);
        criteria.andEqualTo("isDelete",0);
        return this.selectByCondition(condition);
    }

    /**
     * 批量更新虚拟用户状态
     * @param virtualInfoIdList
     * @param status
     * @return
     */
    public Integer batchUpdateVirtualStatus(List<Long> virtualInfoIdList, String status) {
        return virtualUserInfoMapper.batchUpdateVirtualStatus(virtualInfoIdList, status);
    }
}