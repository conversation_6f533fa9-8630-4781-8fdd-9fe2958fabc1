package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.po.ConversationAutoReplyRecord;
import com.wanshifu.iop.im.domain.po.FieldAttribute;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class FieldAttributeRepository extends BaseRepository<FieldAttribute> {

    /**
     * 根据访客信息模板id查询字段
     * @param fromTemplateId
     * @param fromTemplateType
     * @return
     */
    public List<FieldAttribute> selectByVisitorInfoTemplateId(Long fromTemplateId, String fromTemplateType) {
        Condition condition = new Condition(FieldAttribute.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("fromTemplateId", fromTemplateId);
        criteria.andEqualTo("fromTemplateType", fromTemplateType);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }
}