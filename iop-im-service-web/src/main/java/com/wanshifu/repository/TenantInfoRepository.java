package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.TenantInfo;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Repository
public class TenantInfoRepository extends BaseRepository<TenantInfo> {


    /**
     * 通过主键查询状态正常的租户id
     * */
    public TenantInfo getTenantInfoByTenantId(Long tenantId) {
        Condition condition = new Condition(TenantInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andEqualTo("isDelete",0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}