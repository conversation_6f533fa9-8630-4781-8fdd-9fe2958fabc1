package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.VisitorMark;
import com.wanshifu.mapper.VisitorMarkMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class VisitorMarkRepository extends BaseRepository<VisitorMark> {

    @Resource
    private VisitorMarkMapper visitorMarkMapper;

    /**
     * 根据客服账号查询访客标记
     * @param accountId
     * @param accountClass
     * @return
     */
    public List<VisitorMark> selectListBySeatId(String accountId, String accountClass) {
        Condition condition = new Condition(VisitorMark.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("accountId",accountId);
        criteria.andEqualTo("accountClass",accountClass);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

    /**
     * 根据访客账号查询访客标记
     * @param visitorOuterUserIdList
     * @param accountId
     * @param accountClass
     * @return
     */
    public List<VisitorMark> selectListByOuterUserIdAndAccount(List<String> visitorOuterUserIdList, String accountId, String accountClass) {
        if ( CollectionUtils.isEmpty(visitorOuterUserIdList) ){
            return new ArrayList<>();
        }

        Condition condition = new Condition(VisitorMark.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("outerUserId",visitorOuterUserIdList);
        criteria.andEqualTo("accountId",accountId);
        criteria.andEqualTo("accountClass",accountClass);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

    /**
     * 根据访客账号删除访客标记
     * @param outerUserId
     * @param accountId
     * @param accountClass
     */
    public Integer deleteByOuterUserIdAndAccount(String outerUserId, String accountId, String accountClass) {
        return visitorMarkMapper.deleteByOuterUserIdAndAccount(outerUserId, accountId, accountClass);
    }

    /**
     * 移除单个标记
     * @param outerUserId
     * @param accountId
     * @param accountClass
     * @param removeLabel
     * @return
     */
    public Integer deleteSingleByOuterUserIdAndAccount(String outerUserId, String accountId, String accountClass, String removeLabel) {
        return visitorMarkMapper.deleteSingleByOuterUserIdAndAccount(outerUserId, accountId, accountClass, removeLabel);
    }
}