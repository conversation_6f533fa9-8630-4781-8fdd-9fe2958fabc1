package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.iop.im.domain.po.FlowNode;
import com.wanshifu.mapper.FlowNodeMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class FlowNodeRepository extends BaseRepository<FlowNode> {


    @Resource
    private FlowNodeMapper flowNodeMapper;

    /**
     * 批量更新旧的节点配置改成已删除状态
     * */
    public void deleteOldFlowNodeByFlowDefineId(Long flowDefineId) {
        flowNodeMapper.deleteOldFlowNodeByFlowDefineId(flowDefineId);
    }

    /**
     * 通过流程id查询节点配置
     * */
    public List<FlowNode> getFlowNodeListByFlowId(Long flowDefineId) {
        Condition condition = new Condition(FlowNode.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("flowDefineId", flowDefineId);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }
}