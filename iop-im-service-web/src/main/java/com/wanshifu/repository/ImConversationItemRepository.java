package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.mapper.ImConversationItemMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class ImConversationItemRepository extends BaseRepository<ImConversationItem> {

    @Resource
    private ImConversationItemMapper imConversationItemMapper;


    /**
     * 查询聊天信息
     * */
    public List<ImConversationItem> getImConversationItemsByConversationId(Long conversationId) {
        Condition condition = new Condition(ImConversationItem.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationId", conversationId);
        criteria.andEqualTo("isDelete",0);
        return this.selectByCondition(condition);
    }

    /**
     * 批量查询会话最后一条发送的消息
     * @param conversationIdList
     */
    public List<ImConversationItem> selectLastMessageByConversationIdList(List<Long> conversationIdList) {
        if ( CollectionUtils.isEmpty(conversationIdList) ){
            return new ArrayList<>();
        }
        return imConversationItemMapper.selectLastMessageByConversationIdList(conversationIdList);
    }

    /**
     * 批量查询会话最后一条访客和坐席发送的消息
     * @param conversationIdList
     */
    public List<ImConversationItem> selectLastMessageByConversationIdListAndVisitorAndAgent(List<Long> conversationIdList) {
        if ( CollectionUtils.isEmpty(conversationIdList) ){
            return new ArrayList<>();
        }
        return imConversationItemMapper.selectLastMessageByConversationIdListAndVisitorAndAgent(conversationIdList);
    }

    /**
     * 查询会话未读数
     * @param conversationIdList
     * @return
     */
    public List<CommonLabelValueResp> selectUnreadCountByConversationIdList(List<Long> conversationIdList) {
        return imConversationItemMapper.selectUnreadCountByConversationIdList(conversationIdList);
    }

    /**
     * 根据msgId 查询历史记录
     * */
    public ImConversationItem getImConversationItemsByMsgId(String msgId) {
        Condition condition = new Condition(ImConversationItem.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("msgId", msgId);
        criteria.andEqualTo("isDelete",0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 更新会话记录数据
     * */
    public void updateMsgContentByKey(Long key, String content){
        imConversationItemMapper.updateMsgContentByKey(key, content);
    }

    /**
     * 查询会话消息总数
     * @param conversationIdList
     * @return
     */
    public List<ImConversationItem> selectListByConversationIdList(List<Long> conversationIdList, String sort)  {
        if ( CollectionUtils.isEmpty(conversationIdList) ){
            return new ArrayList<>();
        }
        Condition condition = new Condition(ImConversationItem.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("conversationId", conversationIdList);
        criteria.andEqualTo("isDelete",0);
        condition.setOrderByClause("msg_send_time "+sort);
        return this.selectByCondition(condition);
    }
    /**
     * 根据会话id 查询最后一条访客发送的消息和坐席发送的消息
     * */
    public List<ImConversationItem> selectLastListByConversationIdList(Long conversationId) {
        return imConversationItemMapper.selectLastListByConversationIdList(conversationId);
    }

    /**
     * 更新会话已读
     * @param conversationId
     * @return
     */
    public Integer updateConversationRead(Long conversationId) {
        return imConversationItemMapper.updateConversationRead(conversationId);
    }
}