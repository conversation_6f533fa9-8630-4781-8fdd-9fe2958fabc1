package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.enums.SuccessAndFailEnum;
import com.wanshifu.iop.im.domain.po.ConversationAutoReplyRecord;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class ConversationAutoReplyRecordRepository extends BaseRepository<ConversationAutoReplyRecord> {

    public List<ConversationAutoReplyRecord> getListByMsgSubTypeAndCid(Long conversationId, List<String> msgSubType,Long imConversationItemId){
        Condition condition = new Condition(ConversationAutoReplyRecord.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationId", conversationId);
        criteria.andEqualTo("conversationItemId", imConversationItemId);
        criteria.andIn("msgSubType", msgSubType);
        return this.selectByCondition(condition);
    }

    public ConversationAutoReplyRecord getListByMsgSubTypeAndItemId(String msgSubType,Long imConversationItemId){
        Condition condition = new Condition(ConversationAutoReplyRecord.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationItemId", imConversationItemId);
        criteria.andEqualTo("msgSubType", msgSubType);
        criteria.andEqualTo("recordStatus", SuccessAndFailEnum.SUCCESS.type);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

}