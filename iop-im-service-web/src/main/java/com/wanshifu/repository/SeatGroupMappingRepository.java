package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.group.RemoveGroupSeatReq;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.domain.bo.seat.SeatGroupMappingBo;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.po.SeatGroupMapping;
import com.wanshifu.iop.im.domain.po.Tags;
import com.wanshifu.mapper.SeatGroupMappingMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class SeatGroupMappingRepository extends BaseRepository<SeatGroupMapping> {

    @Resource
    private SeatGroupMappingMapper seatGroupMappingMapper;

    /**
     * 根据seatIdList查询seatGroupMapping
     * @param seatIdList
     * @return
     */
    public List<SeatGroupMapping> batchGetMappingBySeatIdList(List<Long> seatIdList, Integer status) {
        if (CollectionUtils.isEmpty(seatIdList)){
            return new ArrayList<>();
        }

        Condition condition = new Condition(SeatGroupMapping.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("seatId", seatIdList);
        if (status != null) {
            criteria.andEqualTo("status", status);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time");
        return this.selectByCondition(condition);
    }

    /**
     * 逻辑删除映射
     * @param seatIdList
     * @return
     */
    public Integer deleteMappingBySeatIdList(List<Long> seatIdList) {
        if (CollectionUtils.isEmpty(seatIdList)) {
            return 0;
        }
        return seatGroupMappingMapper.deleteMappingBySeatIdList(seatIdList);
    }

    /**
     * 获取分组关联坐席数
     * @param groupIdList
     * @return
     */
    public List<CommonLabelValueResp> selectCountByGroupIdList(List<Integer> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return new ArrayList<>();
        }
        return seatGroupMappingMapper.selectCountByGroupIdList(groupIdList);
    }

    /**
     * 批量更新坐席所在分组的关联状态
     * @param seatId
     * @param status
     * @return
     */
    public Integer batchUpdateStatusBySeatId(Long seatId, Integer status) {
        return seatGroupMappingMapper.batchUpdateStatusBySeatId(seatId, status);
    }

    /**
     * 根据groupId查询分组关联坐席关联
     * @param groupId
     * @return
     */
    public List<SeatGroupMapping> selectListByGroupId(Integer groupId) {
        Condition condition = new Condition(SeatGroupMapping.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("groupId", groupId);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return this.selectByCondition(condition);
    }

    /**
     * 根据groupIds查询分组关联坐席关联
     * @param groupIds
     * @return
     */
    public List<SeatGroupMapping> selectListByGroupIds(List<Integer> groupIds) {
        if(CollectionUtils.isEmpty(groupIds)){
            return new ArrayList<>();
        }
        Condition condition = new Condition(SeatGroupMapping.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("groupId", groupIds);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 根据groupId查询分组关联坐席关联
     * @param groupId
     * @return
     */
    public List<SeatGroupMappingBo> selectSeatMappingByGroupId(Integer groupId) {
        return seatGroupMappingMapper.selectSeatMappingByGroupId(groupId);
    }

    /**
     * 根据seatId查询分组关联坐席关联
     * @param seatId
     * @return
     */
    public List<SeatGroupMappingBo> selectSeatMappingBySeatId(Long seatId) {
        return seatGroupMappingMapper.selectSeatMappingBySeatId(seatId);
    }

    /**
     * 根据groupId和坐席查询分组关联坐席关联，查全部
     * @param groupId
     * @return
     */
    public List<SeatGroupMapping> selectListByGroupIdAndSeatId(Integer groupId, List<Long> seatIdList) {
        Condition condition = new Condition(SeatGroupMapping.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("groupId", groupId);
        criteria.andIn("seatId", seatIdList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 移除分组关联坐席
     * @param req
     * @return
     */
    public Integer deleteByGroupIdAndSeatIdList(RemoveGroupSeatReq req) {
        if (CollectionUtils.isEmpty(req.getSeatIdList())) {
            return 0;
        }
        return seatGroupMappingMapper.deleteByGroupIdAndSeatIdList(req);
    }

    /**
     * 根据groupId和seatId查询分组关联坐席
     * @param seatId
     * @return
     */
    public List<Integer> selectGroupIdBySeatId(Long seatId)  {
        return seatGroupMappingMapper.selectGroupIdBySeatId(seatId);
    }

    /**
     * 将分组关联的状态置为禁用
     * @param groupId
     * @return
     */
    public Integer updateDisableStatusByGroupId(Integer groupId)  {
        return seatGroupMappingMapper.updateDisableStatusByGroupId(groupId);
    }

    /**
     * 将分组关联部分坐席的状态置为启用
     * @param groupId
     * @param seatIds
     * @return
     */
    public Integer updateEnableStatusByGroupIdAndSeatIdList(Integer groupId, List<Long> seatIds, Long operatorId) {
        return seatGroupMappingMapper.updateEnableStatusByGroupIdAndSeatIdList(groupId, seatIds, operatorId);
    }

    /**
     * 将坐席关联的分组的状态置为启用
     * @param seatId
     * @param groupIdList
     * @return
     */
    public Integer updateEnableStatusBySeatIdAndGroupIdList(Long seatId, List<Integer> groupIdList, Long operatorId) {
        return seatGroupMappingMapper.updateEnableStatusBySeatIdAndGroupIdList(seatId, groupIdList, operatorId);
    }

    /**
     * 根据groupIdList查询分组关联坐席关联
     * @param groupIdList
     * @return
     */
    public List<SeatGroupMapping> selectByGroupIdList(List<Integer> groupIdList) {
        if ( CollectionUtils.isEmpty(groupIdList) ){
            return new ArrayList<>();
        }

        Condition condition = new Condition(SeatGroupMapping.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("groupId", groupIdList);
        criteria.andEqualTo("status", StatusEnum.ENABLE.type);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }
}