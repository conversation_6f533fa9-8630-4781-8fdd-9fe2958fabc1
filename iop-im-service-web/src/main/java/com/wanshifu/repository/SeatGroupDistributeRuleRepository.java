package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.SeatGroupDistributeRule;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class SeatGroupDistributeRuleRepository extends BaseRepository<SeatGroupDistributeRule> {

    /**
     * 根据groupId获取所有坐席规则
     * @param groupId
     * @return
     */
    public List<SeatGroupDistributeRule> selectListByGroupId(Integer groupId, List<Long> seatIdList) {
        Condition condition = new Condition(SeatGroupDistributeRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("groupId", groupId);
        criteria.andIn("seatId", seatIdList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 根据groupId和seatId获取坐席规则
     */
    public SeatGroupDistributeRule selectOneByGroupIdAndSeatId(Integer groupId, Long seatId, Integer ruleMetricId) {
        Condition condition = new Condition(SeatGroupDistributeRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("groupId", groupId);
        criteria.andEqualTo("seatId", seatId);
        criteria.andEqualTo("ruleMetricId", ruleMetricId);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}