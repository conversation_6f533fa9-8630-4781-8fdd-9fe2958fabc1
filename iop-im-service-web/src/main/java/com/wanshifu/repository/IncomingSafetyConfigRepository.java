package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.IncomingSafetyConfig;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Repository
public class IncomingSafetyConfigRepository extends BaseRepository<IncomingSafetyConfig> {

    /**
     * 根据租户ID查询
     * @param tenantId
     * @return
     */
    public IncomingSafetyConfig selectInfoByTenantId(Long tenantId) {
        Condition condition = new Condition(IncomingSafetyConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}