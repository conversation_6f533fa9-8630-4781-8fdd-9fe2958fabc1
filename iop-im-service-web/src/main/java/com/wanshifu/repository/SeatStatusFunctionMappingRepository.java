package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.bo.seat.SeatStatusFunctionMappingRespBo;
import com.wanshifu.iop.im.domain.po.SeatStatusFunctionMapping;
import com.wanshifu.mapper.SeatStatusFunctionMappingMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class SeatStatusFunctionMappingRepository extends BaseRepository<SeatStatusFunctionMapping> {

    @Resource
    private SeatStatusFunctionMappingMapper seatStatusFunctionMappingMapper;

    /**
     * 批量获取坐席状态功能映射
     * @param seatStatusIdList
     * @return
     */
    public List<SeatStatusFunctionMapping> batchGetMappingBySeatStatusId(List<Integer> seatStatusIdList) {
        if (CollectionUtils.isEmpty(seatStatusIdList)){
            return new ArrayList<>();
        }

        Condition condition = new Condition(SeatStatusFunctionMapping.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("seatStatusId", seatStatusIdList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 批量删除坐席状态关联功能
     * @param seatStatusId
     */
    public Integer deleteAllBySeatStatusId(Integer seatStatusId, List<Integer> seatFunctionIdList, Long operatorId) {
        return seatStatusFunctionMappingMapper.deleteAllBySeatStatusId(seatStatusId, seatFunctionIdList, operatorId);
    }

    /**
     * 通过功能英文名称和类型获取
     * @param seatFunctionEn
     * @param seatFunctionType
     * @return
     */
    public List<SeatStatusFunctionMappingRespBo> getSeatStatusByFunctionEnAndFunctionType(String seatFunctionEn, String seatFunctionType) {
        return seatStatusFunctionMappingMapper.getSeatStatusByFunctionEnAndFunctionType(seatFunctionEn, seatFunctionType);
    }
}