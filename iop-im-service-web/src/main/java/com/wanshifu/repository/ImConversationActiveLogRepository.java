package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.po.ImConversationActiveLog;
import com.wanshifu.mapper.ImConversationActiveLogMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class ImConversationActiveLogRepository extends BaseRepository<ImConversationActiveLog> {

    @Resource
    private ImConversationActiveLogMapper imConversationActiveLogMapper;

    /**
     * 通过会话ID查询会话激活日志
     */
    public ImConversationActiveLog selectByFromConversationId(Long fromConversationId) {
        Condition condition = new Condition(ImConversationActiveLog.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("fromConversationId", fromConversationId);
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 通过会话ID查询会话激活日志
     */
    public List<ImConversationActiveLog> selectListByFromConversationIdList(List<Long> fromConversationIdList) {
        if ( CollectionUtils.isEmpty(fromConversationIdList) ){
            return new java.util.ArrayList<>();
        }

        Condition condition = new Condition(ImConversationActiveLog.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("fromConversationId", fromConversationIdList);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

    /**
     * 删除会话活跃日志
     * @param conversationId
     * @return
     */
    public Integer deleteLogByConversationId(Long conversationId) {
        return imConversationActiveLogMapper.deleteLogByConversationId(conversationId);
    }
}