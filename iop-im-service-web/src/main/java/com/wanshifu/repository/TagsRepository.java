package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.tags.GetTagsListReq;
import com.wanshifu.iop.im.domain.po.Tags;
import com.wanshifu.mapper.TagsMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class TagsRepository extends BaseRepository<Tags> {

    @Resource
    private TagsMapper tagsMapper;

    /**
     * 获取标签列表
     * @param req
     * @return
     */
    public List<Tags> getTagsList(GetTagsListReq req) {
        Condition condition = new Condition(Tags.class);
        Example.Criteria criteria = condition.createCriteria();

        if (StringUtils.isNotEmpty(req.getScene())){
            criteria.andEqualTo("scene", req.getScene());
        }

        if (req.getStatus() != null) {
            criteria.andEqualTo("status", req.getStatus());
        }

        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return this.selectByCondition(condition);
    }

    /**
     * 批量查询
     * @param tagIdList
     * @return
     */
    public List<Tags> batchSelectInfoByTagIdList(List<Integer> tagIdList) {
        if ( CollectionUtils.isEmpty(tagIdList) ){
            return new ArrayList<>();
        }

        Condition condition = new Condition(Tags.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("tagId", tagIdList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time desc");
        return this.selectByCondition(condition);
    }

    /**
     * 通过标签名称获取一条标签信息
     * @param tagName
     * @param editExcludeTagId
     * @return
     */
    public Tags selectInfoByTagName(String tagName, String scene, Integer editExcludeTagId) {
        Condition condition = new Condition(Tags.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("tagName", tagName);
        criteria.andEqualTo("scene", scene);

        if ( editExcludeTagId != null ){
            criteria.andNotEqualTo("tagId", editExcludeTagId);
        }

        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        condition.setOrderByClause("create_time limit 1 ");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 根据tagName查询
     * @param tagId
     * @return
     */
    public Integer selectExistById(Integer tagId) {
        return tagsMapper.selectExistById(tagId);
    }

    /**
     * 获取标签枚举
     *
     * @param scene
     * @param includeEnumsValueIdList
     * @return
     */
    public List<Tags> getEnums(String scene, List<Long> includeEnumsValueIdList) {
        return tagsMapper.getEnums(scene, includeEnumsValueIdList);
    }
}