package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.po.SeatStatus;
import com.wanshifu.iop.im.domain.po.Tags;
import com.wanshifu.mapper.SeatStatusMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class SeatStatusRepository extends BaseRepository<SeatStatus> {

    @Resource
    private SeatStatusMapper seatStatusMapper;

    /**
     * 获取所有坐席状态
     * @return
     */
    public List<SeatStatus> getAll() {
        Condition condition = new Condition(SeatStatus.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 通过坐席状态英文名称获取坐席状态
     * @param seatStatusEn
     * @return
     */
    public SeatStatus selectInfoBySeatStatusEn(String seatStatusEn) {
        Condition condition = new Condition(SeatStatus.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("seatStatusEn", seatStatusEn);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 批量通过坐席状态英文名称获取坐席状态
     * @param seatStatusEnList
     * @return
     */
    public List<SeatStatus> batchSelectInfoBySeatStatusEnList(List<String> seatStatusEnList) {
        if (CollectionUtils.isEmpty(seatStatusEnList)){
            return new ArrayList<>();
        }

        Condition condition = new Condition(SeatStatus.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("seatStatusEn", seatStatusEnList);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 获取坐席状态默认值
     * @param defaultType
     * @return
     */
    public SeatStatus getStatusByDefaultType(Integer defaultType) {
        Condition condition = new Condition(SeatStatus.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("defaultType", defaultType);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 获取坐席状态
     * @param functionEn
     * @return
     */
    public List<SeatStatus> selectStatusByFunctionEn(String functionEn) {
        return seatStatusMapper.selectStatusByFunctionEn(functionEn);
    }
}