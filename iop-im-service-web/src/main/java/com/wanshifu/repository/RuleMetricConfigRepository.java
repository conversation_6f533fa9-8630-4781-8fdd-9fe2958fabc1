package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.po.RuleMetricConfig;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class RuleMetricConfigRepository extends BaseRepository<RuleMetricConfig> {

    /**
     * 根据对象获取规则
     *
     * @param ruleMetricObject
     * @param fromType
     * @return
     */
    public List<RuleMetricConfig> selectRuleMetricByObject(String ruleMetricObject, String fromType) {
        Condition condition = new Condition(RuleMetricConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("ruleMetricObject", ruleMetricObject);
        criteria.andEqualTo("fromType", fromType);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

    /**
     * 通过id获取规则
     * @param ruleMetricId
     * @return
     */
    public RuleMetricConfig selectRuleMetricById(Integer ruleMetricId) {
        Condition condition = new Condition(RuleMetricConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("ruleMetricId", ruleMetricId);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 根据对象加类型加标识获取规则
     *
     * @param ruleMetricObject
     * @param fromType
     * @return
     */
    public RuleMetricConfig selectRuleMetricByObjectAndFromTypeAndEn(String ruleMetricObject, String fromType, String en) {
        Condition condition = new Condition(RuleMetricConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("ruleMetricObject", ruleMetricObject);
        criteria.andEqualTo("fromType", fromType);
        if(StringUtils.isNotEmpty(en)){
            criteria.andEqualTo("ruleMetricEn", en);
        }
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }


}