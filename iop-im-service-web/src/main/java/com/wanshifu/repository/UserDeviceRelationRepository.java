package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.iop.im.domain.po.UserDeviceRelation;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class UserDeviceRelationRepository extends BaseRepository<UserDeviceRelation> {

    /**
     * 根据设备id查询关联记录
     * */
    public List<UserDeviceRelation> getRelationsByDeviceId(String deviceId) {
        Condition condition = new Condition(UserDeviceRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deviceId", deviceId);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

}