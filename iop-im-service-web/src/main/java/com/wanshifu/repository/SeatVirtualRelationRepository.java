package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.SeatVirtualRelation;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Repository
public class SeatVirtualRelationRepository extends BaseRepository<SeatVirtualRelation> {

    /**
     * 根据seatId查询虚拟账号id
     * @param seatId
     * @return
     */
    public SeatVirtualRelation selectBySeatId(Long seatId) {
        Condition condition = new Condition(SeatVirtualRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("seatId", seatId);
        criteria.andEqualTo("isDelete",0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 批量根据坐席外部账号id查询虚拟账号id
     * @param seatOuterUserIdList
     * @param bindStatus
     * @return
     */
    public List<SeatVirtualRelation> batchSelectByOuterUserIdList(List<String> seatOuterUserIdList, String bindStatus)  {
        if ( CollectionUtils.isEmpty(seatOuterUserIdList) ){
            return new ArrayList<>();
        }
        Condition condition = new Condition(SeatVirtualRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("outerUserId", seatOuterUserIdList);
        criteria.andEqualTo("bindStatus", bindStatus);
        criteria.andEqualTo("isDelete",0);
        return this.selectByCondition(condition);
    }
}