package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.enums.GroupStatusEnum;
import com.wanshifu.iop.im.domain.po.ImGroup;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Repository
public class ImGroupRepository extends BaseRepository<ImGroup> {


    /**
     * 根据外部群id查询群信息
     * */
    public ImGroup getImGroupByOutGroupId(Long imId, String outGroupId) {
        Condition condition = new Condition(ImGroup.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("imId", imId);
        criteria.andEqualTo("outerGroupId", outGroupId);
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 根据外部群id查询群信息, 查询使用中的群组
     * */
    public ImGroup getImGroupUsedByOutGroupId(String outGroupId) {
        Condition condition = new Condition(ImGroup.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("outerGroupId", outGroupId);
        criteria.andEqualTo("groupStatus", GroupStatusEnum.USED.type);
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 更新群状态
     * */
    public void updateGroupStatus(Long imGroupId, String groupStatus) {
        ImGroup imGroup = new ImGroup();
        imGroup.setImGroupId(imGroupId);
        imGroup.setGroupStatus(groupStatus);
        this.updateByPrimaryKeySelective(imGroup);
    }

    /**
     * 根据会话查询群组id
     * */
    public ImGroup selectImGroupByConversationId(Long conversationId){
        Condition condition = new Condition(ImGroup.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationId", conversationId);
        criteria.andEqualTo("groupStatus", GroupStatusEnum.USED.type);
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 根据会话查询群组id
     * */
    public ImGroup selectImGroupByConversationIdAndGroupStatus(Long conversationId){
        Condition condition = new Condition(ImGroup.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationId", conversationId);
        criteria.andEqualTo("groupStatus", GroupStatusEnum.USED.type);
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 根据会话查询群组ids
     * */
    public List<ImGroup> selectImGroupByConversationIds(List<Long> conversationIds){
        Condition condition = new Condition(ImGroup.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("conversationId", conversationIds);
        criteria.andEqualTo("isDelete", 0);
        criteria.andEqualTo("groupStatus", GroupStatusEnum.USED.type);
        return this.selectByCondition(condition);
    }


    /**
     * 根据outer_group_id
     * */
    public ImGroup selectImGroupByOuterGroupId(String outerGroupId) {
        Condition condition = new Condition(ImGroup.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("outerGroupId", outerGroupId);
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 根据conversationIdList查询群组信息
     * @param conversationIdList
     * @return
     */
    public List<ImGroup> batchSelectByConversationIdList(List<Long> conversationIdList) {
        if ( CollectionUtils.isEmpty(conversationIdList) ){
            return new ArrayList<>();
        }
        Condition condition = new Condition(ImGroup.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("conversationId", conversationIdList);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }
}