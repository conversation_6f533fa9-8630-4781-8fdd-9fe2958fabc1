package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.po.FlowDefine;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class FlowDefineRepository extends BaseRepository<FlowDefine> {


    public List<FlowDefine> getFlowDefineByName(String name, Integer deleteValue) {
        Condition condition = new Condition(FlowDefine.class);
        Example.Criteria criteria = condition.createCriteria();
        if(StringUtils.isNotEmpty(name)){
            criteria.andEqualTo("flowName",name);
        }

        criteria.andEqualTo("isDelete",deleteValue);

        return this.selectByCondition(condition);
    }

    public List<FlowDefine> getFlowDefineByNameAndId(Long flowDefineId,String name, Integer deleteValue) {
        Condition condition = new Condition(FlowDefine.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andNotEqualTo("flowDefineId",flowDefineId);
        if(StringUtils.isNotEmpty(name)){
            criteria.andEqualTo("flowName",name);
        }

        criteria.andEqualTo("isDelete",deleteValue);

        return this.selectByCondition(condition);
    }

    public List<FlowDefine> getFlowDefineByFlowIds(List<Long> flowDefineIds) {
        Condition condition = new Condition(FlowDefine.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("flowDefineId",flowDefineIds);

        criteria.andEqualTo("isDelete", CommonConstant.ZERO);

        return this.selectByCondition(condition);
    }

    public FlowDefine getFlowDefineById(Long flowDefineId, String status) {
        Condition condition = new Condition(FlowDefine.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("flowDefineId",flowDefineId);
        criteria.andEqualTo("status",status);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    public List<FlowDefine> getFlowDefineAll(String status) {
        Condition condition = new Condition(FlowDefine.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("status",status);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        return this.selectByCondition(condition);
    }

}