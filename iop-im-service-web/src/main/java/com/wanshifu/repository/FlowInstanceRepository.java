package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.FlowInstance;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Repository
public class FlowInstanceRepository extends BaseRepository<FlowInstance> {


    /**
     * 根据会话id、用户id，查询进行中的节点流程
     * */
    public FlowInstance getFlowInstanceByConversationIdAndOuterUserId(Long conversationId, String outerUserId) {
        Condition condition = new Condition(FlowInstance.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationId", conversationId);
        criteria.andEqualTo("outerUserId", outerUserId);
        criteria.andEqualTo("isDelete", CommonConstant.ZERO);
        criteria.andEqualTo("status","running");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}