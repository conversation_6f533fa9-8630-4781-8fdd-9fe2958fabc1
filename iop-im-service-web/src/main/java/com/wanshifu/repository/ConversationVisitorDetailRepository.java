package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.enums.SuccessAndFailEnum;
import com.wanshifu.iop.im.domain.po.ConversationVisitorDetail;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Repository
public class ConversationVisitorDetailRepository extends BaseRepository<ConversationVisitorDetail> {

    /**
     * 根据会话id查询访客详情
     * @param conversationId
     * @return
     */
    public ConversationVisitorDetail selectByConversationId(Long conversationId) {
        Condition condition = new Condition(ConversationVisitorDetail.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationId", conversationId);
        criteria.andEqualTo("isDelete", SuccessAndFailEnum.SUCCESS.type);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}