package com.wanshifu.repository;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.bo.seat.SeatOutUserIdMappingRespBo;
import com.wanshifu.iop.im.domain.enums.ModulesTypeEnum;
import com.wanshifu.iop.im.domain.enums.RegisterStatusEnum;
import com.wanshifu.iop.im.domain.enums.UserClassEnum;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import com.wanshifu.mapper.UserRegisterInfoMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class UserRegisterInfoRepository extends BaseRepository<UserRegisterInfo> {

    @Resource
    private UserRegisterInfoMapper userRegisterInfoMapper;

    /**
     * 用户ID查询，有效期
     * */
    public UserRegisterInfo findByUserId(Long userId, String userClass, Integer isFindExpire) {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId);
        if(userClass!=null){
            criteria.andEqualTo("userClass", userClass);
        }
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("isDelete", 0);
        // 是否查询有效
        if (CommonConstant.ONE.equals(isFindExpire)){
            criteria.andGreaterThan("userSignExpireTime", new Date());
        }
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 用户ID+设备id查询，有效期
     * */
    public UserRegisterInfo findByUserIdAndDeviceId(Long userId, String userClass, String deviceId, Integer isFindExpire) {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId);
        if(userClass!=null){
            criteria.andEqualTo("userClass", userClass);
        }
        if(StringUtils.isNotEmpty(deviceId)){
            criteria.andEqualTo("deviceId", deviceId);
        }
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("isDelete", 0);
        // 是否查询有效
        if (CommonConstant.ONE.equals(isFindExpire)){
            criteria.andGreaterThan("userSignExpireTime", new Date());
        }
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 用户IDs查询，有效期
     * */
    public List<UserRegisterInfo> findByOuterUserIds(List<String> outerUserIds, String userClass) {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("outerUserId", outerUserIds);
        criteria.andEqualTo("userClass", userClass);
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("isDelete", 0);
        criteria.andGreaterThan("userSignExpireTime", new Date());
        return this.selectByCondition(condition);
    }

    /**
     * 用户IDs查询，有效期
     * */
    public List<UserRegisterInfo> findByUserIdAndUserClass(List<Long> userIds, String userClass) {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("userId", userIds);
        criteria.andEqualTo("imId",CommonConstant.DEFAULT_TENANT_ID);
        criteria.andEqualTo("userClass", userClass);
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("isDelete", 0);
        criteria.andGreaterThan("userSignExpireTime", new Date());
        return this.selectByCondition(condition);
    }

    /**
     * 用户IDs查询，有效期
     * */
    public List<UserRegisterInfo> findByOuterUserIds(List<String> outerUserIds) {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("outerUserId", outerUserIds);
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("isDelete", 0);
        criteria.andGreaterThan("userSignExpireTime", new Date());
        return this.selectByCondition(condition);
    }

    /**
     * 用户IDs查询，有效期
     * */
    public UserRegisterInfo findByOuterUserId(String outerUserId) {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("outerUserId", outerUserId);
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("isDelete", 0);
        criteria.andGreaterThan("userSignExpireTime", new Date());
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 查询分组所有坐席的虚拟账号
     * @param groupIdList
     * @return
     */
    public List<SeatOutUserIdMappingRespBo> selectOuterUserIdListByGroupIdList(List<Integer> groupIdList) {
        return userRegisterInfoMapper.selectOuterUserIdListByGroupIdList(groupIdList);
    }

    /**
     * 查询用户的外部账号id
     * @param userId
     * @param userClass
     * @return
     */
    public UserRegisterInfo selectOuterUserIdByUser(Long userId, String userClass)    {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("userClass", userClass);
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("isDelete", 0);
        criteria.andEqualTo("registerStatus", RegisterStatusEnum.SUCCESS.type);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 查询分组所有坐席的虚拟账号
     * @param seatIdList
     * @return
     */
    public List<UserRegisterInfo> selectOuterUserIdListBySeatIdList(List<Long> seatIdList)    {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("userId", seatIdList);
        criteria.andEqualTo("userClass", ModulesTypeEnum.SEAT.type);
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("isDelete", 0);
        criteria.andEqualTo("registerStatus", RegisterStatusEnum.SUCCESS.type);
        return this.selectByCondition(condition);
    }

    /**
     * 查询有效的用户注册信息id
     * */
    public UserRegisterInfo findByDeviceId(String deviceId) {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deviceId", deviceId);
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("userClass", UserClassEnum.TOURIST.type);
        criteria.andEqualTo("isDelete", 0);
        criteria.andGreaterThan("userSignExpireTime", new Date());
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 查询有效的用户注册信息id
     * */
    public UserRegisterInfo selectByOuterUserIdAndUserClass(String outerUserId, String userClass) {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("outerUserId", outerUserId);
        criteria.andEqualTo("userClass", userClass);
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("isDelete", 0);
        criteria.andGreaterThan("userSignExpireTime", new Date());
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 批量查询有效的用户注册信息id
     * */
    public List<UserRegisterInfo> batchSelectByUserIdListAndUserClass(List<Long> userIdList, String userClass) {
        Condition condition = new Condition(UserRegisterInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("userId", userIdList);
        criteria.andEqualTo("userClass", userClass);
        criteria.andEqualTo("status",0);
        criteria.andEqualTo("isDelete", 0);
        criteria.andGreaterThan("userSignExpireTime", new Date());
        return this.selectByCondition(condition);
    }
}