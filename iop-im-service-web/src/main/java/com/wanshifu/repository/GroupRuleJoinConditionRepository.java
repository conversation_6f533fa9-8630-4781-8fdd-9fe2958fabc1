package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.po.GroupRuleJoinCondition;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Repository
public class GroupRuleJoinConditionRepository extends BaseRepository<GroupRuleJoinCondition> {

    /**
     * 根据groupId查询
     * @param groupId
     * @return
     */
    public GroupRuleJoinCondition selectInfoByGroupId(Integer groupId) {
        Condition condition = new Condition(GroupRuleJoinCondition.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("groupId", groupId);
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }
}