package com.wanshifu.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.leftSidebar.FormatSearchGetLeaveConversationListReq;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.domain.bo.leftSidebar.FormatSearchConversationListBo;
import com.wanshifu.iop.im.domain.dto.distribute.SelectOnlineNumDto;
import com.wanshifu.iop.im.domain.enums.ConversationStatusEnum;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.mapper.ImConversationMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class ImConversationRepository extends BaseRepository<ImConversation> {


    @Resource
    private ImConversationMapper imConversationMapper;

    /**
     * 根据发送方id和接收方id查询指定状态的会话记录
     * */
    public List<ImConversation> getInfoByFromIdAndToIdAndStatus(String fromId, String toId, String status) {
        return imConversationMapper.getInfoByFromIdAndToIdAndStatus(fromId, toId, status);
    }

    /**
     * 根据发送方id和接收方id查询指定状态的会话记录
     * */
    public List<ImConversation> getInfoByFromIdAndToId(String fromId) {
        return imConversationMapper.getInfoByFromIdAndToId(fromId);
    }

    /**
     * 更新当前会话，设置聊天对象
     * */
    public ImConversation updateVirtualUserId(ImConversation imConversation, String virtualUserId, int type) {
        imConversation.setConversationId(imConversation.getConversationId());
        if (type == 1) {
            imConversation.setFromOuterUserId(virtualUserId);
        }else{
            imConversation.setToOuterUserId(virtualUserId);
        }
        imConversation.setConversationStatus(ConversationStatusEnum.PROCESSING.type);
        imConversation.setCompleteTime(null);
        this.updateByPrimaryKeySelective(imConversation);
        return imConversation;
    }

    /**
     * 查询指定坐席的当前会话量
     * @param toOutUserIdList
     * @param toOuterUserType
     * @return
     */
    public List<CommonLabelValueResp> selectCountByToOuterUserId(List<String> toOutUserIdList, String toOuterUserType) {
        if ( CollectionUtils.isEmpty(toOutUserIdList) ){
            return new ArrayList<>();
        }
        return imConversationMapper.selectCountByToOuterUserId(toOutUserIdList, toOuterUserType);
    }

    /**
     * 查询待处理会话列表
     * @param searchBo
     * @return
     */
    public List<ImConversation> selectWaitProcessList(FormatSearchConversationListBo searchBo) {
        return imConversationMapper.selectWaitProcessList(searchBo);
    }

    /**
     * 查询留言会话列表
     * @param searchBo
     * @return
     */
    public List<ImConversation> selectLeaveList(FormatSearchGetLeaveConversationListReq searchBo) {
        return imConversationMapper.selectLeaveList(searchBo);
    }

    /**
     * 查询历史会话列表
     * @param searchBo
     * @return
     */
    public List<ImConversation> selectHistoryList(FormatSearchConversationListBo searchBo) {
        return imConversationMapper.selectHistoryList(searchBo);
    }

    /**
     * 根据会话id列表查询会话信息
     * @param conversationIdList
     * @return
     */
    public List<ImConversation> selectListByConversationIdListAndConversationType(List<Long> conversationIdList, String conversationType, String conversationStatus) {
        if ( CollectionUtils.isEmpty(conversationIdList) ){
            return new ArrayList<>();
        }

        Condition condition = new Condition(ImConversation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("conversationId", conversationIdList);
        criteria.andEqualTo("conversationType", conversationType);
        criteria.andEqualTo("conversationStatus", conversationStatus);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }


    /**
     * 根据会话id列表查询会话信息
     * @param conversationId
     * @return
     */
    public ImConversation selectFirstByConversationIdAndConversationType(Long conversationId, String conversationType, String conversationStatus) {
        Condition condition = new Condition(ImConversation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("conversationId", conversationId);
        criteria.andEqualTo("conversationType", conversationType);
        criteria.andEqualTo("conversationStatus", conversationStatus);
        criteria.andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    /**
     * 根据访客id获取所有会话
     * @param fromOutUserId
     * @return
     */
    public List<ImConversation> selectListByFromOutUserId(String fromOutUserId, String toOuterUserType) {
        Condition condition = new Condition(ImConversation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("fromOuterUserId", fromOutUserId);
        if (StringUtils.isNotBlank(toOuterUserType)){
            criteria.andEqualTo("toOuterUserType", toOuterUserType);
        }
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

    /**
     *
     */
    public List<ImConversation> selectByGroupIdsAndToOuterUserIds(List<Integer> groupIdList, List<String> outerUserIdList) {
        Condition condition = new Condition(ImConversation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("groupId", groupIdList);
        criteria.andIn("toOuterUserId", outerUserIdList);
        criteria.andEqualTo("conversationStatus", ConversationStatusEnum.PROCESSING.type);
        criteria.andEqualTo("isDelete", 0);
        condition.selectProperties("group_id","to_outer_user_id");
        return this.selectByCondition(condition);
    }

    public List<ImConversation> selectListByConversationIdList(List<Long> conversationIdList) {
        if ( CollectionUtils.isEmpty(conversationIdList) ){
            return new ArrayList<>();
        }
        Condition condition = new Condition(ImConversation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("conversationId", conversationIdList);
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

    public List<SelectOnlineNumDto> selectOnlineNum(List<String> toOuterUserIdList) {
        if ( CollectionUtils.isEmpty(toOuterUserIdList) ){
            return new ArrayList<>();
        }
        return imConversationMapper.selectOnlineNum(toOuterUserIdList);
    }

    /**
     * 查询指定坐席的当前会话量
     * @param toOuterUserId
     * @return
     */
    public Integer selectWaitingCountByToOuterUserId(String toOuterUserId) {
        return imConversationMapper.selectWaitingCountByToOuterUserId(toOuterUserId);
    }
}