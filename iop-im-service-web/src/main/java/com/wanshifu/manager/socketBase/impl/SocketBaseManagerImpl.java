package com.wanshifu.manager.socketBase.impl;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.constant.ChannelConstant;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.domain.bo.SelectConversationAutoReplyDetailConfigReqBo;
import com.wanshifu.iop.im.domain.bo.SelectConversationAutoReplyDetailConfigRespBo;
import com.wanshifu.iop.im.domain.bo.leftSidebar.GetVisitorInnerInfoReqBo;
import com.wanshifu.iop.im.domain.bo.leftSidebar.VisitorInnerInfoBo;
import com.wanshifu.iop.im.domain.bo.socketBase.*;
import com.wanshifu.iop.im.domain.enums.OuterUserTypeEnum;
import com.wanshifu.iop.im.domain.enums.UserClassEnum;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyMsgSubTypeEnum;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyTypeEnum;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.VisitorMark;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyConfig;
import com.wanshifu.manager.socketBase.SocketBaseManager;
import com.wanshifu.mq.producer.cscBackendApi.ImWebSocketOnlineWorkbenchProducer;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.impl.LeftSidebarServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * socket基础管理
 * <AUTHOR>
 * @date： 2025-07-24 09:47:15
 */
@Slf4j
@Service
public class SocketBaseManagerImpl extends AbstractService implements SocketBaseManager {

    // 待处理会话更新 waitConversationUpdate
    public final static String WAIT_CONVERSATION_UPDATE = "waitConversationUpdate";

    // 坐席新增会话 conversationDistribute
    public final static String CONVERSATION_DISTRIBUTE = "conversationDistribute";

    // 关闭会话 conversationClose
    public final static String CONVERSATION_CLOSE = "conversationClose";

    // 临近超时未回复 conversationNearTimeoutWithoutResponse
    public final static String CONVERSATION_NEAR_TIMEOUT_WITHOUT_RESPONSE = "conversationNearTimeoutWithoutResponse";

    // 超时未回复 conversationTimeoutWithoutResponse
    public final static String CONVERSATION_TIMEOUT_WITHOUT_RESPONSE = "conversationTimeoutWithoutResponse";

    // 更新坐席信息 updateSeatInfo
    public final static String UPDATE_SEAT_INFO = "updateSeatInfo";

    // 刷新坐席的访客标签 refreshSeatVisitorMark
    public final static String REFRESH_SEAT_VISITOR_MARK = "refreshSeatVisitorMark";

    @Resource
    private ImWebSocketOnlineWorkbenchProducer imWebSocketOnlineWorkbenchProducer;

    @Resource
    private LeftSidebarServiceImpl leftSidebarService;

    /**
     * mq消费器
     */
    @Override
    public void consumerMessage(SendImWebSocketMessageBo req) {
        req.checkParams();

        switch (req.getBusinessType()){
            case WAIT_CONVERSATION_UPDATE:
                this.sendWaitConversationUpdate(req.getConversationId());
                break;
            case CONVERSATION_DISTRIBUTE:
                ImConversation imConversation = this.getVerifyConversation(req.getConversationId());
                if ( Objects.nonNull(imConversation) ){
                    this.sendSeatAddConversation(null, imConversation.getToOuterUserId());
                }
                break;
            case CONVERSATION_CLOSE:
                this.sendCloseConversation(req.getConversationId());
                break;
            case CONVERSATION_NEAR_TIMEOUT_WITHOUT_RESPONSE:
                this.sendNearTimeoutMessage(req.getConversationId());
                break;
            case CONVERSATION_TIMEOUT_WITHOUT_RESPONSE:
                this.sendTimeoutMessage(req.getConversationId());
                break;
            case UPDATE_SEAT_INFO:
                this.sendUpdateSeatInfoMessage(req.getSeatId());
                break;
            case REFRESH_SEAT_VISITOR_MARK:
                this.sendRefreshSeatVisitorMarkMessage(req.getConversationId());
                break;
            default:
                log.error("不支持的业务类型:req {}", JSONObject.toJSONString(req));
                throw new BusinessException("不支持的业务类型");
        }
    }

    /**
     * 待处理会话更新
     */
    @Override
    public void sendWaitConversationUpdate(Long conversationId) {

        // 获取会话信息
        ImConversation imConversation = this.getVerifyConversation(conversationId);
        if ( Objects.isNull(imConversation) ){
            log.error("sendWaitConversationUpdate 会话信息异常, conversationId:{}", conversationId);
            return;
        }
        if ( !OuterUserTypeEnum.SEAT.type.equals(imConversation.getToOuterUserType()) ){
            log.error("sendWaitConversationUpdate 异常场景会话未分配, conversationId:{}", conversationId);
            return;
        }

        SeatInfo seatInfo = super.getSeatInfoBySeatIdOrOutUserId(null, imConversation.getToOuterUserId());
        if ( Objects.isNull(seatInfo) ){
            log.error("sendWaitConversationUpdate 坐席不存在, seatOuterUserId:{}", imConversation.getToOuterUserId());
            return;
        }

        // 查询消息未读数
        long unreadNum = 0L;
        List<CommonLabelValueResp> unreadCountByConversationIdList = imConversationItemRepository.selectUnreadCountByConversationIdList(Collections.singletonList(conversationId));
        if ( CollectionUtils.isNotEmpty(unreadCountByConversationIdList)
                && Objects.nonNull(unreadCountByConversationIdList.get(0)) ){
            unreadNum = Long.parseLong(unreadCountByConversationIdList.get(0).getValue());
        }

        SendWaitConversationUpdateMessageBo request = new SendWaitConversationUpdateMessageBo();

        // 查询会话最新发送消息
        List<ImConversationItem> imConversationItems = imConversationItemRepository.selectLastMessageByConversationIdList(Collections.singletonList(conversationId));
        if ( CollectionUtils.isNotEmpty(imConversationItems) ){
            ImConversationItem imConversationItem = imConversationItems.get(0);

            List<String> itemVisitorId = imConversationItems.stream().filter(f ->
                    Arrays.asList(OuterUserTypeEnum.MERCHANT.type, OuterUserTypeEnum.CLIENT.type, OuterUserTypeEnum.MASTER.type, OuterUserTypeEnum.ENTERPRISE.type).contains(f.getFromOuterUserType())
                            || ( OuterUserTypeEnum.VIRTUAL.type.equals(f.getFromOuterUserType())
                            && OuterUserTypeEnum.SEAT.type.equals(f.getResponseOuterUserType()) )
            ).map(item -> {
                if ( OuterUserTypeEnum.VIRTUAL.type.equals(item.getFromOuterUserType()) && OuterUserTypeEnum.SEAT.type.equals(item.getResponseOuterUserType()) ){
                    return item.getResponseOuterUserId();
                } else {
                    return item.getFromOuterUserId();
                }
            }).distinct().collect(Collectors.toList());

            // 获取访客信息
            GetVisitorInnerInfoReqBo getVisitorInnerInfoReqBo = new GetVisitorInnerInfoReqBo();
            getVisitorInnerInfoReqBo.setOuterUserIdList(itemVisitorId);
            List<VisitorInnerInfoBo> visitorInnerInfoBoList = leftSidebarService.getVisitorInnerInfoBoList(getVisitorInnerInfoReqBo);

            // 发送人信息
            request.setLatestMsgTime(imConversationItem.getMsgSendTime());
            request.setLatestMsgContent(leftSidebarService.getListMsgContent(imConversationItem.getMsgContent()));
            VisitorInnerInfoBo conversationMsgSender = leftSidebarService.getConversationMsgSender(imConversationItem
                    , visitorInnerInfoBoList
                    , imConversation.getToOuterUserId());
            request.setLatestMsgSender(conversationMsgSender.getUserName());

            // 获取会话超时状态
            // 有最后一条消息，且发送人不是坐席
            if ( Objects.nonNull(imConversationItem.getMsgSendTime())
                    && !OuterUserTypeEnum.SEAT.type.equals(imConversationItem.getResponseOuterUserType())
            ){
                List<Long> channelIdList = new ArrayList<>(Collections.singletonList(imConversation.getChannelConfigId()));

                // 获取全局配置
                ChannelConfig globalChannel = channelConfigRepository.getChannelConfigByChannelEn(ChannelConstant.CONVERSATION_GLOBAL_CHANNEL_EN);
                ConversationAutoReplyConfig globalConfigReplyConfig;
                if ( Objects.nonNull(globalChannel) ){
                    channelIdList.add(globalChannel.getChannelId());
                    globalConfigReplyConfig = conversationAutoReplyConfigRepository.selectByChannelId(globalChannel.getChannelId());
                } else {
                    globalConfigReplyConfig = null;
                }

                SelectConversationAutoReplyDetailConfigReqBo replyReqBo = new SelectConversationAutoReplyDetailConfigReqBo();
                replyReqBo.setChannelIdList(channelIdList);
                replyReqBo.setMsgTypeList(Collections.singletonList(AutoReplyTypeEnum.TIMEOUT_AUTO_REPLY.type));
                replyReqBo.setMsgSubTypeList(Collections.singletonList(AutoReplyMsgSubTypeEnum.SEAT_REPLY_TIMEOUT_REMIND.type));
                List<SelectConversationAutoReplyDetailConfigRespBo> replyRespBo = conversationAutoReplyDetailConfigRepository.batchSelectConfig(replyReqBo);

                // 获取最新发送消息信息（访客和坐席） 查询配置了超时提醒的会话
                if ( CollectionUtils.isNotEmpty(replyRespBo) ){
                    // 计算当前会话是否即将超时或已超时
                    request.setTimeoutStatus(leftSidebarService.getConversationTimeoutStatus(imConversationItem.getMsgSendTime(), imConversation, replyRespBo, globalConfigReplyConfig));
                }
            }
        }

        request.setBusinessType(WAIT_CONVERSATION_UPDATE);
        request.setConversationId(conversationId);
        request.setUnreadCount(unreadNum);
        request.setToCustomerId(seatInfo.getAccountId());
        imWebSocketOnlineWorkbenchProducer.sendImWebSocketOnlineWorkbenchMessage(request);
    }

    /**
     * 获取会话信息
     * @param conversationId
     * @return
     */
    private ImConversation getVerifyConversation(Long conversationId) {
        if ( Objects.isNull(conversationId) ){
            log.error("会话id不能为空");
            throw new BusinessException("会话id不能为空");
        }

        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if (Objects.isNull(imConversation)) {
            log.error("会话不存在, conversationId:{}", conversationId);
            throw new BusinessException("会话不存在");
        }

        if (!OuterUserTypeEnum.SEAT.type.equals(imConversation.getToOuterUserType())) {
            log.info("会话未接线，无需发送, conversationId:{}", conversationId);
            return null;
        }

        return imConversation;
    }

    /**
     * 坐席新增会话
     * 触发更新待处理列表
     */
    @Override
    public void sendSeatAddConversation(Long seatId, String seatOuterUserId) {
       SeatInfo seatInfo = super.getSeatInfoBySeatIdOrOutUserId(seatId, seatOuterUserId);
       if ( Objects.isNull(seatInfo) ){
           log.error("sendSeatAddConversation 坐席不存在, seatId:{}, seatOuterUserId:{}", seatId, seatOuterUserId);
           return;
       }

        SendSeatAddConversationMessageBo request = new SendSeatAddConversationMessageBo();
        request.setBusinessType(CONVERSATION_DISTRIBUTE);
        request.setToCustomerId(seatInfo.getAccountId());
        imWebSocketOnlineWorkbenchProducer.sendImWebSocketOnlineWorkbenchMessage(request);
    }

    /**
     * 会话关闭
     */
    @Override
    public void sendCloseConversation(Long conversationId) {
        // 获取会话信息
        ImConversation imConversation = this.getVerifyConversation(conversationId);
        if ( Objects.isNull(imConversation) ){
            log.error("sendCloseConversation 会话信息异常, conversationId:{}", conversationId);
            return;
        }

        SeatInfo seatInfo = super.getSeatInfoBySeatIdOrOutUserId(null, imConversation.getToOuterUserId());
        if ( Objects.isNull(seatInfo) ){
            log.error("sendCloseConversation 坐席不存在, seatOuterUserId:{}", imConversation.getToOuterUserId());
            return;
        }

        SendCloseConversationMessageBo request = new SendCloseConversationMessageBo();
        request.setConversationId(conversationId);
        request.setBusinessType(CONVERSATION_CLOSE);
        request.setToCustomerId(seatInfo.getAccountId());
        imWebSocketOnlineWorkbenchProducer.sendImWebSocketOnlineWorkbenchMessage(request);
    }

    /**
     * 临近超时未回复
     */
    @Override
    public void sendNearTimeoutMessage(Long conversationId) {
        // 获取会话信息
        ImConversation imConversation = this.getVerifyConversation(conversationId);
        if ( Objects.isNull(imConversation) ){
            log.error("sendNearTimeoutMessage 会话信息异常, conversationId:{}", conversationId);
            return;
        }

        SeatInfo seatInfo = super.getSeatInfoBySeatIdOrOutUserId(null, imConversation.getToOuterUserId());
        if ( Objects.isNull(seatInfo) ){
            log.error("sendNearTimeoutMessage 坐席不存在, seatOuterUserId:{}", imConversation.getToOuterUserId());
            return;
        }

        SendNearTimeoutMessageBo request = new SendNearTimeoutMessageBo();
        request.setConversationId(conversationId);
        request.setBusinessType(CONVERSATION_NEAR_TIMEOUT_WITHOUT_RESPONSE);
        request.setToCustomerId(seatInfo.getAccountId());
        imWebSocketOnlineWorkbenchProducer.sendImWebSocketOnlineWorkbenchMessage(request);
    }

    /**
     * 超时未回复
     */
    @Override
    public void sendTimeoutMessage(Long conversationId) {
        // 获取会话信息
        ImConversation imConversation = this.getVerifyConversation(conversationId);
        if ( Objects.isNull(imConversation) ){
            log.error("sendTimeoutMessage 会话信息异常, conversationId:{}", conversationId);
            return;
        }

        SeatInfo seatInfo = super.getSeatInfoBySeatIdOrOutUserId(null, imConversation.getToOuterUserId());
        if ( Objects.isNull(seatInfo) ){
            log.error("sendTimeoutMessage 坐席不存在, seatOuterUserId:{}", imConversation.getToOuterUserId());
            return;
        }

        SendTimeoutMessageBo request = new SendTimeoutMessageBo();
        request.setConversationId(conversationId);
        request.setBusinessType(CONVERSATION_TIMEOUT_WITHOUT_RESPONSE);
        request.setToCustomerId(seatInfo.getAccountId());
        imWebSocketOnlineWorkbenchProducer.sendImWebSocketOnlineWorkbenchMessage(request);
    }

    /**
     * 更新坐席信息
     */
    @Override
    public void sendUpdateSeatInfoMessage(Long seatId) {
        if ( Objects.isNull(seatId) ){
            log.error("updateSeatInfo 坐席id为空");
            throw new BusinessException("坐席id为空");
        }

        SeatInfo seatInfo = seatInfoRepository.selectByPrimaryKey(seatId);
        if ( Objects.isNull(seatInfo) ){
            log.error("updateSeatInfo 坐席不存在, seatId:{}", seatId);
            return;
        }

        SendUpdateSeatInfoMessageBo resp = new SendUpdateSeatInfoMessageBo();
        resp.setBusinessType(UPDATE_SEAT_INFO);
        resp.setToCustomerId(seatInfo.getAccountId());
        imWebSocketOnlineWorkbenchProducer.sendImWebSocketOnlineWorkbenchMessage(resp);
    }

    /**
     * 刷新坐席的访客标签 refreshSeatVisitorMark
     */
    @Override
    public void sendRefreshSeatVisitorMarkMessage(Long conversationId) {
        // 获取会话信息
        ImConversation imConversation = this.getVerifyConversation(conversationId);
        if ( Objects.isNull(imConversation) ){
            log.error("sendTimeoutMessage 会话信息异常, conversationId:{}", conversationId);
            return;
        }

        SeatInfo seatInfo = super.getSeatInfoBySeatIdOrOutUserId(null, imConversation.getToOuterUserId());
        if ( Objects.isNull(seatInfo) ){
            log.error("sendTimeoutMessage 坐席不存在, seatOuterUserId:{}", imConversation.getToOuterUserId());
            return;
        }

        // 获取访客标签
        List<VisitorMark> visitorMarkList = visitorMarkRepository.selectListByOuterUserIdAndAccount(Collections.singletonList(imConversation.getFromOuterUserId()), seatInfo.getSeatId().toString(), UserClassEnum.SEAT.type);

        // 访客标签
        List<CommonLabelValueResp> visitorLabelList = visitorMarkList.stream().filter(f -> imConversation.getFromOuterUserId().equals(f.getOuterUserId())).map(visitorMark -> {
            CommonLabelValueResp commonLabelValueResp = new CommonLabelValueResp();
            commonLabelValueResp.setLabel(visitorMark.getLabelName());
            commonLabelValueResp.setValue(visitorMark.getLabelValue());
            return commonLabelValueResp;
        }).collect(Collectors.toList());

        SendRefreshSeatVisitorMarkMessageBo request = new SendRefreshSeatVisitorMarkMessageBo();
        request.setBusinessType(REFRESH_SEAT_VISITOR_MARK);
        request.setToCustomerId(seatInfo.getAccountId());
        request.setConversationId(conversationId);
        request.setVisitorLabelNameList(visitorLabelList);
        imWebSocketOnlineWorkbenchProducer.sendImWebSocketOnlineWorkbenchMessage(request);
    }
}
