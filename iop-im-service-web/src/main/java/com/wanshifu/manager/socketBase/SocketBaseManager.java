package com.wanshifu.manager.socketBase;

import com.wanshifu.iop.im.api.req.group.QueueGroupEnQueueReq;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;

public interface SocketBaseManager {

    /**
     * mq消费器
     * @param req
     */
    void consumerMessage(SendImWebSocketMessageBo req);

    /**
     * 待处理会话更新
     * @param conversationId
     */
    void sendWaitConversationUpdate(Long conversationId);

    /**
     * 坐席新增会话
     * 触发更新待处理列表
     * @param seatId
     * @param seatOuterUserId
     */
    void sendSeatAddConversation(Long seatId, String seatOuterUserId);

    /**
     * 会话关闭
     * @param conversationId
     */
    void sendCloseConversation(Long conversationId);

    /**
     * 临近超时未回复
     * @param conversationId
     */
    void sendNearTimeoutMessage(Long conversationId);

    /**
     * 超时未回复
     * @param conversationId
     */
    void sendTimeoutMessage(Long conversationId);

    /**
     * 更新坐席信息
     * @param seatId
     */
    void sendUpdateSeatInfoMessage(Long seatId);

    /**
     * 刷新坐席的访客标签
     * @param conversationId
     */
    void sendRefreshSeatVisitorMarkMessage(Long conversationId);
}
