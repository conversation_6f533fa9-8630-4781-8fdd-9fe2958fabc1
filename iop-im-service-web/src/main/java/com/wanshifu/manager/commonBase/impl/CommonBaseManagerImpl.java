package com.wanshifu.manager.commonBase.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.bo.ConditionExpressionBo;
import com.wanshifu.iop.im.domain.bo.CreateGroupReqBo;
import com.wanshifu.iop.im.domain.bo.conversation.FlowInfoBo;
import com.wanshifu.iop.im.domain.bo.group.CreateGroupRequestBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.AddMemberToGroupBo;
import com.wanshifu.iop.im.domain.enums.CurrentSeatStatusEnum;
import com.wanshifu.iop.im.domain.enums.EnableStatusEnum;
import com.wanshifu.iop.im.domain.enums.ImConversationTypeEnum;
import com.wanshifu.iop.im.domain.enums.NodeTypeEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserClassTypeEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserTypeEnum;
import com.wanshifu.iop.im.domain.enums.UserClassEnum;
import com.wanshifu.iop.im.domain.po.FlowDefine;
import com.wanshifu.iop.im.domain.po.FlowInstance;
import com.wanshifu.iop.im.domain.po.FlowNode;
import com.wanshifu.iop.im.domain.po.FlowTransition;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.ImConversationLog;
import com.wanshifu.iop.im.domain.po.ImGroup;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.manager.commonBase.CommonBaseManager;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.repository.FlowDefineRepository;
import com.wanshifu.repository.FlowInstanceRepository;
import com.wanshifu.repository.FlowNodeRepository;
import com.wanshifu.repository.FlowTransitionRepository;
import com.wanshifu.repository.ImConversationLogRepository;
import com.wanshifu.repository.ImConversationRepository;
import com.wanshifu.repository.ImGroupRepository;
import com.wanshifu.repository.SeatInfoRepository;
import com.wanshifu.repository.UserRegisterInfoRepository;
import com.wanshifu.repository.channel.ChannelConfigRepository;
import com.wanshifu.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/16 20:10
 * @description TODO
 */
@Service
@Slf4j
public class CommonBaseManagerImpl implements CommonBaseManager {

    @Resource
    private ChannelConfigRepository channelConfigRepository;

    @Resource
    private FlowDefineRepository flowDefineRepository;

    @Resource
    private FlowNodeRepository flowNodeRepository;

    @Resource
    private FlowTransitionRepository flowTransitionRepository;

    @Resource
    private ImConversationRepository imConversationRepository;


    @Resource
    public SeatInfoRepository seatInfoRepository;

    @Resource
    public UserRegisterInfoRepository userRegisterInfoRepository;

    @Resource
    private ImGroupRepository imGroupRepository;

    @Resource
    private TencentManager tencentManager;

    /**
     * 判断路由是否有指定类型节点
     *
     * @param channelConfigId
     * @param nodeType
     * @return Boolean
     */
    @Override
    public Boolean checkFlowExistNodeByType(Long channelConfigId, String nodeType) {
        ChannelConfig channelConfig = channelConfigRepository.selectByPrimaryKey(channelConfigId);
        if(channelConfig==null){
            log.error("查询不到渠道配置，channelConfigId={}", channelConfigId);
            return false;
        }

        FlowDefine flowDefine = flowDefineRepository.getFlowDefineById(channelConfig.getFlowDefineId(), EnableStatusEnum.ENABLE.type);
        if(flowDefine==null){
            log.error("查询不到导航配置，channelConfigId={}, 关联的路由导航id={}", channelConfigId, channelConfig.getFlowDefineId());
            return false;
        }

        //判断是否有路由节点
        List<FlowNode> flowNodeListByFlowId = flowNodeRepository.getFlowNodeListByFlowId(flowDefine.getFlowDefineId());
        Optional<FlowNode> nodeOptional = flowNodeListByFlowId.stream().filter(f -> f.getNodeType().equals(nodeType)).findFirst();
        if(!nodeOptional.isPresent()){
            return false;
        }
        //判断路由节点是否在时间范围内
        FlowTransition flowTransitionByToNodeId = flowTransitionRepository.getFlowTransitionByToNodeId(flowDefine.getFlowDefineId(), nodeOptional.get().getFlowNodeId());
        if(flowTransitionByToNodeId==null){
            log.error("查询不到路由节点，flowDefineId={}, nodeType={}", flowDefine.getFlowDefineId(), nodeOptional.get().getNodeType());
            return false;
        }

        if (StringUtils.isEmpty(flowTransitionByToNodeId.getConditionExpression())) {
            return true;
        }

        //时间判断
        return this.nodeCompareTime(flowTransitionByToNodeId.getConditionExpression());
    }


    /**
     * 判断时间范围
     * 判断当前时间时分秒，是否在时间范围内
     * 判断星期是否在范围内
     * */
    public Boolean nodeCompareTime(String conditionExpression) {

        LocalDate today = LocalDate.now();
        DayOfWeek dayOfWeek = today.getDayOfWeek();
        int weekDayNum = dayOfWeek.getValue() + 1;

        ConditionExpressionBo conditionExpressionBo = JSONObject.parseObject(conditionExpression, ConditionExpressionBo.class);
        if(conditionExpressionBo==null){
            throw new BusException("节点配置异常，没有节点配置条件");
        }

        ConditionExpressionBo.ConditionExpressionBoItem conditionValue = conditionExpressionBo.getConditionValue();

        if(conditionValue==null){
            throw new BusException("节点配置异常，没有节点时间配置条件");
        }

        if(!conditionValue.getWorkDays().contains(weekDayNum)){
            log.info("不在配置的范围内");
            return false;
        }
        //获取今天的年-月-日
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String todayYMD = sdf.format(new Date());

        List<String> workTimeList = conditionValue.getWorkTime();
        if(CollectionUtils.isEmpty(workTimeList)){
            throw new BusException("节点转换条件没有配置具体时间范围");
        }
        if(workTimeList.size()!=2){
            throw new BusException("节点转换条件时间范围配置错误，应为两个");
        }
        String startTimeStr = todayYMD + " " + workTimeList.get(0);
        String endTimeStr = todayYMD + " " + workTimeList.get(1);

        Date startDate = DateUtil.strToDate(startTimeStr);
        Date endDate = DateUtil.strToDate(endTimeStr);
        Date now = new Date();
        if(startDate.before(now) && endDate.after(now)){
            return true;
        }
        return false;
    }

    /**
     * 入参：
     * 当前节点、会话id
     * 返回：
     * 当前节点
     * */
    @Override
    public FlowNode getCurrentNode(Long conversationId, String currentNodeType){
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if(imConversation==null){
            log.error("查询不到会话id，conversationId={}", conversationId);
            return null;
        }
        Long channelConfigId = imConversation.getChannelConfigId();
        ChannelConfig channelConfig = channelConfigRepository.selectByPrimaryKey(channelConfigId);
        if(channelConfig==null){
            log.error("查询不到渠道配置，channelConfigId={}", channelConfigId);
            return null;
        }
        FlowDefine flowDefine = flowDefineRepository.getFlowDefineById(channelConfig.getFlowDefineId(), EnableStatusEnum.ENABLE.type);
        if(flowDefine==null){
            log.error("查询不到导航配置，channelConfigId={}, 关联的路由导航id={}", channelConfigId, channelConfig.getFlowDefineId());
            return null;
        }

        //查询所有路由节点
        List<FlowNode> flowNodeListByFlowId = flowNodeRepository.getFlowNodeListByFlowId(flowDefine.getFlowDefineId());
        if(CollectionUtils.isEmpty(flowNodeListByFlowId)){
            log.error("查询不到路由节点，flowDefineId={}", flowDefine.getFlowDefineId());
            return null;
        }

        FlowNode currentNode = flowNodeListByFlowId.stream().filter(flowNode -> flowNode.getNodeType().equals(currentNodeType)).findFirst().orElse(null);
        return currentNode;
    }



    /**
     * 入参：
     * 当前节点、会话id
     * 返回：
     * 下个节点
     *
     * @param conversationId
     * @param currentNodeType
     */
    @Override
    public FlowNode getNextNode(Long conversationId, String currentNodeType) {
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if(imConversation==null){
            log.error("查询不到会话id，conversationId={}", conversationId);
            return null;
        }
        Long channelConfigId = imConversation.getChannelConfigId();
        ChannelConfig channelConfig = channelConfigRepository.selectByPrimaryKey(channelConfigId);
        if(channelConfig==null){
            log.error("查询不到渠道配置，channelConfigId={}", channelConfigId);
            return null;
        }
        FlowDefine flowDefine = flowDefineRepository.getFlowDefineById(channelConfig.getFlowDefineId(), EnableStatusEnum.ENABLE.type);
        if(flowDefine==null){
            log.error("查询不到导航配置，channelConfigId={}, 关联的路由导航id={}", channelConfigId, channelConfig.getFlowDefineId());
            return null;
        }

        //查询所有路由节点
        List<FlowNode> flowNodeListByFlowId = flowNodeRepository.getFlowNodeListByFlowId(flowDefine.getFlowDefineId());
        if(CollectionUtils.isEmpty(flowNodeListByFlowId)){
            log.error("查询不到路由节点，flowDefineId={}", flowDefine.getFlowDefineId());
            return null;
        }

        Optional<FlowNode> nodeOptional = flowNodeListByFlowId.stream().filter(f -> f.getNodeType().equals(currentNodeType)).findFirst();
        if(!nodeOptional.isPresent()){
            log.error("传入的路由节点找不到，flowDefineId={}, nodeType={}", flowDefine.getFlowDefineId(), currentNodeType);
            return null;
        }
        //传入的路由节点
        FlowNode flowNode = nodeOptional.get();

        List<FlowTransition> flowTransitionByFromNodeId = flowTransitionRepository.getFlowTransitionByFromNodeId(flowDefine.getFlowDefineId(), flowNode.getFlowNodeId());
        if(CollectionUtils.isEmpty(flowTransitionByFromNodeId)){
            log.error("查询不到路由节点关系，flowDefineId={}, fromNodeId={}", flowDefine.getFlowDefineId(), flowNode.getFlowNodeId());
            return null;
        }

        Long toNodeId;

        if(flowTransitionByFromNodeId.size()==2){
            //需要判断时间
            Optional<FlowTransition> transitionOptional = flowTransitionByFromNodeId.stream().filter(f -> StringUtils.isNotEmpty(f.getConditionExpression())).findFirst();
            if(!transitionOptional.isPresent()){
                log.error("流程配置错误，需配置时间范围，flowDefineId={}, fromNodeId={}", flowDefine.getFlowDefineId(), flowNode.getFlowNodeId());
                return null;
            }
            FlowTransition flowTransition = transitionOptional.get();
            //判断时间范围
            Boolean compareTimeResult = this.nodeCompareTime(flowTransition.getConditionExpression());
            if(compareTimeResult){
                toNodeId = flowTransition.getToNodeId();
            }else{
                Optional<FlowTransition> transitionNotExpressionOptional = flowTransitionByFromNodeId.stream().filter(f -> StringUtils.isEmpty(f.getConditionExpression())).findFirst();
                if (!transitionNotExpressionOptional.isPresent()) {
                    log.error("流程配置错误，两个节点都配置了时间，flowDefineId={}, fromNodeId={}", flowDefine.getFlowDefineId(), flowNode.getFlowNodeId());
                    return null;
                }
                toNodeId = transitionNotExpressionOptional.get().getToNodeId();
            }
            Optional<FlowNode> nodeOptional1 = flowNodeListByFlowId.stream().filter(f -> f.getFlowNodeId().toString().equals(toNodeId + "")).findFirst();
            if(!nodeOptional1.isPresent()){
                log.error("查询不到路由节点，flowDefineId={}, toNodeId={}", flowDefine.getFlowDefineId(), toNodeId);
                return null;
            }
            return nodeOptional1.get();
        }

        FlowTransition flowTransition = flowTransitionByFromNodeId.get(0);
        if(flowTransition.getToNodeId()==0L){
            log.error("流程配置错误，toNodeId不能为0，flowDefineId={}, fromNodeId={}", flowDefine.getFlowDefineId(), flowNode.getFlowNodeId());
            return null;
        }
        Optional<FlowNode> nodeOptional1 = flowNodeListByFlowId.stream().filter(f -> f.getFlowNodeId().toString().equals(flowTransition.getToNodeId() + "")).findFirst();
        if(!nodeOptional1.isPresent()){
            log.error("查询不到路由节点，flowDefineId={}, toNodeId={}", flowDefine.getFlowDefineId(), flowTransition.getToNodeId());
            return null;
        }

        if(NodeTypeEnum.TIME.type.equals(nodeOptional1.get().getNodeType())){
            //如果是时间节点，执行下一个节点
            return this.getNextNode(conversationId, nodeOptional1.get().getNodeType());
        }

        return nodeOptional1.get();
    }

    /**
     * 根据节点类型，返回outer_user_type
     * 返回robot:机器人,seat:坐席 system:系统
     *
     * @param nodeType
     */
    @Override
    public String getOuterUserTypeByNodeType(String nodeType) {
        if(NodeTypeEnum.ARTIFICIAL.type.equals(nodeType)){
            return UserClassEnum.SEAT.type;
        }
        if(NodeTypeEnum.ROBOT.type.equals(nodeType)){
            return NodeTypeEnum.ROBOT.type;
        }
        return UserClassEnum.SYSTEM.type;
    }


    /**
     * 随机取在线的坐席
     * */
    @Override
    public String getSeatOnlineInfo(){
        //获取所有在线坐席
        List<SeatInfo> onlineSeatList = seatInfoRepository.getSeatInfoByStatus(CurrentSeatStatusEnum.ONLINE.type);

        //坐席号
        if(CollectionUtils.isEmpty(onlineSeatList)){
            log.error("不存在在线坐席");
            return "";
        }

        Collections.shuffle(onlineSeatList);
        SeatInfo seatInfo = onlineSeatList.get(0);

        UserRegisterInfo byUserId = userRegisterInfoRepository.findByUserId(seatInfo.getSeatId(), OuterUserTypeEnum.SEAT.type, CommonConstant.ONE);
        if(byUserId==null){
            throw new BusException("坐席对应的账号没有注册im用户"+ JSON.toJSONString(seatInfo));
        }
        return byUserId.getOuterUserId();
    }

    /**
     * 转接、机器人转人工，机器人转留言，修改会话记录to_outer_user_id、to_outer_user_type
     * 将用户拉进群聊， 如果toOuterUserType=seat 需要判断是否有群聊，如果没有群，需要建群
     *
     * @param conversationId
     * @param toOuterUserId
     * @param toOuterUserType
     */
    @Override
    public void changeToOuterUserInfo(Long conversationId, String toOuterUserId, String toOuterUserType) {
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if(imConversation==null){
            throw new BusException("会话不存在，conversationId="+ conversationId);
        }
        ImConversation imConversationInsert = new ImConversation();
        imConversationInsert.setConversationId(conversationId);
        imConversationInsert.setToOuterUserId(toOuterUserId);
        imConversationInsert.setToOuterUserType(toOuterUserType);
        imConversationInsert.setConversationType(ImConversationTypeEnum.online.type);
        imConversationRepository.updateByPrimaryKeySelective(imConversationInsert);

        ImGroup imGroup = imGroupRepository.selectImGroupByConversationId(conversationId);

        if(OuterUserClassTypeEnum.SEAT.type.equals(toOuterUserType)){
            if(imGroup==null){
                //如果转接给客服，并且没有群组，需要建群
                List<CreateGroupRequestBo> createGroupRequestBoList = this.returnCreateGroupRequestBo(
                        toOuterUserId,toOuterUserType,
                        imConversation.getAgentOuterUserId(),
                        OuterUserClassTypeEnum.VIRTUAL.type);
                this.createGroup(createGroupRequestBoList, conversationId);
            }
        }
    }

    public List<CreateGroupRequestBo> returnCreateGroupRequestBo(String toOuterUserId, String toOuterUserType,
                                                                 String fromOuterUserId, String fromOuterUserType){
        List<CreateGroupRequestBo> createGroupRequestBoList = new ArrayList<>();
        CreateGroupRequestBo createGroupRequestBo = new CreateGroupRequestBo();
        createGroupRequestBo.setOuterUserId(toOuterUserId);
        createGroupRequestBo.setOuterClassType(toOuterUserType);
        createGroupRequestBoList.add(createGroupRequestBo);
        CreateGroupRequestBo createGroupRequestBo1 = new CreateGroupRequestBo();
        createGroupRequestBo1.setOuterUserId(fromOuterUserId);
        createGroupRequestBo1.setOuterClassType(fromOuterUserType);
        createGroupRequestBoList.add(createGroupRequestBo1);
        return createGroupRequestBoList;
    }

    /**
     * 创建群聊，并写表
     *
     * @param req
     */
    @Override
    public String createGroup(List<CreateGroupRequestBo> req, Long conversationId) {

        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);

        //创建群聊成员：虚拟账号+指定测试账号对应的outerUserId
        CreateGroupReqBo createGroupReqBo = new CreateGroupReqBo();
        createGroupReqBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        createGroupReqBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        createGroupReqBo.setGroupName(CommonConstant.GROUP_NAME);
        createGroupReqBo.setConversationId(conversationId);
        createGroupReqBo.setAdminOuterUserId(imConversation.getAgentOuterUserId());
        createGroupReqBo.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);

        List<CreateGroupReqBo.CreateGroupReqBoItem> groupMembers = new ArrayList<>();
        for (CreateGroupRequestBo createGroupRequestBo : req) {
            CreateGroupReqBo.CreateGroupReqBoItem item = new CreateGroupReqBo.CreateGroupReqBoItem();
            item.setOuterUserId(createGroupRequestBo.getOuterUserId());
            item.setOuterClassType(createGroupRequestBo.getOuterClassType());
            groupMembers.add(item);
        }
        Optional<CreateGroupReqBo.CreateGroupReqBoItem> first = groupMembers.stream().filter(f -> f.getOuterUserId().equals(imConversation.getAgentOuterUserId())).findFirst();
        if(!first.isPresent()){
            CreateGroupReqBo.CreateGroupReqBoItem item = new CreateGroupReqBo.CreateGroupReqBoItem();
            item.setOuterUserId(imConversation.getAgentOuterUserId());
            item.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);
            groupMembers.add(item);
        }
        createGroupReqBo.setGroupMembers(groupMembers);

        try {
            String groupId = tencentManager.createGroup(createGroupReqBo);
            if (groupId == null) {
                throw new BusException("创建群聊失败");
            }
            return groupId;
        } catch (BusException e) {
            throw new BusException(e.getMessage());
        }
    }

    @Resource
    private FlowInstanceRepository flowInstanceRepository;

    /**
     * 获取节点信息
     *
     * @param conversationId
     * @param fromOuterUserId
     */
    @Override
    public FlowInfoBo returnFlowInfo(Long conversationId, String fromOuterUserId) {

        FlowInstance flowInstance = flowInstanceRepository.getFlowInstanceByConversationIdAndOuterUserId(conversationId, fromOuterUserId);
        if (flowInstance==null) {
            return new FlowInfoBo();
        }
        FlowNode flowNode = flowNodeRepository.selectByPrimaryKey(flowInstance.getCurrentNodeId());
        if(flowNode==null) {
            return new FlowInfoBo();
        }
        FlowNode nextNode = this.getNextNode(conversationId, flowNode.getNodeType());

        FlowInfoBo result = new FlowInfoBo();
        result.setFlowInstance(flowInstance);
        result.setCurrentFlowNode(flowNode);
        result.setNextFlowNode(nextNode);
        return result;
    }

    @Resource
    private ImConversationLogRepository imConversationLogRepository;

    /**
     * 分配更新会话组id、坐席id，建群，
     * todo 将单聊消息导入群聊
     *
     * @param conversationId
     * @param toOuterUserId
     */
    @Override
    public Integer distributeUpdateConversation(Long conversationId, Integer groupId, String toOuterUserId, String toOuterUserType) {
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if(imConversation==null){
            return 0;
        }

        //建群
        try{
            CreateGroupReqBo createGroupReqBo = new CreateGroupReqBo();
            createGroupReqBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            createGroupReqBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
            createGroupReqBo.setGroupName(CommonConstant.GROUP_NAME);
            createGroupReqBo.setConversationId(conversationId);
            createGroupReqBo.setAdminOuterUserId(imConversation.getAgentOuterUserId());
            createGroupReqBo.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);
            List<CreateGroupReqBo.CreateGroupReqBoItem> groupMembers = new ArrayList<>();
            CreateGroupReqBo.CreateGroupReqBoItem item = new CreateGroupReqBo.CreateGroupReqBoItem();
            item.setOuterUserId(toOuterUserId);
            item.setOuterClassType(toOuterUserType);
            groupMembers.add(item);
            CreateGroupReqBo.CreateGroupReqBoItem itemAgent = new CreateGroupReqBo.CreateGroupReqBoItem();
            itemAgent.setOuterUserId(imConversation.getAgentOuterUserId());
            itemAgent.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);
            groupMembers.add(itemAgent);
            createGroupReqBo.setGroupMembers(groupMembers);
            tencentManager.createGroup(createGroupReqBo);
        }catch (Exception e){
            log.error("创建群聊失败", e);
            return 0;
        }

        imConversation.setGroupId(Long.valueOf(groupId));
        imConversation.setToOuterUserId(toOuterUserId);
        imConversation.setToOuterUserType(toOuterUserType);
        imConversation.setConversationType(CurrentSeatStatusEnum.ONLINE.type);
        imConversationRepository.updateByPrimaryKeySelective(imConversation);
        ImConversationLog imConversationLog = new ImConversationLog();
        BeanUtils.copyProperties(imConversation, imConversationLog);
        imConversationLogRepository.insertSelective(imConversationLog);

        return 1;
    }

}
