package com.wanshifu.manager.commonBase;


import com.wanshifu.iop.im.domain.bo.conversation.FlowInfoBo;
import com.wanshifu.iop.im.domain.bo.group.CreateGroupRequestBo;
import com.wanshifu.iop.im.domain.po.FlowNode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/16 20:10
 * @description TODO
 */
public interface CommonBaseManager {

    /**
     * 判断路由是否有指定类型节点
     * @param channelConfigId
     * @param nodeType
     * @return Boolean
     * */
    Boolean checkFlowExistNodeByType(Long channelConfigId, String nodeType);

    /**
     * 入参：
     * 当前节点、会话id
     * 返回：
     * 当前节点
     * */
    FlowNode getCurrentNode(Long conversationId, String currentNodeType);

    /**
     * 入参：
     * 当前节点、会话id
     * 返回：
     * 下个节点
     * */
    FlowNode getNextNode(Long conversationId, String currentNodeType);

    /**
     * 根据节点类型，返回outer_user_type
     * 返回robot:机器人,seat:坐席 system:系统
     * */
    String getOuterUserTypeByNodeType(String nodeType);

    /**
     * 随机取在线的坐席
     * */
    String getSeatOnlineInfo();

    /**
     * 转接、机器人转人工，机器人转留言，修改会话记录to_outer_user_id、to_outer_user_type
     * 将用户拉进群聊
     * 将原有的用户退出群聊
     * 将虚拟robot,system类型用户退出的账号改成待使用
     * */
    void changeToOuterUserInfo(Long conversationId, String toOuterUserId, String toOuterUserType);

    /**
     * 创建群聊
     * */
    String createGroup(List<CreateGroupRequestBo> req, Long conversationId);


    /**
     * 获取节点信息
     * */
    FlowInfoBo returnFlowInfo(Long conversationId, String toOuterUserId);


    /**
     * 分配更新会话组id、坐席id，建群，
     * todo 将单聊消息导入群聊
     * */
    Integer distributeUpdateConversation(Long conversationId, Integer groupId, String toOuterUserId, String toOuterUserType);


}
