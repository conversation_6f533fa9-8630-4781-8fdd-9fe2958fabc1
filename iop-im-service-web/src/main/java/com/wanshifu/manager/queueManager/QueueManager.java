package com.wanshifu.manager.queueManager;

import com.wanshifu.iop.im.api.req.group.QueueGroupEnQueueReq;
import com.wanshifu.iop.im.domain.bo.queue.BatchGetQueueInfoBo;
import com.wanshifu.iop.im.domain.po.GroupInfo;

import java.util.List;

public interface QueueManager {
    void groupEnQueue(QueueGroupEnQueueReq req);

    /**
     * 从redis中批量查询队列信息
     * */
    List<BatchGetQueueInfoBo> batchGetQueueInfo(List<GroupInfo> groupInfoList);
}
