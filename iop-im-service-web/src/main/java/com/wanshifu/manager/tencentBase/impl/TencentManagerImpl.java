package com.wanshifu.manager.tencentBase.impl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.iop.im.api.req.tencent.AddMemberToGroupTencentReq;
import com.wanshifu.iop.im.api.req.tencent.DelMemberFromGroupTencentReq;
import com.wanshifu.iop.im.api.req.tencent.DeleteFriendShipReq;
import com.wanshifu.iop.im.api.req.tencent.ImportMsgToSingleReq.MsgContentItem;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.factory.callback.CallbackFactory;
import com.wanshifu.factory.callback.CallbackHandleI;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.tencent.DestroyGroupReq;
import com.wanshifu.iop.im.api.req.tencent.GetOnlineStatusReq;
import com.wanshifu.iop.im.api.req.tencent.ImportMsgToGroupReq;
import com.wanshifu.iop.im.api.req.tencent.ImportMsgToSingleReq;
import com.wanshifu.iop.im.api.req.tencent.ModifyMsgSingleTencentReq;
import com.wanshifu.iop.im.api.req.tencent.SendCustomerMsgPersonalReq;
import com.wanshifu.iop.im.api.req.tencent.SendMsgGroupReq.MsgBodyContent;
import com.wanshifu.iop.im.api.req.tencent.SendMsgGroupReq.MsgBodyItem;
import com.alibaba.fastjson.JSON;
import com.wanshifu.iop.im.api.req.tencent.SendMsgGroupReq;
import com.google.common.collect.Lists;

import com.wanshifu.constant.TencentConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.iop.im.api.req.AccountImportReq;
import com.wanshifu.iop.im.api.req.tencent.CreateGroupReq;
import com.wanshifu.iop.im.api.req.tencent.SendMsgPersonalReq;
import com.wanshifu.iop.im.api.resp.tencent.TencentCallBackResp;
import com.wanshifu.iop.im.api.resp.tencent.TencentResultResp;
import com.wanshifu.iop.im.domain.bo.CreateGroupReqBo;
import com.wanshifu.iop.im.domain.bo.DestroyGroupBo;
import com.wanshifu.iop.im.domain.bo.GenerateImSignBo;
import com.wanshifu.iop.im.domain.bo.GetOnlineStatusBo;
import com.wanshifu.iop.im.domain.bo.GetOnlineStatusRespBo;
import com.wanshifu.iop.im.domain.bo.ImConversationBeanBo;
import com.wanshifu.iop.im.domain.bo.ImInfoBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToGroupBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToGroupRespBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToSingleBo;
import com.wanshifu.iop.im.domain.bo.RegisterUserReqBo;
import com.wanshifu.iop.im.domain.bo.RegisterUserRespBo;
import com.wanshifu.iop.im.domain.bo.SendCustomerMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgGroupBo;
import com.wanshifu.iop.im.domain.bo.SendMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgToPersonalResultBo;
import com.wanshifu.iop.im.domain.bo.conversation.FormCustomerMsgContentBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.AddMemberToGroupBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.DelMemberFromGroupBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.DeleteFriendShipBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.UpdateMsgSingleBo;
import com.wanshifu.iop.im.domain.enums.*;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.ImGroup;
import com.wanshifu.iop.im.domain.po.ImGroupUserRelation;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.repository.ImConversationItemRepository;
import com.wanshifu.repository.ImConversationRepository;
import com.wanshifu.utils.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class TencentManagerImpl extends BaseManager implements TencentManager {


    private final ImConversationItemRepository imConversationItemRepository;
    private final ImConversationRepository imConversationRepository;

    public TencentManagerImpl(ImConversationItemRepository imConversationItemRepository, ImConversationRepository imConversationRepository) {
        super();
        this.imConversationItemRepository = imConversationItemRepository;
        this.imConversationRepository = imConversationRepository;
    }

    /**
     * 注册用户
     * 返回用户外部id+加密签名
     */
    @Override
    public RegisterUserRespBo registerUser(RegisterUserReqBo req) {

        RegisterUserRespBo result = new RegisterUserRespBo();

        String outerUserId = StringUtils.isEmpty(req.getOuterUserId())?super.generateOuterUserId():req.getOuterUserId();
        result.setOuterUserId(outerUserId);
        if (req.getTenantId() == null || req.getTenantId() == 0L) {
            return result;
        }

        if (req.getImType() == null) {
            return result;
        }

        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            return result;
        }

        GenerateImSignBo generateUserImSignBo = this.returnGenerateImSignBean(imInfoBo, outerUserId);

        //用户签名
        String userSign = super.generateSign(generateUserImSignBo);
        if (StringUtils.isEmpty(userSign)) {
            return result;
        }

        AccountImportReq accountImportReq = new AccountImportReq();
        accountImportReq.setUserID(outerUserId);
        accountImportReq.setNick(req.getNick());
        accountImportReq.setFaceUrl(req.getFaceUrl());
        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            return result;
        }
        try {
            TencentResultResp tencentResultResp = super.questTencentInterFace(TencentInterFaceTypeEnum.ACCOUNT_IMPORT, accountImportReq, imInfoBo, sign);
            if (ObjectUtils.isEmpty(tencentResultResp)) {
                log.error("注册用户异常");
                return result;
            }
            if (!ActionStatusEnum.OK.type.equals(tencentResultResp.getActionStatus())) {
                log.error("注册失败，错误信息:" + tencentResultResp.getErrorInfo() + tencentResultResp.getErrorCode());
                return result;
            }

            result.setOuterUserId(outerUserId);
            result.setUserSign(userSign);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage());
            return result;
        }
    }

    /**
     * 创建群聊
     *
     * @param req
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createGroup(CreateGroupReqBo req) {

        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            return null;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            return null;
        }

        CreateGroupReq createGroupReq = new CreateGroupReq();
        createGroupReq.setName(StringUtils.isEmpty(req.getGroupName()) ? TencentConstant.GROUP_DEFAULT_NAME : req.getGroupName());
        createGroupReq.setType(TencentConstant.GROUP_TYPE);
        createGroupReq.setMemberList(Lists.newArrayList());
        List<CreateGroupReq.CreateGroupItem> memberList = Lists.newArrayList();

        for (CreateGroupReqBo.CreateGroupReqBoItem groupMember : req.getGroupMembers()) {
            CreateGroupReq.CreateGroupItem createGroupItem = new CreateGroupReq.CreateGroupItem();
            createGroupItem.setMember_Account(groupMember.getOuterUserId());
            memberList.add(createGroupItem);
        }
        CreateGroupReq.CreateGroupItem createGroupItemAdmin = new CreateGroupReq.CreateGroupItem();
        createGroupItemAdmin.setMember_Account(req.getAdminOuterUserId());
        createGroupItemAdmin.setRole(TencentConstant.GROUP_DEFAULT_ADMIN);
        memberList.add(createGroupItemAdmin);
        createGroupReq.setMemberList(memberList);

        TencentResultResp groupResult = super.questTencentInterFace(TencentInterFaceTypeEnum.CREATE_GROUP, createGroupReq, imInfoBo, sign);
        if (ObjectUtils.isEmpty(groupResult)) {
            log.error("创建群聊异常");
            return null;
        }
        if (!ActionStatusEnum.OK.type.equals(groupResult.getActionStatus())) {
            throw new BusException("创建群聊异常，错误信息:" + groupResult.getErrorInfo() + groupResult.getErrorCode());
        }

        //群写表
        ImGroup imGroup = new ImGroup();
        imGroup.setImId(imInfoBo.getImId());
        imGroup.setOuterGroupId(groupResult.getGroupId());
        imGroup.setConversationId(req.getConversationId());
        imGroup.setGroupStatus(GroupStatusEnum.USED.type);
        imGroup.setIsDelete(0);
        imGroup.setUpdateTime(new Date());
        imGroup.setCreateTime(new Date());
        imGroupRepository.insertSelective(imGroup);

        //群成员关系
        List<ImGroupUserRelation> imGroupUserRelationList = Lists.newArrayList();
        for (CreateGroupReqBo.CreateGroupReqBoItem groupMember : req.getGroupMembers()) {
            ImGroupUserRelation imGroupUserRelation = new ImGroupUserRelation();
            imGroupUserRelation.setImGroupId(imGroup.getImGroupId());
            imGroupUserRelation.setOuterUserId(groupMember.getOuterUserId());
            imGroupUserRelation.setOuterClassType(groupMember.getOuterClassType());
            imGroupUserRelation.setMemberStatus(MemberStatusEnum.IN.type);
            imGroupUserRelation.setIsDelete(0);
            imGroupUserRelation.setUpdateTime(new Date());
            imGroupUserRelation.setCreateTime(new Date());
            imGroupUserRelationList.add(imGroupUserRelation);
        }
        if(CollectionUtils.isNotEmpty(imGroupUserRelationList)){
            imGroupUserRelationRepository.insertList(imGroupUserRelationList);
        }
        return groupResult.getGroupId();
    }

    /**
     * 向个人发送消息
     * 目前只支持文本消息
     */
    @Override
    public SendMsgToPersonalResultBo sendMsgToPersonal(SendMsgPersonalBo req) {


        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            return null;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            return null;
        }

        SendMsgPersonalReq sendMsgPersonalReq = new SendMsgPersonalReq();
        sendMsgPersonalReq.setFrom_Account(req.getFromAccountId());
        sendMsgPersonalReq.setTo_Account(req.getToAccountId());
        sendMsgPersonalReq.setMsgRandom(Long.valueOf(MathUtil.generateCustomerLength(16) + ""));


        List<SendMsgPersonalReq.MsgBodyItem> msgBody = new ArrayList<>();
        for (SendMsgPersonalBo.MsgContentItem msgContentItem : req.getMsgContentItemList()) {
            SendMsgPersonalReq.MsgBodyItem msgBodyItem = new SendMsgPersonalReq.MsgBodyItem();
            msgBodyItem.setMsgType(msgContentItem.getMsgType());

//            MsgBodyContent msgBodyContent1 = JSONObject.parseObject(JSON.toJSONString(msgContentItem.getContent()), MsgBodyContent.class);
//            String msgText = (String) msgContentItem.getContent();
//            if(msgBodyContent1.getText()==null){
//                msgText = msgContentItem.getContent().toString();
//            }
//            MsgBodyContent msgBodyContent = new MsgBodyContent();
//            msgBodyContent.setText(msgText);
            msgBodyItem.setMsgContent(msgContentItem.getContent());
            msgBody.add(msgBodyItem);
        }

        sendMsgPersonalReq.setMsgBody(msgBody);
        sendMsgPersonalReq.setIsNeedReadReceipt(req.getIsNeedReadReceipt());

        TencentResultResp createGroupResp = super.questTencentInterFace(TencentInterFaceTypeEnum.SEND_MSG_PERSONAL, sendMsgPersonalReq, imInfoBo, sign);
        if (ObjectUtils.isEmpty(createGroupResp)) {
            log.error("发送消息失败，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(sendMsgPersonalReq));
            return null;
        }
        if (!ActionStatusEnum.OK.type.equals(createGroupResp.getActionStatus())) {
            throw new BusException("发送消息失败，错误信息:" + createGroupResp.getErrorInfo() + createGroupResp.getErrorCode());
        }
        SendMsgToPersonalResultBo sendMsgToPersonalResultBo = new SendMsgToPersonalResultBo();
        sendMsgToPersonalResultBo.setMsgTime(createGroupResp.getMsgTime());
        sendMsgToPersonalResultBo.setMsgKey(createGroupResp.getMsgKey());
        sendMsgToPersonalResultBo.setMsgId(createGroupResp.getMsgId());
        return sendMsgToPersonalResultBo;
    }

    /**
     * 向个人发送消息
     * 目前只支持文本消息
     */
    @Override
    public SendMsgToPersonalResultBo sendCustomerMsgToPersonal(SendCustomerMsgPersonalBo req) {


        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            return null;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            return null;
        }

        SendCustomerMsgPersonalReq sendCustomerMsgPersonalReq = new SendCustomerMsgPersonalReq();
        sendCustomerMsgPersonalReq.setFrom_Account(req.getFromAccountId());
        sendCustomerMsgPersonalReq.setTo_Account(req.getToAccountId());
        sendCustomerMsgPersonalReq.setMsgRandom(Long.valueOf(MathUtil.generateCustomerLength(16) + ""));

        SendCustomerMsgPersonalReq.MsgBodyItem msgBodyItem = new SendCustomerMsgPersonalReq.MsgBodyItem();
        msgBodyItem.setMsgType(req.getMsgContentItemList().get(0).getMsgType());

        SendCustomerMsgPersonalReq.MsgBodyContent msgBodyContent = new SendCustomerMsgPersonalReq.MsgBodyContent();
        FormCustomerMsgContentBo formCustomerMsgContentBo = JSONObject.parseObject((String) req.getMsgContentItemList().get(0).getContent(), FormCustomerMsgContentBo.class);

        msgBodyContent.setData(formCustomerMsgContentBo.getData());

        msgBodyItem.setMsgContent(msgBodyContent);

        List<SendCustomerMsgPersonalReq.MsgBodyItem> msgBody = new ArrayList<>();
        msgBody.add(msgBodyItem);
        sendCustomerMsgPersonalReq.setMsgBody(msgBody);
        sendCustomerMsgPersonalReq.setIsNeedReadReceipt(req.getIsNeedReadReceipt());
        log.info("发送自定义消息入参={}", JSON.toJSONString(sendCustomerMsgPersonalReq));
        TencentResultResp createGroupResp = super.questTencentInterFace(TencentInterFaceTypeEnum.SEND_CUSTOMER_MSG_PERSONAL, sendCustomerMsgPersonalReq, imInfoBo, sign);
        if (ObjectUtils.isEmpty(createGroupResp)) {
            log.error("发送自定义消息失败，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(sendCustomerMsgPersonalReq));
            return null;
        }
        if (!ActionStatusEnum.OK.type.equals(createGroupResp.getActionStatus())) {
            throw new BusException("发送消息失败，错误信息:" + createGroupResp.getErrorInfo() + createGroupResp.getErrorCode());
        }
        SendMsgToPersonalResultBo sendMsgToPersonalResultBo = new SendMsgToPersonalResultBo();
        sendMsgToPersonalResultBo.setMsgTime(createGroupResp.getMsgTime());
        sendMsgToPersonalResultBo.setMsgKey(createGroupResp.getMsgKey());
        sendMsgToPersonalResultBo.setMsgId(createGroupResp.getMsgId());
        return sendMsgToPersonalResultBo;
    }

    /**
     * 向群中添加用户
     *
     * @param req
     */
    @Override
    public Boolean addMemberToGroup(AddMemberToGroupBo req) {

        ImGroup imGroup = imGroupRepository.selectByPrimaryKey(req.getGroupId());
        if(imGroup==null){
            log.error("群不存在，群id={}",req.getGroupId());
            return false;
        }

        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            log.error("查询IM配置信息异常");
            return false;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            log.error("管理员签名异常");
            return false;
        }

        AddMemberToGroupTencentReq addMemberToGroupTencentReq = new AddMemberToGroupTencentReq();
        addMemberToGroupTencentReq.setGroupId(imGroup.getOuterGroupId());

        List<AddMemberToGroupTencentReq.AddMemberToGroupItem> MemberList = new ArrayList<>();
        for (String member : req.getMembers()) {
            AddMemberToGroupTencentReq.AddMemberToGroupItem addMemberToGroupItem = new AddMemberToGroupTencentReq.AddMemberToGroupItem();
            addMemberToGroupItem.setMember_Account(member);
            MemberList.add(addMemberToGroupItem);
        }
        addMemberToGroupTencentReq.setMemberList(MemberList);

        TencentResultResp createGroupResp = super.questTencentInterFace(TencentInterFaceTypeEnum.ADD_MEMBER_TO_GROUP, addMemberToGroupTencentReq, imInfoBo, sign);
        if (ObjectUtils.isEmpty(createGroupResp)) {
            log.error("向群中添加用户失败，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(addMemberToGroupTencentReq));
            return false;
        }
        if (!ActionStatusEnum.OK.type.equals(createGroupResp.getActionStatus())) {
            log.error("向群中添加用户失败，错误信息:" + createGroupResp.getErrorInfo() + createGroupResp.getErrorCode());
            return false;
        }
        return true;
    }

    /**
     * 从群中移除用户
     *
     * @param req
     */
    @Override
    public Boolean delMemberFromGroup(DelMemberFromGroupBo req) {

        ImGroup imGroup = imGroupRepository.selectByPrimaryKey(req.getGroupId());
        if(imGroup==null){
            log.error("群不存在，群id={}",req.getGroupId());
            return false;
        }

        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            log.error("查询IM配置信息异常");
            return false;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            log.error("管理员签名异常");
            return false;
        }

        DelMemberFromGroupTencentReq delMemberFromGroupTencentReq = new DelMemberFromGroupTencentReq();
        delMemberFromGroupTencentReq.setGroupId(imGroup.getOuterGroupId());
        delMemberFromGroupTencentReq.setMemberToDel_Account(req.getMembers());
        TencentResultResp createGroupResp = super.questTencentInterFace(TencentInterFaceTypeEnum.DEL_MEMBER_FROM_GROUP, delMemberFromGroupTencentReq, imInfoBo, sign);
        if (ObjectUtils.isEmpty(createGroupResp)) {
            log.error("从群中移除用户失败，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(delMemberFromGroupTencentReq));
            return false;
        }
        if (!ActionStatusEnum.OK.type.equals(createGroupResp.getActionStatus())) {
            log.error("从群中移除用户失败，错误信息:" + createGroupResp.getErrorInfo() + createGroupResp.getErrorCode());
            return false;
        }
        return true;
    }

    /**
     * 修改单聊消息
     *
     * @param req
     */
    @Override
    public Boolean updateMsgSingle(UpdateMsgSingleBo req) {


        ImConversationItem imConversationItem = imConversationItemRepository.selectByPrimaryKey(req.getImConversationItemId());
        if(imConversationItem==null){
            log.error("会话记录不存在，会话记录id={}",req.getImConversationItemId());
            return false;
        }

        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            log.error("查询IM配置信息异常");
            return false;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            log.error("管理员签名异常");
            return false;
        }

        ModifyMsgSingleTencentReq modifyMsgSingleTencentReq = new ModifyMsgSingleTencentReq();
        modifyMsgSingleTencentReq.setFrom_Account(imConversationItem.getFromOuterUserId());
        modifyMsgSingleTencentReq.setTo_Account(imConversationItem.getToOuterUserId());
        modifyMsgSingleTencentReq.setMsgKey(imConversationItem.getMsgKey());

        String msgContent = imConversationItem.getMsgContent();
        List<ImConversationBeanBo.MsgContentItem > msgContentItems = JSONArray.parseArray(msgContent, ImConversationBeanBo.MsgContentItem.class);

        List<ModifyMsgSingleTencentReq.ModifyMsgSingleTencentReqItem> msgBodyList = new ArrayList<>();
        for (ImConversationBeanBo.MsgContentItem  msgContentItem : msgContentItems) {
            ModifyMsgSingleTencentReq.ModifyMsgSingleTencentReqItem modifyMsgSingleTencentReqItem = new ModifyMsgSingleTencentReq.ModifyMsgSingleTencentReqItem();
            modifyMsgSingleTencentReqItem.setMsgType(msgContentItem.getMsgType());

            FormCustomerMsgContentBo formCustomerMsgContentBo = JSON.parseObject((String) msgContentItem.getMsgContent(), FormCustomerMsgContentBo.class);
            formCustomerMsgContentBo.setData(formCustomerMsgContentBo.getData());

            modifyMsgSingleTencentReqItem.setMsgContent(formCustomerMsgContentBo);
            msgBodyList.add(modifyMsgSingleTencentReqItem);
        }

        modifyMsgSingleTencentReq.setMsgBody(msgBodyList);

        TencentResultResp createGroupResp = super.questTencentInterFace(TencentInterFaceTypeEnum.MODIFY_MSG_SINGLE, modifyMsgSingleTencentReq, imInfoBo, sign);
        if (ObjectUtils.isEmpty(createGroupResp)) {
            log.error("修改历史消息，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(modifyMsgSingleTencentReq));
            return false;
        }
        if (!ActionStatusEnum.OK.type.equals(createGroupResp.getActionStatus())) {
            log.error("修改历史消息失败，错误信息:" + createGroupResp.getErrorInfo() + createGroupResp.getErrorCode());
            return false;
        }
        return true;
    }

    /**
     * 删除好友关系
     *
     * @param req
     */
    @Override
    public Boolean deleteFriendShip(DeleteFriendShipBo req) {

        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            return null;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            return null;
        }

        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getImConversationId());
        if(imConversation==null){
            return null;
        }


        DeleteFriendShipReq deleteFriendShipReq = new DeleteFriendShipReq();
        deleteFriendShipReq.setFrom_Account(imConversation.getAgentOuterUserId());
        deleteFriendShipReq.setTo_Account(Collections.singletonList(imConversation.getFromOuterUserId()));
        //单向删除好友关系
        deleteFriendShipReq.setDeleteType("Delete_Type_Single");
//        try {
            TencentResultResp createGroupResp = super.questTencentInterFace(TencentInterFaceTypeEnum.DELETE_FRIEND_SHIP, deleteFriendShipReq, imInfoBo, sign);
            if (ObjectUtils.isEmpty(createGroupResp)) {
                log.error("删除好友关系失败，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(deleteFriendShipReq));
                return null;
            }
            if (!ActionStatusEnum.OK.type.equals(createGroupResp.getActionStatus())) {
                throw new BusException("删除好友关系失败，错误信息:" + createGroupResp.getErrorInfo() + createGroupResp.getErrorCode());
            }

//        } catch (BusException e) {
//            throw new BusException("删除好友关系失败，请求参数={}" + JSON.toJSONString(req) + "腾讯请求参数=｛｝，" + JSON.toJSONString(sendMsgGroupReq));
//        }

        return null;
    }

    /**
     * 向群里发送消息
     *
     * @param req
     */
    @Override
    public Integer sendMsgToGroup(SendMsgGroupBo req) {

        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            return null;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            return null;
        }

        SendMsgGroupReq sendMsgGroupReq = new SendMsgGroupReq();
        sendMsgGroupReq.setGroupId(req.getGroupId());
        sendMsgGroupReq.setFrom_Account(req.getFromAccountId());
        sendMsgGroupReq.setRandom(Long.valueOf(MathUtil.generateCustomerLength(16) + ""));

        //消息类型组装 使用策略模式
        List<MsgBodyItem> msgBodyItemList = new ArrayList<>();
        for (SendMsgGroupBo.MsgListItem msgListItem : req.getMsgList()) {
            MsgBodyItem msgBodyItem = new MsgBodyItem();
            msgBodyItem.setMsgType(msgListItem.getMsgType());
            msgBodyItem.setMsgContent(msgListItem.getContent());
            msgBodyItemList.add(msgBodyItem);
        }

        sendMsgGroupReq.setMsgBody(msgBodyItemList);

//        try {
            TencentResultResp createGroupResp = super.questTencentInterFace(TencentInterFaceTypeEnum.SEND_MSG_GROUP, sendMsgGroupReq, imInfoBo, sign);
            if (ObjectUtils.isEmpty(createGroupResp)) {
                log.error("发送群消息失败，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(sendMsgGroupReq));
                return null;
            }
            if (!ActionStatusEnum.OK.type.equals(createGroupResp.getActionStatus())) {
                throw new BusException("发送群消息失败，错误信息:" + createGroupResp.getErrorInfo() + createGroupResp.getErrorCode());
            }

//        } catch (BusException e) {
//            throw new BusException("向群内发送消息异常，请求参数={}" + JSON.toJSONString(req) + "腾讯请求参数=｛｝，" + JSON.toJSONString(sendMsgGroupReq));
//        }

        return 1;
    }

    /**
     * 向群里导入消息
     *
     * @param req
     */
    @Override
    public ImportMsgToGroupRespBo importMsgToGroup(ImportMsgToGroupBo req) {
        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            return null;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            return null;
        }

        ImportMsgToGroupReq importMsgToGroupReq = new ImportMsgToGroupReq();
        importMsgToGroupReq.setGroupId(req.getGroupId());
        importMsgToGroupReq.setRecentContactFlag(req.getRecentContactFlag());

        List<ImportMsgToGroupReq.MsgListItem> msgList = new ArrayList<>();
        for (ImportMsgToGroupBo.MsgListItem msgListItem : req.getMsgList()) {
            ImportMsgToGroupReq.MsgListItem importMsgListItem = new ImportMsgToGroupReq.MsgListItem();
            importMsgListItem.setFrom_Account(msgListItem.getFrom_Account());
            importMsgListItem.setSendTime(msgListItem.getSendTime());
            importMsgListItem.setRandom(msgListItem.getRandom());

            List<ImportMsgToGroupReq.MsgBodyItem> msgBodyList  = new ArrayList<>();
            for (ImportMsgToGroupBo.MsgBodyItem msgBodyItemReq : msgListItem.getMsgBody()) {
                ImportMsgToGroupReq.MsgBodyItem msgBodyItem = new ImportMsgToGroupReq.MsgBodyItem();
                msgBodyItem.setMsgType(msgBodyItemReq.getMsgType());
                ImportMsgToGroupReq.MsgContentItem msgContentItem = new ImportMsgToGroupReq.MsgContentItem();
                msgContentItem.setText(msgBodyItemReq.getMsgContent().getText());
                msgBodyItem.setMsgContent(msgContentItem);
                msgBodyList.add(msgBodyItem);
            }
            importMsgListItem.setMsgBody(msgBodyList);
            msgList.add(importMsgListItem);
        }

        importMsgToGroupReq.setMsgList(msgList);

        try{
            TencentResultResp importMsgToGroupResp = super.questTencentInterFace(TencentInterFaceTypeEnum.IMPORT_MSG_TO_GROUP, importMsgToGroupReq, imInfoBo, sign);
            if (ObjectUtils.isEmpty(importMsgToGroupResp)) {
                log.error("向群内导入消息失败，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(importMsgToGroupResp));
                return null;
            }
            if (!ActionStatusEnum.OK.type.equals(importMsgToGroupResp.getActionStatus())) {
                log.error("向群内导入消息失败，错误信息:" + importMsgToGroupResp.getErrorInfo() + importMsgToGroupResp.getErrorCode());
                throw new BusException("向群内导入消息失败，错误信息:" + importMsgToGroupResp.getErrorInfo() + importMsgToGroupResp.getErrorCode());
            }

            List<TencentResultResp.ImportMsgResultItem> importMsgResult = importMsgToGroupResp.getImportMsgResult();
            if(CollectionUtils.isEmpty(importMsgResult)){
                log.error("向群内导入消息失败,返回数据为空，请求腾讯接口入参=" + JSON.toJSON(importMsgToGroupResp));
                return null;
            }

            ImportMsgToGroupRespBo result = new ImportMsgToGroupRespBo();
            result.setGroupId(req.getGroupId());

            List<ImportMsgToGroupRespBo.MsgImportResultItem> resultMsgList = new ArrayList<>();

            for (ImportMsgToGroupBo.MsgListItem msgListItem : req.getMsgList()) {
                ImportMsgToGroupRespBo.MsgImportResultItem msgImportResultItem = new ImportMsgToGroupRespBo.MsgImportResultItem();

                Optional<TencentResultResp.ImportMsgResultItem> itemOptional = importMsgResult.stream().filter(f -> f.getMsgSeq().toString().equals(msgListItem.getMsgSeq())).findFirst();
                msgImportResultItem.setMsgSeq(msgListItem.getMsgSeq());
                msgImportResultItem.setImportResult(0);
                if (itemOptional.isPresent()) {
                    msgImportResultItem.setImportResult(1);
                }
                resultMsgList.add(msgImportResultItem);
            }
            result.setResultMsgList(resultMsgList);

            return result;
        } catch (Exception e) {
            throw new BusException("向群内发送消息异常，请求参数={}" + JSON.toJSONString(req) + "腾讯请求参数=｛｝，" + JSON.toJSONString(importMsgToGroupReq), e);
        }
    }

    /**
     * 回调，场景：
     * 1、发送消息后
     * 2、拉群后
     * 3、群聊新增成员后
     * 4、消息已读后
     *
     * @param object
     * @param callbackType
     */
    @Override
    public TencentCallBackResp tencentCallBack(Object object, String callbackType) {

        TencentCallBackResp result = new TencentCallBackResp();
        result.setActionStatus("");
        result.setErrorInfo("");
        result.setErrorCode(0);

        TencentCallbackTypeEnum enumByType = TencentCallbackTypeEnum.getEnumByType(callbackType);
        if(enumByType==null){
            result.setActionStatus(CommonConstant.ACTION_STATUS_NO);
            return result;
        }

        CallbackHandleI callbackHandleI = CallbackFactory.setFactoryType(enumByType.beanType);
        Integer i = callbackHandleI.callbackAction(object);
        if(i==1){
            result.setActionStatus(CommonConstant.ACTION_STATUS_OK);
        }
        return result;
    }

    /**
     * 解散群聊
     *
     * @param req
     */
    @Override
    public Integer destroyGroup(DestroyGroupBo req) {

        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            return null;
        }

        ImGroup imGroupByOutGroupId = imGroupRepository.getImGroupByOutGroupId(imInfoBo.getImId(), req.getGroupId());
        if(ObjectUtils.isEmpty(imGroupByOutGroupId)){
            log.error("找不到群记录信息，无法解散");
            return null;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            return null;
        }

        try {
            DestroyGroupReq destroyGroupReq = new DestroyGroupReq();
            destroyGroupReq.setGroupId(req.getGroupId());

            TencentResultResp createGroupResp = super.questTencentInterFace(TencentInterFaceTypeEnum.DESTROY_GROUP, destroyGroupReq, imInfoBo, sign);
            if (ObjectUtils.isEmpty(createGroupResp)) {
                log.error("解散群聊异常，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(destroyGroupReq));
                return null;
            }
            if (!ActionStatusEnum.OK.type.equals(createGroupResp.getActionStatus())) {
                throw new BusException("解散群聊异常，错误信息:" + createGroupResp.getErrorInfo() + createGroupResp.getErrorCode());
            }
            //修改群状态
            imGroupRepository.updateGroupStatus(imGroupByOutGroupId.getImGroupId(),GroupStatusEnum.NO_USED.type);

            return 1;
        } catch (BusException e) {
            throw new BusException("解散群聊异常，请求参数={}" + JSON.toJSONString(req) + "腾讯请求参数=｛｝，" + JSON.toJSONString(req));
        }
    }

    /**
     * 查询账号在线状态
     *
     * @param req
     */
    @Override
    public GetOnlineStatusRespBo getOnlineStatus(GetOnlineStatusBo req) {

        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            return null;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            return null;
        }

        try {
            GetOnlineStatusReq getOnlineStatusReq = new GetOnlineStatusReq();

            getOnlineStatusReq.setToAccount(req.getOuterUserIds());

            TencentResultResp onlineStatusResp = super.questTencentInterFace(TencentInterFaceTypeEnum.ONLINE_STATUS, getOnlineStatusReq, imInfoBo, sign);
            if (ObjectUtils.isEmpty(onlineStatusResp)) {
                log.error("查询账号状态异常，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(getOnlineStatusReq));
                return null;
            }
            if (!ActionStatusEnum.OK.type.equals(onlineStatusResp.getActionStatus())) {
                throw new BusException("查询账号状态异常，错误信息:" + onlineStatusResp.getErrorInfo() + onlineStatusResp.getErrorCode());
            }

            if(CollectionUtils.isEmpty(onlineStatusResp.getQueryResult())){
                log.error("查询账号在线状态异常，返回数据为空");
                throw new BusException("查询账号状态异常，错误信息:" + onlineStatusResp.getErrorInfo() + onlineStatusResp.getErrorCode());
            }
            GetOnlineStatusRespBo result = new GetOnlineStatusRespBo();
            List<GetOnlineStatusRespBo.OnlineItem> results = new ArrayList<>();

            for (TencentResultResp.QueryResultItem queryResultItem : onlineStatusResp.getQueryResult()) {
                GetOnlineStatusRespBo.OnlineItem onlineItem = new GetOnlineStatusRespBo.OnlineItem();
                onlineItem.setOuterUserId(queryResultItem.getToAccount());
                onlineItem.setStatus(queryResultItem.getStatus());
                results.add(onlineItem);
            }
            result.setResults(results);
            return result;
        } catch (BusException e) {
            throw new BusException("查询账号状态异常，请求参数={}" + JSON.toJSONString(req) + "腾讯请求参数=｛｝，" + JSON.toJSONString(req));
        }
    }

    /**
     * 导入单聊消息
     *
     * @param req
     */
    @Override
    public Integer importMsgToSingle(ImportMsgToSingleBo req) {
        ImInfoBo imInfoBo = super.returnImInfo(req.getTenantId(), req.getImType());

        if (ObjectUtils.isEmpty(imInfoBo)) {
            return null;
        }

        GenerateImSignBo generateImSignBo = super.returnGenerateImSignBean(imInfoBo, imInfoBo.getAdminAccountIds().get(0));

        //管理员签名
        String sign = super.generateSign(generateImSignBo);
        if (StringUtils.isEmpty(sign)) {
            return null;
        }

        ImportMsgToSingleReq importMsgToSingleReq = new ImportMsgToSingleReq();
        importMsgToSingleReq.setSyncFromOldSystem(req.getSyncFromOldSystem());
        importMsgToSingleReq.setFromAccount(req.getFromAccount());
        importMsgToSingleReq.setToAccount(req.getToAccount());
        importMsgToSingleReq.setMsgSeq(req.getMsgSeq());
        importMsgToSingleReq.setMsgRandom(req.getMsgRandom());
        importMsgToSingleReq.setMsgTimeStamp(req.getMsgTimeStamp());

        List<ImportMsgToSingleReq.MsgBodyItem> msgBody = new ArrayList<>();

        for (ImportMsgToSingleBo.MsgBodyItem msgBodyItem : req.getMsgBody()) {
            ImportMsgToSingleReq.MsgBodyItem msgBodyItemTmp = new ImportMsgToSingleReq.MsgBodyItem();
            msgBodyItemTmp.setMsgType(msgBodyItem.getMsgType());
            MsgContentItem msgContentItem = new MsgContentItem();
            msgContentItem.setText(msgBodyItem.getMsgContent().getText());
            msgBodyItemTmp.setMsgContent(msgContentItem);
            msgBody.add(msgBodyItemTmp);
        }

        importMsgToSingleReq.setMsgBody(msgBody);

        TencentResultResp onlineStatusResp = super.questTencentInterFace(TencentInterFaceTypeEnum.IMPORT_MSG_TO_SINGLE, importMsgToSingleReq, imInfoBo, sign);
        if (ObjectUtils.isEmpty(onlineStatusResp)) {
            log.error("导入单聊异常，消息内容=" + JSON.toJSONString(req) + "请求腾讯接口入参=" + JSON.toJSON(importMsgToSingleReq));
            return null;
        }
        if (!ActionStatusEnum.OK.type.equals(onlineStatusResp.getActionStatus())) {
            throw new BusException("导入单聊异常，错误信息:" + onlineStatusResp.getErrorInfo() + onlineStatusResp.getErrorCode());
        }
        return 1;
    }

}
