package com.wanshifu.manager.tencentBase.impl;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;


import com.alibaba.fastjson.JSONObject;
import com.wanshifu.constant.TencentConstant;
import com.wanshifu.constant.TencentRedisConstant;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.iop.im.api.req.AccountImportReq;
import com.wanshifu.iop.im.api.req.tencent.AddMemberToGroupTencentReq;
import com.wanshifu.iop.im.api.req.tencent.CreateGroupReq;
import com.wanshifu.iop.im.api.req.tencent.DelMemberFromGroupTencentReq;
import com.wanshifu.iop.im.api.req.tencent.DeleteFriendShipReq;
import com.wanshifu.iop.im.api.req.tencent.DestroyGroupReq;
import com.wanshifu.iop.im.api.req.tencent.GetOnlineStatusReq;
import com.wanshifu.iop.im.api.req.tencent.ImportMsgToGroupReq;
import com.wanshifu.iop.im.api.req.tencent.ImportMsgToSingleReq;
import com.wanshifu.iop.im.api.req.tencent.ModifyMsgSingleTencentReq;
import com.wanshifu.iop.im.api.req.tencent.SendCustomerMsgPersonalReq;
import com.wanshifu.iop.im.api.req.tencent.SendMsgGroupReq;
import com.wanshifu.iop.im.api.req.tencent.SendMsgPersonalReq;
import com.wanshifu.iop.im.api.resp.tencent.TencentResultResp;
import com.wanshifu.iop.im.domain.bo.GenerateImSignBo;
import com.wanshifu.iop.im.domain.bo.ImInfoBo;
import com.wanshifu.iop.im.domain.enums.ImTypeEnum;
import com.wanshifu.iop.im.domain.enums.TencentInterFaceTypeEnum;
import com.wanshifu.iop.im.domain.po.ImAdmin;
import com.wanshifu.iop.im.domain.po.ImConfig;
import com.wanshifu.iop.im.domain.po.ImGroupUserRelation;
import com.wanshifu.iop.im.domain.po.TenantInfo;
import com.wanshifu.repository.ImAdminRepository;
import com.wanshifu.repository.ImConfigRepository;
import com.wanshifu.repository.ImGroupRepository;
import com.wanshifu.repository.ImGroupUserRelationRepository;
import com.wanshifu.repository.TenantInfoRepository;
import com.wanshifu.sdk.GuidServiceApi;
import com.wanshifu.sdk.TencentServiceApi;
import com.wanshifu.utils.MathUtil;
import com.wanshifu.utils.TencentUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public abstract class BaseManager {

    @Resource
    public TencentServiceApi tencentServiceApi;

    @Resource
    public TenantInfoRepository tenantInfoRepository;

    @Resource
    public ImConfigRepository imConfigRepository;

    @Resource
    public ImAdminRepository imAdminRepository;

    @Resource
    public RedisHelper redisHelper;

    @Resource
    private GuidServiceApi guidServiceApi;

    @Resource
    public ImGroupRepository imGroupRepository;

    @Resource
    public ImGroupUserRelationRepository imGroupUserRelationRepository;


    /**
     * 获取guid，
     *
     * */
    public String generateOuterUserId(){
        Long guid = guidServiceApi.getGuid();
        return String.format(TencentConstant.OUTER_USER_FORMAT, guid);

    }

    /**
     * 通过租户id，im类型查询应用id+密钥+管理员信息
     */
    public ImInfoBo returnImInfo(Long tenantId, String imType) {
        String nameByType = ImTypeEnum.getNameByType(imType);
        if (StringUtils.isEmpty(nameByType)) {
            log.error("获取im信息入参错误，租户id：" + tenantId + "，im类型：" + imType);
            return null;
        }

        String cacheKey = String.format(TencentRedisConstant.IM_INFO_KEY, tenantId, imType);
        String cacheValue = redisHelper.get(cacheKey);
        if (!StringUtils.isEmpty(cacheValue)) {
            return JSONObject.parseObject(cacheValue, ImInfoBo.class);
        }
        TenantInfo tenantInfoByTenantId = tenantInfoRepository.getTenantInfoByTenantId(tenantId);
        if(ObjectUtils.isEmpty(tenantInfoByTenantId)){
            log.error("查询不到租户信息，租户id：" + tenantId + "，im类型：" + imType);
            return null;
        }

        ImConfig imConfigInfo = imConfigRepository.getImConfigInfo(tenantId, imType);
        if(ObjectUtils.isEmpty(imConfigInfo)){
            log.error("查询不到im配置信息，租户id：" + tenantId + "，im类型：" + imType);
            return null;
        }
        Long imId = imConfigInfo.getImId();
        List<ImAdmin> imAdminByImIdList = imAdminRepository.getImAdminByImId(imId);
        if(CollectionUtils.isEmpty(imAdminByImIdList)){
            log.error("查询不到im配置管理员信息，租户id：" + tenantId + "，im类型：" + imType);
            return null;
        }

        ImInfoBo result = new ImInfoBo();
        result.setImId(imId);
        result.setImThirdId(imConfigInfo.getImThirdId());
        result.setImThirdSecret(imConfigInfo.getImThirdSecret());
        result.setAdminAccountIds(imAdminByImIdList.stream().map(ImAdmin::getAdminAccountId).collect(Collectors.toList()));

        redisHelper.set(cacheKey,JSONObject.toJSONString(result),TencentRedisConstant.IM_INFO_KEY_EXPIRE);
        return result;
    }


    /**
     * 计算签名
     * */
    public String generateSign(GenerateImSignBo generateImSignBo){
        try{
            TencentUtil tencentUtil = new TencentUtil(Long.valueOf(generateImSignBo.getSdkappid()), generateImSignBo.getImSecret());
            return tencentUtil.getUserSig(generateImSignBo.getOuterUserId(), generateImSignBo.getExpireTime());
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 组装签名入参
     * */
    public GenerateImSignBo returnGenerateImSignBean(ImInfoBo imInfoBo, String outerUserId) {
        GenerateImSignBo generateImSignBo = new GenerateImSignBo();

        Date nowDate = new Date();
        //1年后
        Date oneYearAfterNow = DateUtils.addYears(nowDate, TencentConstant.SIGN_EXPIRE_TIME);

        //一年的秒数，必须要计算，因为有些是31天 有些是29天
        long signExpireTime = (oneYearAfterNow.getTime() - nowDate.getTime()) / 1000;
        generateImSignBo.setExpireTime(signExpireTime);
        generateImSignBo.setOuterUserId(outerUserId);
        generateImSignBo.setSdkappid(imInfoBo.getImThirdId());
        generateImSignBo.setImSecret(imInfoBo.getImThirdSecret());
        return generateImSignBo;
    }

    /**
     * 腾讯接口封装
     * */
    public TencentResultResp questTencentInterFace(TencentInterFaceTypeEnum tencentInterFaceTypeEnum,
                                                   Object object,
                                                   ImInfoBo imInfoBo,
                                                   String userSign){

        if(ObjectUtils.isEmpty(tencentInterFaceTypeEnum)){
            return null;
        }

        if(ObjectUtils.isEmpty(object)){
            return null;
        }
        log.info("请求腾讯接口参数, body={}", JSON.toJSONString(object));
        switch (tencentInterFaceTypeEnum){

            case ACCOUNT_IMPORT:
                AccountImportReq accountImportReq = (AccountImportReq) object;

                TencentResultResp tencentResultResp = tencentServiceApi.accountImport(
                        accountImportReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );
                return tencentResultResp;

            case CREATE_GROUP:
                CreateGroupReq createGroupReq = (CreateGroupReq) object;
                return tencentServiceApi.createGroup(
                        createGroupReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );

            case SEND_MSG_PERSONAL:
                SendMsgPersonalReq sendMsgPersonalReq = (SendMsgPersonalReq) object;
                log.info("请求腾讯发送普通信息参数, body={}", JSON.toJSONString(sendMsgPersonalReq));
                return tencentServiceApi.sendMsgPersonal(
                        sendMsgPersonalReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );
            case SEND_CUSTOMER_MSG_PERSONAL:
                SendCustomerMsgPersonalReq sendCustomerMsgPersonalReq = (SendCustomerMsgPersonalReq) object;
                return tencentServiceApi.sendMsgPersonal(
                        sendCustomerMsgPersonalReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );
            case SEND_MSG_GROUP:
                SendMsgGroupReq sendMsgGroupReq = (SendMsgGroupReq) object;
                return tencentServiceApi.sendMsgGroup(
                        sendMsgGroupReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );

            case IMPORT_MSG_TO_GROUP:
                ImportMsgToGroupReq importMsgToGroupReq = (ImportMsgToGroupReq) object;
                return tencentServiceApi.importMsgToGroup(
                        importMsgToGroupReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );
            case DESTROY_GROUP:
                DestroyGroupReq destroyGroupReq = (DestroyGroupReq) object;
                return tencentServiceApi.destroyGroup(
                        destroyGroupReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );
            case ONLINE_STATUS:
                GetOnlineStatusReq getOnlineStatusReq = (GetOnlineStatusReq) object;
                return tencentServiceApi.getOnlineStatus(
                        getOnlineStatusReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );
            case IMPORT_MSG_TO_SINGLE:
                ImportMsgToSingleReq importMsgToSingleReq = (ImportMsgToSingleReq) object;
                return tencentServiceApi.importMsgToSingle(
                        importMsgToSingleReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );
            case ADD_MEMBER_TO_GROUP:
                AddMemberToGroupTencentReq addMemberToGroupTencentReq = (AddMemberToGroupTencentReq) object;
                return tencentServiceApi.addGroupMember(
                        addMemberToGroupTencentReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );
            case DEL_MEMBER_FROM_GROUP:
                DelMemberFromGroupTencentReq delMemberFromGroupTencentReq = (DelMemberFromGroupTencentReq) object;
                return tencentServiceApi.deleteGroupMember(
                        delMemberFromGroupTencentReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );
            case MODIFY_MSG_SINGLE:
                ModifyMsgSingleTencentReq modifyMsgSingleTencentReq = (ModifyMsgSingleTencentReq) object;
                return tencentServiceApi.modifyMsgSingle(
                        modifyMsgSingleTencentReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );
            case DELETE_FRIEND_SHIP:
                DeleteFriendShipReq deleteFriendShipReq = (DeleteFriendShipReq) object;
                return tencentServiceApi.deleteFriendShip(
                        deleteFriendShipReq,
                        imInfoBo.getImThirdId(),
                        imInfoBo.getAdminAccountIds().get(0),
                        userSign,
                        MathUtil.generateCustomerLength(32) + "",
                        TencentConstant.REQUEST_CONTENT_TYPE
                );

            default:
                return null;
        }
    }



}
