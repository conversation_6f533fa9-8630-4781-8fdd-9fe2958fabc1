package com.wanshifu.manager.tencentBase;


import com.wanshifu.iop.im.api.resp.tencent.TencentCallBackResp;
import com.wanshifu.iop.im.domain.bo.CreateGroupReqBo;
import com.wanshifu.iop.im.domain.bo.DestroyGroupBo;
import com.wanshifu.iop.im.domain.bo.GetOnlineStatusBo;
import com.wanshifu.iop.im.domain.bo.GetOnlineStatusRespBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToGroupBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToGroupRespBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToSingleBo;
import com.wanshifu.iop.im.domain.bo.RegisterUserReqBo;
import com.wanshifu.iop.im.domain.bo.RegisterUserRespBo;
import com.wanshifu.iop.im.domain.bo.SendCustomerMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgGroupBo;
import com.wanshifu.iop.im.domain.bo.SendMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgToPersonalResultBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.AddMemberToGroupBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.DelMemberFromGroupBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.DeleteFriendShipBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.UpdateMsgSingleBo;

public interface TencentManager {


    /**
     * 注册用户
     * 返回用户外部id+加密签名
     * */
    RegisterUserRespBo registerUser(RegisterUserReqBo req);

    /**
     * 创建群聊
     * */
    String createGroup(CreateGroupReqBo req);


    /**
     * 向个人发送消息
     * */
    SendMsgToPersonalResultBo sendMsgToPersonal(SendMsgPersonalBo req);

    /**
     * 向群里发送消息
     * */
    Integer sendMsgToGroup(SendMsgGroupBo req);

    /**
     * 向群里导入消息
     * 一次最多导入七条
     * */
    ImportMsgToGroupRespBo importMsgToGroup(ImportMsgToGroupBo req);

    /**
     * 回调，场景：
     * 1、发送消息后
     * 2、拉群后
     * 3、群聊新增成员后
     * 4、消息已读后
     * */
    TencentCallBackResp tencentCallBack(Object object, String callbackType);

    /**
     * 解散群聊
     * */
    Integer destroyGroup(DestroyGroupBo req);

    /**
     * 查询账号在线状态
     * */
    GetOnlineStatusRespBo getOnlineStatus(GetOnlineStatusBo req);

    /**
     * 导入单聊消息
     * */
    Integer importMsgToSingle(ImportMsgToSingleBo req);

    /**
     * 发送自定义消息
     * */
    SendMsgToPersonalResultBo sendCustomerMsgToPersonal(SendCustomerMsgPersonalBo req);

    /**
     * 向群中添加用户
     * */
    Boolean addMemberToGroup(AddMemberToGroupBo req);


    /**
     * 从群中移除用户
     * */
    Boolean delMemberFromGroup(DelMemberFromGroupBo req);

    /**
     * 修改单聊消息
     * */
    Boolean updateMsgSingle(UpdateMsgSingleBo req);

    /**
     * 删除好友关系
     * */
    Boolean deleteFriendShip(DeleteFriendShipBo req);

}
