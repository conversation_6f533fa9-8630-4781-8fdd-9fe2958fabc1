package com.wanshifu.components.xxl;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum XxlJobPathEnum {

    LOGIN("/login", "登录"),
    ADD("/jobinfo/add", "添加Job"),
    UPDATE("/jobinfo/update", "更新Job"),
    REMOVE("/jobinfo/remove", "删除Job"),
    PAGE_JOB("/jobinfo/pageList", "查询Job"),
    PAGE_GROUP("/jobgroup/pageList", "查询Job组"),
    START("/jobinfo/start", "开启Job"),
    STOP("/jobinfo/stop", "停止Job")
    ;

    private final String path;
    private final String desc;
}
