package com.wanshifu.components.xxl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.HttpCookie;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * https://blog.csdn.net/weixin_44768189/article/details/117091536?spm=1001.2101.3001.6650.17&utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7Edefault-17.no_search_link&depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7Edefault-17.no_search_link
 */
@Component
public class XxlJobTemplate {


    @Value("${xxl.job.admin.addresses.v2}")
    private String addressesV2;

    @Value("${xxl.job.admin.username.v2}")
    private String usernameV2;


    @Value("${xxl.job.admin.password.v2}")
    private String passwordV2;

    @Value("${xxl.job.executor.route.strategy.v2}")
    private String executorRouteStrategyV2;

    private String cookie;

    /**
     * 新版本xxljob的cookie
     */
    private String cookieV2;

    /**
     * 查询执行器
     *
     * @param appname
     * @param title
     * @return
     */
    public List<XxlJobGroup> listGroup(String appname, String title, Integer jobType) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appname", appname);
        paramMap.put("title", title);

        JSONObject pageGroup = doRequest(XxlJobPathEnum.PAGE_GROUP, paramMap, jobType);

        List<XxlJobGroup> jobGroupList = JSONUtil.toList(pageGroup.getJSONArray("data"), XxlJobGroup.class);
        return jobGroupList;
    }

    /**
     * 查询job列表
     *
     * @param jobGroup        -1
     * @param triggerStatus   -1
     * @param jobDesc
     * @param executorHandler
     * @param author
     * @return
     */
    public List<XxlJobInfo> listJob(int jobGroup, int triggerStatus, String jobDesc, String executorHandler, String author, Integer jobType) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("jobGroup", jobGroup);
        paramMap.put("triggerStatus", triggerStatus);
        paramMap.put("jobDesc", jobDesc);
        paramMap.put("executorHandler", executorHandler);
        paramMap.put("author", author);

        JSONObject pageJob = doRequest(XxlJobPathEnum.PAGE_JOB, paramMap, jobType);

        List<XxlJobInfo> jobList = JSONUtil.toList(pageJob.getJSONArray("data"), XxlJobInfo.class);
        return jobList;
    }

    /**
     * 条件查询一个Job
     *
     * @param jobGroup        -1
     * @param triggerStatus   -1
     * @param jobDesc
     * @param executorHandler
     * @param author
     * @return
     */
    public XxlJobInfo getOneJob(int jobGroup, int triggerStatus, String jobDesc, String executorHandler, String author, Integer jobType) {
        List<XxlJobInfo> xxlJobInfos = listJob(jobGroup, triggerStatus, jobDesc, executorHandler, author, jobType);

        if (xxlJobInfos.size() > 1) {
            throw new RuntimeException(String.format("xxl-job-admin任务查询结果不唯一:%s", xxlJobInfos.toString()));
        }

        return xxlJobInfos.isEmpty() ? null : xxlJobInfos.get(0);
    }

    /**
     * 添加job
     *
     * @param addJob
     * @return
     */
    public Integer addJobAndStart(AddXxlJob addJob) {
        Map<String, Object> paramMap = new HashMap<>();

        //不传scheduleType也默认为固定间隔
        paramMap.put("scheduleType", "FIX_RATE");
        paramMap.put("scheduleConf", addJob.getIntervalSecond());
        paramMap.put("misfireStrategy", "DO_NOTHING");
        paramMap.put("executorRouteStrategy", executorRouteStrategyV2);

        paramMap.put("jobGroup", addJob.getJobGroup());
        paramMap.put("jobDesc", addJob.getJobDesc());
        paramMap.put("author", addJob.getAuthor());
        paramMap.put("executorHandler", addJob.getExecutorHandler());
        paramMap.put("executorParam", addJob.getExecutorParam());
        paramMap.put("executorBlockStrategy", "SERIAL_EXECUTION");

        paramMap.put("executorTimeout", 0);
        paramMap.put("executorFailRetryCount", 0);
        paramMap.put("glueType", "BEAN");
        paramMap.put("glueSource", "");
        paramMap.put("glueRemark", "GLUE代码初始化");
        paramMap.put("glueUpdatetime", null);
        paramMap.put("childJobId", "");
        paramMap.put("triggerStatus", 0);
        paramMap.put("triggerLastTime", 0);
        paramMap.put("triggerNextTime", 0);

        //添加任务
        JSONObject result = doRequest(XxlJobPathEnum.ADD, paramMap, 1);
        //开始任务
        this.startJob(result.getInt("content"), 1);

        return result.getInt("content");
    }


    public Integer startJob(Integer id, Integer jobType) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        JSONObject result = doRequest(XxlJobPathEnum.START, paramMap, jobType);
        return result.getInt("content");
    }

    public Integer stopJob(Integer id, Integer jobType) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        JSONObject result = doRequest(XxlJobPathEnum.STOP, paramMap, jobType);
        return result.getInt("content");
    }

    /**
     * 更新job
     *
     * @param updateXxlJob
     */
    public void updateJob(UpdateXxlJob updateXxlJob, Integer jobType) {
        updateJob(JSONUtil.parseObj(updateXxlJob), jobType);
    }

    private void updateJob(Map<String, Object> paramMap, Integer jobType) {
        JSONObject result = doRequest(XxlJobPathEnum.UPDATE, paramMap, jobType);

        if (HttpStatus.HTTP_OK != result.getInt("code")) {
            throw new RuntimeException(String.format("xxl-job-admin更新Job失败:%s", result.getStr("msg")));
        }
    }

    /**
     * 删除job
     *
     * @param jobId
     */
    public void removeJob(int jobId, Integer jobType) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", jobId);

        doRequest(XxlJobPathEnum.REMOVE, paramMap, jobType);
    }

    /**
     * 获取登录cookie
     *
     * @return
     */
    private String getCookie(Integer jobType) {
        Map<String, Object> paramsMap = new HashMap();
        String address = addressesV2;
        String factUserName = usernameV2;
        String factPassword = passwordV2;

        paramsMap.put("userName", factUserName);
        paramsMap.put("password", factPassword);
        HttpResponse response = HttpRequest.post(String.format("%s%s", address, XxlJobPathEnum.LOGIN.getPath()))
                .form(paramsMap).execute();
        if (HttpStatus.HTTP_OK != response.getStatus()) {
            throw new RuntimeException(String.format("xxl-job-admin登录失败:statusCode=%s", response.getStatus()));
        }

        List<HttpCookie> cookies = response.getCookies();

        if (cookies.isEmpty()) {
            throw new RuntimeException(String.format("xxl-job-admin登录失败:[userName=%s,password=%s]", factUserName, factPassword));
        }

        return cookies.stream().map(cookie -> cookie.toString()).collect(Collectors.joining());
    }

    /**
     * 远程调用xxl-job-admin
     *
     * @param xxlJobPathEnum
     * @param paramMap
     * @param jobType xxljob类型[0:老版本,1:新版本]
     * @return
     */
    private JSONObject doRequest(XxlJobPathEnum xxlJobPathEnum, Map<String, Object> paramMap, Integer jobType) {
        String factCookie;
        if(JobTypeEnum.NEW.code.equals(jobType)){
            if (StringUtils.isBlank(cookieV2)) {
                cookieV2 = getCookie(jobType);
            }
            factCookie = cookieV2;
        } else {
            if (StringUtils.isBlank(cookie)) {
                cookie = getCookie(jobType);
            }
            factCookie = cookie;
        }
        String address = addressesV2;
        HttpResponse response = HttpRequest.post(String.format("%s%s", address, xxlJobPathEnum.getPath()))
                .cookie(factCookie).form(paramMap).execute();
        if (HttpStatus.HTTP_OK != response.getStatus()) {
            throw new RuntimeException(String.format("xxl-job-admin%s请求失败:statusCode=%s",
                    xxlJobPathEnum.getDesc(), response.getStatus()));
        }

        JSONObject result = JSONUtil.parseObj(response.body());

        Integer code = result.getInt("code");
        if (code != null && HttpStatus.HTTP_OK != code) {
            throw new RuntimeException(String.format("xxl-job-admin%s失败:msg=%s", xxlJobPathEnum.getDesc(), result.getStr("msg")));
        }

        return result;
    }
}
