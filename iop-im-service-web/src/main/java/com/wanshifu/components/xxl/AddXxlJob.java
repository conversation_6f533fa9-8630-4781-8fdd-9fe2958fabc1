package com.wanshifu.components.xxl;

import lombok.Data;
import lombok.experimental.Accessors;
/**
 * 添加Job
 */
@Data
@Accessors(chain = true)
public class AddXxlJob {

    /** 执行器主键ID*/
    private int jobGroup;
    /** job描述*/
    private String jobDesc;

    private String executorRouteStrategy;
    /** 负责人*/
    private String author;
    /** 调度类型*/
    private String jobCron;
    /** 执行器，任务Handler名称*/
    private String executorHandler;
    /** 任务参数*/
    private String executorParam = "";
    /** 固定速率秒速*/
    private int intervalSecond;
    /** 定时类型[FIX_RATE:固定时间间隔,CRON:cron表达式]*/
    private String scheduleType;
}
