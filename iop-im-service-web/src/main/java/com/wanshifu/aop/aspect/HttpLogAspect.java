package com.wanshifu.aop.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.wanshifu.aop.annotation.LogOperate;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.po.OperateLog;
import com.wanshifu.repository.OperateLogRepository;
import com.wanshifu.utils.BaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.persistence.Table;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Objects;

/**
 * 日志操作注解
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Aspect
@Component
@Slf4j
public class HttpLogAspect {

    @Resource
    private OperateLogRepository operateLogRepository;

    @Around("execution(* com.wanshifu.controller..*.*(..)) || @annotation(com.wanshifu.aop.annotation.LogOperate) ")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;

        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();

        try {
            // 跳过查询类方法
            if (isQueryMethod(methodName, request)) {
                // 直接执行，不记录日志
                return joinPoint.proceed();
            }

            // 执行原方法
            Object result = joinPoint.proceed();
            Object[] args = joinPoint.getArgs();
            // 没有入参不打印日志
            if(args == null || args.length == 0) {
                return result;
            }

            // 并记录日志
            try {
                LogOperate logOperate = getAnnotationFromMethod(joinPoint);
                // 有标记注解的方法才打印日志
                if (Objects.nonNull(logOperate)) {

                    // 构建日志内容 取第一个参数
                    String params = JSONObject.toJSONString(args[0]);

                    log.info("请求方法: {}.{} 耗时: {}ms", className, methodName, System.currentTimeMillis() - startTime);
                    // 从请求参数中 ID
                    Long businessId = extractLongValueFromParams(params, logOperate.idField());

                    //  logOperate.businessType() 是新增的时候, 主键在实现方法中生成 因此没有值 TODO 这里需要编码规范
                    if (! BaseUtil.hasLongValue(businessId)) {
                        //  从返回值中提取业务 ID
                        businessId = extractBusinessIdFromResponse(result);
                    }
                    // 获取到业务ID才记录到日志表
                    if(BaseUtil.hasLongValue(businessId)) {
                        Long operatorId = extractLongValueFromParams(params, logOperate.operatorIdField());
                        insertOperateLog(businessId, logOperate.businessClass(), logOperate.businessType(), params, operatorId);
                    }
                }
            } catch (NoSuchMethodException e) {
                // 打印日志出错不能影响业务方法执行
                log.error("获取方法注解异常", e);
            }

            return result;
        } catch (Exception e) {
            log.error("接口调用异常", e);
            throw e;
        }
    }

    /**
     * 从请求参数 JSON 字符串中提取业务 ID
     */
    private Long extractLongValueFromParams(String paramsJson, String idFieldName) {
        if (StringUtils.isBlank(paramsJson) || StringUtils.isBlank(idFieldName)) {
            return null;
        }

        try {
            // 判断是 JSONArray 还是 JSONObject
            Object json = JSON.parse(paramsJson);
            if (json instanceof JSONArray) {
                JSONArray array = (JSONArray) json;
                if (!array.isEmpty()) {
                    // 取第一个元素处理，适用于批量操作场景
                    Object firstItem = array.get(0);
                    if (firstItem instanceof JSONObject) {
                        Long value = getaLong(((JSONObject) firstItem), idFieldName);
                        if (value != null) return value;
                    }
                }
            } else if (json instanceof JSONObject) {
                Long value = getaLong(((JSONObject) json), idFieldName);
                if (value != null) return value;
            }
        } catch (Exception e) {
            log.error("从请求参数 JSON 获取字段值异常", e);
        }

        return null;
    }

    private static Long getaLong(JSONObject firstItem, String idFieldName) {
        Object value = firstItem.get(idFieldName);
        if (value != null) {
            if (value instanceof Number) {
                return ((Number) value).longValue();
            } else if (value instanceof String && !((String) value).isEmpty()) {
                return Long.valueOf((String) value);
            }
        }
        return null;
    }

    private String getTableName(Class<?> clazz) {
        // 检查类是否被 @Table 注解修饰
        if (clazz.isAnnotationPresent(Table.class)) {
            Table tableAnnotation = clazz.getAnnotation(Table.class);
            return tableAnnotation.name();
        }
        return "";
    }


    /**
     * 插入操作日志
     */
    protected void insertOperateLog(Long businessId, Class<?> businessClass, String businessType, String content, Long operatorId) {
        OperateLog logEntry = new OperateLog();
        logEntry.setFromTableId(businessId);
        logEntry.setFromTableName(this.getTableName(businessClass));
        logEntry.setFromBusiness(businessType);
        logEntry.setOperateContent(content);
        logEntry.setOperatorId(operatorId);
        logEntry.setIsDelete(0);
        logEntry.setCreateTime(new Date());
        logEntry.setUpdateTime(new Date());

        operateLogRepository.insertSelective(logEntry);
    }


    /**
     * 获取方法上的指定注解
     */
    private LogOperate getAnnotationFromMethod(ProceedingJoinPoint joinPoint) throws NoSuchMethodException {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getAnnotation(LogOperate.class);
    }


    /**
     * 判断是否为查询类方法（可根据命名规范或 HTTP 方法定义）
     */
    private boolean isQueryMethod(String methodName, HttpServletRequest request) {
        // 方式一：通过方法名判断（如 getXXX, queryXXX, listXXX）
        if (methodName.startsWith("get") ||
                methodName.startsWith("query") ||
                methodName.startsWith("enums") ||
                methodName.startsWith("list") ||
                methodName.startsWith("search")) {
            return true;
        }

        // 方式二：通过 HTTP Method 判断（可选）
        return request != null && "GET".equalsIgnoreCase(request.getMethod());
    }


    /**
     * 从响应体中提取业务 ID
     */
    private <T> T extractBusinessIdFromResponse(Object result) {
        if (result == null) return null;

        // 返回参数是 Long 或 Integer 类型时，直接返回
        if (result instanceof Long) {
            return (T) result;
        } else if (result instanceof Integer) {
            return (T) (Long.valueOf(((Integer) result)));
        }

        try {
            // 假设响应体是一个包含 id 字段的 JSON 对象
            String responseJson = JSONObject.toJSONString(result);
            JSONObject response = JSONObject.parseObject(responseJson);
            Object idValue = getaLong(response, "id");
            if (idValue != null) return (T) idValue;
        } catch (Exception ignored) {
        }
        return null;
    }

}
