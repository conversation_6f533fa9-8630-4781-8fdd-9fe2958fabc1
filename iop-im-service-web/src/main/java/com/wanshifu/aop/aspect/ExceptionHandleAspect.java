package com.wanshifu.aop;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.utils.FeishuUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * <AUTHOR>
 */
@Aspect
@Component
public class ExceptionHandleAspect {

    @Value("${spring.application.name}")
    private String applicationName;

    @Resource
    private FeishuUtils feishuUtils;


    //    @Pointcut("execution(* *(..))")
    @Pointcut("execution(* com.wanshifu.service..*.*(..)) " +
            "|| execution(* com.wanshifu.manager..*.*(..)) "
    )
    public void myPointcut() {

    }

    @AfterThrowing(value = "myPointcut()", throwing = "exception")
    public void mylogger(JoinPoint joinPoint, Exception exception) {
        StringWriter sw = new StringWriter();
        exception.printStackTrace(new PrintWriter(sw, true));
        Object[] args = joinPoint.getArgs();
        String url = "";
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            url = request.getRequestURL().toString();
        }
        String params = args != null && args.length > 0 ? JSONObject.toJSONString(args[0]) : "";
        String methodName = joinPoint.getSignature().getName();
        String fullStackTrace = ExceptionUtils.getFullStackTrace(exception).substring(0, 3000);
        //飞书提醒开关
       // boolean flag = exception instanceof BusException || exception instanceof BusinessException;
        boolean flag = exception instanceof BusinessException;
        if (!flag) {
            String format = String.format("\n【异常通知】 \n\n【服务】 %s\n\n【URL】 %s\n\n【方法】 %s\n\n【参数】 %s\n\n【原因】 %s",
                    applicationName, url, methodName, params, fullStackTrace);
            feishuUtils.sendErrorInfoFeishu(format);
        }
    }

}
