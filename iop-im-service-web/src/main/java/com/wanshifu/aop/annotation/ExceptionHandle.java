package com.wanshifu.aop.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 异常处理自定义注解
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON>@wshifu.com
 * @Date: 2022-06-24 15:18
 * @Description:
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExceptionHandle {

    /**
     * 备注说明
     * @return
     */
    String note() default "";

}
