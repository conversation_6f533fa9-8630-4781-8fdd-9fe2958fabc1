package com.wanshifu.aop.annotation;

import java.lang.annotation.*;

/**
 * 日志操作注解
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogOperate {
    Class<?> businessClass(); // 操作对应的业务类[多个表时取主表]，如 Tags.class

    /**
     * 操作类型
     * @see <a href="com.wanshifu.iop.im.domain.enums.OperateLogFromBusinessEnum">OperateLogFromBusinessEnum</a>
     */
    String businessType();
    String idField() default "id"; // ID 字段名，默认为 id
    String operatorIdField() default "operatorId"; // 操作人 字段名，默认为 operatorId
}