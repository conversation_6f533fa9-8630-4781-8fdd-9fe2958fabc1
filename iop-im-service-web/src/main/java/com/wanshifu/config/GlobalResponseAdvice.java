package com.wanshifu.config;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.util.http.HttpUtil;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.bean.ApiResult;
import com.wanshifu.framework.utils.Http;
import com.wanshifu.iop.im.api.resp.tencent.TencentCallBackResp;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


@ControllerAdvice
public class GlobalResponseAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        return true; // 对所有Controller生效
    }


    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType,
                                  MediaType selectedContentType,
                                  Class selectedConverterType,
                                  ServerHttpRequest request,
                                  ServerHttpResponse response) {
        String path = request.getURI().getPath();
        if(CommonConstant.TENCENT_CALL_BACK_URL.equals(path)) {
            // 修改响应结构（示例：包装为统一格式）
            String string = JSON.toJSONString(body);

            ApiResult apiResult = JSONObject.parseObject(string, ApiResult.class);
            TencentCallBackResp tencentCallBackResp = new TencentCallBackResp();
            tencentCallBackResp.setErrorInfo("");
            tencentCallBackResp.setErrorCode(0);
            tencentCallBackResp.setActionStatus(CommonConstant.ACTION_STATUS_NO);
            if ("200".equals(apiResult.getRetCode())) {

                Object retData = apiResult.getRetData();
                String jsonString = JSON.toJSONString(retData);
                try{
                    TencentCallBackResp tencentCallBackResp1 = JSONObject.parseObject(jsonString, TencentCallBackResp.class);
                    if(tencentCallBackResp1.getActionStatus().equals(CommonConstant.ACTION_STATUS_OK)){
                        tencentCallBackResp.setActionStatus(CommonConstant.ACTION_STATUS_OK);
                    }
                }catch (Exception e){
                    throw new BusException("返回值解析异常");
                }
            }
            Map<String, Object> data = new HashMap<>();
            data.put("ActionStatus", tencentCallBackResp.getActionStatus());
            data.put("ErrorInfo", tencentCallBackResp.getErrorInfo());
            data.put("ErrorCode", tencentCallBackResp.getErrorCode());
            return data;
        }
        return body;
    }
}