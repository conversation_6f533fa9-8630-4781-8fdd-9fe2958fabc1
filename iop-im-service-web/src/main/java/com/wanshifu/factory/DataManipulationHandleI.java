package com.wanshifu.factory;

import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.CommonRunEnumsResp;
import com.wanshifu.iop.im.api.resp.CommonRunStrategyResp;
import com.wanshifu.iop.im.api.resp.ResultResp;

import java.util.List;

public interface DataManipulationHandleI {

    /**
     * 验证重复名称
     */
    ResultResp<CommonRunStrategyResp> verifyRepeatName(VerifyRepeatNameReq req);

    /**
     * 切换启用禁用状态
     */
    ResultResp<CommonRunStrategyResp> switchStatus(SwitchStatusReq req);

    /**
     * 获取枚举
     */
    ResultResp<CommonRunEnumsResp> getEnums(GetEnumsReq req);

    /**
     * 获取
     */
    List<Long> checkNonexistent(CheckNonexistentReq req);
}
