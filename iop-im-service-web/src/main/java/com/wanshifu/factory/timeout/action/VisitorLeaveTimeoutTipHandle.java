package com.wanshifu.factory.timeout.action;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.factory.timeout.TimeoutHandleI;
import com.wanshifu.iop.im.api.req.tencent.SendMsgPersonalReq;
import com.wanshifu.iop.im.domain.bo.ImConversationBeanBo;
import com.wanshifu.iop.im.domain.bo.SendMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgToPersonalResultBo;
import com.wanshifu.iop.im.domain.bo.channel.reply.TimeoutStrategyReqBo;
import com.wanshifu.iop.im.domain.enums.MsgTypeEnum;
import com.wanshifu.iop.im.domain.enums.OnlineStatusEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserClassTypeEnum;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import com.wanshifu.utils.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/23 16:09
 * @description 访客离开超时自动关闭
 */
@Component("visitor_leave_timeout_tip")
@Slf4j
public class VisitorLeaveTimeoutTipHandle extends TimeoutAbstract implements TimeoutHandleI {
    @Override
    public Integer timeoutAction(TimeoutStrategyReqBo timeoutStrategyReqBo) {

        ImConversation conversation = timeoutStrategyReqBo.getConversation();
        String fromOuterUserId = conversation.getFromOuterUserId();

        UserRegisterInfo byOuterUserId = userRegisterInfoRepository.findByOuterUserId(fromOuterUserId);
        if(byOuterUserId==null){
            log.error("用户没有注册，参数错误，req=" + JSON.toJSONString(timeoutStrategyReqBo));
            return 1;
        }
        if(OnlineStatusEnum.ON_LINE.type.equals(byOuterUserId.getOnlineState())){
            log.info("用户在线状态，无需执行");
            return 1;
        }

        int timeoutSecondSet = Integer.parseInt(timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getTimeoutCostSecond());

        if (byOuterUserId.getOnlineStateChangeTime().getTime() + timeoutSecondSet*1000L <= System.currentTimeMillis()) {
            log.info("用户在线状态，还没到执行时间，无需执行");
            return 1;
        }

        //是否已经发送
        boolean sendMsgDone = super.isSendMsgDone(timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getMsgSubType(), timeoutStrategyReqBo.getFromConversationItem().getConversationItemId());
        if (sendMsgDone) {
            log.info("该消息已发送过，不需要重复发送");
            return 1;
        }

        Integer i = 0;
        //先使用坐席账号发送超时访客提示，如果已经是坐席接待，就使用坐席账号发送群聊提醒，如果还没到坐席接待就使用代理账号发送提醒
        if(OuterUserClassTypeEnum.SEAT.type.equals(conversation.getToOuterUserType())){
            i = super.sendMsgToGroup(conversation.getConversationId(), conversation.getToOuterUserId(), timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getAutoReplyContent());
        }else{
            //需要写会话记录，如果还没到坐席账号，就使用代理账号发送单聊消息
            SendMsgToPersonalResultBo sendMsgToPersonalResultBo = super.sendMsgToPersonal(conversation.getAgentOuterUserId(), conversation.getFromOuterUserId(), timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getAutoReplyContent());
            ImConversationBeanBo imConversationBeanBo = new ImConversationBeanBo();
            imConversationBeanBo.setFromAccount(conversation.getAgentOuterUserId());
            imConversationBeanBo.setFromAccountType(conversation.getToOuterUserType());
            imConversationBeanBo.setToAccount(conversation.getFromOuterUserId());
            imConversationBeanBo.setToAccountType(conversation.getFromOuterUserType());
            imConversationBeanBo.setResponseAccount("");
            imConversationBeanBo.setResponseAccountType("");
            imConversationBeanBo.setMsgKey(sendMsgToPersonalResultBo.getMsgKey());
            imConversationBeanBo.setMsgId(sendMsgToPersonalResultBo.getMsgId());

            List<SendMsgPersonalBo.MsgContentItem> msgContentItemList = new ArrayList<>();
            SendMsgPersonalBo.MsgContentItem msgContentItem = new SendMsgPersonalBo.MsgContentItem();
            msgContentItem.setMsgType(MsgTypeEnum.TEXT.type);
            SendMsgPersonalReq.MsgBodyContent msgBodyContent = new SendMsgPersonalReq.MsgBodyContent();
            msgBodyContent.setText(timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getAutoReplyContent());
            msgContentItem.setContent(msgBodyContent);
            msgContentItemList.add(msgContentItem);
            imConversationBeanBo.setMsgSeq(MathUtil.generateCustomerLength(32));
            imConversationBeanBo.setMsgContentJson(JSON.toJSONString(msgContentItemList));
            imConversationBeanBo.setMsgContentItemList(Lists.newArrayList());
            super.returnImConversationItemBean(imConversationBeanBo,conversation.getConversationId());
        }
        super.writeTimeoutRecord(timeoutStrategyReqBo, i, timeoutStrategyReqBo.getFromConversationItem().getConversationItemId());

        // todo 调用会话关闭接口

        return 1;
    }
}
