package com.wanshifu.factory.timeout.action;

import com.alibaba.fastjson.JSON;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.iop.im.api.req.tencent.CallbackSingleSendMsgAfterReq;
import com.wanshifu.iop.im.domain.bo.ImConversationBeanBo;
import com.wanshifu.iop.im.domain.bo.SendMsgGroupBo;
import com.wanshifu.iop.im.domain.bo.SendMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgToPersonalResultBo;
import com.wanshifu.iop.im.domain.bo.channel.reply.TimeoutStrategyReqBo;
import com.wanshifu.iop.im.domain.enums.SuccessAndFailEnum;
import com.wanshifu.iop.im.domain.po.ConversationAutoReplyRecord;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.ImGroup;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.repository.ConversationAutoReplyRecordRepository;
import com.wanshifu.repository.ImConversationRepository;
import com.wanshifu.repository.ImGroupRepository;
import com.wanshifu.repository.UserRegisterInfoRepository;
import com.wanshifu.service.AbstractService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/23 19:20
 * @description TODO
 */
@Slf4j
public abstract class TimeoutAbstract {


    @Resource
    public ConversationAutoReplyRecordRepository conversationAutoReplyRecordRepository;

    @Resource
    public TencentManager tencentManager;

    @Resource
    public ImGroupRepository imGroupRepository;

    @Resource
    public UserRegisterInfoRepository userRegisterInfoRepository;

    /**
     * 查询会话记录，是否发送指定的消息
     * */
    public boolean isSendMsgDone(String msgSubType, Long imConversationItemId) {
        ConversationAutoReplyRecord conversationAutoReplyRecord = conversationAutoReplyRecordRepository.getListByMsgSubTypeAndItemId(msgSubType, imConversationItemId);
        if (conversationAutoReplyRecord != null) {
            return true;
        }
        return false;
    }

    /**
     * 向群聊中发送消息
     * */
    public Integer sendMsgToGroup(Long conversationId, String fromAccount,  String content){

        ImGroup imGroup = imGroupRepository.selectImGroupByConversationId(conversationId);
        if(imGroup==null){
            log.error("群组不存在，conversationId={}", conversationId);
            return 0;
        }
        SendMsgGroupBo sendMsgGroupBo = new SendMsgGroupBo();
        sendMsgGroupBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        sendMsgGroupBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        sendMsgGroupBo.setGroupId(imGroup.getOuterGroupId());
        sendMsgGroupBo.setFromAccountId(fromAccount);
        List<SendMsgGroupBo.MsgListItem> msgList = new ArrayList<>();
        SendMsgGroupBo.MsgListItem msgListItem = new SendMsgGroupBo.MsgListItem();
        msgListItem.setMsgType("Text");
        CallbackSingleSendMsgAfterReq.MsgContent msgContent = new CallbackSingleSendMsgAfterReq.MsgContent();
        msgContent.setText(content);
        msgListItem.setContent(msgContent);
        msgList.add(msgListItem);

        sendMsgGroupBo.setMsgList(msgList);
        Integer i = tencentManager.sendMsgToGroup(sendMsgGroupBo);
        if(i==null){
            log.error("群组发送消息失败，conversationId={}", conversationId);
            return 0;
        }
        return 1;

    }

    /**
     * 写入会话记录Bean生成
     * */
    public ImConversationItem returnImConversationItemBean(ImConversationBeanBo req, Long conversationId){
        ImConversationItem imConversationItem = new ImConversationItem();
        imConversationItem.setConversationId(conversationId);
        imConversationItem.setFromOuterUserId(req.getFromAccount());
        imConversationItem.setFromOuterUserType(req.getFromAccountType());
        imConversationItem.setToOuterUserId(req.getToAccount());
        imConversationItem.setToOuterUserType(req.getToAccountType());
        imConversationItem.setResponseOuterUserId(req.getResponseAccount());
        imConversationItem.setResponseOuterUserType(req.getResponseAccountType());
        imConversationItem.setMsgSendTime(new Date());
        imConversationItem.setHasRead(0);
        imConversationItem.setMsgReadTime(new Date());
        imConversationItem.setMsgType(req.getMsgContentItemList().get(0).getMsgType());
        imConversationItem.setMsgContent(JSON.toJSONString(req.getMsgContentItemList()));
        imConversationItem.setMsgSeq(req.getMsgSeq());
        imConversationItem.setMsgKey(req.getMsgKey());
        imConversationItem.setMsgId(req.getMsgId());
        imConversationItem.setIsDelete(0);
        imConversationItem.setUpdateTime(new Date());
        imConversationItem.setCreateTime(new Date());
        return imConversationItem;
    }

    /**
     * 写入定时任务发送记录表
     * */
    public void writeTimeoutRecord(TimeoutStrategyReqBo timeoutStrategyReqBo, Integer recordStatusInt, Long imConversationItemId){
        ConversationAutoReplyRecord conversationAutoReplyRecord = new ConversationAutoReplyRecord();
        conversationAutoReplyRecord.setConversationId(timeoutStrategyReqBo.getConversation().getConversationId());
        conversationAutoReplyRecord.setConversationItemId(imConversationItemId);
        conversationAutoReplyRecord.setConversationAutoReplyDetailConfigId(timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getConversationAutoReplyConfigId());
        conversationAutoReplyRecord.setMsgSubType(timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getMsgSubType());
        conversationAutoReplyRecord.setRecordStatus(SuccessAndFailEnum.SUCCESS.type);
        if(recordStatusInt==0){
            conversationAutoReplyRecord.setRecordStatus(SuccessAndFailEnum.FAIL.type);
        }

        conversationAutoReplyRecord.setContent(timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getAutoReplyContent());
        conversationAutoReplyRecord.setIsDelete(0);
        conversationAutoReplyRecord.setUpdateTime(new Date());
        conversationAutoReplyRecord.setCreateTime(new Date());
        conversationAutoReplyRecordRepository.insertSelective(conversationAutoReplyRecord);
    }


    /**
     * 发送消息给个人
     */
    public SendMsgToPersonalResultBo sendMsgToPersonal(String fromAccount, String toAccount, String content) {
        SendMsgPersonalBo sendMsgPersonalBo = new SendMsgPersonalBo();
        sendMsgPersonalBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        sendMsgPersonalBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        sendMsgPersonalBo.setFromAccountId(fromAccount);
        sendMsgPersonalBo.setToAccountId(toAccount);

        List<SendMsgPersonalBo.MsgContentItem> msgContentItemList = new ArrayList<>();
        SendMsgPersonalBo.MsgContentItem msgListItem = new SendMsgPersonalBo.MsgContentItem();
        msgListItem.setMsgType("Text");
        CallbackSingleSendMsgAfterReq.MsgContent msgContent = new CallbackSingleSendMsgAfterReq.MsgContent();
        msgContent.setText(content);
        msgListItem.setContent(msgContent);
        msgContentItemList.add(msgListItem);

        sendMsgPersonalBo.setMsgContentItemList(msgContentItemList);
        sendMsgPersonalBo.setIsNeedReadReceipt(CommonConstant.ONE);
        return tencentManager.sendMsgToPersonal(sendMsgPersonalBo);

    }


}
