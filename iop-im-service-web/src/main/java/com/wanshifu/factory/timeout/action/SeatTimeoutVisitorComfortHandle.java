package com.wanshifu.factory.timeout.action;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import com.wanshifu.factory.timeout.TimeoutHandleI;
import com.wanshifu.iop.im.domain.bo.ImConversationBeanBo;
import com.wanshifu.iop.im.domain.bo.channel.reply.TimeoutStrategyReqBo;
import com.wanshifu.iop.im.domain.enums.OuterUserClassTypeEnum;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyDetailConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/23 16:09
 * @description 坐席超时访客安抚语
 */
@Component("seat_timeout_visitor_comfort")
@Slf4j
public class SeatTimeoutVisitorComfortHandle extends TimeoutAbstract implements TimeoutHandleI {
    @Override
    public Integer timeoutAction(TimeoutStrategyReqBo timeoutStrategyReqBo) {
        ImConversation conversation = timeoutStrategyReqBo.getConversation();
        if (!OuterUserClassTypeEnum.SEAT.type.equals(conversation.getToOuterUserType())) {
            log.info("还没到坐席端回复阶段，不需要回复安抚语");
            return 1;
        }
        //访客端发送
        ImConversationItem fromConversationItem = timeoutStrategyReqBo.getFromConversationItem();

        if (fromConversationItem == null) {
            log.info("访客没有发送过消息，无需发送安抚语");
            return 1;
        }

        ConversationAutoReplyDetailConfig conversationAutoReplyDetailConfig = timeoutStrategyReqBo.getConversationAutoReplyDetailConfig();

        if (!this.checkSendMsgByTime(timeoutStrategyReqBo)) {
            log.info("还没到发送时间，无需发送");
            return 1;
        }

        boolean sendMsgDone = super.isSendMsgDone(conversationAutoReplyDetailConfig.getMsgSubType(), fromConversationItem.getConversationItemId());
        if (sendMsgDone) {
            log.info("该消息已发送过，不需要重复发送");
            return 1;
        }

        //先使用坐席账号发送超时访客安抚语
        String toOuterUserId = conversation.getToOuterUserId();
        Integer i = super.sendMsgToGroup(conversation.getConversationId(), toOuterUserId, conversationAutoReplyDetailConfig.getAutoReplyContent());

        super.writeTimeoutRecord(timeoutStrategyReqBo, i, fromConversationItem.getConversationItemId());
        return 1;
    }

    /**
     * 判断是否发送消息 （时间计算）
     *
     * */
    public Boolean checkSendMsgByTime(TimeoutStrategyReqBo timeoutStrategyReqBo){

        ImConversationItem fromConversationItem = timeoutStrategyReqBo.getFromConversationItem();
        ImConversationItem seatConversationItem = timeoutStrategyReqBo.getSeatConversationItem();

        Integer timeoutSecondSet = Integer.valueOf(timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getTimeoutCostSecond());

        if(seatConversationItem==null){
            //坐席没发过消息
            if (System.currentTimeMillis() > fromConversationItem.getMsgSendTime().getTime() + timeoutSecondSet*1000) {
                return true;
            }
        }

        if(seatConversationItem!=null){
            //坐席发过消息
            if (seatConversationItem.getMsgSendTime().before(fromConversationItem.getMsgSendTime()) && System.currentTimeMillis() > fromConversationItem.getMsgSendTime().getTime() + timeoutSecondSet*1000) {
                return true;
            }
        }
        return false;
    }
}
