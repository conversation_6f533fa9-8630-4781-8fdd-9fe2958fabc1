package com.wanshifu.factory.timeout.action;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.factory.timeout.TimeoutHandleI;
import com.wanshifu.iop.im.api.req.tencent.SendMsgPersonalReq;
import com.wanshifu.iop.im.domain.bo.ImConversationBeanBo;
import com.wanshifu.iop.im.domain.bo.SendMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgToPersonalResultBo;
import com.wanshifu.iop.im.domain.bo.channel.reply.TimeoutStrategyReqBo;
import com.wanshifu.iop.im.domain.enums.MsgTypeEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserClassTypeEnum;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyDetailConfig;
import com.wanshifu.utils.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/23 16:09
 * @description 访客超时关闭访客提示语
 */
@Component("visitor_timeout_close_visitor_tip")
@Slf4j
public class VisitorTimeoutCloseVisitorTipHandle extends TimeoutAbstract implements TimeoutHandleI {
    @Override
    public Integer timeoutAction(TimeoutStrategyReqBo timeoutStrategyReqBo) {
        ImConversation conversation = timeoutStrategyReqBo.getConversation();

        //访客端发送
        ImConversationItem fromConversationItem = timeoutStrategyReqBo.getFromConversationItem();

        if (fromConversationItem == null) {
            log.info("访客没有发送过消息，无需发送超时关闭");
            return 1;
        }

        ConversationAutoReplyDetailConfig conversationAutoReplyDetailConfig = timeoutStrategyReqBo.getConversationAutoReplyDetailConfig();

        if (!this.checkSendMsgByTime(timeoutStrategyReqBo)) {
            log.info("还没到发送时间，无需发送超时关闭");
            return 1;
        }

        boolean sendMsgDone = super.isSendMsgDone(conversationAutoReplyDetailConfig.getMsgSubType(), fromConversationItem.getConversationItemId());
        if (sendMsgDone) {
            log.info("该消息已发送过，不需要重复发送超时关闭");
            return 1;
        }
        Integer i = 0;
        //先使用坐席账号发送超时访客提示，如果已经是坐席接待，就使用坐席账号发送群聊提醒，如果还没到坐席接待就使用代理账号发送提醒
        if(OuterUserClassTypeEnum.SEAT.type.equals(conversation.getToOuterUserType())){
            i = super.sendMsgToGroup(conversation.getConversationId(), conversation.getToOuterUserId(), conversationAutoReplyDetailConfig.getAutoReplyContent());
        }else{
            //需要写会话记录，如果还没到坐席账号，就使用代理账号发送单聊消息
            SendMsgToPersonalResultBo sendMsgToPersonalResultBo = super.sendMsgToPersonal(conversation.getAgentOuterUserId(), conversation.getFromOuterUserId(), conversationAutoReplyDetailConfig.getAutoReplyContent());
            ImConversationBeanBo imConversationBeanBo = new ImConversationBeanBo();
            imConversationBeanBo.setFromAccount(conversation.getAgentOuterUserId());
            imConversationBeanBo.setFromAccountType(conversation.getToOuterUserType());
            imConversationBeanBo.setToAccount(conversation.getFromOuterUserId());
            imConversationBeanBo.setToAccountType(conversation.getFromOuterUserType());
            imConversationBeanBo.setResponseAccount("");
            imConversationBeanBo.setResponseAccountType("");
            imConversationBeanBo.setMsgKey(sendMsgToPersonalResultBo.getMsgKey());
            imConversationBeanBo.setMsgId(sendMsgToPersonalResultBo.getMsgId());

            List<SendMsgPersonalBo.MsgContentItem> msgContentItemList = new ArrayList<>();
            SendMsgPersonalBo.MsgContentItem msgContentItem = new SendMsgPersonalBo.MsgContentItem();
            msgContentItem.setMsgType(MsgTypeEnum.TEXT.type);
            SendMsgPersonalReq.MsgBodyContent msgBodyContent = new SendMsgPersonalReq.MsgBodyContent();
            msgBodyContent.setText(conversationAutoReplyDetailConfig.getAutoReplyContent());
            msgContentItem.setContent(msgBodyContent);
            msgContentItemList.add(msgContentItem);
            imConversationBeanBo.setMsgSeq(MathUtil.generateCustomerLength(32));
            imConversationBeanBo.setMsgContentJson(JSON.toJSONString(msgContentItemList));
            imConversationBeanBo.setMsgContentItemList(Lists.newArrayList());
            super.returnImConversationItemBean(imConversationBeanBo,conversation.getConversationId());
        }

        super.writeTimeoutRecord(timeoutStrategyReqBo, i, fromConversationItem.getConversationItemId());
        return 1;
    }


    /**
     * 判断是否发送消息 （时间计算）  有坐席接待
     *
     * */
    public Boolean checkSendMsgByTime(TimeoutStrategyReqBo timeoutStrategyReqBo){
        ImConversationItem fromConversationItem = timeoutStrategyReqBo.getFromConversationItem();
        ImConversationItem seatConversationItem = timeoutStrategyReqBo.getSeatConversationItem();
        ImConversationItem robotConversationItem = timeoutStrategyReqBo.getRobotConversationItem();

        int timeoutSecondSet = Integer.parseInt(timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getTimeoutCostSecond());

        if(OuterUserClassTypeEnum.SEAT.type.equals(timeoutStrategyReqBo.getConversation().getToOuterUserType())){
            //有坐席接待,坐席发送时间大于访客发送时间且当前时间大于坐席发送时间+超时时间
            if (seatConversationItem.getMsgSendTime().after(fromConversationItem.getMsgSendTime()) && System.currentTimeMillis() > seatConversationItem.getMsgSendTime().getTime() + timeoutSecondSet * 1000L) {
                return true;
            }
        }else{
            //无坐席接待
            if (robotConversationItem.getMsgSendTime().after(fromConversationItem.getMsgSendTime()) && System.currentTimeMillis() > robotConversationItem.getMsgSendTime().getTime() + timeoutSecondSet * 1000L) {
                return true;
            }
        }

        return false;
    }
}
