package com.wanshifu.factory.action;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.aop.annotation.LogOperate;
import com.wanshifu.factory.DataManipulationHandleI;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.CommonEnumsResp;
import com.wanshifu.iop.im.api.resp.CommonRunEnumsResp;
import com.wanshifu.iop.im.api.resp.CommonRunStrategyResp;
import com.wanshifu.iop.im.api.resp.ResultResp;
import com.wanshifu.iop.im.domain.bo.seat.SeatGroupMappingBo;
import com.wanshifu.iop.im.domain.enums.ErrorCodeEnum;
import com.wanshifu.iop.im.domain.enums.OperateLogFromBusinessEnum;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.po.GroupInfo;
import com.wanshifu.iop.im.domain.po.IncomingSafetyConfig;
import com.wanshifu.repository.GroupInfoRepository;
import com.wanshifu.service.AbstractService;
import lombok.extern.slf4j.Slf4j;
import org.omg.CORBA.Object;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分组管理模块
 * <AUTHOR>
 * @date： 2025-06-04 15:19:51
 */
@Component("group")
@Slf4j
public class GroupHandle extends AbstractService implements DataManipulationHandleI {

    @Resource
    private PlatformTransactionManager platformTransactionManager;

    @Resource
    private GroupInfoRepository groupInfoRepository;

    /**
     * 验证重复名称
     * @param req
     * @return
     */
    @Override
    public ResultResp<CommonRunStrategyResp> verifyRepeatName(VerifyRepeatNameReq req) {
        Integer excludeId = null;
        if ( req.getExcludeId() != null ){
            excludeId = req.getExcludeId().intValue();
        }
        GroupInfo groupInfo = groupInfoRepository.selectInfoByName(req.getVerifyName(), excludeId);
        if(groupInfo != null){
            return ResultResp.fail("群组名称重复", ErrorCodeEnum.DUPLICATE_KEY_CODE);
        }

        return ResultResp.success();
    }

    /**
     * 切换启用禁用状态
     * @param req
     * @return
     */
    @Override
    @LogOperate(businessClass = GroupInfo.class, businessType = "changeStatus", idField = "switchId")
    public ResultResp<CommonRunStrategyResp> switchStatus(SwitchStatusReq req) {
        // 保底组不能被禁用
        if ( minimumGuaranteeGroupId.equals(req.getSwitchId().intValue()) && StatusEnum.DISABLE.type.equals(req.getStatus()) ){
            throw new BusinessException("保底组不能被禁用");
        }

        // 手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        try {

            GroupInfo groupInfo = new GroupInfo();
            groupInfo.setGroupId(req.getSwitchId().intValue());
            groupInfo.setStatus(req.getStatus());
            groupInfo.setOperatorId(req.getOperatorId());
            int update = groupInfoRepository.updateByPrimaryKeySelective(groupInfo);
            Assert.isTrue(update > 0, "切换状态失败");

            /**
             * 追加逻辑
             * 分组禁用和启用的时候，需要去控制seatGroupMapping表中对应的关联状态
             * 启用：将关联的坐席状态置为启用 （需要校验当前坐席是否启用）
             * 禁用：将关联的坐席状态置为禁用
             */
            if ( StatusEnum.DISABLE.type.equals(req.getStatus()) ){
                // 禁用：将关联的坐席状态置为禁用
                seatGroupMappingRepository.updateDisableStatusByGroupId(req.getSwitchId().intValue());
            }else{
                // 查询当前分组关联的坐席
                List<SeatGroupMappingBo> seatGroupMappingBos = seatGroupMappingRepository.selectSeatMappingByGroupId(req.getSwitchId().intValue());
                List<Long> seatIds = seatGroupMappingBos.stream().filter(f->StatusEnum.ENABLE.type.equals(f.getSeatStatus())).map(SeatGroupMappingBo::getSeatId).collect(Collectors.toList());
                if ( CollectionUtils.isNotEmpty(seatIds) ){
                    seatGroupMappingRepository.updateEnableStatusByGroupIdAndSeatIdList(req.getSwitchId().intValue(), seatIds, req.getOperatorId());
                }
            }

//            super.insertOperateLog(req.getSwitchId(), GroupInfo.class,  OperateLogFromBusinessEnum.CHANGE_STATUS.type, JSONObject.toJSONString(req), req.getOperatorId());
            platformTransactionManager.commit(status);
            return ResultResp.success();
        } catch (Exception e) {
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 获取分组枚举
     * @param req
     * @return
     */
    @Override
    public ResultResp<CommonRunEnumsResp> getEnums(GetEnumsReq req) {

        List<GroupInfo> enums = groupInfoRepository.getEnums(req.getIncludeEnumsValueIdList(), req.getTenantId());
        if ( enums != null ){
            List<CommonEnumsResp> enumsList = enums.stream().map(groupInfo -> {
                CommonEnumsResp commonEnumsResp = new CommonEnumsResp();
                commonEnumsResp.setLabel(groupInfo.getGroupName());
                commonEnumsResp.setValue(groupInfo.getGroupId().toString());
                commonEnumsResp.setDisabled(StatusEnum.DISABLE.type.equals(groupInfo.getStatus()));
                return commonEnumsResp;
            }).collect(Collectors.toList());

            CommonRunEnumsResp commonRunEnumsResp = new CommonRunEnumsResp();
            commonRunEnumsResp.setEnumsList(enumsList);
            return ResultResp.success(commonRunEnumsResp);
        }

        return null;
    }

    @Override
    public List<Long> checkNonexistent(CheckNonexistentReq req) {
        return null;
    }
}
