package com.wanshifu.factory.action;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.aop.annotation.LogOperate;
import com.wanshifu.factory.DataManipulationHandleI;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.CommonEnumsResp;
import com.wanshifu.iop.im.api.resp.CommonRunEnumsResp;
import com.wanshifu.iop.im.api.resp.CommonRunStrategyResp;
import com.wanshifu.iop.im.api.resp.ResultResp;
import com.wanshifu.iop.im.domain.enums.ErrorCodeEnum;
import com.wanshifu.iop.im.domain.enums.OperateLogFromBusinessEnum;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.enums.TagsSceneEnum;
import com.wanshifu.iop.im.domain.po.GroupInfo;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.Tags;
import com.wanshifu.repository.TagsRepository;
import com.wanshifu.service.AbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签管理模块
 * <AUTHOR>
 * @date： 2025-06-04 15:19:51
 */
@Component("tags")
@Slf4j
public class TagsHandle extends AbstractService implements DataManipulationHandleI {

    @Resource
    private TagsRepository tagsRepository;

    /**
     * 验证重复名称
     * @param req
     * @return
     */
    @Override
    public ResultResp<CommonRunStrategyResp> verifyRepeatName(VerifyRepeatNameReq req) {
        Integer excludeId = null;
        if ( req.getExcludeId() != null ){
            excludeId = req.getExcludeId().intValue();
        }
        Tags tags = tagsRepository.selectInfoByTagName(req.getVerifyName(), TagsSceneEnum.SEAT.type, excludeId);
        if(tags != null){
            return ResultResp.fail("坐席标签名不可重复，请重新输入提交；", ErrorCodeEnum.DUPLICATE_KEY_CODE);
        }

        return ResultResp.success();
    }

    /**
     * 切换标签状态
     * @param req
     * @return
     */
    @Override
    @LogOperate(businessClass = Tags.class, businessType = "changeStatus", idField = "switchId")
    public ResultResp<CommonRunStrategyResp> switchStatus(SwitchStatusReq req) {
        Tags tags = new Tags();
        tags.setTagId(req.getSwitchId().intValue());
        tags.setStatus(req.getStatus());
        tags.setOperatorId(req.getOperatorId());
        int update = tagsRepository.updateByPrimaryKeySelective(tags);
        Assert.isTrue(update > 0, "切换状态失败");

//        super.insertOperateLog(req.getSwitchId(), Tags.class,  OperateLogFromBusinessEnum.CHANGE_STATUS.type, JSONObject.toJSONString(req), req.getOperatorId());
        return ResultResp.success();
    }

    /**
     * 获取分组枚举
     * @param req
     * @return
     */
    @Override
    public ResultResp<CommonRunEnumsResp> getEnums(GetEnumsReq req) {
        List<Tags> enums = tagsRepository.getEnums(TagsSceneEnum.SEAT.type, req.getIncludeEnumsValueIdList());
        if ( enums != null ){
            List<CommonEnumsResp> enumsList = enums.stream().map(tags -> {
                CommonEnumsResp commonEnumsResp = new CommonEnumsResp();
                commonEnumsResp.setLabel(tags.getTagName());
                commonEnumsResp.setValue(tags.getTagId().toString());
                commonEnumsResp.setDisabled(StatusEnum.DISABLE.type.equals(tags.getStatus()));
                return commonEnumsResp;
            }).collect(Collectors.toList());

            CommonRunEnumsResp commonRunEnumsResp = new CommonRunEnumsResp();
            commonRunEnumsResp.setEnumsList(enumsList);
            return ResultResp.success(commonRunEnumsResp);
        }

        return null;
    }

    @Override
    public List<Long> checkNonexistent(CheckNonexistentReq req) {
        return null;
    }

}
