package com.wanshifu.factory.action;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.aop.annotation.LogOperate;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.CommonRedisConstant;
import com.wanshifu.factory.DataManipulationHandleI;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.account.domain.resp.account.AccountInfoListResp;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.ConversationFlowSeatStatusUpdateReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.*;
import com.wanshifu.iop.im.domain.bo.seat.SeatGroupMappingBo;
import com.wanshifu.iop.im.domain.enums.ErrorCodeEnum;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.po.SeatGroupMapping;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.repository.SeatGroupMappingRepository;
import com.wanshifu.repository.SeatInfoRepository;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 坐席管理模块
 * <AUTHOR>
 * @date： 2025-06-04 15:19:51
 */
@Component("seat")
@Slf4j
public class SeatHandle extends AbstractService implements DataManipulationHandleI {

    @Resource
    private SeatInfoRepository seatInfoRepository;

    @Resource
    private SeatGroupMappingRepository seatGroupMappingRepository;

    @Resource
    private AuthService authService;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    /**
     * 验证重复名称
     * @param req
     * @return
     */
    @Override
    public ResultResp<CommonRunStrategyResp> verifyRepeatName(VerifyRepeatNameReq req) {
        SeatInfo seatInfo = seatInfoRepository.selectInfoByName(req.getVerifyName(), req.getExcludeId());
        if(seatInfo != null){
            return ResultResp.fail("坐席昵称已存在", ErrorCodeEnum.DUPLICATE_KEY_CODE);
        }

        return ResultResp.success();
    }

    /**
     * 验证重复名称
     * @param req
     * @return
     */
    @Override
    @LogOperate(businessClass = SeatInfo.class, businessType = "changeStatus", idField = "switchId")
    public ResultResp<CommonRunStrategyResp> switchStatus(SwitchStatusReq req) {
        log.info("切换坐席状态：{}", req);
        SeatInfo seatInfo = seatInfoRepository.selectByPrimaryKey(req.getSwitchId());
        if (Objects.isNull(seatInfo)){
            log.error("切换状态失败，该坐席不存在 req:{}", JSONObject.toJSONString(req));
            throw new BusinessException("切换状态失败，该坐席不存在");
        }

        if ( seatInfo.getStatus().equals(req.getStatus()) ){
            log.error("切换状态失败，该坐席已处于该状态");
            return ResultResp.success();
        }

        // 启用的时候，校验账号状态
        if ( StatusEnum.ENABLE.type.equals(req.getStatus()) ){
            super.verifyAccountStatus(seatInfo.getAccountId());
        }

        //手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        try{
            SeatInfo swtichSeatInfo = new SeatInfo();
            swtichSeatInfo.setSeatId(req.getSwitchId());
            swtichSeatInfo.setStatus(req.getStatus());
            swtichSeatInfo.setOperatorId(req.getOperatorId());
            int update = seatInfoRepository.updateByPrimaryKeySelective(swtichSeatInfo);
            Assert.isTrue(update > 0, "切换状态失败");

            /**
             * 追加逻辑
             * 坐席禁用和启用的时候，需要去控制seatGroupMapping表中对应的关联状态
             * 禁用直接禁用
             * 启用的话，需要查询关联分组是否启用状态
             */
            if ( StatusEnum.DISABLE.type.equals(req.getStatus()) ){
                // 禁用直接禁用 更新坐席关联分组的状态
                seatGroupMappingRepository.batchUpdateStatusBySeatId(req.getSwitchId(), req.getStatus());
            }else{
                // 查询当前分组关联的坐席
                List<SeatGroupMappingBo> groupMappings = seatGroupMappingRepository.selectSeatMappingBySeatId(req.getSwitchId());
                List<Integer> groupIdList = groupMappings.stream().filter(f->StatusEnum.ENABLE.type.equals(f.getGroupStatus())).map(SeatGroupMappingBo::getGroupId).collect(Collectors.toList());
                if ( CollectionUtils.isNotEmpty(groupIdList) ){
                    // 更新关联分组的状态
                    seatGroupMappingRepository.updateEnableStatusBySeatIdAndGroupIdList(req.getSwitchId(), groupIdList, req.getOperatorId());
                }
            }

            platformTransactionManager.commit(status);
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }

//        super.insertOperateLog(req.getSwitchId(), SeatInfo.class,  OperateLogFromBusinessEnum.CHANGE_STATUS.type, JSONObject.toJSONString(req), req.getOperatorId());

        // 如果切换禁用状态，则将所有正在使用的账号下线
        if(StatusEnum.DISABLE.type.equals(req.getStatus())){
            SetLoginAuthResp setLoginAuthResp = new SetLoginAuthResp();
            authService.updateSeatLoginCache(seatInfo.getAccountId(), setLoginAuthResp, req.getStatus());
        }

        // 如果切换启用状态，更新当前登录的坐席登录缓存
        if (StatusEnum.ENABLE.type.equals(req.getStatus())){
            // 获取组信息
            List<SeatGroupMapping> groupMappings = seatGroupMappingRepository.batchGetMappingBySeatIdList(Collections.singletonList(seatInfo.getSeatId()), StatusEnum.ENABLE.type);
            SetLoginAuthResp setLoginAuthResp = new SetLoginAuthResp();
            LoginSeatAuthInfoResp loginSeatAuthInfoResp = new LoginSeatAuthInfoResp();
            BeanUtils.copyProperties(seatInfo, loginSeatAuthInfoResp);
            // 记录分组
            loginSeatAuthInfoResp.setGroupIdList(groupMappings.stream().map(SeatGroupMapping::getGroupId).collect(Collectors.toList()));
            // 兼容异常情况，只有离线状态时，将上线状态赋给当前状态
            if ( StringUtils.isEmpty(loginSeatAuthInfoResp.getCurrentSeatStatusEn())
                    || CommonConstant.DEFAULT_OFFLINE_SEAT_STATUS.equals(loginSeatAuthInfoResp.getCurrentSeatStatusEn()) ){
                loginSeatAuthInfoResp.setCurrentSeatStatusEn( StringUtils.isNotEmpty(loginSeatAuthInfoResp.getLoginSeatStatusEn()) ? loginSeatAuthInfoResp.getLoginSeatStatusEn() : CommonConstant.DEFAULT_ONLINE_SEAT_STATUS );
            }
            setLoginAuthResp.setSeatInfo(loginSeatAuthInfoResp);
            authService.updateSeatLoginCache(seatInfo.getAccountId(), setLoginAuthResp, req.getStatus());

            // 如果启用的坐席登录状态是在线，监听会话状态
            if ( CommonConstant.SEAT_STATUS_ONLINE.equals(seatInfo.getLoginSeatStatusEn()) ){
                // 监听会话状态
                ConversationFlowSeatStatusUpdateReq conversationFlowSeatStatusUpdateReq = new ConversationFlowSeatStatusUpdateReq();
                conversationFlowSeatStatusUpdateReq.setSeatId(seatInfo.getSeatId());
                conversationFlowSeatStatusUpdateProducer.sendMessage(conversationFlowSeatStatusUpdateReq);
            }
        }

        // 删除坐席账号枚举缓存
        redisHelper.del(CommonRedisConstant.SEAT_ACCOUNT_ENUM_KEY);
        return ResultResp.success();
    }

    /**
     * 获取坐席枚举
     * @param req
     * @return
     */
    @Override
    public ResultResp<CommonRunEnumsResp> getEnums(GetEnumsReq req) {
        CommonRunEnumsResp commonRunEnumsResp = new CommonRunEnumsResp();

        // 剔除分组中已被关联的坐席
        List<Long> excludeIdList = new ArrayList<>();
        if ( CollectionUtils.isNotEmpty(req.getEnumsParentIdList()) ){
            List<SeatGroupMapping> groupMappings = seatGroupMappingRepository.selectListByGroupId(req.getEnumsParentIdList().get(0).intValue());
            if ( CollectionUtils.isNotEmpty(groupMappings) ){
                excludeIdList.addAll(groupMappings.stream().map(SeatGroupMapping::getSeatId).collect(Collectors.toList()));
            }
        }

        List<SeatInfo> enums = seatInfoRepository.getEnums(req);
        if (CollectionUtils.isNotEmpty(enums)){
            // 获取坐席姓名
            List<AccountInfoListResp> accountInfoListResps = iopAccountService.batchGetInfoListByAccountIds(enums.stream().map(SeatInfo::getAccountId).collect(Collectors.toList()));
            List<CommonEnumsResp> enumsList = enums.stream().filter(f->!excludeIdList.contains(f.getSeatId())).map(seatInfo -> {
                CommonEnumsResp commonEnumsResp = new CommonEnumsResp();
                commonEnumsResp.setLabel(seatInfo.getSeatName());

                accountInfoListResps.stream().filter(f->f.getAccountId().equals(seatInfo.getAccountId())).findFirst().ifPresent(accountInfoListResp -> {
                    commonEnumsResp.setLabel(accountInfoListResp.getUsername());
                });

                commonEnumsResp.setValue(seatInfo.getSeatId().toString());
                commonEnumsResp.setDisabled(StatusEnum.DISABLE.type.equals(seatInfo.getStatus()));
                return commonEnumsResp;
            }).collect(Collectors.toList());

            commonRunEnumsResp.setEnumsList(enumsList);
        }

        return ResultResp.success(commonRunEnumsResp);
    }

    @Override
    public List<Long> checkNonexistent(CheckNonexistentReq req) {
        return null;
    }
}
