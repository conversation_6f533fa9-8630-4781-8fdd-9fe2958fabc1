package com.wanshifu.factory.action;

import com.wanshifu.factory.DataManipulationHandleI;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.CommonEnumsResp;
import com.wanshifu.iop.im.api.resp.CommonRunEnumsResp;
import com.wanshifu.iop.im.api.resp.CommonRunStrategyResp;
import com.wanshifu.iop.im.api.resp.ResultResp;
import com.wanshifu.iop.im.domain.enums.ErrorCodeEnum;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.po.UserProblemClass;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.repository.UserProblemClassRepository;
import com.wanshifu.repository.channel.ChannelConfigRepository;
import com.wanshifu.service.AbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 咨询类型
 * <AUTHOR>
 * @date： 2025-06-04 15:19:51
 */
@Component("problemClass")
@Slf4j
public class ProblemClassHandle extends AbstractService implements DataManipulationHandleI {

    @Resource
    private UserProblemClassRepository userProblemClassRepository;

    /**
     * 验证重复名称
     * @param req
     * @return
     */
    @Override
    public ResultResp<CommonRunStrategyResp> verifyRepeatName(VerifyRepeatNameReq req) {
        return ResultResp.success();
    }

    /**
     * 切换启用禁用状态
     * @param req
     * @return
     */
    @Override
    public ResultResp<CommonRunStrategyResp> switchStatus(SwitchStatusReq req) {
        return ResultResp.success();
    }

    /**
     * 获取分组枚举
     * @param req
     * @return
     */
    @Override
    public ResultResp<CommonRunEnumsResp> getEnums(GetEnumsReq req) {
        if ( CollectionUtils.isEmpty(req.getEnumsParentIdList()) ){
            // 改成空集合
            return ResultResp.success(new CommonRunEnumsResp());
//            return ResultResp.fail("会话入口id为null", ErrorCodeEnum.FAIL);
        }
        CommonRunEnumsResp commonRunEnumsResp = new CommonRunEnumsResp();

        List<UserProblemClass> enums = userProblemClassRepository.getEnums(req);
        if (CollectionUtils.isNotEmpty(enums)){
            List<CommonEnumsResp> enumsList = enums.stream().map(userProblemClass -> {
                CommonEnumsResp commonEnumsResp = new CommonEnumsResp();
                commonEnumsResp.setLabel(userProblemClass.getProblemName());
                commonEnumsResp.setValue(userProblemClass.getProblemId().toString());
                commonEnumsResp.setDisabled(StatusEnum.DISABLE.en.equals(userProblemClass.getStatus()));
                return commonEnumsResp;
            }).collect(Collectors.toList());

            commonRunEnumsResp.setEnumsList(enumsList);
        }

        return ResultResp.success(commonRunEnumsResp);
    }

    /**
     * 检查是否存在
     * @param req
     * @return
     */
    @Override
    public List<Long> checkNonexistent(CheckNonexistentReq req) {
        if ( CollectionUtils.isEmpty(req.getExistIdList()) ){
            return new ArrayList<>();
        }
        List<Long> exist = userProblemClassRepository.checkNonexistent(req);
        return req.getExistIdList().stream().filter(id -> !exist.contains(id)).collect(Collectors.toList());
    }
}
