package com.wanshifu.factory.callback.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.impl.rocketmq.ONSChannel;
import com.wanshifu.base.ai.api.WorkFlowApi;
import com.wanshifu.base.ai.domain.req.WorkFlowRunReq;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.ConversationConstant;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.bo.GetOnlineStatusBo;
import com.wanshifu.iop.im.domain.bo.GetOnlineStatusRespBo;
import com.wanshifu.iop.im.domain.bo.groupManage.GroupRuleConfigValueBo;
import com.wanshifu.iop.im.domain.enums.EnableStatusEnum;
import com.wanshifu.iop.im.domain.enums.NodeTypeEnum;
import com.wanshifu.iop.im.domain.enums.OnlineStatusEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricConfigEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricFromTypeEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricObjectEnum;
import com.wanshifu.iop.im.domain.po.FlowDefine;
import com.wanshifu.iop.im.domain.po.FlowNode;
import com.wanshifu.iop.im.domain.po.GroupRule;
import com.wanshifu.iop.im.domain.po.RuleMetricConfig;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.manager.commonBase.CommonBaseManager;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.mapper.VirtualUserInfoMapper;
import com.wanshifu.repository.FlowDefineRepository;
import com.wanshifu.repository.FlowInstanceRepository;
import com.wanshifu.repository.FlowNodeRepository;
import com.wanshifu.repository.GroupRuleRepository;
import com.wanshifu.repository.ImConversationItemRepository;
import com.wanshifu.repository.ImConversationRepository;
import com.wanshifu.repository.ImGroupRepository;
import com.wanshifu.repository.ImGroupUserRelationRepository;
import com.wanshifu.repository.RuleMetricConfigRepository;
import com.wanshifu.repository.SeatInfoRepository;
import com.wanshifu.repository.UserRegisterInfoRepository;
import com.wanshifu.repository.UserVirtualRelationRepository;
import com.wanshifu.repository.VirtualUserInfoRepository;
import com.wanshifu.repository.channel.ChannelConfigRepository;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public abstract class CallbackAbstract {

    @Resource
    public ImConversationRepository imConversationRepository;

    @Resource
    public ImConversationItemRepository imConversationItemRepository;

    @Resource
    public ImGroupRepository imGroupRepository;

    @Resource
    public ImGroupUserRelationRepository imGroupUserRelationRepository;

    @Resource
    public TencentManager tencentManager;

    @Resource
    public VirtualUserInfoRepository virtualUserInfoRepository;

    @Resource
    public UserVirtualRelationRepository userVirtualRelationRepository;

    @Resource
    public RuleMetricConfigRepository ruleMetricConfigRepository;

    @Resource
    public  UserRegisterInfoRepository userRegisterInfoRepository;

    @Resource
    public SeatInfoRepository seatInfoRepository;

    @Resource
    public GroupRuleRepository groupRuleRepository;

    @Resource
    public ChannelConfigRepository channelConfigRepository;

    @Resource
    public FlowDefineRepository flowDefineRepository;

    @Resource
    public FlowNodeRepository flowNodeRepository;

    @Resource
    public WorkFlowApi workFlowApi;

    @Resource
    public CommonBaseManager commonBaseManager;

    @Resource
    public FlowInstanceRepository flowInstanceRepository;


    /**
     * 查询指定用户是否在线
     * true:在线，
     * false:不在线
     * */
    public boolean checkUserOnlineStatus(String outerUserId){

        GetOnlineStatusBo getOnlineStatusBo = new GetOnlineStatusBo();
        getOnlineStatusBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        getOnlineStatusBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        getOnlineStatusBo.setOuterUserIds(Arrays.asList(outerUserId));

        GetOnlineStatusRespBo onlineStatus = tencentManager.getOnlineStatus(getOnlineStatusBo);
        if(onlineStatus==null){
            log.error("查询账号在线状态异常,{}",outerUserId);
            return false;
        }
        List<GetOnlineStatusRespBo.OnlineItem> results = onlineStatus.getResults();
        if(CollectionUtils.isEmpty(results)){
            log.error("查询账号在线状态异常22,{}",outerUserId);
            return false;
        }
        GetOnlineStatusRespBo.OnlineItem onlineItem = results.get(0);
        if(OnlineStatusEnum.ON_LINE.type.equals(onlineItem.getStatus())){
            return true;
        }
        return false;

    }

    /**
     * 查询指标配置
     * */
    public RuleMetricConfig getRuleMetricConfigByType(String type){
        RuleMetricConfig ruleMetricConfig = ruleMetricConfigRepository.selectRuleMetricByObjectAndFromTypeAndEn(RuleMetricObjectEnum.GROUP.type, RuleMetricFromTypeEnum.KF.type, type);
        return ruleMetricConfig;
    }


    /**
     *
     *
     * 根据会话关联的分组id 获取分组中关联的渠道id
     * 再根据渠道id获取关联路由信息，判断路由是否开启机器人
     * */
    public Boolean openRobotBoolean(Long channelConfigId){


        ChannelConfig channelConfig = channelConfigRepository.selectByPrimaryKey(channelConfigId);
        if(channelConfig==null){
            log.error("该channelConfigId:{} 不存在",channelConfigId);
            return false;
        }

        Long flowDefineId = channelConfig.getFlowDefineId();
        if(flowDefineId==0L){
            log.error("该channelConfig 类型为:{}, 且 channelConfigId:{} 异常，请检查",channelConfig.getChannelType(),channelConfigId);
            return false;
        }

        FlowDefine flowDefine = flowDefineRepository.getFlowDefineById(flowDefineId, EnableStatusEnum.ENABLE.type);
        if(flowDefine==null){
            log.error("该channelConfig 类型为:{}, 且 channelConfigId:{} 异常，请检查",channelConfig.getChannelType(),channelConfigId);
            return false;
        }

        List<FlowNode> flowNodeListByFlowId = flowNodeRepository.getFlowNodeListByFlowId(flowDefine.getFlowDefineId());

        Optional<FlowNode> nodeOptional = flowNodeListByFlowId.stream().filter(f -> f.getNodeType().equals(NodeTypeEnum.ROBOT.type)).findFirst();
        if(!nodeOptional.isPresent()){
            log.error("该channelConfig 类型为:{}, 且 channelConfigId:{} 异常，请检查",channelConfig.getChannelType(),channelConfigId);
            return false;
        }
        return true;

    }


    /**
     * 向机器人发送消息
     * */
    public void sendMsgToRobot(Long conversationId, String msg){
        //此处省略具体实现细节...
        WorkFlowRunReq workFlowRunReq = new WorkFlowRunReq();
        workFlowRunReq.setAppCode(ConversationConstant.ROBOT_APP_CODE);
        workFlowRunReq.setConversationId(conversationId+"");
        workFlowRunReq.setUserInput(msg);
        workFlowRunReq.setAccountId(0L);
        workFlowRunReq.setAccountType("");
        log.info("向AI发送请求，参数={}",JSON.toJSONString(workFlowRunReq));
        workFlowApi.runChatWorkFlow(workFlowRunReq);
    }





}
