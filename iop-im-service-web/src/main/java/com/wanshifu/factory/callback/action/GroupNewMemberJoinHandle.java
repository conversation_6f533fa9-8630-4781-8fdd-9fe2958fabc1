package com.wanshifu.factory.callback.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.factory.callback.CallbackHandleI;
import com.wanshifu.iop.im.api.req.tencent.CallbackCreateGroupReq;
import com.wanshifu.iop.im.api.req.tencent.CallbackGroupNewMemberJoinReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 进群后的策略执行
 * */


@Component("callback_after_new_member_join")
@Slf4j
public class GroupNewMemberJoinHandle implements CallbackHandleI {
    @Override
    public Integer callbackAction(Object object) {
        String jsonString = JSON.toJSONString(object);
        CallbackGroupNewMemberJoinReq callbackGroupNewMemberJoinReq = JSONObject.parseObject(jsonString, CallbackGroupNewMemberJoinReq.class);

        //拉人进群后回调

        return 0;
    }
}
