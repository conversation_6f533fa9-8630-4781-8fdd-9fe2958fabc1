package com.wanshifu.factory.callback.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.factory.callback.CallbackHandleI;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.tencent.CallbackGroupSendMsgAfterReq;
import com.wanshifu.iop.im.api.req.tencent.CallbackSingleSendMsgAfterReq;
import com.wanshifu.iop.im.domain.bo.CreateGroupReqBo;
import com.wanshifu.iop.im.domain.bo.GetOnlineStatusBo;
import com.wanshifu.iop.im.domain.bo.GetOnlineStatusRespBo;
import com.wanshifu.iop.im.domain.bo.ImConversationBeanBo;
import com.wanshifu.iop.im.domain.bo.SendMsgGroupBo;
import com.wanshifu.iop.im.domain.bo.SendMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgToPersonalResultBo;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.iop.im.domain.enums.BindStatusEnum;
import com.wanshifu.iop.im.domain.enums.ConversationStatusEnum;
import com.wanshifu.iop.im.domain.enums.OnlineStatusEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserClassTypeEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserTypeEnum;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.ImGroup;
import com.wanshifu.iop.im.domain.po.ImGroupUserRelation;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import com.wanshifu.iop.im.domain.po.UserVirtualRelation;
import com.wanshifu.iop.im.domain.po.VirtualUserInfo;
import com.wanshifu.manager.socketBase.impl.SocketBaseManagerImpl;
import com.wanshifu.mq.producer.SendImWebSocketMessageProducer;
import com.wanshifu.repository.ImGroupUserRelationRepository;
import com.wanshifu.repository.SeatInfoRepository;
import com.wanshifu.repository.UserRegisterInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 单聊发送消息后回调
 * 留言也会生成会话，但是在发消息回调时，如果第一次接收回调，聊天对象不在线，不生成群聊；只有会话；
 * 在对话过程中如果有一方离线状态，给指定用户发送站内信或者push推送通知
 *
 * */


@Component("callback_after_msg_group_report")
@Slf4j
public class SingleSendMsgAfterGroupHandle extends CallbackAbstract implements CallbackHandleI {

    @Resource
    private SendImWebSocketMessageProducer sendImWebSocketMessageProducer;

    private final UserRegisterInfoRepository userRegisterInfoRepository;
    private final SeatInfoRepository seatInfoRepository;
    private final ImGroupUserRelationRepository imGroupUserRelationRepository;
    @Value("${wanshifu.seat.online}")
    private List<String> seatOnlineList;

    public SingleSendMsgAfterGroupHandle(UserRegisterInfoRepository userRegisterInfoRepository, SeatInfoRepository seatInfoRepository, ImGroupUserRelationRepository imGroupUserRelationRepository) {
        super();
        this.userRegisterInfoRepository = userRegisterInfoRepository;
        this.seatInfoRepository = seatInfoRepository;
        this.imGroupUserRelationRepository = imGroupUserRelationRepository;
    }

    /**
     * 需要确认是虚拟账号给用户发消息的回调，回调无需处理
     * 还是用户给虚拟账号发消息的回调
     *
     * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer callbackAction(Object object) {
        String jsonString = JSON.toJSONString(object);
        CallbackSingleSendMsgAfterReq callbackSingleSendMsgAfterReq = JSONObject.parseObject(jsonString, CallbackSingleSendMsgAfterReq.class);

        if(StringUtils.isNotEmpty(callbackSingleSendMsgAfterReq.getGroupId())){
            //群聊回调
            return this.groupCallback(jsonString);
        }
        return 1;
    }


    /**
     * 写入会话记录
     * */
    private ImConversationItem returnImConversationItemBean(ImConversationBeanBo req, Long conversationId){
        ImConversationItem imConversationItem = new ImConversationItem();
        imConversationItem.setConversationId(conversationId);
        imConversationItem.setFromOuterUserId(req.getFromAccount());
        imConversationItem.setFromOuterUserType(req.getFromAccountType());
        imConversationItem.setToOuterUserId(req.getToAccount());
        imConversationItem.setToOuterUserType(req.getToAccountType());
        imConversationItem.setResponseOuterUserId(req.getResponseAccount());
        imConversationItem.setResponseOuterUserType(req.getResponseAccountType());
        imConversationItem.setMsgSendTime(new Date());
        imConversationItem.setHasRead(0);
        imConversationItem.setMsgReadTime(new Date());
        imConversationItem.setMsgType(req.getMsgContentItemList().get(0).getMsgType());
        imConversationItem.setMsgContent(JSON.toJSONString(req.getMsgContentItemList()));
        imConversationItem.setMsgSeq(req.getMsgSeq());
        imConversationItem.setMsgKey(req.getMsgKey());
        imConversationItem.setMsgId(req.getMsgId());
        imConversationItem.setIsDelete(0);
        imConversationItem.setUpdateTime(new Date());
        imConversationItem.setCreateTime(new Date());
        return imConversationItem;
    }



    /**
     * 用户是否在线
     * */
    public boolean getUserOnlineStatus(String outUserId){
        //获取用户在线状态，如果是给虚拟账号发送无需判断状态
        GetOnlineStatusBo getOnlineStatusBo = new GetOnlineStatusBo();
        getOnlineStatusBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        getOnlineStatusBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        getOnlineStatusBo.setOuterUserIds(Collections.singletonList(outUserId));
        GetOnlineStatusRespBo onlineStatus = tencentManager.getOnlineStatus(getOnlineStatusBo);
        if(onlineStatus == null){
            return false;
        }
        List<GetOnlineStatusRespBo.OnlineItem> results = onlineStatus.getResults();
        if(CollectionUtils.isEmpty(results)){
            return false;
        }
        if (!OnlineStatusEnum.ON_LINE.type.equals(results.get(0).getStatus())) {
            return false;
        }
        return true;
    }

    /**
     * 群聊回调执行
     * 坐席账号才需要处理回调
     * */
    public Integer groupCallback(String objectStr){
        CallbackGroupSendMsgAfterReq callbackGroupSendMsgAfterReq = JSONObject.parseObject(objectStr, CallbackGroupSendMsgAfterReq.class);
        String groupId = callbackGroupSendMsgAfterReq.getGroupId();
        ImGroup imGroup = imGroupRepository.selectImGroupByOuterGroupId(groupId);
        if(imGroup==null){
            throw new BusException("群组不存在");
        }
        //发送消息账号
        String fromAccount = callbackGroupSendMsgAfterReq.getFrom_Account();
        VirtualUserInfo virtualInfoById = virtualUserInfoRepository.getVirtualInfoById(fromAccount);
        if(virtualInfoById!=null){
            log.info("群聊中，如果是虚拟账号发送的消息不需要回调，虚拟账号id+"+fromAccount);
            return 1;
        }

        //查询群里的用户代理账号
        List<ImGroupUserRelation> groupUserRelationByImGroupId = imGroupUserRelationRepository.getGroupUserRelationsByImGroupId(imGroup.getImGroupId(), null);
        Optional<ImGroupUserRelation> userRelationOptional = groupUserRelationByImGroupId.stream().filter(f -> f.getOuterClassType().equals(OuterUserClassTypeEnum.VIRTUAL.type)).findFirst();
        if(!userRelationOptional.isPresent()){
            throw new BusException("群组不存在虚拟账号");
        }
        ImGroupUserRelation imGroupUserRelation = userRelationOptional.get();

        //群里的用户代理账号
        String outerUserId = imGroupUserRelation.getOuterUserId();

        UserVirtualRelation relationByVirtualUserId = userVirtualRelationRepository.getRelationByVirtualUserId(outerUserId, BindStatusEnum.BINDING.type);
        if(relationByVirtualUserId==null){
            throw new BusException("代理账号不存在绑定关系");
        }

        //会话id
        Long conversationId = imGroup.getConversationId();

        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if(imConversation==null){
            throw new BusException("会话不存在");
        }
        Optional<ImGroupUserRelation> notVirtualOuterUserIdOptional = groupUserRelationByImGroupId.stream().filter(f -> !f.getOuterClassType().equals(OuterUserClassTypeEnum.VIRTUAL.type)).findFirst();
        if(!notVirtualOuterUserIdOptional.isPresent()){
            throw new BusException("群里只有虚拟账号异常");
        }
        ImGroupUserRelation notVirtualUserIdRelation = notVirtualOuterUserIdOptional.get();

        List<ImConversationBeanBo.MsgContentItem> msgContentItemList = new ArrayList<>();
        for (CallbackGroupSendMsgAfterReq.MsgBodyItem msgBodyItem : callbackGroupSendMsgAfterReq.getMsgBody()) {
            ImConversationBeanBo.MsgContentItem msgContentItem = new ImConversationBeanBo.MsgContentItem();
            msgContentItem.setMsgType(msgBodyItem.getMsgType());
            msgContentItem.setMsgContent(msgBodyItem.getMsgContent());
            msgContentItemList.add(msgContentItem);
        }

        //向用户转发消息，插入会话明细
        ImConversationBeanBo imConversationBeanBo = ImConversationBeanBo.builder().MsgKey(null)
                .MsgId(callbackGroupSendMsgAfterReq.getMsgId())
                .MsgSeq(callbackGroupSendMsgAfterReq.getMsgSeq())
                .msgContentJson(JSON.toJSONString(callbackGroupSendMsgAfterReq.getMsgBody()))
                .msgContentItemList(msgContentItemList)
                .fromAccount(outerUserId)
                .fromAccountType(OuterUserClassTypeEnum.VIRTUAL.type)
                .toAccount(imConversation.getFromOuterUserId())
                .toAccountType(imConversation.getFromOuterUserType())
                .responseAccountType(notVirtualUserIdRelation.getOuterClassType())
                .responseAccount(notVirtualUserIdRelation.getOuterUserId())
                .build();
        ImConversationItem imConversationItem = this.returnImConversationItemBean(imConversationBeanBo, conversationId);

        //判断群里虚拟账号对应的账号是否在线，如果不在线发送站内信
        String outerUserIdVirtual = relationByVirtualUserId.getOuterUserId();
        //查询用户是否在线，不在线发送站内信，暂时抛异常给地址
        if(!super.checkUserOnlineStatus(outerUserIdVirtual)){
            log.error("https://test-im-chat-user-client.wanshifu.com/home?cid="+imConversation.getConversationId());
        }

        SendMsgToPersonalResultBo sendMsgToPersonalResultBo = this.sendMsgToPersonal(imConversationBeanBo);
        if(sendMsgToPersonalResultBo==null){
            throw new BusException("向个人发送消息失败");
        }
        imConversationItem.setMsgKey(sendMsgToPersonalResultBo.getMsgKey());
        imConversationItem.setMsgId(sendMsgToPersonalResultBo.getMsgId());
        imConversationItemRepository.insertSelective(imConversationItem);

        // socket推送待处理消息更新
        SendImWebSocketMessageBo sendImWebSocketMessageBo = new SendImWebSocketMessageBo();
        sendImWebSocketMessageBo.setConversationId(conversationId);
        sendImWebSocketMessageBo.setBusinessType(SocketBaseManagerImpl.WAIT_CONVERSATION_UPDATE);
        sendImWebSocketMessageProducer.sendImWebSocketMessage(sendImWebSocketMessageBo);

        return 1;
    }

    /**
     * 发送消息给个人
     * */
    private SendMsgToPersonalResultBo sendMsgToPersonal(ImConversationBeanBo imConversationBeanBo){
        SendMsgPersonalBo sendMsgPersonalBo = new SendMsgPersonalBo();
        sendMsgPersonalBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        sendMsgPersonalBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        sendMsgPersonalBo.setFromAccountId(imConversationBeanBo.getFromAccount());
        sendMsgPersonalBo.setToAccountId(imConversationBeanBo.getToAccount());

        List<SendMsgPersonalBo.MsgContentItem> msgContentItemList = new ArrayList<>();

        for (ImConversationBeanBo.MsgContentItem msgContentItem : imConversationBeanBo.getMsgContentItemList()) {
            SendMsgPersonalBo.MsgContentItem msgContentItem1 = new SendMsgPersonalBo.MsgContentItem();
            msgContentItem1.setMsgType(msgContentItem.getMsgType());
            msgContentItem1.setContent(msgContentItem.getMsgContent());
            msgContentItemList.add(msgContentItem1);
        }
        sendMsgPersonalBo.setMsgContentItemList(msgContentItemList);
        sendMsgPersonalBo.setIsNeedReadReceipt(CommonConstant.ONE);
        return tencentManager.sendMsgToPersonal(sendMsgPersonalBo);

    }





}
