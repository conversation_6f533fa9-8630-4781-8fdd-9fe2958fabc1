package com.wanshifu.factory.callback.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.factory.callback.CallbackHandleI;
import com.wanshifu.iop.im.api.req.tencent.CallbackAfterMsgReportReq;
import com.wanshifu.iop.im.api.req.tencent.CallbackSingleSendMsgAfterReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 单聊发送消息后已读回调
 * 专业版不支持已读回调
 * */


@Component("callback_after_msg_report")
@Slf4j
public class SingleSendMsgAfterReadHandle implements CallbackHandleI {
    @Override
    public Integer callbackAction(Object object) {
        String jsonString = JSON.toJSONString(object);
        CallbackAfterMsgReportReq callbackAfterMsgReportReq = JSONObject.parseObject(jsonString, CallbackAfterMsgReportReq.class);

        //已读回调

        return 0;
    }
}
