package com.wanshifu.factory.callback.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.factory.callback.CallbackHandleI;
import com.wanshifu.iop.im.api.req.tencent.CallbackCreateGroupReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 创建群组后的策略执行
 * */


@Component("callback_after_create_group")
@Slf4j
public class AfterCreateGroupHandle implements CallbackHandleI {
    @Override
    public Integer callbackAction(Object object) {
        String jsonString = JSON.toJSONString(object);
        CallbackCreateGroupReq callbackCreateGroupReq = JSONObject.parseObject(jsonString, CallbackCreateGroupReq.class);

        //加群事件

        return 0;
    }
}
