package com.wanshifu.factory.callback.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.factory.callback.CallbackHandleI;
import com.wanshifu.iop.im.api.req.tencent.CallbackGroupNewMemberJoinReq;
import com.wanshifu.iop.im.api.req.tencent.CallbackImStateChangeReq;
import com.wanshifu.iop.im.domain.enums.OnlineStateEnum;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import com.wanshifu.repository.UserRegisterInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 用户状态变更后的策略执行
 * */


@Component("callback_im_state_change")
@Slf4j
public class ImUserStateChangeHandle implements CallbackHandleI {

    @Resource
    private UserRegisterInfoRepository userRegisterInfoRepository;

    private static final String LOGIN = "login";

    @Override
    public Integer callbackAction(Object object) {
        String jsonString = JSON.toJSONString(object);
        CallbackImStateChangeReq callbackImStateChangeReq = JSONObject.parseObject(jsonString, CallbackImStateChangeReq.class);

        //用户状态变更后回调
        CallbackImStateChangeReq.ImUserInfoItem info = callbackImStateChangeReq.getInfo();
        if(info==null){
            return 1;
        }

        String outerUserId = info.getTo_Account();
        UserRegisterInfo byOuterUserId = userRegisterInfoRepository.findByOuterUserId(outerUserId);
        if(byOuterUserId==null){
            log.info("找不到注册信息，req={}",jsonString);
            return 1;
        }
        UserRegisterInfo userRegisterInfo = new UserRegisterInfo();
        userRegisterInfo.setRegisterInfoId(byOuterUserId.getRegisterInfoId());
        userRegisterInfo.setOnlineState(OnlineStateEnum.OFFLINE.type);
        if(LOGIN.equals(info.getAction())){
            userRegisterInfo.setOnlineState(OnlineStateEnum.ONLINE.type);
        }
        userRegisterInfo.setOnlineStateChangeTime(new Date());
        userRegisterInfo.setUpdateTime(new Date());
        userRegisterInfoRepository.updateByPrimaryKeySelective(userRegisterInfo);
        return 1;
    }
}
