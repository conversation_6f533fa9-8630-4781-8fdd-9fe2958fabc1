package com.wanshifu.factory.callback.action;
import java.util.ArrayList;
import java.util.Date;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.factory.callback.CallbackHandleI;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.ObjectUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.tencent.CallbackGroupNewMemberJoinReq;
import com.wanshifu.iop.im.api.req.tencent.CallbackGroupSendMsgAfterReq;
import com.wanshifu.iop.im.api.req.tencent.CallbackSingleSendMsgAfterReq;
import com.wanshifu.iop.im.domain.bo.AIWorkflowExecuteResultMsgBo;
import com.wanshifu.iop.im.domain.bo.CreateGroupReqBo;
import com.wanshifu.iop.im.domain.bo.GetConversationToAccountInfoBo;
import com.wanshifu.iop.im.domain.bo.GetOnlineStatusBo;
import com.wanshifu.iop.im.domain.bo.GetOnlineStatusRespBo;
import com.wanshifu.iop.im.domain.bo.ImConversationBeanBo;
import com.wanshifu.iop.im.domain.bo.NodeConfigBo;
import com.wanshifu.iop.im.domain.bo.SendMsgGroupBo;
import com.wanshifu.iop.im.domain.bo.SendMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgToPersonalResultBo;
import com.wanshifu.iop.im.domain.bo.ToArtificialMsgBo;
import com.wanshifu.iop.im.domain.bo.conversation.FlowInfoBo;
import com.wanshifu.iop.im.domain.bo.groupManage.GroupRuleConfigValueBo;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.iop.im.domain.enums.BindStatusEnum;
import com.wanshifu.iop.im.domain.enums.ConversationFromTypeEnum;
import com.wanshifu.iop.im.domain.enums.ConversationStatusEnum;
import com.wanshifu.iop.im.domain.enums.CurrentSeatStatusEnum;
import com.wanshifu.iop.im.domain.enums.FlowInstanceStatusEnum;
import com.wanshifu.iop.im.domain.enums.ImConversationTypeEnum;
import com.wanshifu.iop.im.domain.enums.MsgTypeEnum;
import com.wanshifu.iop.im.domain.enums.NodeTypeEnum;
import com.wanshifu.iop.im.domain.enums.OnlineStatusEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserClassTypeEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserTypeEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricConfigEnum;
import com.wanshifu.iop.im.domain.enums.VirtualStatusEnum;
import com.wanshifu.iop.im.domain.po.FlowInstance;
import com.wanshifu.iop.im.domain.po.FlowNode;
import com.wanshifu.iop.im.domain.po.GroupRule;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.ImGroup;
import com.wanshifu.iop.im.domain.po.ImGroupUserRelation;
import com.wanshifu.iop.im.domain.po.RuleMetricConfig;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import com.wanshifu.iop.im.domain.po.UserVirtualRelation;
import com.wanshifu.iop.im.domain.po.VirtualUserInfo;
import com.wanshifu.manager.commonBase.impl.CommonBaseManagerImpl;
import com.wanshifu.manager.socketBase.impl.SocketBaseManagerImpl;
import com.wanshifu.mq.producer.SendImWebSocketMessageProducer;
import com.wanshifu.mq.producer.ToArtificialMsgProducer;
import com.wanshifu.repository.ImConversationRepository;
import com.wanshifu.repository.ImGroupRepository;
import com.wanshifu.repository.SeatInfoRepository;
import com.wanshifu.repository.UserRegisterInfoRepository;
import com.wanshifu.repository.VirtualUserInfoRepository;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 单聊发送消息后回调
 * 留言也会生成会话，但是在发消息回调时，如果第一次接收回调，聊天对象不在线，不生成群聊；只有会话；
 * 在对话过程中如果有一方离线状态，给指定用户发送站内信或者push推送通知
 *
 * */


@Component("callback_after_send_msg")
@Slf4j
public class SingleSendMsgAfterHandle extends CallbackAbstract implements CallbackHandleI {

    @Value("${wanshifu.seat.online}")
    private List<String> seatOnlineList;

    @Value("${wanshifu.im.toArtificial}")
    private String toArtificialWordList;


    @Resource
    private ImInterService imInterService;

    @Resource
    private ToArtificialMsgProducer toArtificialMsgProducer;
    @Autowired
    private CommonBaseManagerImpl commonBaseManagerImpl;

    @Resource
    private SendImWebSocketMessageProducer sendImWebSocketMessageProducer;

    public SingleSendMsgAfterHandle() {
        super();
    }

    /**
     * 需要确认是虚拟账号给用户发消息的回调，回调无需处理
     * 还是用户给虚拟账号发消息的回调
     *
     * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer callbackAction(Object object) {
        String jsonString = JSON.toJSONString(object);
        CallbackSingleSendMsgAfterReq callbackSingleSendMsgAfterReq = JSONObject.parseObject(jsonString, CallbackSingleSendMsgAfterReq.class);

        //发送单聊消息后回调，
        log.info("发消息后回调：" + JSON.toJSONString(callbackSingleSendMsgAfterReq));
        String fromAccount = callbackSingleSendMsgAfterReq.getFrom_Account();
        String toAccount = callbackSingleSendMsgAfterReq.getTo_Account();

        List<VirtualUserInfo> virtualInfoByOuterUserIds = virtualUserInfoRepository.getVirtualInfoByOuterUserIds(Arrays.asList(toAccount, fromAccount));
        if (CollectionUtils.isEmpty(virtualInfoByOuterUserIds)) {
            // 无论师傅+用户，还是师傅+客服，查询结果多有虚拟账号
            log.error("没有虚拟账号信息，无需回调处理，默认返回回调成功");
            return 1;
        }

        if (virtualInfoByOuterUserIds.size() == 2) {
            log.error("都是虚拟账号，无需回调处理，默认返回回调成功");
            return 1;
        }

        VirtualUserInfo virtualUserInfo = virtualInfoByOuterUserIds.get(0);
        if (virtualUserInfo.getOuterUserId().equals(fromAccount)) {
            log.info("虚拟账号发送消息的回调，无需回调处理，默认返回回调成功");
            return 1;
        }

        /**
         * 查询当前回调的会话记录
         * */
        List<ImConversation> infoByFromIdAndToIdAndStatusList = imConversationRepository.getInfoByFromIdAndToIdAndStatus(fromAccount, toAccount, ConversationStatusEnum.PROCESSING.type);

        if (CollectionUtils.isEmpty(infoByFromIdAndToIdAndStatusList)) {
            throw new BusException("不存在会话记录");
        }

        ImConversation infoByFromIdAndToIdAndStatus = infoByFromIdAndToIdAndStatusList.get(0);

        /**
         * 判断是否有群聊记录，判断在线状态，
         * 如果没有群记录，并且会话对象不在线，无需创建群聊，发送站内信、或推送
         * 如果有群聊记录，并且会话对象不在线，发送站内信
         *
         * 单聊无需判断用户状态
         * */
        UserVirtualRelation relationByOuterUserIdAndVirtualUserId = userVirtualRelationRepository.getRelationByOuterUserIdAndVirtualUserId(fromAccount, toAccount, BindStatusEnum.BINDING.type);
        if (relationByOuterUserIdAndVirtualUserId == null) {
            throw new BusException("无绑定关系");
        }

        //路由信息
        FlowInfoBo flowInfoBo = commonBaseManager.returnFlowInfo(infoByFromIdAndToIdAndStatus.getConversationId(), fromAccount);

        List<CallbackSingleSendMsgAfterReq.MsgBodyItem> msgBody = callbackSingleSendMsgAfterReq.getMsgBody();
        CallbackSingleSendMsgAfterReq.MsgBodyItem msgBodyItem = msgBody.get(0);

        String text = null;
        if(MsgTypeEnum.TEXT.type.equals(msgBodyItem.getMsgType())){
            String s = JSON.toJSONString(msgBodyItem.getMsgContent());
            CallbackSingleSendMsgAfterReq.MsgContent msgContent = JSONObject.parseObject(s, CallbackSingleSendMsgAfterReq.MsgContent.class);
            text = msgContent.getText();
        }

        //如果是用户+师傅对话，判断会话记录类型
        ImGroup imGroup = imGroupRepository.selectImGroupByConversationId(infoByFromIdAndToIdAndStatus.getConversationId());
        String groupId = "";
        if(imGroup!=null){
            groupId = imGroup.getOuterGroupId();
        }
        if(StringUtils.isEmpty(infoByFromIdAndToIdAndStatus.getToOuterUserType())){
            //获取群聊接待对象
            GetConversationToAccountInfoBo conversationToAccountInfoBo = this.getConversationToAccountInfoBo(infoByFromIdAndToIdAndStatus);
            infoByFromIdAndToIdAndStatus.setToOuterUserId(conversationToAccountInfoBo.getToAccount());
            infoByFromIdAndToIdAndStatus.setToOuterUserType(conversationToAccountInfoBo.getToAccountType());
            //第一次进入需要重新赋值

            flowInfoBo.setCurrentFlowNode(conversationToAccountInfoBo.getCurrentFlowNode());

            //需要更新会话表，记录机器人账号id
            ImConversation imConversation = new ImConversation();
            imConversation.setConversationId(infoByFromIdAndToIdAndStatus.getConversationId());
            imConversation.setToOuterUserId(conversationToAccountInfoBo.getToAccount());
            imConversation.setToOuterUserType(conversationToAccountInfoBo.getToAccountType());
            imConversationRepository.updateByPrimaryKeySelective(imConversation);

            if(OuterUserClassTypeEnum.SEAT.type.equals(conversationToAccountInfoBo.getToAccountType())){
                //转人工发送表单
                this.callbackToArtificial(infoByFromIdAndToIdAndStatus,text,msgBodyItem,toAccount);
                return 1;
            }
        }

        String fromAccountType = infoByFromIdAndToIdAndStatus.getFromOuterUserType();
        String toAccountType = infoByFromIdAndToIdAndStatus.getToOuterUserType();
        if(!fromAccount.equals(infoByFromIdAndToIdAndStatus.getFromOuterUserId())){
            fromAccountType = toAccountType;
            toAccountType = infoByFromIdAndToIdAndStatus.getFromOuterUserType();
        }
        List<ImConversationBeanBo.MsgContentItem> msgContentItemList = new ArrayList<>();
        for (CallbackSingleSendMsgAfterReq.MsgBodyItem msgBodyItem1 : callbackSingleSendMsgAfterReq.getMsgBody()) {
            ImConversationBeanBo.MsgContentItem msgContentItem = new ImConversationBeanBo.MsgContentItem();
            msgContentItem.setMsgType(msgBodyItem1.getMsgType());
            msgContentItem.setMsgContent(msgBodyItem1.getMsgContent());
            msgContentItemList.add(msgContentItem);
        }

        ImConversationBeanBo imConversationBeanBo = ImConversationBeanBo.builder()
                .MsgKey(callbackSingleSendMsgAfterReq.getMsgKey())
                .MsgId(callbackSingleSendMsgAfterReq.getMsgId())
                .msgContentItemList(msgContentItemList)
                .MsgSeq(callbackSingleSendMsgAfterReq.getMsgSeq())
                .msgContentJson(JSON.toJSONString(callbackSingleSendMsgAfterReq.getMsgBody()))
                .fromAccount(fromAccount)
                //用户向虚拟账号发送消息，没有回复值
                .fromAccountType(fromAccountType)
                .responseAccountType("")
                .responseAccount("")
                .toAccount(toAccount)
                .toAccountType(toAccountType)
                .build();

        ImConversationItem imConversationItem = this.returnImConversationItemBean(imConversationBeanBo, infoByFromIdAndToIdAndStatus.getConversationId());
        imConversationItemRepository.insertSelective(imConversationItem);

        if(imGroup!=null
                && OuterUserTypeEnum.SINGLE_USER_TYPE.contains(infoByFromIdAndToIdAndStatus.getFromOuterUserType())
                && OuterUserTypeEnum.SINGLE_USER_TYPE.contains(infoByFromIdAndToIdAndStatus.getToOuterUserType())
        ){
            //如果是用户给师傅发消息，需要判断接收对象是否在线，如果不在线，暂时抛异常发送留言链接站内信
            this.notKfConversation(imGroup,infoByFromIdAndToIdAndStatus);
            return 1;
        }

        if(OuterUserClassTypeEnum.SEAT.type.equals(infoByFromIdAndToIdAndStatus.getToOuterUserType()) && StringUtils.isNotEmpty(infoByFromIdAndToIdAndStatus.getToOuterUserId())){
            //会话消息对象只有客服时才向群聊中发送消息
            this.sendMsgToGroup(groupId, toAccount, callbackSingleSendMsgAfterReq);
            // socket推送待处理消息更新
            SendImWebSocketMessageBo sendImWebSocketMessageBo = new SendImWebSocketMessageBo();
            sendImWebSocketMessageBo.setConversationId(infoByFromIdAndToIdAndStatus.getConversationId());
            sendImWebSocketMessageBo.setBusinessType(SocketBaseManagerImpl.WAIT_CONVERSATION_UPDATE);
            sendImWebSocketMessageProducer.sendImWebSocketMessage(sendImWebSocketMessageBo);
            return 1;
        }


        if(StringUtils.isNotEmpty(toArtificialWordList) && flowInfoBo.getNextFlowNode()!=null && NodeTypeEnum.ARTIFICIAL.type.equals(flowInfoBo.getNextFlowNode().getNodeType())){
            //转人工发送表单
            List<String> toArtificialWords = Arrays.stream(toArtificialWordList.split(",")).collect(Collectors.toList());
            if (toArtificialWords.contains(text)) {
                this.callbackToArtificial(infoByFromIdAndToIdAndStatus,text,msgBodyItem,toAccount);
                return 1;
            }
        }

        //给机器人发消息
        if(MsgTypeEnum.TEXT.type.equals(msgBodyItem.getMsgType()) && flowInfoBo.getCurrentFlowNode()!=null && NodeTypeEnum.ROBOT.type.equals(flowInfoBo.getCurrentFlowNode().getNodeType())){
            super.sendMsgToRobot(infoByFromIdAndToIdAndStatus.getConversationId(), text);
            return 1;
        }
        //发送系统消息
        if(flowInfoBo.getCurrentFlowNode()!=null && NodeTypeEnum.SYSTEM_MSG.type.equals(flowInfoBo.getCurrentFlowNode().getNodeType())){
            AIWorkflowExecuteResultMsgBo aiWorkflowExecuteResultMsgBo = new AIWorkflowExecuteResultMsgBo();

            NodeConfigBo nodeConfigBo = JSONObject.parseObject(flowInfoBo.getCurrentFlowNode().getConfigJson(), NodeConfigBo.class);
            if(StringUtils.isNotEmpty(nodeConfigBo.getAutoReplyContent())){
                aiWorkflowExecuteResultMsgBo.setAnswer(nodeConfigBo.getAutoReplyContent());
                aiWorkflowExecuteResultMsgBo.setConversationId(infoByFromIdAndToIdAndStatus.getConversationId()+"");
                imInterService.sendAIMsgToPersonal(aiWorkflowExecuteResultMsgBo);
            }
        }

        return 1;
    }

    /**
     * 师傅、用户对话场景
     * */
    private void notKfConversation(ImGroup imGroup, ImConversation imConversation){
        //如果是用户给师傅发消息，需要判断接收对象是否在线，如果不在线，暂时抛异常发送留言链接站内信
        List<ImGroupUserRelation> groupUserRelationsByImGroupIdList = imGroupUserRelationRepository.getGroupUserRelationsByImGroupId(imGroup.getImGroupId(), OuterUserClassTypeEnum.VIRTUAL.type);
        if(CollectionUtils.isNotEmpty(groupUserRelationsByImGroupIdList)){
            ImGroupUserRelation imGroupUserRelation = groupUserRelationsByImGroupIdList.get(0);
            String outerUserId = imGroupUserRelation.getOuterUserId();
            //查询用户是否在线，不在线发送站内信，暂时抛异常给地址
            if(!super.checkUserOnlineStatus(outerUserId)){
                throw new BusException("https://test-im-chat-user-client.wanshifu.com/home?cid="+imConversation.getConversationId());
            }
        }
    }

    /**
     * 回调转人工
     * */
    private void callbackToArtificial(ImConversation infoByFromIdAndToIdAndStatus, String text, CallbackSingleSendMsgAfterReq.MsgBodyItem msgBodyItem, String fromAccount) {

        if(!MsgTypeEnum.TEXT.type.equals(msgBodyItem.getMsgType())){
            return;
        }

        if(StringUtils.isNotEmpty(text) ){
            //发送询前表单给用户
            ToArtificialMsgBo toArtificialMsgBo = new ToArtificialMsgBo();
            toArtificialMsgBo.setConversationId(infoByFromIdAndToIdAndStatus.getConversationId());
            toArtificialMsgBo.setFromAccount(fromAccount);
            imInterService.sendPreForm(toArtificialMsgBo);
//          toArtificialMsgProducer.sendMessage(toArtificialMsgBo);
        }

    }

    /**
     * 返回会话接待的对象，可能是机器人或者是坐席，只有第一次进入的时候才有
     * */
    private GetConversationToAccountInfoBo getConversationToAccountInfoBo(ImConversation imConversation){
        GetConversationToAccountInfoBo result = new GetConversationToAccountInfoBo();
        FlowNode nextNode = commonBaseManager.getNextNode(imConversation.getConversationId(), NodeTypeEnum.START.type);
        //默认机器人 
        String toAccountType = null;
        String toAccount;
        if(nextNode==null){
            log.error("没有下一个节点，会话id={}", imConversation.getConversationId()); 
            return result;
        }

        if(NodeTypeEnum.ROBOT.type.equals(nextNode.getNodeType())){
            toAccountType = OuterUserClassTypeEnum.ROBOT.type;
        }

        if(NodeTypeEnum.SYSTEM_MSG.type.equals(nextNode.getNodeType()) || NodeTypeEnum.LEAVE_WORD.type.equals(nextNode.getNodeType()) ){
            toAccountType = OuterUserTypeEnum.SYSTEM.type;
        }

        if(NodeTypeEnum.ARTIFICIAL.type.equals(nextNode.getNodeType())){
            toAccountType = OuterUserClassTypeEnum.SEAT.type;
        }
        result.setToAccountType(toAccountType);

        result.setCurrentFlowNode(nextNode);

        FlowInstance flowInstance = new FlowInstance();
        flowInstance.setFlowDefineId(nextNode.getFlowDefineId());
        flowInstance.setConversationId(imConversation.getConversationId());
        flowInstance.setOuterUserId(imConversation.getFromOuterUserId());
        flowInstance.setCurrentNodeId(nextNode.getFlowNodeId());
        flowInstance.setStatus(FlowInstanceStatusEnum.RUNNING.type);
        flowInstance.setIsDelete(CommonConstant.ZERO);
        flowInstance.setUpdateTime(new Date());
        flowInstance.setCreateTime(new Date());
        flowInstanceRepository.insertSelective(flowInstance);
        return result;
    }

    /**
     * 生成入参、建群
     * */
    public String returnGroupIdStr(Long conversationId, String toAccount, String seatOuterUserId, String outerClassType) {
            if(CollectionUtils.isNotEmpty(seatOnlineList)){
                //创建群聊成员：虚拟账号+指定测试账号对应的outerUserId
                CreateGroupReqBo createGroupReqBo = new CreateGroupReqBo();
                createGroupReqBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
                createGroupReqBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
                createGroupReqBo.setGroupName(CommonConstant.GROUP_NAME);
                createGroupReqBo.setConversationId(conversationId);
                createGroupReqBo.setAdminOuterUserId(toAccount);
                createGroupReqBo.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);

                List<CreateGroupReqBo.CreateGroupReqBoItem> groupMembers = new ArrayList<>();
                CreateGroupReqBo.CreateGroupReqBoItem createGroupReqBoItem = new CreateGroupReqBo.CreateGroupReqBoItem();
                createGroupReqBoItem.setOuterUserId(toAccount);
                createGroupReqBoItem.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);
                groupMembers.add(createGroupReqBoItem);
                CreateGroupReqBo.CreateGroupReqBoItem createGroupSeatReqBoItem = new CreateGroupReqBo.CreateGroupReqBoItem();
                createGroupSeatReqBoItem.setOuterUserId(seatOuterUserId);
                createGroupSeatReqBoItem.setOuterClassType(outerClassType);
                groupMembers.add(createGroupSeatReqBoItem);

                createGroupReqBo.setGroupMembers(groupMembers);
                try {
                    String groupId = tencentManager.createGroup(createGroupReqBo);
                    if (groupId == null) {
                        throw new BusException("创建群聊失败");
                    }
                    return groupId;
                } catch (BusException e) {
                    throw new BusException(e.getMessage());
                }
            }else{
                throw new BusException("没有在线坐席，无法创建群聊");
            }

    }

    /**
     * 向群聊中发送消息
     * */
    public void sendMsgToGroup(String groupId, String toAccount,  CallbackSingleSendMsgAfterReq callbackSingleSendMsgAfterReq){
        SendMsgGroupBo sendMsgGroupBo = new SendMsgGroupBo();
        sendMsgGroupBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        sendMsgGroupBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        sendMsgGroupBo.setGroupId(groupId);
        sendMsgGroupBo.setFromAccountId(toAccount);

        List<CallbackSingleSendMsgAfterReq.MsgBodyItem> msgBody = callbackSingleSendMsgAfterReq.getMsgBody();

        List<SendMsgGroupBo.MsgListItem> msgList = new ArrayList<>();

        for (CallbackSingleSendMsgAfterReq.MsgBodyItem msgBodyItem : msgBody) {
            SendMsgGroupBo.MsgListItem msgListItem = new SendMsgGroupBo.MsgListItem();
            msgListItem.setMsgType(msgBodyItem.getMsgType());
            msgListItem.setContent(msgBodyItem.getMsgContent());
            msgList.add(msgListItem);
        }
        sendMsgGroupBo.setMsgList(msgList);
        tencentManager.sendMsgToGroup(sendMsgGroupBo);
    }

    /**
     * 写入会话记录
     * */
    private ImConversationItem returnImConversationItemBean(ImConversationBeanBo req, Long conversationId){

        String nodeType = null;
        if(OuterUserTypeEnum.SYSTEM.type.equals(req.getToAccountType())){
            FlowInstance flowInstanceByConversationIdAndOuterUserId = flowInstanceRepository.getFlowInstanceByConversationIdAndOuterUserId(conversationId, req.getFromAccount());
            if(flowInstanceByConversationIdAndOuterUserId!=null){
                FlowNode flowNode = flowNodeRepository.selectByPrimaryKey(flowInstanceByConversationIdAndOuterUserId.getCurrentNodeId());
                nodeType = flowNode.getNodeType();
            }
        }

        ImConversationItem imConversationItem = new ImConversationItem();
        imConversationItem.setConversationId(conversationId);
        imConversationItem.setFromOuterUserId(req.getFromAccount());
        imConversationItem.setFromOuterUserType(req.getFromAccountType());
        imConversationItem.setToOuterUserId(req.getToAccount());
        imConversationItem.setToOuterUserType(req.getToAccountType());
        imConversationItem.setResponseOuterUserId(req.getResponseAccount());
        imConversationItem.setResponseOuterUserType(req.getResponseAccountType());
        imConversationItem.setMsgSendTime(new Date());
        imConversationItem.setHasRead(0);
        imConversationItem.setMsgReadTime(new Date());
        imConversationItem.setMsgType(req.getMsgContentItemList().get(0).getMsgType());
        imConversationItem.setMsgContent(JSON.toJSONString(req.getMsgContentItemList()));
        imConversationItem.setMsgSeq(req.getMsgSeq());
        imConversationItem.setMsgKey(req.getMsgKey());
        imConversationItem.setMsgId(req.getMsgId());
        imConversationItem.setIsDelete(0);
        imConversationItem.setUpdateTime(new Date());
        imConversationItem.setCreateTime(new Date());
        imConversationItem.setMsgLabel(ImConversationTypeEnum.online.type);
        if(NodeTypeEnum.LEAVE_WORD.type.equals(nodeType)){
            imConversationItem.setMsgLabel(ImConversationTypeEnum.leave.type);
        }

        return imConversationItem;
    }



    /**
     * 用户是否在线
     * */
    public boolean getUserOnlineStatus(String outUserId){
        //获取用户在线状态，如果是给虚拟账号发送无需判断状态
        GetOnlineStatusBo getOnlineStatusBo = new GetOnlineStatusBo();
        getOnlineStatusBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        getOnlineStatusBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        getOnlineStatusBo.setOuterUserIds(Collections.singletonList(outUserId));
        GetOnlineStatusRespBo onlineStatus = tencentManager.getOnlineStatus(getOnlineStatusBo);
        if(onlineStatus == null){
            return false;
        }
        List<GetOnlineStatusRespBo.OnlineItem> results = onlineStatus.getResults();
        if(CollectionUtils.isEmpty(results)){
            return false;
        }
        if (!OnlineStatusEnum.ON_LINE.type.equals(results.get(0).getStatus())) {
            return false;
        }
        return true;
    }

    /**
     * 群聊回调执行
     * 坐席账号才需要处理回调
     * */
    public Integer groupCallback(String objectStr){
        CallbackGroupSendMsgAfterReq callbackGroupSendMsgAfterReq = JSONObject.parseObject(objectStr, CallbackGroupSendMsgAfterReq.class);
        String groupId = callbackGroupSendMsgAfterReq.getGroupId();
        ImGroup imGroup = imGroupRepository.selectImGroupByOuterGroupId(groupId);
        if(imGroup==null){
            throw new BusException("群组不存在");
        }

        //查询群里的用户代理账号
        List<ImGroupUserRelation> groupUserRelationByImGroupId = imGroupUserRelationRepository.getGroupUserRelationsByImGroupId(imGroup.getImGroupId(), null);
        Optional<ImGroupUserRelation> userRelationOptional = groupUserRelationByImGroupId.stream().filter(f -> f.getOuterClassType().equals(OuterUserClassTypeEnum.VIRTUAL.type)).findFirst();
        if(!userRelationOptional.isPresent()){
            throw new BusException("群组不存在虚拟账号");
        }
        ImGroupUserRelation imGroupUserRelation = userRelationOptional.get();

        //群里的用户代理账号
        String outerUserId = imGroupUserRelation.getOuterUserId();

        UserVirtualRelation relationByVirtualUserId = userVirtualRelationRepository.getRelationByVirtualUserId(outerUserId, BindStatusEnum.BINDING.type);
        if(relationByVirtualUserId==null){
            throw new BusException("代理账号不存在绑定关系");
        }

        //会话id
        Long conversationId = imGroup.getConversationId();

        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if(imConversation==null){
            throw new BusException("会话不存在");
        }
        Optional<ImGroupUserRelation> notVirtualOuterUserIdOptional = groupUserRelationByImGroupId.stream().filter(f -> !f.getOuterClassType().equals(OuterUserClassTypeEnum.VIRTUAL.type)).findFirst();
        if(!notVirtualOuterUserIdOptional.isPresent()){
            throw new BusException("群里只有虚拟账号异常");
        }
        ImGroupUserRelation notVirtualUserIdRelation = notVirtualOuterUserIdOptional.get();

        UserRegisterInfo byOuterUserId = userRegisterInfoRepository.findByOuterUserId(relationByVirtualUserId.getOuterUserId());

        List<ImConversationBeanBo.MsgContentItem> msgContentItemList = new ArrayList<>();
        for (CallbackGroupSendMsgAfterReq.MsgBodyItem msgBodyItem : callbackGroupSendMsgAfterReq.getMsgBody()) {
            ImConversationBeanBo.MsgContentItem msgContentItem = new ImConversationBeanBo.MsgContentItem();
            msgContentItem.setMsgType(msgBodyItem.getMsgType());
            msgContentItem.setMsgContent(msgBodyItem.getMsgContent());
            msgContentItemList.add(msgContentItem);
        }


        //向用户转发消息，插入会话明细
        ImConversationBeanBo imConversationBeanBo = ImConversationBeanBo.builder().MsgKey(null)
                .MsgId(callbackGroupSendMsgAfterReq.getMsgId())
                .MsgSeq(callbackGroupSendMsgAfterReq.getMsgSeq())
                .msgContentJson(JSON.toJSONString(callbackGroupSendMsgAfterReq.getMsgBody()))
                .msgContentItemList(msgContentItemList)
                .fromAccount(outerUserId)
                .fromAccountType(OuterUserClassTypeEnum.VIRTUAL.type)
                .toAccount(relationByVirtualUserId.getOuterUserId())
                .toAccountType(byOuterUserId.getUserClass())
                .responseAccountType(notVirtualUserIdRelation.getOuterClassType())
                .responseAccount(notVirtualUserIdRelation.getOuterUserId())
                .build();

        boolean sendMsgToPersonalBoolean = this.sendMsgToPersonal(imConversationBeanBo);
        if(!sendMsgToPersonalBoolean){
            throw new BusException("向个人发送消息失败");
        }
        ImConversationItem imConversationItem = this.returnImConversationItemBean(imConversationBeanBo, conversationId);
        imConversationItemRepository.insertSelective(imConversationItem);

        return 1;
    }

    /**
     * 发送消息给个人
     * */
    private boolean sendMsgToPersonal(ImConversationBeanBo imConversationBeanBo){
        SendMsgPersonalBo sendMsgPersonalBo = new SendMsgPersonalBo();
        sendMsgPersonalBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        sendMsgPersonalBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        sendMsgPersonalBo.setFromAccountId(imConversationBeanBo.getFromAccount());
        sendMsgPersonalBo.setToAccountId(imConversationBeanBo.getToAccount());
        List<SendMsgPersonalBo.MsgContentItem> msgContentItemList = new ArrayList<>();
        for (ImConversationBeanBo.MsgContentItem msgContentItem : imConversationBeanBo.getMsgContentItemList()) {
            SendMsgPersonalBo.MsgContentItem msgContentItem1 = new SendMsgPersonalBo.MsgContentItem();
            msgContentItem1.setMsgType(msgContentItem.getMsgType());
            msgContentItem1.setContent(msgContentItem.getMsgContent());
            msgContentItemList.add(msgContentItem1);
        }

        sendMsgPersonalBo.setMsgContentItemList(msgContentItemList);
        sendMsgPersonalBo.setIsNeedReadReceipt(CommonConstant.ONE);
        SendMsgToPersonalResultBo sendMsgToPersonalResultBo = tencentManager.sendMsgToPersonal(sendMsgPersonalBo);
        if(sendMsgToPersonalResultBo==null){
            return false;
        }
        if(StringUtils.isEmpty(sendMsgToPersonalResultBo.getMsgId())){
            return false;
        }
        return true;
    }





}
