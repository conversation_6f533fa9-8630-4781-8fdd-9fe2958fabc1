package com.wanshifu.factory.rule.action;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.factory.rule.RuleHandleI;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.group.SelectRuleMetricMappingReq;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.domain.bo.groupManage.GroupRuleConfigValueBo;
import com.wanshifu.iop.im.domain.bo.groupManage.RuleMetricMappingBo;
import com.wanshifu.iop.im.domain.enums.RuleConfigValueSelectiveTypeEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricConfigEnum;
import com.wanshifu.iop.im.domain.enums.UserClassEnum;
import com.wanshifu.iop.im.domain.po.GroupRule;
import com.wanshifu.iop.im.domain.po.RuleMetricConfig;
import com.wanshifu.iop.im.domain.po.UserProblemClass;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.iop.im.domain.po.channel.ClientCategoryConfig;
import com.wanshifu.repository.GroupInfoRepository;
import com.wanshifu.repository.UserProblemClassRepository;
import com.wanshifu.repository.channel.ChannelConfigRepository;
import com.wanshifu.repository.channel.ClientCategoryConfigRepository;
import com.wanshifu.service.AbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 分组管理模块
 * <AUTHOR>
 * @date： 2025-06-04 15:19:51
 */
@Component("groupRule")
@Slf4j
public class GroupRuleHandle extends AbstractService implements RuleHandleI {

    @Resource
    private ClientCategoryConfigRepository clientCategoryConfigRepository;

    @Resource
    private ChannelConfigRepository channelConfigRepository;

    @Resource
    private UserProblemClassRepository userProblemClassRepository;

    /**
     * 验证重复名称
     * @param req
     * @return
     */
    @Override
    public List<RuleMetricMappingBo> selectRuleMetricMapping(SelectRuleMetricMappingReq req) {
        List<RuleMetricConfig> ruleMetricList = req.getRuleMetricConfigList();
        List<GroupRule> groupRulesList = req.getGroupRulesList();

        // 从规则中获取对于的规则配置，比如会话渠道的规则指标id
        RuleMetricConfig ruleMetricConfig = ruleMetricList.stream().filter(ruleMetric -> ruleMetric.getRuleMetricEn().equals(req.getRuleMetricConfigEnum().type)).findFirst().orElse(null);
        if (Objects.isNull(ruleMetricConfig)) {
            return new ArrayList<>();
        }

        // 从分组规则中获取对应的规则的配置
        List<GroupRule> rangeGroupRulesList = groupRulesList.stream().filter(groupRule -> groupRule.getRuleMetricId().equals(ruleMetricConfig.getRuleMetricId())).collect(Collectors.toList());
        if ( CollectionUtils.isEmpty(rangeGroupRulesList) ){
            return new ArrayList<>();
        }

        // 获取规则中配置选中的值
        List<String> ruleConfigValueList = new ArrayList<>();
        rangeGroupRulesList.stream().filter(groupRule-> StringUtils.isNotBlank(groupRule.getRuleConfigValue())).forEach(groupRule -> {
            GroupRuleConfigValueBo groupRuleConfigValueBo = JSONObject.parseObject(groupRule.getRuleConfigValue(), GroupRuleConfigValueBo.class);

            if ( StringUtils.isBlank(groupRuleConfigValueBo.getSelectiveType()) ||
                    ( CollectionUtils.isEmpty(groupRuleConfigValueBo.getMultipleValue()) && StringUtils.isBlank(groupRuleConfigValueBo.getSingleValue()) ) ){
                return;
            }

            // 多选值
            if (RuleConfigValueSelectiveTypeEnum.MULTIPLE.type.equals(groupRuleConfigValueBo.getSelectiveType())){
                ruleConfigValueList.addAll(groupRuleConfigValueBo.getMultipleValue());
            }

            // 单选值
            if (RuleConfigValueSelectiveTypeEnum.SINGLE.type.equals(groupRuleConfigValueBo.getSelectiveType())){
                ruleConfigValueList.add(groupRuleConfigValueBo.getSingleValue());
            }
        });

        if ( CollectionUtils.isEmpty(ruleConfigValueList) ){
            return new ArrayList<>();
        }

        // 通过配置的值和传递的规则类型查询源表数据
        List<CommonLabelValueResp> ruleMetricMappingList = this.getConfigValueMappingByRule(ruleMetricConfig, ruleConfigValueList);
        if ( CollectionUtils.isEmpty(ruleMetricMappingList) ){
            return new ArrayList<>();
        }

        // 拼装源表数据
        List<RuleMetricMappingBo> result = new ArrayList<>();
        rangeGroupRulesList.stream().filter(groupRule-> StringUtils.isNotBlank(groupRule.getRuleConfigValue())).forEach(groupRule -> {
            GroupRuleConfigValueBo groupRuleConfigValueBo = JSONObject.parseObject(groupRule.getRuleConfigValue(), GroupRuleConfigValueBo.class);
            if ( StringUtils.isBlank(groupRuleConfigValueBo.getSelectiveType()) ||
                    ( CollectionUtils.isEmpty(groupRuleConfigValueBo.getMultipleValue()) && StringUtils.isBlank(groupRuleConfigValueBo.getSingleValue()) ) ){
                return;
            }

            RuleMetricMappingBo ruleMetricMappingBo = new RuleMetricMappingBo();
            ruleMetricMappingBo.setGroupId(groupRule.getGroupId());
            ruleMetricMappingBo.setGroupRuleId(groupRule.getGroupRuleId());
            ruleMetricMappingBo.setRuleMetricEn(ruleMetricConfig.getRuleMetricEn());

            // 多选值
            if (RuleConfigValueSelectiveTypeEnum.MULTIPLE.type.equals(groupRuleConfigValueBo.getSelectiveType())
                    && CollectionUtils.isNotEmpty(groupRuleConfigValueBo.getMultipleValue())
            ){
                List<CommonLabelValueResp> mappingRelationList = ruleMetricMappingList.stream().filter(f -> groupRuleConfigValueBo.getMultipleValue().contains(f.getValue())).collect(Collectors.toList());
                ruleMetricMappingBo.setMappingRelationList(mappingRelationList);
                result.add(ruleMetricMappingBo);
            }

            // 单选值
            if (RuleConfigValueSelectiveTypeEnum.SINGLE.type.equals(groupRuleConfigValueBo.getSelectiveType())
                    && StringUtils.isNotBlank(groupRuleConfigValueBo.getSingleValue())
            ){
                List<CommonLabelValueResp> mappingRelationList = ruleMetricMappingList.stream().filter(f -> groupRuleConfigValueBo.getSingleValue().equals(f.getValue())).collect(Collectors.toList());
                ruleMetricMappingBo.setMappingRelationList(mappingRelationList);
                result.add(ruleMetricMappingBo);
            }
        });

        return result;
    }

    /**
     * 通过配置的值和传递的规则类型查询源表数据
     * @param ruleMetricConfig
     * @param ruleConfigValueList
     * @return
     */
    private List<CommonLabelValueResp> getConfigValueMappingByRule(RuleMetricConfig ruleMetricConfig, List<String> ruleConfigValueList) {
        List<Long> longIdList = ruleConfigValueList.stream().map(Long::parseLong).collect(Collectors.toList());

        // 会话渠道
        if (RuleMetricConfigEnum.CONVERSATION_CHANNEL.type.equals(ruleMetricConfig.getRuleMetricEn())){
            List<ClientCategoryConfig> clientCategoryConfigs = clientCategoryConfigRepository.selectByIdList(longIdList);
            return clientCategoryConfigs.stream().map(clientCategoryConfig -> {
                CommonLabelValueResp commonLabelValueResp = new CommonLabelValueResp();
                commonLabelValueResp.setLabel(clientCategoryConfig.getClientCategoryName());
                commonLabelValueResp.setValue(clientCategoryConfig.getClientCategoryConfigId().toString());
                return commonLabelValueResp;
            }).collect(Collectors.toList());
        }

        // 会话入口
        if (RuleMetricConfigEnum.CONVERSATION_ENTRY.type.equals(ruleMetricConfig.getRuleMetricEn())){
            List<ChannelConfig> channelConfigList = channelConfigRepository.selectByIdList(longIdList);
            return channelConfigList.stream().map(channelConfig -> {
                CommonLabelValueResp commonLabelValueResp = new CommonLabelValueResp();
                commonLabelValueResp.setLabel(channelConfig.getChannelName());
                commonLabelValueResp.setValue(channelConfig.getChannelId().toString());
                return commonLabelValueResp;
            }).collect(Collectors.toList());
        }

        // 获取咨询类型
        if (RuleMetricConfigEnum.PROBLEM_CLASS.type.equals(ruleMetricConfig.getRuleMetricEn())){
            List<UserProblemClass> userClassList = userProblemClassRepository.selectByIdList(longIdList);
            return userClassList.stream().map(userProblemClass -> {
                CommonLabelValueResp commonLabelValueResp = new CommonLabelValueResp();
                commonLabelValueResp.setLabel(userProblemClass.getProblemName());
                commonLabelValueResp.setValue(userProblemClass.getProblemId().toString());
                return commonLabelValueResp;
            }).collect(Collectors.toList());
        }

        return new ArrayList<>();
    }
}
