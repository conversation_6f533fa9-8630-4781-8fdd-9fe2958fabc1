package com.wanshifu.factory.rule;

import com.wanshifu.factory.DataManipulationHandleI;
import com.wanshifu.utils.ApplicationContextUtil;

/**
 * 数据操作
 * <AUTHOR>
 * @date： 2025-06-04 15:03:40
 */
public class RuleFactory {

    /**
     * 获取规则配置信息
     * @param ruleType
     * @return
     */
    public static RuleHandleI setRuleType(String ruleType) {
        return ApplicationContextUtil.getBean(ruleType);
    }
}
