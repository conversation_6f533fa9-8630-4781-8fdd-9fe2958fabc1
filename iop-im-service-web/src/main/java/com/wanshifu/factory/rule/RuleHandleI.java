package com.wanshifu.factory.rule;

import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.group.SelectRuleMetricMappingReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.CommonRunEnumsResp;
import com.wanshifu.iop.im.api.resp.CommonRunStrategyResp;
import com.wanshifu.iop.im.api.resp.ResultResp;
import com.wanshifu.iop.im.domain.bo.groupManage.RuleMetricMappingBo;

import java.util.List;

public interface RuleHandleI {

    /**
     * 获取规则配置值
     */
    List<RuleMetricMappingBo> selectRuleMetricMapping(SelectRuleMetricMappingReq req);
}
