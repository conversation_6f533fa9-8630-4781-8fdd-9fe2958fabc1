package com.wanshifu.factory.visitor.action;

import com.wanshifu.factory.visitor.VisitorHandleI;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.resp.rightSidebar.GetVisitorInfoResp;
import com.wanshifu.iop.im.domain.bo.innerAccountService.GetThirdPartyIpLocationRespBo;
import com.wanshifu.iop.im.domain.enums.OuterUserTypeEnum;
import com.wanshifu.iop.im.domain.enums.PropertyTypeEnum;
import com.wanshifu.iop.im.domain.enums.rightSidebar.FieldAttributeFromTemplateTypeEnum;
import com.wanshifu.iop.im.domain.po.ConversationVisitorDetail;
import com.wanshifu.iop.im.domain.po.FieldAttribute;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import com.wanshifu.iop.im.domain.po.VisitorInfoTemplate;
import com.wanshifu.orderconfig.domains.respbean.ConfigServerCategoryResp;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.inner.InnerVisitorQueryService;
import com.wanshifu.user.domain.resp.user.GetUserInfoAllToIopResp;
import com.wanshifu.wallet.domains.po.WalletBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 家庭访客处理
 * <AUTHOR>
 * @date： 2025-07-25 11:24:06
 */
@Component("client")
@Slf4j
public class ClientHandle extends AbstractService implements VisitorHandleI {

    @Resource
    private InnerVisitorQueryService innerVisitorQueryService;

    /**
     * 查询访客信息
     * @param req
     * @return
     */
    @Override
    public List<GetVisitorInfoResp> queryVisitorInfo(UserRegisterInfo req, Long conversationId) {
        // 查询当前用户的访客模板
        VisitorInfoTemplate visitorInfoTemplate = visitorInfoTemplateRepository.selectByVisitorClassEn(req.getUserClass(), req.getImId());
        if (Objects.isNull(visitorInfoTemplate)){
            log.error("访客{}模板不存在", req.getUserClass());
            return new ArrayList<>();
        }

        // 访问信息
        List<FieldAttribute> fieldAttributeList = fieldAttributeRepository.selectByVisitorInfoTemplateId(visitorInfoTemplate.getVisitorInfoTemplateId()
                , FieldAttributeFromTemplateTypeEnum.VISITOR_INFO.type);
        if (CollectionUtils.isEmpty(fieldAttributeList)){
            log.error("访客{}模板字段不存在", req.getUserClass());
            return new ArrayList<>();
        }

        // 查询用户信息
        GetUserInfoAllToIopResp userInfoAllToIop = innerVisitorQueryService.getUserInfoAllToIop(req.getUserId());
        if (Objects.isNull(userInfoAllToIop)){
            log.error("获取访客{}用户{}信息异常", req.getUserClass(), req.getUserId());
            return new ArrayList<>();
        }

        // 商家类目
        List<ConfigServerCategoryResp> configServerList = new ArrayList<>();
        if ( StringUtils.isNotBlank(userInfoAllToIop.getMainCategories()) ){
            SimplePageInfo<ConfigServerCategoryResp> configServerListSimple = innerVisitorQueryService.getListServerCategory(PropertyTypeEnum.USER.type);
            if ( CollectionUtils.isNotEmpty(configServerListSimple.getList()) ){
                configServerList.addAll(configServerListSimple.getList());
            }
        }

        // 查询用户钱包信息
        WalletBase walletBase = innerVisitorQueryService.getWalletInfo(req.getUserId(), OuterUserTypeEnum.MERCHANT.type);

        // 通过ip地址获取用户所在地区
        // conversation_visitor_detail
        ConversationVisitorDetail conversationVisitorDetail = conversationVisitorDetailRepository.selectByConversationId(conversationId);
        GetThirdPartyIpLocationRespBo thirdPartyIpLocation;
        if ( Objects.nonNull(conversationVisitorDetail) && StringUtils.isNotBlank(conversationVisitorDetail.getIp()) ){
            thirdPartyIpLocation = innerVisitorQueryService.getThirdPartyIpLocation(conversationVisitorDetail.getIp());
        } else {
            thirdPartyIpLocation = null;
        }

        return fieldAttributeList.stream().map(fieldAttribute -> {
            GetVisitorInfoResp respItem = new GetVisitorInfoResp();
            respItem.setFieldName(fieldAttribute.getFieldCn());
            respItem.setFieldEn(fieldAttribute.getFieldEn());
            respItem.setValueType("single");

            switch (fieldAttribute.getFieldEn()) {
                case "userId":// 用户id
                    respItem.setSingleValue(userInfoAllToIop.getUserId().toString());
                    break;
                case "walletBalance":// 钱包余额
                    if ( Objects.nonNull(walletBase) ){
                        respItem.setSingleValue(walletBase.getRemainAmount().toString());
                    }
                    break;
                case "voucherAmount":// 现金券金额
                    respItem.setSingleValue(userInfoAllToIop.getVoucherAmout().toString());
                    break;
                case "mainCategories":
                    if ( StringUtils.isNoneBlank(userInfoAllToIop.getMainCategories()) ){
                        List<String> categoryIdList = new ArrayList<>(Arrays.asList(userInfoAllToIop.getMainCategories().split(",")));
                        // 逗号连接
                        List<String> categoryName = configServerList.stream().filter(f -> categoryIdList.contains(f.getId().toString())).map(ConfigServerCategoryResp::getCnName).collect(Collectors.toList());
                        respItem.setSingleValue(StringUtils.join(categoryName, ","));
                    }
                    break;
                case "customerNote":// 用户备注
                    respItem.setSingleValue(userInfoAllToIop.getCustomerNote());
                    break;
                case "restrictedFunctions":// 用户限制 todo
//                    respItem.setSingleValue(JSONObject.toJSONString(userInfoAllToIop.getRestrictedFunctions()));
                    break;
                case "region":// ip地址所在地区
                    List<String> regionList = new ArrayList<>();
                    if ( Objects.nonNull(thirdPartyIpLocation) ){
                        if ( StringUtils.isNoneBlank(thirdPartyIpLocation.getProvince()) ){
                            regionList.add(thirdPartyIpLocation.getProvince());
                        }
                        if ( StringUtils.isNoneBlank(thirdPartyIpLocation.getCity()) ){
                            regionList.add(thirdPartyIpLocation.getCity());
                        }
                        if ( StringUtils.isNoneBlank(thirdPartyIpLocation.getDistricts()) ){
                            regionList.add(thirdPartyIpLocation.getDistricts());
                        }
                    }

                    if ( CollectionUtils.isNotEmpty(regionList) ){
                        respItem.setSingleValue(StringUtils.join(regionList, "-"));
                    }
                    break;
                default:
                    break;
            }

            return respItem;
        }).collect(Collectors.toList());
    }
}
