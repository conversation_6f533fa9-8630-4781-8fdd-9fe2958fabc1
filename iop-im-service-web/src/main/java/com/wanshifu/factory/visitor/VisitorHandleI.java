package com.wanshifu.factory.visitor;

import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.resp.rightSidebar.GetVisitorInfoResp;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;

import java.util.List;

/**
 * 访客处理
 * <AUTHOR>
 * @date： 2025-07-25 11:20:40
 */
public interface VisitorHandleI {

    /**
     * 查询访客信息
     */
    List<GetVisitorInfoResp> queryVisitorInfo(UserRegisterInfo req, Long conversationId);
}
