package com.wanshifu.factory.visitor.action;

import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.factory.visitor.VisitorHandleI;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.resp.rightSidebar.GetVisitorInfoResp;
import com.wanshifu.iop.im.domain.bo.innerAccountService.GetThirdPartyIpLocationRespBo;
import com.wanshifu.iop.im.domain.enums.PropertyTypeEnum;
import com.wanshifu.iop.im.domain.enums.rightSidebar.FieldAttributeFromTemplateTypeEnum;
import com.wanshifu.iop.im.domain.po.ConversationVisitorDetail;
import com.wanshifu.iop.im.domain.po.FieldAttribute;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import com.wanshifu.iop.im.domain.po.VisitorInfoTemplate;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoForKeFuResp;
import com.wanshifu.orderconfig.domains.respbean.ConfigServerCategoryResp;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.inner.InnerVisitorQueryService;
import com.wanshifu.user.domain.resp.user.GetUserInfoAllToIopResp;
import com.wanshifu.wallet.domains.po.WalletBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 师傅访客处理
 * <AUTHOR>
 * @date： 2025-07-25 11:24:06
 */
@Component("master")
@Slf4j
public class MasterHandle extends AbstractService implements VisitorHandleI {

    @Resource
    private InnerVisitorQueryService innerVisitorQueryService;

    /**
     * 查询访客信息
     * @param req
     * @return
     */
    @Override
    public List<GetVisitorInfoResp> queryVisitorInfo(UserRegisterInfo req, Long conversationId) {
        // 查询当前用户的访客模板
        VisitorInfoTemplate visitorInfoTemplate = visitorInfoTemplateRepository.selectByVisitorClassEn(req.getUserClass(), req.getImId());
        if (Objects.isNull(visitorInfoTemplate)){
            log.error("访客{}模板不存在", req.getUserClass());
            return new ArrayList<>();
        }

        // 访问信息
        List<FieldAttribute> fieldAttributeList = fieldAttributeRepository.selectByVisitorInfoTemplateId(visitorInfoTemplate.getVisitorInfoTemplateId()
                , FieldAttributeFromTemplateTypeEnum.VISITOR_INFO.type);
        if (CollectionUtils.isEmpty(fieldAttributeList)){
            log.error("访客{}模板字段不存在", req.getUserClass());
            return new ArrayList<>();
        }

        // 查询师傅信息
        GetMasterInfoForKeFuResp masterInfoForKeFu = innerVisitorQueryService.getMasterInfoForKeFu(req.getUserId());
        if (Objects.isNull(masterInfoForKeFu)){
            log.error("获取访客{}师傅{}信息异常", req.getUserClass(), req.getUserId());
            return new ArrayList<>();
        }

        // 查询钱包信息
        WalletBase walletBase = innerVisitorQueryService.getWalletInfo(req.getUserId(), req.getUserClass());

        // 根据divisionIds批量查询地址信息
        List<Address> divisionInfoIds = innerVisitorQueryService.getDivisionInfoListByDivisionIds(masterInfoForKeFu.getServeDivisionIdList());

        return fieldAttributeList.stream().map(fieldAttribute -> {
            GetVisitorInfoResp respItem = new GetVisitorInfoResp();
            respItem.setFieldName(fieldAttribute.getFieldCn());
            respItem.setFieldEn(fieldAttribute.getFieldEn());
            respItem.setValueType("single");

            switch (fieldAttribute.getFieldEn()) {
                // 通过注释完善代码
                case "masterId":
                    respItem.setSingleValue(masterInfoForKeFu.getMasterId().toString());
                    break;
                case "masterName":
                    respItem.setSingleValue(masterInfoForKeFu.getContact());
                    break;
                case "phone":
                    respItem.setSingleValue(masterInfoForKeFu.getPhone());
                    break;
                case "walletBalance":// 钱包余额
                    if ( Objects.nonNull(walletBase) ){
                        respItem.setSingleValue(walletBase.getRemainAmount().toString());
                    }
                    break;
                case "freeCertify":// 是否免保师傅
                    respItem.setSingleValue(CommonConstant.ONE.equals(masterInfoForKeFu.getFreeCertifyStatus()) ? "是" : "否");
                    break;
                case "certifyAmount":// 诚信保证金
                    if ( Objects.nonNull(walletBase) ){
                        respItem.setSingleValue(walletBase.getCertifyAmount().toString());
                    }
                    break;
                case "integral":// 积分
                    respItem.setSingleValue(masterInfoForKeFu.getIntegral().toString());
                    break;
                case "isTeamMaster":// 是否团队师傅
                    respItem.setSingleValue(CommonConstant.ZERO.equals(masterInfoForKeFu.getMasterTeamState()) ? "否" : "是");
                    break;
                case "waitPunishAmount":// 是否有待缴纳违约金、赔偿金 todo
//                    respItem.setSingleValue(masterInfoForKeFu.getWaitPunishAmount());
                    break;
                case "workState":// 师傅开工/休息状态：已开工 / 休息中
                    respItem.setSingleValue(CommonConstant.ONE.equals(masterInfoForKeFu.getRestState()) ? "已开工" : "休息中");
                    break;
                case "limitFunction":// 后台限制的功能
                    List<String> limitFunction = new ArrayList<>();
                    if ( CollectionUtils.isNotEmpty(masterInfoForKeFu.getMasterRestrictList()) ){
                        // 获取被限制的功能
                        masterInfoForKeFu.getMasterRestrictList().stream().filter(f->Arrays.asList(CommonConstant.ZERO, CommonConstant.ONE).contains(f.getRestrictStatus())).forEach(restrict -> {
                            String functionName = restrict.getRestrictCn();
                            // 获取限制功能的时间
                            if ( CommonConstant.ONE.equals(restrict.getPermanentRestrict()) ){
                                functionName += "永久";
                            }else{
                                if ( Objects.nonNull(restrict.getRestrictStartTime()) && Objects.nonNull(restrict.getRestrictEndTime()) ){
                                    // 限制结束时间-限制开始时间 再转化为天，向上取整数
                                    functionName += (int) Math.ceil((restrict.getRestrictEndTime().getTime() - restrict.getRestrictStartTime().getTime()) / (1000 * 60 * 60 * 24)) + "天";
                                }
                            }
                            limitFunction.add(functionName);
                        });
                    }

                    if ( CollectionUtils.isNotEmpty(limitFunction) ){
                        respItem.setSingleValue(StringUtils.join(limitFunction, "/"));
                    }
                    break;
                case "region":// 服务区域（省、市、区）

                    if ( CollectionUtils.isNotEmpty(masterInfoForKeFu.getServeDivisionIdList()) ){
                        String regionStr = divisionInfoIds.stream().filter(f -> masterInfoForKeFu.getServeDivisionIdList().contains(f.getDivisionId())).map(divisionInfo -> {
                            List<String> region = new ArrayList<>();
                            if (StringUtils.isNoneBlank(divisionInfo.getLv2DivisionName())) {
                                region.add(divisionInfo.getLv2DivisionName());
                            }
                            if (StringUtils.isNoneBlank(divisionInfo.getLv3DivisionName())) {
                                region.add(divisionInfo.getLv3DivisionName());
                            }
                            if (StringUtils.isNoneBlank(divisionInfo.getLv4DivisionName())) {
                                region.add(divisionInfo.getLv4DivisionName());
                            }
                            if (StringUtils.isNoneBlank(divisionInfo.getLv5DivisionName())) {
                                region.add(divisionInfo.getLv5DivisionName());
                            }
                            // 取前三个
                            return String.join("-", region.subList(0, Math.min(3, region.size())));
                        }).collect(Collectors.joining(","));

                        if ( StringUtils.isNoneBlank(regionStr) ){
                            respItem.setSingleValue(regionStr);
                        }
                    }
                    break;
                default:
                    break;
            }
            return respItem;
        }).collect(Collectors.toList());
    }
}
