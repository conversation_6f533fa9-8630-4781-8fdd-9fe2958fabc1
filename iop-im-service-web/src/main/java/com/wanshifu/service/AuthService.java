package com.wanshifu.service;

import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.BatchLogoutSeatReq;
import com.wanshifu.iop.im.api.req.seat.DisableSeatStatusReq;
import com.wanshifu.iop.im.api.req.seat.SetLoginAuthReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.SetLoginAuthResp;
import com.wanshifu.iop.im.api.resp.VerifyRepeatNameResp;

import java.util.List;

/**
 * 鉴权服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:06
 */
public interface AuthService {

    SetLoginAuthResp loginSeat(SetLoginAuthReq req);

    Integer batchLogoutSeat(BatchLogoutSeatReq req);

    void destroySeatLoginCache(List<Long> accountIdList);

    void updateSeatLoginCache(Long accountId, SetLoginAuthResp cacheResp, Integer status);

    Integer disableSeatStatus(DisableSeatStatusReq req);
}
