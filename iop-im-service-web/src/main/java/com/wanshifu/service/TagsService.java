package com.wanshifu.service;

import com.wanshifu.iop.im.api.req.tags.CreateTagsReq;
import com.wanshifu.iop.im.api.req.tags.GetTagsListReq;
import com.wanshifu.iop.im.api.req.tags.SwitchTagsStatusReq;
import com.wanshifu.iop.im.domain.po.Tags;

import java.util.List;

public interface TagsService {

    List<Tags> getTagsList(GetTagsListReq req);

    Integer createTags(CreateTagsReq req);

    Integer editTags(CreateTagsReq req);

    Integer switchStatus(SwitchTagsStatusReq req);
}
