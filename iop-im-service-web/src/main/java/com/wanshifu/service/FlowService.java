package com.wanshifu.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.flow.FlowAddReq;
import com.wanshifu.iop.im.api.req.flow.FlowConfigReq;
import com.wanshifu.iop.im.api.req.flow.FlowDelReq;
import com.wanshifu.iop.im.api.req.flow.FlowDetailReq;
import com.wanshifu.iop.im.api.req.flow.FlowListReq;
import com.wanshifu.iop.im.api.req.flow.FlowModifyReq;
import com.wanshifu.iop.im.api.resp.flow.FlowAllListResp;
import com.wanshifu.iop.im.api.resp.flow.FlowDetailResp;
import com.wanshifu.iop.im.api.resp.flow.FlowListResp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/1 16:23
 * @description 流程服务接口
 */
public interface FlowService {
    /**
     * 路由导航列表
     *
     * @param req
     */
    SimplePageInfo<FlowListResp> flowList(FlowListReq req);

    /**
     * 路由导航删除
     *
     * @param req
     */
    Integer flowDel(FlowDelReq req);

    /**
     * 路由导航详情
     *
     * @param req
     */
    FlowDetailResp flowDetail(FlowDetailReq req);

    /**
     * 路由导航编辑
     *
     * @param req
     */
    Integer flowModify(FlowModifyReq req);

    /**
     * 路由导航新增
     *
     * @param req
     */
    Long flowAdd(FlowAddReq req);

    /**
     * 路由导航新增
     *
     * @param req
     */
    Integer flowConfig(FlowConfigReq req);

    /**
     * 路由导航列表
     * */
    List<FlowAllListResp> flowAllList();
}
