package com.wanshifu.service.inner;

import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.domain.bo.innerAccountService.GetThirdPartyIpLocationRespBo;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoForKeFuResp;
import com.wanshifu.orderconfig.domains.respbean.ConfigServerCategoryResp;
import com.wanshifu.user.domain.resp.user.GetUserInfoAllToIopResp;
import com.wanshifu.wallet.domains.po.WalletBase;

import java.util.List;

/**
 * 顶边栏服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:06
 */
public interface InnerVisitorQueryService {

    /**
     * 获取服务品类列表
     * @param sourceType
     * @return
     */
    SimplePageInfo<ConfigServerCategoryResp> getListServerCategory(String sourceType);

    /**
     * 获取用户信息
     * @param userId
     * @return
     */
    GetUserInfoAllToIopResp getUserInfoAllToIop(Long userId);

    /**
     * 获取钱包信息
     * @param accountId
     * @return
     */
    WalletBase getWalletInfo(Long accountId, String accountType);

    /**
     * 获取师傅信息
     * @param masterId
     * @return
     */
    GetMasterInfoForKeFuResp getMasterInfoForKeFu(Long masterId);

    /**
     * 通过ip地址获取地址信息
     * @param ip
     * @return
     */
    GetThirdPartyIpLocationRespBo getThirdPartyIpLocation(String ip);

    /**
     * 根据divisionIds批量查询地址信息
     * @param divisionIds
     * @return
     */
    List<Address> getDivisionInfoListByDivisionIds(List<Long> divisionIds);
}
