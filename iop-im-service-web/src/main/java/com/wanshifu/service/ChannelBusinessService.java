package com.wanshifu.service;

import com.wanshifu.iop.im.api.req.channel.GetConversationThemeInfoRqt;
import com.wanshifu.iop.im.api.req.channel.PreviewClientCategoryAndChannelReq;
import com.wanshifu.iop.im.api.req.channel.SaveSatisfactionDataRqt;
import com.wanshifu.iop.im.api.resp.channel.GetConversationThemeInfoResp;
import com.wanshifu.iop.im.api.resp.channel.PreviewClientCategoryAndChannelResp;

public interface ChannelBusinessService {
    PreviewClientCategoryAndChannelResp previewDraft(PreviewClientCategoryAndChannelReq req);

    GetConversationThemeInfoResp getConversationThemeInfo(GetConversationThemeInfoRqt req);

    Integer saveSatisfactionData(SaveSatisfactionDataRqt req);
}
