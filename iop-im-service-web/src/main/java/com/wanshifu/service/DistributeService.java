package com.wanshifu.service;

import com.wanshifu.iop.im.api.req.group.DistributeEnGroupReq;
import com.wanshifu.iop.im.api.req.group.DistributeGroupSeatReq;
import com.wanshifu.iop.im.api.req.seat.BatchLogoutSeatReq;
import com.wanshifu.iop.im.api.req.seat.DisableSeatStatusReq;
import com.wanshifu.iop.im.api.req.seat.SetLoginAuthReq;
import com.wanshifu.iop.im.api.resp.SetLoginAuthResp;
import com.wanshifu.iop.im.domain.bo.queue.DistributeGroupSeatBo;
import com.wanshifu.iop.im.domain.bo.queue.XxlDistributeMqReqBo;
import com.wanshifu.iop.im.domain.po.SeatInfo;

import java.util.List;

/**
 * 分配服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:06
 */
public interface DistributeService {

    Integer distributeEnGroup(DistributeEnGroupReq req);

    Integer seatStatusSwitch(SeatInfo seatInfo, String switchStatus);


    /**
     * 队列分配
     * */
    Integer distribute();

    /**
     * xxl 消息分配
     * */
    Integer distributeXxl(XxlDistributeMqReqBo req);

    DistributeGroupSeatBo distributeGroupSeat(DistributeGroupSeatReq req);
}
