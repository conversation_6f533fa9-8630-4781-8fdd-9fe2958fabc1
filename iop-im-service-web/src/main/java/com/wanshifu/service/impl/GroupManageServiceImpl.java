package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.factory.DataManipulationFactory;
import com.wanshifu.factory.rule.RuleFactory;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.account.domain.resp.account.AccountInfoListResp;
import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.req.group.*;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.*;
import com.wanshifu.iop.im.api.resp.group.*;
import com.wanshifu.iop.im.domain.bo.groupManage.*;
import com.wanshifu.iop.im.domain.enums.*;
import com.wanshifu.iop.im.domain.po.*;
import com.wanshifu.repository.*;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.GroupManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 进线配置服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:44
 */
@Service
@Slf4j
public class GroupManageServiceImpl extends AbstractService implements GroupManageService {

    /**
     * 全局队列最大长度
     */
    @Value("${wanshifu.globalQueueConfig.length}")
    private Long globalQueueConfigLength;

    /**
     * 全局队列成员间隔时间（分钟）
     */
    @Value("${wanshifu.globalQueueConfig.memberIntervalMinute}")
    private Long globalQueueConfigMemberIntervalMinute;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Resource
    private RuleMetricConfigRepository ruleMetricConfigRepository;

    @Resource
    private GroupInfoRepository groupInfoRepository;

    @Resource
    private GroupRuleRepository groupRuleRepository;

    @Resource
    private GroupRuleJoinConditionRepository groupRuleJoinConditionRepository;

    @Resource
    private SeatInfoRepository seatInfoRepository;

    @Resource
    private SeatGroupMappingRepository seatGroupMappingRepository;

    @Resource
    private SeatGroupDistributeRuleRepository seatGroupDistributeRuleRepository;

    @Resource
    private QueueConfigRepository queueConfigRepository;

    /**
     * 获取枚举
     * @return
     */
    @Override
    public GroupConfigEnumsResp enums(){
        GroupConfigEnumsResp resp = new GroupConfigEnumsResp();

        // 获取分组规则指标
        List<RuleMetricConfig> groupRuleList = ruleMetricConfigRepository.selectRuleMetricByObject(RuleMetricObjectEnum.GROUP.type, RuleMetricFromTypeEnum.KF.type);
        if (CollectionUtils.isNotEmpty(groupRuleList)){
            List<RuleMetricConfigEnumsOptionResp> controlTypeEnums = groupRuleList.stream().map(item -> {
                RuleMetricConfigEnumsOptionResp labelValueResp = new RuleMetricConfigEnumsOptionResp();
                labelValueResp.setLabel(item.getRuleMetricCn());
                labelValueResp.setValue(item.getRuleMetricId());
                RuleMetricConfigEnum enumByType = RuleMetricConfigEnum.getEnumByType(item.getRuleMetricEn());
                if ( Objects.nonNull(enumByType) ){
                    labelValueResp.setEnumType(enumByType.action);
                }
                return labelValueResp;
            }).collect(Collectors.toList());
            resp.setGroupRuleEnums(controlTypeEnums);
        }

        // 逻辑运算符枚举
//        List<CommonLabelValueResp> logicalOperatorEnums = Arrays.stream(LogicalOperatorEnum.values()).map(item -> {
        List<CommonLabelValueResp> logicalOperatorEnums = Stream.of(LogicalOperatorEnum.AND).map(item -> {
            CommonLabelValueResp labelValueResp = new CommonLabelValueResp();
            labelValueResp.setLabel(item.name);
            labelValueResp.setValue(item.type);
            return labelValueResp;
        }).collect(Collectors.toList());
        resp.setLogicalOperatorEnums(logicalOperatorEnums);

        // 比较运算符枚举
//        List<CommonLabelValueResp> comparisonOperatorEnums = Arrays.stream(ComparisonOperatorEnum.values()).map(item -> {
        List<CommonLabelValueResp> comparisonOperatorEnums = Stream.of(ComparisonOperatorEnum.EQ).map(item -> {
            CommonLabelValueResp labelValueResp = new CommonLabelValueResp();
            labelValueResp.setLabel(item.name);
            labelValueResp.setValue(item.type);
            return labelValueResp;
        }).collect(Collectors.toList());
        resp.setComparisonOperatorEnums(comparisonOperatorEnums);

        return resp;
    }

    /**
     * 获取分组列表
     * @param req
     * @return
     */
    @Override
    public SimplePageInfo<GroupListResp> list(GroupListReq req){
        SimplePageInfo<GroupListResp> respSimplePageInfo = new SimplePageInfo<>();

        // 查询分组列表
        Page<Object> page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<GroupInfo> groupList = groupInfoRepository.selectList(req);

        if ( CollectionUtils.isEmpty(groupList) ){
            return respSimplePageInfo;
        }

        List<Integer> groupIdList = groupList.stream().map(GroupInfo::getGroupId).collect(Collectors.toList());

        // 获取分组配置指标
        List<RuleMetricConfig> ruleMetricList = ruleMetricConfigRepository.selectRuleMetricByObject(RuleMetricObjectEnum.GROUP.type, RuleMetricFromTypeEnum.KF.type);
        // 获取分组关联配置
        List<GroupRule> groupRulesList = groupRuleRepository.selectByGroupIdList(groupIdList);

        SelectRuleMetricMappingReq selectRuleMetricMappingReq = new SelectRuleMetricMappingReq();
        selectRuleMetricMappingReq.setRuleMetricConfigList(ruleMetricList);
        selectRuleMetricMappingReq.setGroupRulesList(groupRulesList);

        List<RuleMetricMappingBo> conversationChannelRulesList = new ArrayList<>();// 会话渠道
        List<RuleMetricMappingBo> conversationEntryRulesList = new ArrayList<>();// 会话入口
        List<RuleMetricMappingBo> problemClassRulesList = new ArrayList<>();// 咨询类型
        if ( CollectionUtils.isNotEmpty(groupRulesList) ){
            // 获取关联会话渠道的规则指标
            selectRuleMetricMappingReq.setRuleMetricConfigEnum(RuleMetricConfigEnum.CONVERSATION_CHANNEL);
            List<RuleMetricMappingBo> conversationChannel = RuleFactory.setRuleType(RuleMetricObjectEnum.GROUP.ruleType).selectRuleMetricMapping(selectRuleMetricMappingReq);
            conversationChannelRulesList.addAll(conversationChannel);

            // 获取关联会话入口
            selectRuleMetricMappingReq.setRuleMetricConfigEnum(RuleMetricConfigEnum.CONVERSATION_ENTRY);
            List<RuleMetricMappingBo> conversationEntry = RuleFactory.setRuleType(RuleMetricObjectEnum.GROUP.ruleType).selectRuleMetricMapping(selectRuleMetricMappingReq);
            conversationEntryRulesList.addAll(conversationEntry);

            // 获取关联咨询类型
            selectRuleMetricMappingReq.setRuleMetricConfigEnum(RuleMetricConfigEnum.PROBLEM_CLASS);
            List<RuleMetricMappingBo> problemClassList = RuleFactory.setRuleType(RuleMetricObjectEnum.GROUP.ruleType).selectRuleMetricMapping(selectRuleMetricMappingReq);
            problemClassRulesList.addAll(problemClassList);
        }

        // 获取关联会话坐席数
        List<CommonLabelValueResp> seatCount = seatGroupMappingRepository.selectCountByGroupIdList(groupIdList);

        // 获取操作人
        List<Long> operatorIdList = groupList.stream().map(GroupInfo::getOperatorId).collect(Collectors.toList());
        List<AccountInfoListResp> accountInfoListResps = iopAccountService.batchGetInfoListByAccountIds(operatorIdList);

        // 格式化数据
        List<GroupListResp> respList = groupList.stream().map(groupInfo -> {
            GroupListResp groupListResp = new GroupListResp();
            groupListResp.setGroupId(groupInfo.getGroupId());
            groupListResp.setGroupName(groupInfo.getGroupName());

            // 关联会话坐席数
            CommonLabelValueResp commonLabelValueResp = seatCount.stream().filter(f -> f.getLabel().equals(groupInfo.getGroupId().toString())).findFirst().orElse(null);
            if(Objects.nonNull(commonLabelValueResp)) {
                groupListResp.setSeatCount(Integer.parseInt(commonLabelValueResp.getValue()));
            }

            // 操作人
            AccountInfoListResp accountInfoListResp = accountInfoListResps.stream().filter(f -> f.getAccountId().equals(groupInfo.getOperatorId())).findFirst().orElse(null);
            if(Objects.nonNull(accountInfoListResp)) {
                groupListResp.setOperatorName(accountInfoListResp.getUsername());
            }

            groupListResp.setCreateTime(groupInfo.getCreateTime());
            groupListResp.setUpdateTime(groupInfo.getUpdateTime());
            groupListResp.setStatus(groupInfo.getStatus());
            groupListResp.setStatusName(StatusEnum.getNameByType(groupInfo.getStatus()));
            groupListResp.setSort(groupInfo.getSort());

            if ( minimumGuaranteeGroupId.equals(groupInfo.getGroupId()) ){
                groupListResp.setIsGuaranteedGroup(CommonConstant.ONE);
                groupListResp.setClientCategoryNameList(Collections.singletonList("所有渠道"));
                groupListResp.setChannelNameList(Collections.singletonList("所有入口"));
                groupListResp.setProblemClassNameList(Collections.singletonList("所有咨询类型"));
                return groupListResp;
            }

            // 会话渠道
            RuleMetricMappingBo conversationChannel = conversationChannelRulesList.stream().filter(ruleMetricMappingBo ->
                    ruleMetricMappingBo.getGroupId().equals(groupInfo.getGroupId())
                            && ruleMetricMappingBo.getRuleMetricEn().equals(RuleMetricConfigEnum.CONVERSATION_CHANNEL.type)
            ).findFirst().orElse(null);
            if ( Objects.nonNull(conversationChannel) && CollectionUtils.isNotEmpty(conversationChannel.getMappingRelationList()) ){
                List<String> clientCategoryNameList = conversationChannel.getMappingRelationList().stream().map(CommonLabelValueResp::getLabel).collect(Collectors.toList());
                groupListResp.setClientCategoryNameList(clientCategoryNameList);
            }

            // 会话入口
            RuleMetricMappingBo conversationEntry = conversationEntryRulesList.stream().filter(ruleMetricMappingBo ->
                    ruleMetricMappingBo.getGroupId().equals(groupInfo.getGroupId())
                            && ruleMetricMappingBo.getRuleMetricEn().equals(RuleMetricConfigEnum.CONVERSATION_ENTRY.type)
            ).findFirst().orElse(null);
            if ( Objects.nonNull(conversationEntry) && CollectionUtils.isNotEmpty(conversationEntry.getMappingRelationList()) ){
                List<String> channelNameList = conversationEntry.getMappingRelationList().stream().map(CommonLabelValueResp::getLabel).collect(Collectors.toList());
                groupListResp.setChannelNameList(channelNameList);
            }

            // 咨询类型
            RuleMetricMappingBo problemClass = problemClassRulesList.stream().filter(ruleMetricMappingBo ->
                    ruleMetricMappingBo.getGroupId().equals(groupInfo.getGroupId())
                            && ruleMetricMappingBo.getRuleMetricEn().equals(RuleMetricConfigEnum.PROBLEM_CLASS.type)
            ).findFirst().orElse(null);
            if ( Objects.nonNull(problemClass) && CollectionUtils.isNotEmpty(problemClass.getMappingRelationList()) ){
                List<String> problemClassNameList = problemClass.getMappingRelationList().stream().map(CommonLabelValueResp::getLabel).collect(Collectors.toList());
                groupListResp.setProblemClassNameList(problemClassNameList);
            }

            return groupListResp;
        }).collect(Collectors.toList());

        respSimplePageInfo.setList(respList);
        respSimplePageInfo.setTotal(page.getTotal());
        respSimplePageInfo.setPages(page.getPages());
        respSimplePageInfo.setPageNum(page.getPageNum());
        respSimplePageInfo.setPageSize(page.getPageSize());
        return respSimplePageInfo;
    }

    /**
     * 添加分组
     */
    @Override
    public Long add(AddGroupReq req){
        // 入参校验
        req.checkParams();

        // 校验分组名是否重复
        VerifyRepeatNameReq verifyRepeatNameReq = new VerifyRepeatNameReq();
        verifyRepeatNameReq.setVerifyName(req.getGroupName());
        verifyRepeatNameReq.setVerifyType(VerifyRepeatNameTypeEnum.GROUP.type);
        if(!super.baseVerifyRepeatName(verifyRepeatNameReq)){
            throw new BusinessException("该分组名已被使用");
        }

        GetEnumsReq getEnumsReq = new GetEnumsReq();
        getEnumsReq.setTenantId(req.getTenantId());
        // 校验关联渠道
        getEnumsReq.setEnumsType(ModulesTypeEnum.CLIENT_CATEGORY.type);
        List<CommonEnumsResp> clientCategoryList = this.getEnums(getEnumsReq);
        if ( clientCategoryList.stream().noneMatch(item -> item.getValue().equals(req.getClientCategoryId().toString())) ){
            throw new BusinessException("该分组关联的渠道不存在或已禁用");
        }

        // 校验关联入口
        getEnumsReq.setEnumsType(ModulesTypeEnum.CHANNEL_ENTRY.type);
        getEnumsReq.setEnumsParentIdList(Collections.singletonList(req.getClientCategoryId()));
        List<CommonEnumsResp> channelEntryList = this.getEnums(getEnumsReq);
        List<String> exceptionchannelEntryItem = req.getChannelIdList().stream().map(Object::toString).filter(item ->
                channelEntryList.stream().map(CommonEnumsResp::getValue).noneMatch(item::equals)
        ).collect(Collectors.toList());
        if ( CollectionUtils.isNotEmpty(exceptionchannelEntryItem) ){
            throw new BusinessException("会话入口ID："+String.join("、", exceptionchannelEntryItem)+" 不存在或已禁用");
        }

        // 校验咨询类型
        getEnumsReq.setEnumsType(ModulesTypeEnum.PROBLEM_CLASS.type);
        getEnumsReq.setEnumsParentIdList(req.getChannelIdList());
        List<CommonEnumsResp> problemClassList = this.getEnums(getEnumsReq);
        List<String> exceptionProblemClassItem = req.getProblemClassIdList().stream().map(Object::toString).filter(item ->
                problemClassList.stream().map(CommonEnumsResp::getValue).noneMatch(item::equals)
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(exceptionProblemClassItem)) {
            throw new BusinessException("咨询类型ID："+String.join("、", exceptionProblemClassItem)+" 不存在或已禁用");
        }

        // 获取分组规则指标
        List<RuleMetricConfig> ruleMetricConfigList = ruleMetricConfigRepository.selectRuleMetricByObject(RuleMetricObjectEnum.GROUP.type, RuleMetricFromTypeEnum.KF.type);

        //手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            // 获取分组最新排序
            Integer maxSort = groupInfoRepository.selectMaxSortByTenantId(req.getTenantId());
            if ( maxSort == null ){
                maxSort = 0;
            }

            // 新增分组
            GroupInfo groupInfo = new GroupInfo();
            groupInfo.setGroupName(req.getGroupName());
            groupInfo.setOperatorId(req.getOperatorId());
            groupInfo.setTenantId(req.getTenantId());
            groupInfo.setSort(maxSort + 1);
            groupInfoRepository.insertSelective(groupInfo);

            // 新增分组队列配置
            QueueConfig queueConfig = new QueueConfig();
            queueConfig.setFromId(groupInfo.getGroupId().longValue());
            queueConfig.setFromType(ModulesTypeEnum.GROUP.type);
            queueConfig.setMaxLength(globalQueueConfigLength);
            queueConfig.setMemberIntervalMinute(globalQueueConfigMemberIntervalMinute);
            queueConfig.setMsgTtl(CommonConstant.ONE.longValue());
            queueConfig.setOperatorId(req.getOperatorId());
            queueConfigRepository.insertSelective(queueConfig);

            // 新增分组规则
            if ( CollectionUtils.isNotEmpty(ruleMetricConfigList) ){

                List<GroupRule> insertGroupRuleList = new ArrayList<>();
                for (RuleMetricConfig ruleMetricConfig : ruleMetricConfigList) {
                    GroupRule insertGroupRule = new GroupRule();
                    insertGroupRule.setGroupId(groupInfo.getGroupId());
                    insertGroupRule.setRuleMetricId(ruleMetricConfig.getRuleMetricId());
                    insertGroupRule.setComparisonOperator(ComparisonOperatorEnum.EQ.type);

                    // 配置值
                    if ( ruleMetricConfig.getRuleMetricEn().equals(RuleMetricConfigEnum.CONVERSATION_CHANNEL.type) ){
                        GroupRuleConfigValueBo clientCategoryConfigValueBo = this.formatConfigValueBoByParams(Collections.singletonList(req.getClientCategoryId()), ruleMetricConfig);
                        insertGroupRule.setRuleConfigValue(JSONObject.toJSONString(clientCategoryConfigValueBo));
                    }else if ( ruleMetricConfig.getRuleMetricEn().equals(RuleMetricConfigEnum.CONVERSATION_ENTRY.type) ){
                        GroupRuleConfigValueBo channelEntryConfigValueBo = this.formatConfigValueBoByParams(req.getChannelIdList(), ruleMetricConfig);
                        insertGroupRule.setRuleConfigValue(JSONObject.toJSONString(channelEntryConfigValueBo));
                    }else if ( ruleMetricConfig.getRuleMetricEn().equals(RuleMetricConfigEnum.PROBLEM_CLASS.type) ){
                        GroupRuleConfigValueBo problemClassConfigValueBo = this.formatConfigValueBoByParams(req.getProblemClassIdList(), ruleMetricConfig);
                        insertGroupRule.setRuleConfigValue(JSONObject.toJSONString(problemClassConfigValueBo));
                    }else{
                        continue;
                    }

                    insertGroupRule.setOperatorId(req.getOperatorId());
                    insertGroupRule.setStatus(StatusEnum.ENABLE.type);
                    insertGroupRule.setIsDelete(CommonConstant.ZERO);
                    insertGroupRule.setCreateTime(new Date());
                    insertGroupRule.setUpdateTime(new Date());
                    insertGroupRuleList.add(insertGroupRule);
                }
                groupRuleRepository.insertList(insertGroupRuleList);
            }

            // 新增分组规则连接条件
            GroupRuleJoinCondition groupRuleJoinCondition = new GroupRuleJoinCondition();
            groupRuleJoinCondition.setGroupId(groupInfo.getGroupId());
            groupRuleJoinCondition.setLogicalOperator(LogicalOperatorEnum.AND.type);
            groupRuleJoinCondition.setOperatorId(req.getOperatorId());
            groupRuleJoinConditionRepository.insertSelective(groupRuleJoinCondition);

            platformTransactionManager.commit(status);
            return groupInfo.getGroupId().longValue();
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 格式化配置值
     * @param paramsList
     * @param ruleMetricConfig
     * @return
     */
    private GroupRuleConfigValueBo formatConfigValueBoByParams(List<Long> paramsList, RuleMetricConfig ruleMetricConfig) {
        if ( CollectionUtils.isEmpty(paramsList) ){
            return null;
        }

        if (StringUtils.isEmpty(ruleMetricConfig.getRuleMetricExtraJson())){
            log.error(ruleMetricConfig.getRuleMetricCn()+"规则指标配置值为空 ruleMetricConfig：{}", JSONObject.toJSONString(ruleMetricConfig));
            throw new BusinessException("添加分组失败："+ruleMetricConfig.getRuleMetricCn()+"规则指标配置值为空");
        }

        // 获取分组配置值
        RuleMetricExtraJsonBo ruleMetricExtraJsonBo = JSONObject.parseObject(ruleMetricConfig.getRuleMetricExtraJson(), RuleMetricExtraJsonBo.class);

        if( StringUtils.isEmpty(ruleMetricExtraJsonBo.getSelectiveType()) ){
            log.error(ruleMetricConfig.getRuleMetricCn()+"规则指标配置异常:选择类型为null ruleMetricConfig：{}", JSONObject.toJSONString(ruleMetricConfig));
            throw new BusinessException("添加分组失败："+ruleMetricConfig.getRuleMetricCn()+"规则指标配置异常:选择类型为null");
        }

        GroupRuleConfigValueBo groupRuleConfigValueBo = new GroupRuleConfigValueBo();
        groupRuleConfigValueBo.setSelectiveType(ruleMetricExtraJsonBo.getSelectiveType());

        if ( StringUtils.equals(ruleMetricExtraJsonBo.getSelectiveType(), RuleConfigValueSelectiveTypeEnum.MULTIPLE.type) ){
            List<String> params = paramsList.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList());
            groupRuleConfigValueBo.setMultipleValue(params);
        }else if ( StringUtils.equals(ruleMetricExtraJsonBo.getSelectiveType(), RuleConfigValueSelectiveTypeEnum.SINGLE.type) ) {
            groupRuleConfigValueBo.setSingleValue(paramsList.get(0).toString());
        }else{
            log.error(ruleMetricConfig.getRuleMetricCn()+"规则指标配置异常(不存在的选择类型):选择类型为"+ruleMetricExtraJsonBo.getSelectiveType()+" ruleMetricConfig：{}", JSONObject.toJSONString(ruleMetricConfig));
            throw new BusinessException("添加分组失败："+ruleMetricConfig.getRuleMetricCn()+"规则指标配置异常(不存在的选择类型):选择类型为"+ruleMetricExtraJsonBo.getSelectiveType());
        }

        return groupRuleConfigValueBo;
    }

    /**
     * 获取枚举
     * @param req
     * @return
     */
    public List<CommonEnumsResp> getEnums(GetEnumsReq req){
        ResultResp<CommonRunEnumsResp> enums = DataManipulationFactory.setFactoryType(req.getEnumsType()).getEnums(req);
        if ( Objects.isNull(enums) || !enums.isSuccess() || Objects.isNull(enums.getData()) || CollectionUtils.isEmpty(enums.getData().getEnumsList()) ){
            if ( ModulesTypeEnum.SEAT.type.equals(req.getEnumsType()) ){
                throw new BusinessException("添加坐席存在重复");
            }else{
                throw new BusinessException("数据校验不通过:"+ModulesTypeEnum.getNameByType(req.getEnumsType())+"下拉数据为空");
            }
        }

        return enums.getData().getEnumsList();
    }

    /**
     * 调整顺序
     */
    @Override
    public Integer rearrangement(GroupRearrangementReq req){
        // 校验参数
        req.checkParam();

        // 校验修改的分组排序中不能出现sort为0的分组
        if ( req.getRearrangementField().stream().anyMatch(groupRearrangementField -> CommonConstant.ZERO.equals(groupRearrangementField.getSort())) ) {
            throw new BusinessException("调整顺序分组失败:分组调整顺序排序不能为0");
        }

        // 获取当前分组的是否存在
        List<Integer> groupIdList = req.getRearrangementField().stream().map(GroupRearrangementReq.GroupRearrangementField::getGroupId).filter(Objects::nonNull).collect(Collectors.toList());
        if ( CollectionUtils.isEmpty(groupIdList) ){
            throw new BusinessException("调整顺序分组入参异常");
        }

        List<GroupInfo> groupInfoList = groupInfoRepository.batchSelectInfoByGroupIdList(groupIdList);
        if ( CollectionUtils.isEmpty(groupInfoList) ){
            throw new BusinessException("调整顺序分组不存在");
        }

        if ( groupInfoList.stream().anyMatch(groupInfo -> groupInfo.getGroupId().equals(minimumGuaranteeGroupId)) ){
            throw new BusinessException("调整顺序分组失败:兜底组不允许调整顺序");
        }

        // 查询groupIdList中是否有groupInfoList中不存在的groupId
        List<Integer> groupIdListNotExist = groupIdList.stream().filter(groupId ->
                groupInfoList.stream().noneMatch(groupInfo -> groupInfo.getGroupId().equals(groupId))
        ).collect(Collectors.toList());
        if ( CollectionUtils.isNotEmpty(groupIdListNotExist) ){
            throw new BusinessException("入参存在不存在的分组");
        }

        //手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        try{
            req.getRearrangementField().forEach(groupRearrangementField -> {
                GroupInfo groupInfo = new GroupInfo();
                groupInfo.setGroupId(groupRearrangementField.getGroupId());
                groupInfo.setSort(groupRearrangementField.getSort());
                groupInfoRepository.updateByPrimaryKeySelective(groupInfo);
            });

            platformTransactionManager.commit(status);
            return CommonConstant.OTHER_BUSINESS_TABLE_ID;
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 获取分组坐席列表
     * @param req
     * @return
     */
    @Override
    public SimplePageInfo<GroupSeatListResp> seatList(GroupSeatListReq req){
        SimplePageInfo<GroupSeatListResp> respSimplePageInfo = new SimplePageInfo<>();

        // 查询分组关联坐席
        Page<Object> page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<GroupSeatListRespBo> groupSeatListRespBos = seatInfoRepository.selectSeatInfoByGroupId(req.getGroupId());
        if ( CollectionUtils.isEmpty(groupSeatListRespBos) ){
            return respSimplePageInfo;
        }

        // 查询坐席所属会话组信息
        List<SeatGroupMapping> seatGroupMappingBySeatIdList = new ArrayList<>();
        List<GroupInfo> groupInfoList = new ArrayList<>();
        if ( CollectionUtils.isNotEmpty(groupSeatListRespBos) ){
            List<Long> seatIdList = groupSeatListRespBos.stream().map(GroupSeatListRespBo::getSeatId).collect(Collectors.toList());
            seatGroupMappingBySeatIdList.addAll(seatGroupMappingRepository.batchGetMappingBySeatIdList(seatIdList, null));
            if ( CollectionUtils.isNotEmpty(seatGroupMappingBySeatIdList) ) {
                groupInfoList.addAll(groupInfoRepository.batchSelectInfoByGroupIdList(seatGroupMappingBySeatIdList.stream().map(SeatGroupMapping::getGroupId).collect(Collectors.toList())));
            }
        }

        // 获取坐席姓名
        List<AccountInfoListResp> accountInfoListResps = iopAccountService.batchGetInfoListByAccountIds(groupSeatListRespBos.stream().map(GroupSeatListRespBo::getAccountId).collect(Collectors.toList()));

        // 格式化数据
        List<GroupSeatListResp> respList = groupSeatListRespBos.stream().map(seatInfo -> {
            GroupSeatListResp groupSeatListResp = new GroupSeatListResp();
            groupSeatListResp.setSeatId(seatInfo.getSeatId());
            groupSeatListResp.setSeatName(seatInfo.getSeatName());

            // 坐席姓名
            accountInfoListResps.stream().filter(accountInfoListResp -> accountInfoListResp.getAccountId().equals(seatInfo.getAccountId())).findFirst().ifPresent(accountInfoListResp -> {
                groupSeatListResp.setSeatName(accountInfoListResp.getUsername());
            });

            groupSeatListResp.setSeatNo(seatInfo.getSeatNo());
            groupSeatListResp.setMaxWiringQuantity(seatInfo.getMaxWiringQuantity());

            // 关联分组
            List<String> groupNameList = groupInfoList.stream().filter(groupInfo ->
                    seatGroupMappingBySeatIdList.stream().anyMatch(seatGroupMapping ->
                            seatGroupMapping.getGroupId().equals(groupInfo.getGroupId())
                                    && seatGroupMapping.getSeatId().equals(seatInfo.getSeatId())
                    )
            ).map(GroupInfo::getGroupName).collect(Collectors.toList());
            groupSeatListResp.setGroupNameList(groupNameList);
            return groupSeatListResp;
        }).collect(Collectors.toList());

        respSimplePageInfo.setList(respList);
        respSimplePageInfo.setTotal(page.getTotal());
        respSimplePageInfo.setPages(page.getPages());
        respSimplePageInfo.setPageNum(page.getPageNum());
        respSimplePageInfo.setPageSize(page.getPageSize());
        return respSimplePageInfo;
    }

    /**
     * 添加分组坐席
     */
    @Override
    public Long addSeat(AddGroupSeatReq req){
        // 校验参数
        req.checkParams();

        // 校验分组是否存在
        GroupInfo groupInfo = groupInfoRepository.selectByPrimaryKey(req.getGroupId());
        if ( Objects.isNull(groupInfo) || CommonConstant.ONE.equals(groupInfo.getIsDelete()) ){
            throw new BusinessException("分组不存在");
        }

        // 校验添加坐席是否已经在组内
        GetEnumsReq getEnumsReq = new GetEnumsReq();
        getEnumsReq.setTenantId(req.getTenantId());
        getEnumsReq.setEnumsParentIdList(Collections.singletonList(req.getGroupId().longValue()));
        getEnumsReq.setEnumsType(ModulesTypeEnum.SEAT.type);
        List<CommonEnumsResp> seatEnums = this.getEnums(getEnumsReq);
        // 校验seatEnums中是否有req.getSeatIdList()中没有的数据
        List<Long> notExistSeatIdList = req.getSeatIdList().stream().filter(seatId ->
                seatEnums.stream().noneMatch(seatEnum -> seatEnum.getValue().equals(seatId.toString()))
        ).collect(Collectors.toList());
        if ( CollectionUtils.isNotEmpty(notExistSeatIdList) ){
            throw new BusinessException("添加坐席已禁用或不存在");
        }

        // 手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        try{

            // 查询是否有已经关联的关系，有的话且状态为启用，则跳过，没有或状态为禁用则更新为启用
            List<SeatGroupMapping> groupMappingList = seatGroupMappingRepository.selectListByGroupIdAndSeatId(req.getGroupId(), req.getSeatIdList());

            // 需要添加的关系
            List<SeatGroupMapping> insertList = new ArrayList<>();
            for (Long seatId : req.getSeatIdList()) {

                // 查询是否已经关联
                SeatGroupMapping seatGroupMapping = groupMappingList.stream().filter(f -> f.getSeatId().equals(seatId)).findFirst().orElse(null);
                if ( Objects.nonNull(seatGroupMapping) ){
                    if ( StatusEnum.ENABLE.type.equals(seatGroupMapping.getStatus()) ){
                        continue;
                    }

                    SeatGroupMapping update = new SeatGroupMapping();
                    update.setSeatGroupMappingId(seatGroupMapping.getSeatGroupMappingId());
                    update.setStatus(StatusEnum.ENABLE.type);
                    update.setOperatorId(req.getOperatorId());
                    update.setUpdateTime(new Date());
                    seatGroupMappingRepository.updateByPrimaryKeySelective(update);

                    }else{
                        // 新增关系
                        SeatGroupMapping insert = new SeatGroupMapping();
                        insert.setGroupId(req.getGroupId());
                        insert.setSeatId(seatId);
                        insert.setStatus(StatusEnum.ENABLE.type);
                        insert.setIsDelete(CommonConstant.ZERO);
                        insert.setOperatorId(req.getOperatorId());
                        insert.setCreateTime(new Date());
                        insert.setUpdateTime(new Date());
                        insertList.add(insert);
                    }
            }

            if (CollectionUtils.isNotEmpty(insertList)){
                seatGroupMappingRepository.insertList(insertList);
            }

            // 更新分组更新时间
            groupInfoRepository.updateNowTime(groupInfo.getGroupId(), req.getOperatorId());

            // 记录日志
            super.insertOperateLog(CommonConstant.OTHER_BUSINESS_TABLE_ID.longValue(), SeatGroupMapping.class, OperateLogFromBusinessEnum.ADD.type, JSONObject.toJSONString(req), req.getOperatorId());

            platformTransactionManager.commit(status);
            return 1L;
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 移除分组坐席
     */
    @Override
    public Integer removeSeat(RemoveGroupSeatReq req){
        // 校验参数
        req.checkParams();

        // 校验分组是否存在
        this.checkGroupInfo(req.getGroupId(), req.getTenantId());

        // 手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        try{

            // 移除分组坐席
            Integer integer = seatGroupMappingRepository.deleteByGroupIdAndSeatIdList(req);

            // 更新分组更新时间
            groupInfoRepository.updateNowTime(req.getGroupId(), req.getOperatorId());

            // 记录日志
            super.insertOperateLog(CommonConstant.OTHER_BUSINESS_TABLE_ID.longValue(), SeatGroupMapping.class, OperateLogFromBusinessEnum.DELETE.type, JSONObject.toJSONString(req), req.getOperatorId());

            platformTransactionManager.commit(status);
            return integer;
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 校验分组基础信息
     * @param groupId
     * @param tenantId
     * @return
     */
    private GroupInfo checkGroupInfo(Integer groupId, Long tenantId){
        GroupInfo groupInfo = groupInfoRepository.selectByPrimaryKey(groupId);
        if ( Objects.isNull(groupInfo) || CommonConstant.ONE.equals(groupInfo.getIsDelete()) ){
            throw new BusinessException("分组不存在");
        }

        if ( !tenantId.equals(groupInfo.getTenantId()) ){
            throw new BusinessException("该分组非当前租户应用下");
        }

        return groupInfo;
    }

    /**
     * 分组接入规则详情
     */
    @Override
    public GroupAccessRuleDetailResp ruleDetail(GroupAccessRuleDetailReq req){
        GroupAccessRuleDetailResp resp = new GroupAccessRuleDetailResp();

        // 获取分组逻辑运算符
        GroupRuleJoinCondition groupRuleJoinCondition = groupRuleJoinConditionRepository.selectInfoByGroupId(req.getGroupId());
        if ( Objects.nonNull(groupRuleJoinCondition) ){
            resp.setGlobalLogicalOperator(groupRuleJoinCondition.getLogicalOperator());
        }

        // 获取分组规则指标
        List<RuleMetricConfig> ruleMetricConfigs = ruleMetricConfigRepository.selectRuleMetricByObject(RuleMetricObjectEnum.GROUP.type, RuleMetricFromTypeEnum.KF.type);
        if ( CollectionUtils.isEmpty(ruleMetricConfigs) ){
            return resp;
        }

        // 获取分组规则
        List<GroupRule> groupRuleList = groupRuleRepository.selectByGroupId(req.getGroupId());

        List<GroupAccessRuleOptionBo> groupAccessRuleOptionBos = ruleMetricConfigs.stream().map(ruleMetricConfig -> {
            GroupAccessRuleOptionBo groupAccessRuleOptionBo = new GroupAccessRuleOptionBo();
            groupAccessRuleOptionBo.setRuleMetricId(ruleMetricConfig.getRuleMetricId());

            GroupRule groupRule = groupRuleList.stream().filter(f -> f.getRuleMetricId().equals(ruleMetricConfig.getRuleMetricId())).findFirst().orElse(null);
            if ( Objects.nonNull(groupRule) ){
                groupAccessRuleOptionBo.setComparisonOperator(groupRule.getComparisonOperator());

                // 获取配置值
                GroupRuleConfigValueBo groupRuleConfigValueBo = JSONObject.parseObject(groupRule.getRuleConfigValue(), GroupRuleConfigValueBo.class);
                if (RuleConfigValueSelectiveTypeEnum.MULTIPLE.type.equals(groupRuleConfigValueBo.getSelectiveType())) {
                    groupAccessRuleOptionBo.setConfigValueList(groupRuleConfigValueBo.getMultipleValue().stream().map(Long::valueOf).collect(Collectors.toList()));
                } else if (RuleConfigValueSelectiveTypeEnum.SINGLE.type.equals(groupRuleConfigValueBo.getSelectiveType())) {
                    groupAccessRuleOptionBo.setConfigValueList(Collections.singletonList(Long.valueOf(groupRuleConfigValueBo.getSingleValue())));
                }
            }

            return groupAccessRuleOptionBo;
        }).collect(Collectors.toList());

        resp.setGroupAccessRuleOptionList(groupAccessRuleOptionBos);
        return resp;
    }

    /**
     * 分组接入规则编辑
     */
    @Override
    public Integer editRule(EditGroupAccessRuleReq req){
        // 校验参数
        req.checkParams();

        // 校验分组信息
        GroupInfo groupInfo = this.checkGroupInfo(req.getGroupId(), req.getTenantId());

        // 获取分组规则指标
        List<RuleMetricConfig> ruleMetricConfigs = ruleMetricConfigRepository.selectRuleMetricByObject(RuleMetricObjectEnum.GROUP.type, RuleMetricFromTypeEnum.KF.type);

        // 取出每个规则的id
        List<GroupAccessRuleOptionCheckReqBo> checkReqBoList = new ArrayList<>();

        CheckNonexistentReq checkNonexistentReq = new CheckNonexistentReq();
        checkNonexistentReq.setTenantId(req.getTenantId());
        req.getGroupAccessRuleOptionList().forEach(groupAccessRuleOptionBo -> {
            // 规则id为空
            if ( groupAccessRuleOptionBo.getRuleMetricId() == null || groupAccessRuleOptionBo.getRuleMetricId() == 0 ){
                throw new BusinessException(" 不合法请求：规则id为空 ");
            }

            // 校验规则指标
            RuleMetricConfig ruleMetricConfig = ruleMetricConfigs.stream().filter(f -> f.getRuleMetricId().equals(groupAccessRuleOptionBo.getRuleMetricId())).findFirst().orElse(null);
            if ( Objects.isNull(ruleMetricConfig) ){
                throw new BusinessException("不存在的规则指标："+groupAccessRuleOptionBo.getRuleMetricId());
            }

            // 校验入参
            if ( CollectionUtils.isEmpty(groupAccessRuleOptionBo.getConfigValueList()) ){
                throw new BusinessException(ruleMetricConfig.getRuleMetricCn()+"配置值为空");
            }

            if ( StringUtils.isEmpty(groupAccessRuleOptionBo.getComparisonOperator()) ){
                throw new BusinessException(ruleMetricConfig.getRuleMetricCn()+"选择运算符为空");
            }

            if ( ComparisonOperatorEnum.getEnumByType(groupAccessRuleOptionBo.getComparisonOperator()) == null ){
                throw new BusinessException(ruleMetricConfig.getRuleMetricCn()+"选择运算符不合法");
            }

            if ( StringUtils.isEmpty(ruleMetricConfig.getRuleMetricExtraJson()) ){
                throw new BusinessException("规则指标配置异常(规则指标配置为空)："+ruleMetricConfig.getRuleMetricCn());
            }

            RuleMetricConfigEnum enumByType = RuleMetricConfigEnum.getEnumByType(ruleMetricConfig.getRuleMetricEn());
            if ( Objects.isNull(enumByType) ){
                throw new BusinessException("不存在的规则指标类型："+ruleMetricConfig.getRuleMetricEn());
            }

            // 存入入参
            GroupAccessRuleOptionCheckReqBo checkReqBo = new GroupAccessRuleOptionCheckReqBo();
            BeanUtils.copyProperties(groupAccessRuleOptionBo, checkReqBo);
            checkReqBo.setRuleMetricEn(ruleMetricConfig.getRuleMetricEn());
            checkReqBoList.add(checkReqBo);

            // 校验是否存在不存在的配置值
            checkNonexistentReq.setType(enumByType.action);
            if ( RuleMetricConfigEnum.CONVERSATION_ENTRY.equals(enumByType) ){
                checkNonexistentReq.setParentIdList(checkReqBoList.stream().filter(f->RuleMetricConfigEnum.CONVERSATION_CHANNEL.type.equals(f.getRuleMetricEn()))
                        .map(GroupAccessRuleOptionCheckReqBo::getConfigValueList).findFirst().orElse(Collections.emptyList()));
            }
            if ( RuleMetricConfigEnum.PROBLEM_CLASS.equals(enumByType) ){
                checkNonexistentReq.setParentIdList(checkReqBoList.stream().filter(f->RuleMetricConfigEnum.CONVERSATION_ENTRY.type.equals(f.getRuleMetricEn()))
                        .map(GroupAccessRuleOptionCheckReqBo::getConfigValueList).findFirst().orElse(Collections.emptyList()));
            }
            checkNonexistentReq.setExistIdList(groupAccessRuleOptionBo.getConfigValueList());
            List<Long> NonexistentIdList = DataManipulationFactory.setFactoryType(enumByType.action).checkNonexistent(checkNonexistentReq);
            if ( CollectionUtils.isNotEmpty(NonexistentIdList) ){
                throw new BusinessException("不存在的"+ruleMetricConfig.getRuleMetricCn()+"值："+NonexistentIdList);
            }
        });

        // 获取分组规则关联条件
        GroupRuleJoinCondition groupRuleJoinCondition = groupRuleJoinConditionRepository.selectInfoByGroupId(req.getGroupId());

        // 手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        try{

            // 获取分组配置值
            List<GroupRule> groupRuleList = groupRuleRepository.selectByGroupId(req.getGroupId());

            List<GroupRule> insertGroupRuleList = new ArrayList<>();
            req.getGroupAccessRuleOptionList().forEach(groupAccessRuleOptionBo -> {

                // 规则指标配置
                RuleMetricConfig ruleMetricConfig = ruleMetricConfigs.stream().filter(f -> f.getRuleMetricId().equals(groupAccessRuleOptionBo.getRuleMetricId())).findFirst().orElse(null);

                // 格式化配置值
                RuleMetricExtraJsonBo ruleMetricExtraJsonBo = JSONObject.parseObject(ruleMetricConfig.getRuleMetricExtraJson(), RuleMetricExtraJsonBo.class);
                GroupRuleConfigValueBo groupRuleConfigValueBo = new GroupRuleConfigValueBo();
                groupRuleConfigValueBo.setSelectiveType(ruleMetricExtraJsonBo.getSelectiveType());
                // 多选值
                if (RuleConfigValueSelectiveTypeEnum.MULTIPLE.type.equals(ruleMetricExtraJsonBo.getSelectiveType())){
                    groupRuleConfigValueBo.setMultipleValue(groupAccessRuleOptionBo.getConfigValueList().stream().map(Object::toString).collect(Collectors.toList()));
                }
                // 单选值
                if (RuleConfigValueSelectiveTypeEnum.SINGLE.type.equals(ruleMetricExtraJsonBo.getSelectiveType())){
                    groupRuleConfigValueBo.setSingleValue(groupAccessRuleOptionBo.getConfigValueList().get(0).toString());
                }

                // 获取现有分组规则，存在更新，不存在新增
                GroupRule groupRule = groupRuleList.stream().filter(f -> groupAccessRuleOptionBo.getRuleMetricId().equals(f.getRuleMetricId())).findFirst().orElse(null);
                if ( Objects.nonNull(groupRule) ){
                    // 更新
                    GroupRule update = new GroupRule();
                    update.setGroupRuleId(groupRule.getGroupRuleId());
                    update.setComparisonOperator(groupAccessRuleOptionBo.getComparisonOperator());
                    update.setRuleConfigValue(JSONObject.toJSONString(groupRuleConfigValueBo));
                    update.setOperatorId(req.getOperatorId());
                    groupRuleRepository.updateByPrimaryKeySelective(update);
                }else {
                    // 新增
                    GroupRule insert = new GroupRule();
                    insert.setGroupId(groupInfo.getGroupId());
                    insert.setRuleMetricId(groupAccessRuleOptionBo.getRuleMetricId());
                    insert.setComparisonOperator(groupAccessRuleOptionBo.getComparisonOperator());
                    insert.setRuleConfigValue(JSONObject.toJSONString(groupRuleConfigValueBo));
                    insert.setOperatorId(req.getOperatorId());
                    insert.setStatus(StatusEnum.ENABLE.type);
                    insert.setIsDelete(CommonConstant.ZERO);
                    insert.setCreateTime(new Date());
                    insert.setUpdateTime(new Date());
                    insertGroupRuleList.add(insert);
                }
            });

            if ( CollectionUtils.isNotEmpty(insertGroupRuleList) ){
                groupRuleRepository.insertList(insertGroupRuleList);
            }

            // 更新关联条件
            if ( Objects.isNull(groupRuleJoinCondition) ){
                GroupRuleJoinCondition insert = new GroupRuleJoinCondition();
                insert.setGroupId(groupInfo.getGroupId());
                insert.setLogicalOperator(req.getGlobalLogicalOperator());
                insert.setOperatorId(req.getOperatorId());
                groupRuleJoinConditionRepository.insertSelective(insert);
            }else{
                // 不一样才更新
                if ( !req.getGlobalLogicalOperator().equals(groupRuleJoinCondition.getLogicalOperator()) ){
                    GroupRuleJoinCondition update = new GroupRuleJoinCondition();
                    update.setJoinConditionId(groupRuleJoinCondition.getJoinConditionId());
                    update.setLogicalOperator(req.getGlobalLogicalOperator());
                    update.setOperatorId(req.getOperatorId());
                    update.setUpdateTime(new Date());
                    groupRuleJoinConditionRepository.updateByPrimaryKeySelective(update);
                }
            }

            // 更新分组更新时间
            groupInfoRepository.updateNowTime(req.getGroupId(), req.getOperatorId());

            // 分配规则这里存分组id
            super.insertOperateLog(groupInfo.getGroupId().longValue(), GroupRule.class, OperateLogFromBusinessEnum.MODIFY.type, JSONObject.toJSONString(req), req.getOperatorId());

            platformTransactionManager.commit(status);
            return 1;
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 坐席优先分配规则枚举
     */
    @Override
    public SeatPriorityDistributeRuleEnumsResp seatPriorityDistributeRuleEnums(GroupAccessRuleDetailReq req){
        req.checkParams();
        SeatPriorityDistributeRuleEnumsResp resp = new SeatPriorityDistributeRuleEnumsResp();

        // 分配策略枚举
        List<CommonLabelValueResp> distributeStrategyEnum = Arrays.stream(DistributeStrategyEnum.values()).map(item -> {
            CommonLabelValueResp labelValueResp = new CommonLabelValueResp();
            labelValueResp.setLabel(item.name);
            labelValueResp.setValue(item.type);
            return labelValueResp;
        }).collect(Collectors.toList());
        resp.setDistributeStrategyEnums(distributeStrategyEnum);

        // 坐席分配规则指标
        List<RuleMetricConfig> ruleMetricConfigs = ruleMetricConfigRepository.selectRuleMetricByObject(RuleMetricObjectEnum.SEAT.type, RuleMetricFromTypeEnum.KF.type);
        if ( CollectionUtils.isNotEmpty(ruleMetricConfigs) ){
            List<SeatPriorityDistributeRuleOptionBo> seatPriorityDistributeRuleOptionEnums = ruleMetricConfigs.stream().map(ruleMetricConfig -> {
                SeatPriorityDistributeRuleOptionBo seatPriorityDistributeRuleOptionBo = new SeatPriorityDistributeRuleOptionBo();
                seatPriorityDistributeRuleOptionBo.setRuleMetricId(ruleMetricConfig.getRuleMetricId());
                seatPriorityDistributeRuleOptionBo.setRuleMetricCn(ruleMetricConfig.getRuleMetricCn());
                if (StringUtils.isBlank(ruleMetricConfig.getRuleMetricExtraJson())) {
                    return seatPriorityDistributeRuleOptionBo;
                }
                RuleMetricExtraJsonBo ruleMetricExtraJsonBo = JSONObject.parseObject(ruleMetricConfig.getRuleMetricExtraJson(), RuleMetricExtraJsonBo.class);
                if (CollectionUtils.isEmpty(ruleMetricExtraJsonBo.getDefaultEnums())) {
                    return seatPriorityDistributeRuleOptionBo;
                }
                List<CommonLabelValueResp> ruleConfigEnums = ruleMetricExtraJsonBo.getDefaultEnums().stream().map(item -> {
                    CommonLabelValueResp commonLabelValueResp = new CommonLabelValueResp();
                    commonLabelValueResp.setLabel(item);
                    commonLabelValueResp.setValue(item);
                    return commonLabelValueResp;
                }).collect(Collectors.toList());
                seatPriorityDistributeRuleOptionBo.setRuleConfigEnums(ruleConfigEnums);
                return seatPriorityDistributeRuleOptionBo;
            }).collect(Collectors.toList());
            resp.setSeatPriorityDistributeRuleOptionEnums(seatPriorityDistributeRuleOptionEnums);
        }

        return resp;
    }

    /**
     * 坐席优先分配规则列表
     */
    @Override
    public SeatPriorityDistributeRuleDetailResp seatPriorityDistributeRuleList(SeatPriorityDistributeRuleDetailReq req) {
        SeatPriorityDistributeRuleDetailResp result = new SeatPriorityDistributeRuleDetailResp();
        SimplePageInfo<SeatPriorityDistributeRuleListResp> respSimplePageInfo = new SimplePageInfo<>();
        req.checkParams();

        // 校验分组信息
        GroupInfo groupInfo = this.checkGroupInfo(req.getGroupId(), req.getTenantId());
        result.setDistributeStrategy(groupInfo.getDistributeStrategy());

        // 查询分组关联坐席 todo 优化
        Page<Object> page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<SeatGroupMapping> seatGroupMappingList = seatGroupMappingRepository.selectListByGroupId(req.getGroupId());
        if ( CollectionUtils.isEmpty(seatGroupMappingList) ){
            return result;
        }

        // 查询坐席信息
        List<SeatInfo> seatInfos = seatInfoRepository.selectListBySeatIdList(seatGroupMappingList.stream().map(SeatGroupMapping::getSeatId).collect(Collectors.toList()));
        if ( CollectionUtils.isEmpty(seatInfos) ){
            return result;
        }

        // 获取坐席姓名
        List<AccountInfoListResp> accountInfoListResps = iopAccountService.batchGetInfoListByAccountIds(seatInfos.stream().map(SeatInfo::getAccountId).collect(Collectors.toList()));

        // 坐席分配规则指标
        List<RuleMetricConfig> ruleMetricConfigs = ruleMetricConfigRepository.selectRuleMetricByObject(RuleMetricObjectEnum.SEAT.type, RuleMetricFromTypeEnum.KF.type);

        // 坐席分配规则
        List<SeatGroupDistributeRule> seatGroupDistributeRuleList = seatGroupDistributeRuleRepository.selectListByGroupId(req.getGroupId(), seatInfos.stream().map(SeatInfo::getSeatId).collect(Collectors.toList()));

        // 格式化数据
        List<SeatPriorityDistributeRuleListResp> respList = seatInfos.stream().map(seatInfo -> {
            SeatPriorityDistributeRuleListResp seatItem = new SeatPriorityDistributeRuleListResp();
            seatItem.setSeatId(seatInfo.getSeatId());
            seatItem.setSeatName(seatInfo.getSeatName());

            accountInfoListResps.stream().filter(f -> f.getAccountId().equals(seatInfo.getAccountId())).findFirst().ifPresent(accountInfoListResp -> {
                seatItem.setSeatName(accountInfoListResp.getUsername());
            });

            // 遍历所有分配规则配置
            List<SeatPriorityDistributeRuleSelectOptionBo> distributeRuleSelectList = new ArrayList<>();
            for (RuleMetricConfig ruleMetricConfig : ruleMetricConfigs) {
                SeatPriorityDistributeRuleSelectOptionBo distributeRuleSelectItem = new SeatPriorityDistributeRuleSelectOptionBo();
                distributeRuleSelectItem.setRuleMetricId(ruleMetricConfig.getRuleMetricId());
                distributeRuleSelectItem.setRuleMetricCn(ruleMetricConfig.getRuleMetricCn());

                // 查询坐席分配规则
                SeatGroupDistributeRule seatGroupDistributeRule = seatGroupDistributeRuleList.stream().filter(f ->
                        f.getSeatId().equals(seatInfo.getSeatId()) && f.getRuleMetricId().equals(ruleMetricConfig.getRuleMetricId())
                ).findFirst().orElse(null);
                if ( Objects.nonNull(seatGroupDistributeRule) ){
                    SeatGroupDistributeRuleConfigValueBo seatGroupDistributeRuleConfigValueBo = JSONObject.parseObject(seatGroupDistributeRule.getRuleConfigValue(), SeatGroupDistributeRuleConfigValueBo.class);
                    if ( RuleConfigValueSelectiveTypeEnum.SINGLE.type.equals(seatGroupDistributeRuleConfigValueBo.getSelectiveType()) ){
                        distributeRuleSelectItem.setRuleConfigValueList(Collections.singletonList(seatGroupDistributeRuleConfigValueBo.getSingleValue()));
                    }else if ( RuleConfigValueSelectiveTypeEnum.MULTIPLE.type.equals(seatGroupDistributeRuleConfigValueBo.getSelectiveType()) ){
                        distributeRuleSelectItem.setRuleConfigValueList(seatGroupDistributeRuleConfigValueBo.getMultipleValue());
                    }
                }

                distributeRuleSelectList.add(distributeRuleSelectItem);
            }

            seatItem.setDistributeRuleSelectList(distributeRuleSelectList);
            return seatItem;
        }).collect(Collectors.toList());

        respSimplePageInfo.setList(respList);
        respSimplePageInfo.setTotal(page.getTotal());
        respSimplePageInfo.setPages(page.getPages());
        respSimplePageInfo.setPageNum(page.getPageNum());
        respSimplePageInfo.setPageSize(page.getPageSize());
        result.setSeatRuleList(respSimplePageInfo);
        return result;
    }

    /**
     * 分组分配策略编辑
     */
    @Override
    public Integer editDistributeStrategy(EditDistributeStrategyReq req) {
        req.checkParams();

        // 校验分组信息
        GroupInfo groupInfo = this.checkGroupInfo(req.getGroupId(), req.getTenantId());

        GroupInfo update = new GroupInfo();
        update.setGroupId(groupInfo.getGroupId());
        update.setDistributeStrategy(req.getDistributeStrategy());
        update.setOperatorId(req.getOperatorId());
        return groupInfoRepository.updateByPrimaryKeySelective(update);
    }

    /**
     * 坐席分配规则编辑
     */
    @Override
    public Integer editSeatDistributeRule(EditGroupSeatDistributeRuleReq req) {
        req.checkParams();

        // 校验分组信息
        GroupInfo groupInfo = this.checkGroupInfo(req.getGroupId(), req.getTenantId());

        // 获取坐席规则指标
        RuleMetricConfig ruleMetricConfig = ruleMetricConfigRepository.selectRuleMetricById(req.getRuleMetricId());
        if ( Objects.isNull(ruleMetricConfig) || !RuleMetricObjectEnum.SEAT.type.equals(ruleMetricConfig.getRuleMetricObject()) ){
            throw new BusinessException("不存在的坐席规则指标："+req.getRuleMetricId());
        }
        if ( StringUtils.isBlank(ruleMetricConfig.getRuleMetricExtraJson()) ){
            throw new BusinessException("坐席规则指标配置异常(规则指标配置为空)："+ruleMetricConfig.getRuleMetricCn());
        }
        RuleMetricExtraJsonBo ruleMetricExtraJsonBo = JSONObject.parseObject(ruleMetricConfig.getRuleMetricExtraJson(), RuleMetricExtraJsonBo.class);
        if ( StringUtils.isBlank(ruleMetricExtraJsonBo.getSelectiveType()) ){
            throw new BusinessException("坐席规则指标配置异常(规则指标选择类型为空)："+ruleMetricConfig.getRuleMetricCn());
        }

        // 格式化配置值
        SeatGroupDistributeRuleConfigValueBo seatGroupDistributeRuleConfigValueBo = new SeatGroupDistributeRuleConfigValueBo();
        seatGroupDistributeRuleConfigValueBo.setSelectiveType(ruleMetricExtraJsonBo.getSelectiveType());
        if ( RuleConfigValueSelectiveTypeEnum.MULTIPLE.type.equals(ruleMetricExtraJsonBo.getSelectiveType()) ){
            seatGroupDistributeRuleConfigValueBo.setMultipleValue(req.getRuleConfigValueList());
        }else if (  RuleConfigValueSelectiveTypeEnum.SINGLE.type.equals(ruleMetricExtraJsonBo.getSelectiveType()) ){
            seatGroupDistributeRuleConfigValueBo.setSingleValue(req.getRuleConfigValueList().get(0));
        }

        // 手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            // 获取分组坐席配置值
            SeatGroupDistributeRule seatGroupDistributeRule = seatGroupDistributeRuleRepository.selectOneByGroupIdAndSeatId(req.getGroupId(), req.getSeatId(), req.getRuleMetricId());
            if ( Objects.nonNull(seatGroupDistributeRule) ){
                SeatGroupDistributeRule update = new SeatGroupDistributeRule();
                update.setSeatGroupDistributeRuleId(seatGroupDistributeRule.getSeatGroupDistributeRuleId());
                update.setRuleConfigValue(JSONObject.toJSONString(seatGroupDistributeRuleConfigValueBo));
                update.setOperatorId(req.getOperatorId());
                seatGroupDistributeRuleRepository.updateByPrimaryKeySelective(update);

                super.insertOperateLog(seatGroupDistributeRule.getSeatGroupDistributeRuleId().longValue(), SeatGroupDistributeRule.class, OperateLogFromBusinessEnum.MODIFY.type, JSONObject.toJSONString(req), req.getOperatorId());
            }else{
                SeatGroupDistributeRule insert = new SeatGroupDistributeRule();
                insert.setGroupId(groupInfo.getGroupId());
                insert.setSeatId(req.getSeatId());
                insert.setRuleMetricId(req.getRuleMetricId());
                insert.setRuleConfigValue(JSONObject.toJSONString(seatGroupDistributeRuleConfigValueBo));
                insert.setOperatorId(req.getOperatorId());
                seatGroupDistributeRuleRepository.insertSelective(insert);

                super.insertOperateLog(insert.getSeatGroupDistributeRuleId().longValue(), SeatGroupDistributeRule.class, OperateLogFromBusinessEnum.ADD.type, JSONObject.toJSONString(req), req.getOperatorId());
            }

            // 更新分组更新时间
            groupInfoRepository.updateNowTime(req.getGroupId(), req.getOperatorId());

            platformTransactionManager.commit(status);
            return 1;
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }
    }

}
