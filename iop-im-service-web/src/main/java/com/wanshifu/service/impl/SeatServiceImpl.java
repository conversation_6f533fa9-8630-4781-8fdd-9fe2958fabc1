package com.wanshifu.service.impl;

import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.constant.ChannelConstant;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.CommonRedisConstant;
import com.wanshifu.constant.TencentConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.account.domain.enums.AccountStatus;
import com.wanshifu.iop.account.domain.resp.account.AccountAllResp;
import com.wanshifu.iop.account.domain.resp.account.AccountDetailResp;
import com.wanshifu.iop.account.domain.resp.account.AccountInfoListResp;
import com.wanshifu.iop.im.api.req.group.DistributeEnGroupReq;
import com.wanshifu.iop.im.api.req.leftSidebar.BaseOnlineSeatReq;
import com.wanshifu.iop.im.api.req.seat.*;
import com.wanshifu.iop.im.api.req.tags.CreateTagsReq;
import com.wanshifu.iop.im.api.req.tags.GetTagsListReq;
import com.wanshifu.iop.im.api.req.tags.SwitchTagsStatusReq;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.api.resp.LoginSeatAuthInfoResp;
import com.wanshifu.iop.im.api.resp.SetLoginAuthResp;
import com.wanshifu.iop.im.api.resp.seat.*;
import com.wanshifu.iop.im.api.resp.tags.GetTagsListResp;
import com.wanshifu.iop.im.domain.bo.DestroyGroupBo;
import com.wanshifu.iop.im.domain.bo.RegisterUserReqBo;
import com.wanshifu.iop.im.domain.bo.RegisterUserRespBo;
import com.wanshifu.iop.im.domain.bo.seat.GetSeatRegisterInfoRespBo;
import com.wanshifu.iop.im.domain.bo.seat.SeatSearchListReqBo;
import com.wanshifu.iop.im.domain.bo.seat.SeatStatusFunctionMappingRespBo;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.AddMemberToGroupBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.DelMemberFromGroupBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.DeleteFriendShipBo;
import com.wanshifu.iop.im.domain.enums.*;
import com.wanshifu.iop.im.domain.po.*;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyDetailConfig;
import com.wanshifu.manager.socketBase.impl.SocketBaseManagerImpl;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.repository.*;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.AuthService;
import com.wanshifu.service.DistributeService;
import com.wanshifu.service.SeatService;
import com.wanshifu.service.TagsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签服务
 * <AUTHOR>
 * @date： 2025-05-29 15:27:02
 */
@Service
@Slf4j
public class SeatServiceImpl extends AbstractService implements SeatService {

    @Resource
    private TagsService tagsService;

    @Resource
    private SeatInfoRepository seatInfoRepository;

    @Resource
    private SeatGroupMappingRepository seatGroupMappingRepository;

    @Resource
    private GroupInfoRepository groupInfoRepository;

    @Resource
    private SeatStatusRepository  seatStatusRepository;

    @Resource
    private SeatFunctionRepository seatFunctionRepository;

    @Resource
    private SeatStatusFunctionMappingRepository seatStatusfunctionMappingRepository;

    @Resource
    private UserRegisterInfoRepository userRegisterInfoRepository;

    @Resource
    private TagsRepository tagsRepository;

    @Resource
    private AuthService authService;

    @Resource
    private TencentManager tencentManager;

    @Resource
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private ImConversationRepository imConversationRepository;

    @Resource
    private DistributeService distributeService;

    @Resource
    private ImConversationTransferLogRepository imConversationTransferLogRepository;

    /**
     * 坐席列表
     * @param req
     * @return
     */
    @Override
    public SimplePageInfo<SeatSearchListResp> searchList(SeatSearchListReq req){
        SimplePageInfo<SeatSearchListResp> pageInfo = new SimplePageInfo<>();

        // 格式化搜索参数
        SeatSearchListReqBo searchListReqBo = this.formatBySeatSearchListReq(req);
        if ( searchListReqBo == null ){
            return pageInfo;
        }

        // 查询坐席信息
        Page<Object> page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<SeatInfo> seatList = seatInfoRepository.searchList(searchListReqBo);

        // 格式化出参
        List<SeatSearchListResp> seatSearchListRespList = this.transformBySeatInfoList(seatList);

        pageInfo.setList(seatSearchListRespList);
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        return pageInfo;
    }

    /**
     * 格式化搜索参数
     * @param req
     * @return
     */
    private SeatSearchListReqBo formatBySeatSearchListReq(SeatSearchListReq req) {
        if ( req == null || req.getTenantId() == null || req.getTenantId() == 0L ){
            return null;
        }

        SeatSearchListReqBo searchListReqBo = new SeatSearchListReqBo();
        BeanUtils.copyProperties(req, searchListReqBo);

        // 坐席姓名转换账号id
        if ( StringUtils.isNotEmpty(req.getUsername()) ){
            AccountDetailResp userInfoByUsername = iopAccountService.getUserInfoByUsername(req.getUsername());
            if (userInfoByUsername == null){
                return null;
            }
            searchListReqBo.setAccountId(userInfoByUsername.getAccountId());
        }

        return searchListReqBo;
    }

    /**
     * 格式化出参
     * @param seatList
     * @return
     */
    private List<SeatSearchListResp> transformBySeatInfoList(List<SeatInfo> seatList) {

        // 查询坐席姓名
        List<Long> accountIdList = seatList.stream().map(SeatInfo::getAccountId).collect(Collectors.toList());
        List<AccountInfoListResp> accountInfoListRespList = iopAccountService.batchGetInfoListByAccountIds(accountIdList);

        // 查询坐席状态
        List<String> seatStatusEnList = new ArrayList<>();
        seatStatusEnList.addAll(seatList.stream().map(SeatInfo::getCurrentSeatStatusEn).collect(Collectors.toList()));
        seatStatusEnList.addAll(seatList.stream().map(SeatInfo::getLoginSeatStatusEn).collect(Collectors.toList()));
        List<SeatStatus> seatStatusList = seatStatusRepository.batchSelectInfoBySeatStatusEnList(seatStatusEnList.stream().distinct().collect(Collectors.toList()));

        // 查询坐席分组
        List<GroupInfo> groupInfoList = new ArrayList<>();
        List<Long> seatIdList = seatList.stream().map(SeatInfo::getSeatId).collect(Collectors.toList());
        List<SeatGroupMapping> seatGroupMappingList = seatGroupMappingRepository.batchGetMappingBySeatIdList(seatIdList, null);
        if ( CollectionUtils.isNotEmpty(seatGroupMappingList) ) {
            groupInfoList = groupInfoRepository.batchSelectEnableStatusByGroupIdList(seatGroupMappingList.stream().map(SeatGroupMapping::getGroupId).collect(Collectors.toList()));
        }

        // 查询坐席标签
        List<Integer> tagIdList = seatList.stream().map(SeatInfo::getTagId).collect(Collectors.toList());
        List<Tags> tagInfoList = tagsRepository.batchSelectInfoByTagIdList(tagIdList);

        List<SeatSearchListResp> seatSearchListRespList = new ArrayList<>();
        for (SeatInfo seatInfo : seatList) {
            SeatSearchListResp seatSearchListResp = new SeatSearchListResp();
            BeanUtils.copyProperties(seatInfo, seatSearchListResp);
            // 坐席类型名称
            seatSearchListResp.setSeatTypeName(SeatTypeEnum.getNameByType(seatInfo.getSeatType()));
            // 坐席启用状态名称
            seatSearchListResp.setStatusName(StatusEnum.getNameByType(seatInfo.getStatus()));

            // 获取坐席姓名
            if ( CollectionUtils.isNotEmpty(accountInfoListRespList) ){
                accountInfoListRespList.stream().filter(accountInfoListResp -> accountInfoListResp.getAccountId().equals(seatInfo.getAccountId())).findFirst().ifPresent(accountInfoListResp -> {
                    seatSearchListResp.setUsername(accountInfoListResp.getUsername());
                });
            }

            // 获取当前坐席状态中文名称
            seatSearchListResp.setCurrentSeatStatusCn("--");
            if (seatInfo.getCurrentSeatStatusEn() != null && CommonConstant.ZERO.equals(seatInfo.getStatus())) {
                seatStatusList.stream().filter(seatStatus -> seatStatus.getSeatStatusEn().equals(seatInfo.getCurrentSeatStatusEn())).findFirst().ifPresent(seatStatus -> {
                    seatSearchListResp.setCurrentSeatStatusCn(seatStatus.getSeatStatusCn());
                });
            }

            // 获取坐席登录状态中文名称
            if (seatInfo.getLoginSeatStatusEn() != null) {
                seatStatusList.stream().filter(seatStatus -> seatStatus.getSeatStatusEn().equals(seatInfo.getLoginSeatStatusEn())).findFirst().ifPresent(seatStatus -> {
                    seatSearchListResp.setLoginSeatStatusCn(seatStatus.getSeatStatusCn());
                });
            }

            // 获取分组信息
            if ( CollectionUtils.isNotEmpty(seatGroupMappingList) ){
                List<Integer> seatGroupIdMappingList = seatGroupMappingList.stream().filter(groupMapping -> groupMapping.getSeatId().equals(seatInfo.getSeatId()))
                        .map(SeatGroupMapping::getGroupId).collect(Collectors.toList());

                if ( CollectionUtils.isNotEmpty(groupInfoList) && CollectionUtils.isNotEmpty(seatGroupIdMappingList) ){
                    List<CommonLabelValueResp> groupInfoResp = groupInfoList.stream().filter(groupInfo -> seatGroupIdMappingList.contains(groupInfo.getGroupId())).map(groupInfo -> {
                        CommonLabelValueResp commonLabelValueResp = new CommonLabelValueResp();
                        commonLabelValueResp.setLabel(groupInfo.getGroupName());
                        commonLabelValueResp.setValue(groupInfo.getGroupId().toString());
                        return commonLabelValueResp;
                    }).collect(Collectors.toList());
                    seatSearchListResp.setGroupMappingList(groupInfoResp);
                }
            }

            // 获取标签名称
            if ( CollectionUtils.isNotEmpty(tagInfoList) ){
                tagInfoList.stream().filter(tagInfo -> tagInfo.getTagId().equals(seatInfo.getTagId())).findFirst().ifPresent(tagInfo -> {
                    seatSearchListResp.setTagName(tagInfo.getTagName());
                });
            }

            seatSearchListRespList.add(seatSearchListResp);
        }


        // 查询坐席姓名
        return seatSearchListRespList;
    }

    /**
     * 搜索列表枚举
     * @return
     */
    @Override
    public SeatEnumsResp enums(SeatEnumsReq req){
        SeatEnumsResp seatSearchListEnumsResp = new SeatEnumsResp();

        // 坐席类型
        List<SeatTypeMappingEnumsResp> seatTypeMappingList = Arrays.stream(SeatTypeEnum.values()).map(seatTypeEnum -> {
            SeatTypeMappingEnumsResp seatTypeEnumResp = new SeatTypeMappingEnumsResp();
            seatTypeEnumResp.setLabel(seatTypeEnum.name);
            seatTypeEnumResp.setValue(seatTypeEnum.type.toString());
            seatTypeEnumResp.setDesc(seatTypeEnum.desc);
            return seatTypeEnumResp;
        }).collect(Collectors.toList());
        seatSearchListEnumsResp.setSeatTypeMappingList(seatTypeMappingList);

        // 启用状态
        List<CommonLabelValueResp> statusMappingList = Arrays.stream(StatusEnum.values()).map(seatTypeEnum -> {
            CommonLabelValueResp seatTypeEnumResp = new CommonLabelValueResp();
            seatTypeEnumResp.setLabel(seatTypeEnum.name);
            seatTypeEnumResp.setValue(seatTypeEnum.type.toString());
            return seatTypeEnumResp;
        }).collect(Collectors.toList());
        seatSearchListEnumsResp.setStatusMappingList(statusMappingList);

        // 详情枚举
        if ( CommonConstant.ONE.equals(req.getEnumType()) ){
            // 账号枚举
            List<CommonLabelValueResp> canCreateSeatAccountList = this.getCanCreateSeatAccountList();
            seatSearchListEnumsResp.setAccountMappingList(canCreateSeatAccountList);

            // 坐席状态
            List<SeatStatusFunctionMappingRespBo> seatStatusList = seatStatusfunctionMappingRepository.getSeatStatusByFunctionEnAndFunctionType(CommonConstant.SEAT_STATUS_ONLINE_SWITCH_TYPE, SeatFunctionTypeEnum.SUPPORT.type);
            List<CommonLabelValueResp> seatStatusMappingList = seatStatusList.stream().map(seatStatus -> {
                CommonLabelValueResp commonLabelValueResp = new CommonLabelValueResp();
                commonLabelValueResp.setLabel(seatStatus.getSeatStatusCn());
                commonLabelValueResp.setValue(seatStatus.getSeatStatusEn());
                return commonLabelValueResp;
            }).collect(Collectors.toList());
            seatSearchListEnumsResp.setCanSelectSeatStatusMappingList(seatStatusMappingList);
        }

        return seatSearchListEnumsResp;
    }

    /**
     * 获取可添加坐席的账号列表
     */
    private List<CommonLabelValueResp> getCanCreateSeatAccountList() {

//        String seatAccountJson = redisHelper.get(CommonRedisConstant.SEAT_ACCOUNT_ENUM_KEY);
//        if(StringUtils.isNotEmpty(seatAccountJson)){
//            return JSONObject.parseArray(seatAccountJson, CommonLabelValueResp.class);
//        }

        // 获取所有的账号
        List<AccountAllResp> allAccount = iopAccountService.getAllAccount(AccountStatus.NORMAL.code);
        if ( CollectionUtils.isEmpty(allAccount) ){
            return new ArrayList<>();
        }

        // 获取已经被分配的坐席
        List<Long> existSeatAccountIdList = seatInfoRepository.getAccountIdByAllSeatInfo();

        List<CommonLabelValueResp> accountList = allAccount.stream().filter(account -> !existSeatAccountIdList.contains(account.getAccountId())).map(account -> {
            CommonLabelValueResp commonLabelValueResp = new CommonLabelValueResp();
            commonLabelValueResp.setLabel(account.getUsername());
            commonLabelValueResp.setValue(account.getAccountId().toString());
            return commonLabelValueResp;
        }).collect(Collectors.toList());

//        redisHelper.set(CommonRedisConstant.SEAT_ACCOUNT_ENUM_KEY, JSONObject.toJSONString(accountList), CommonRedisConstant.SEAT_ACCOUNT_ENUM_TIME);
        return accountList;
    }

    /**
     * 创建坐席
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(SeatCreateReq req){
        // 参数校验
        req.checkParams();
        // 并发控制
        String cacheKey = String.format(CommonRedisConstant.SEAT_ACCOUNT_CREATE_KEY, req.getAccountId());
        String cache = redisHelper.get(cacheKey);
        if(StringUtils.isNotEmpty(cache)){
            throw new BusinessException("该账号已被创建");
        }

        // 校验账号是否已经被创建
        SeatInfo seatInfoByAccountId = seatInfoRepository.selectInfoByAccountId(req.getAccountId(), null);
        if(seatInfoByAccountId != null){
            throw new BusinessException("该账号已被创建");
        }

        // 校验昵称是否重复
        VerifyRepeatNameReq verifyRepeatNameReq = new VerifyRepeatNameReq();
        verifyRepeatNameReq.setVerifyName(req.getSeatName());
        verifyRepeatNameReq.setVerifyType(VerifyRepeatNameTypeEnum.SEAT.type);
        if(!super.baseVerifyRepeatName(verifyRepeatNameReq)){
            throw new BusinessException("该昵称已被使用");
        }

        // 检验入参
        VerifySeatReq verifySeatReq = new VerifySeatReq();
        verifySeatReq.setGroupIdList(req.getGroupIdList());
        verifySeatReq.setTagId(req.getTagId());
        this.verifySeatMappingReq(verifySeatReq);

        // 校验账号状态
        super.verifyAccountStatus(req.getAccountId());

        // 获取最新一条的坐席工号
        Integer LastSeatNo = this.getLastSeatNo(req.getTenantId());

        SeatInfo seatInfo = new SeatInfo();
        seatInfo.setSeatName(req.getSeatName());
        seatInfo.setFaceUrl(req.getFaceUrl());
        seatInfo.setAccountId(req.getAccountId());
        seatInfo.setAccountType( CommonConstant.ONE.equals(req.getTenantId().intValue()) ? PlatformTypeEnum.KF.accountType : PlatformTypeEnum.ENTERPRISE.accountType );
        seatInfo.setTenantId(req.getTenantId());
        seatInfo.setSeatNo(LastSeatNo.longValue());
        seatInfo.setSeatType(req.getSeatType());
        seatInfo.setTagId(req.getTagId());
        seatInfo.setLoginSeatStatusEn(req.getLoginSeatStatusEn());
        seatInfo.setMaxWiringQuantity(req.getMaxWiringQuantity());
        seatInfo.setStatus(req.getStatus());
        seatInfo.setOperatorId(req.getOperatorId());

        // 获取当前坐席默认状态
        SeatStatus defaultStatus = seatStatusRepository.getStatusByDefaultType(SeatStatusDefaultTypeEnum.OFFLINE.type);
        if ( Objects.nonNull(defaultStatus) ){
            seatInfo.setCurrentSeatStatusEn(defaultStatus.getSeatStatusEn());
        }

        try{
            Assert.isTrue(seatInfoRepository.insertSelective(seatInfo) > 0, "添加坐席失败，请稍后重试");

            // 写入坐席分组映射关系
            req.getGroupIdList().forEach(groupId -> {
                SeatGroupMapping mapping = new SeatGroupMapping();
                mapping.setSeatId(seatInfo.getSeatId());
                mapping.setGroupId(groupId);
                mapping.setStatus(req.getStatus());
                mapping.setOperatorId(req.getOperatorId());
                seatGroupMappingRepository.insertSelective(mapping);
            });

            // 写入日志
//            super.insertOperateLog(seatInfo.getSeatId(), SeatInfo.class, OperateLogFromBusinessEnum.ADD.type, JSONObject.toJSONString(req), req.getOperatorId());

            // 更新账号坐席缓存
            if ( StatusEnum.ENABLE.type.equals(req.getStatus()) ){
                this.updateSeatLoginCache(req.getAccountId(), seatInfo, req.getGroupIdList());
            }else{
                this.updateSeatLoginCache(req.getAccountId(), null, null);
            }

            redisHelper.set(cacheKey, "1", CommonRedisConstant.SEAT_ACCOUNT_CREATE_TIME);
            // 删除坐席账号枚举缓存
            redisHelper.del(CommonRedisConstant.SEAT_ACCOUNT_ENUM_KEY);

        }catch (Exception e){
            log.error("添加坐席失败 req:{}", JSONObject.toJSONString(req), e);
            throw new BusinessException("添加坐席失败，请稍后重试");
        }

        // 注册im账号
        this.seatReigisterImAccount(seatInfo);

        // 注册虚拟账号
        this.seatRegisterVirtualAccount(seatInfo);

        return seatInfo.getSeatId().intValue();
    }

    /**
     * 坐席注册im虚拟账号
     * @param seatInfo
     * @return
     */
    private Integer seatReigisterImAccount(SeatInfo seatInfo) {
        // 获取坐席和im关联关系
        UserRegisterInfo userRegisterInfo = userRegisterInfoRepository.findByUserId(seatInfo.getSeatId(), OuterUserClassTypeEnum.SEAT.type, 0);

        // 注册im账号
        RegisterUserReqBo registerUserReqBo = new RegisterUserReqBo();
        registerUserReqBo.setTenantId(seatInfo.getTenantId());
        registerUserReqBo.setImType(ImTypeEnum.CHAT.type);
        registerUserReqBo.setNick(seatInfo.getSeatName());
        registerUserReqBo.setFaceUrl(seatInfo.getFaceUrl());
        if ( Objects.nonNull(userRegisterInfo) ){
            // 编辑传外部用户id
            registerUserReqBo.setOuterUserId(userRegisterInfo.getOuterUserId());
        }
        RegisterUserRespBo registerUserRespBo = tencentManager.registerUser(registerUserReqBo);
        if (Objects.isNull(registerUserRespBo)){
            log.error("注册im账号失败 seatInfo:{}", JSONObject.toJSONString(seatInfo));
            return 0;
        }

        if ( Objects.nonNull(userRegisterInfo) ){
            if ( StringUtils.isEmpty(registerUserRespBo.getUserSign()) ){
                log.error("编辑坐席注册im账号签名信息为空 seatInfo:{}", JSONObject.toJSONString(seatInfo));
                return 0;
            }

            // 更新注册信息
            UserRegisterInfo updateUserRegisterInfo = new UserRegisterInfo();
            updateUserRegisterInfo.setRegisterInfoId(userRegisterInfo.getRegisterInfoId());
            updateUserRegisterInfo.setRegisterStatus(RegisterStatusEnum.SUCCESS.type);
            updateUserRegisterInfo.setUserSign(registerUserRespBo.getUserSign());

            // 如果是null&&签名过期时间小于当前时间，则设置签名过期时间为TencentConstant.SIGN_EXPIRE_TIME
            if ( Objects.isNull(userRegisterInfo.getUserSignExpireTime())
                    || userRegisterInfo.getUserSignExpireTime().getTime() < System.currentTimeMillis() ){
                updateUserRegisterInfo.setUserSignExpireTime(DateUtils.addYears(new Date(), TencentConstant.SIGN_EXPIRE_TIME));
            }
            return userRegisterInfoRepository.updateByPrimaryKeySelective(updateUserRegisterInfo);

        }else {
            // 新增坐席注册信息
            UserRegisterInfo newUserRegisterInfo = new UserRegisterInfo();
            newUserRegisterInfo.setUserId(seatInfo.getSeatId());
            newUserRegisterInfo.setUserClass(OuterUserClassTypeEnum.SEAT.type);
            newUserRegisterInfo.setImId(CommonConstant.DEFAULT_TENANT_ID);
            newUserRegisterInfo.setRegisterStatus(RegisterStatusEnum.FAIL.type);
            newUserRegisterInfo.setRegisterTime(new Date());
            newUserRegisterInfo.setOnlineState(OnlineStateEnum.ONLINE.type);
            newUserRegisterInfo.setOnlineStateChangeTime(new Date());
            newUserRegisterInfo.setOuterUserId(registerUserRespBo.getOuterUserId());
            newUserRegisterInfo.setUserSign("");

            if ( StringUtils.isEmpty(registerUserRespBo.getUserSign()) ){
                log.error("创建坐席注册im账号签名信息为空 seatInfo:{}", JSONObject.toJSONString(seatInfo));
                return userRegisterInfoRepository.insertSelective(newUserRegisterInfo);
            }

            newUserRegisterInfo.setRegisterStatus(RegisterStatusEnum.SUCCESS.type);
            newUserRegisterInfo.setUserSign(registerUserRespBo.getUserSign());
            newUserRegisterInfo.setUserSignExpireTime(DateUtils.addYears(new Date(), TencentConstant.SIGN_EXPIRE_TIME));
            return userRegisterInfoRepository.insertSelective(newUserRegisterInfo);
        }
    }

    /**
     * 注册虚拟账号
     * @param seatInfo
     */
    @Override
    public SeatVirtualRelation seatRegisterVirtualAccount(SeatInfo seatInfo) {

        // 手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        try{
            // 获取坐席和im关联关系
            SeatVirtualRelation seatVirtualRelation = seatVirtualRelationRepository.selectBySeatId(seatInfo.getSeatId());

            // 注册im账号
            RegisterUserReqBo registerUserReqBo = new RegisterUserReqBo();
            registerUserReqBo.setTenantId(seatInfo.getTenantId());
            registerUserReqBo.setImType(ImTypeEnum.CHAT.type);
            registerUserReqBo.setNick(seatInfo.getSeatName());
            registerUserReqBo.setFaceUrl(seatInfo.getFaceUrl());
            if ( Objects.nonNull(seatVirtualRelation) ){
                // 编辑传外部用户id
                registerUserReqBo.setOuterUserId(seatVirtualRelation.getVirtualUserId());
            }
            RegisterUserRespBo registerUserRespBo = tencentManager.registerUser(registerUserReqBo);
            if (Objects.isNull(registerUserRespBo)){
                log.error("坐席虚拟账号注册im账号失败 seatInfo:{}", JSONObject.toJSONString(seatInfo));
                throw new BusinessException("坐席虚拟账号注册im账号失败");
            }

            SeatVirtualRelation returnSeatVirtualRelation = new SeatVirtualRelation();
            if ( Objects.nonNull(seatVirtualRelation) ){
                if ( StringUtils.isEmpty(registerUserRespBo.getUserSign()) ){
                    log.error("编辑坐席注册im账号签名信息为空 seatInfo:{}", JSONObject.toJSONString(seatInfo));
                    throw new BusinessException("编辑坐席注册im账号签名信息为空");
                }

                // 获取虚拟账号注册信息
                VirtualUserInfo virtualUserInfo = virtualUserInfoRepository.getVirtualInfoById(seatVirtualRelation.getVirtualUserId());
                if ( Objects.nonNull(virtualUserInfo) ){
                    // 更新注册信息
                    VirtualUserInfo updateVirtualUserInfo = new VirtualUserInfo();
                    updateVirtualUserInfo.setVirtualInfoId(virtualUserInfo.getVirtualInfoId());
                    updateVirtualUserInfo.setUserSign(registerUserRespBo.getUserSign());
                    // 如果是null&&签名过期时间小于当前时间，则设置签名过期时间为TencentConstant.SIGN_EXPIRE_TIME
                    if ( Objects.isNull(virtualUserInfo.getUserSignExpireTime())
                            || virtualUserInfo.getUserSignExpireTime().getTime() < System.currentTimeMillis() ){
                        updateVirtualUserInfo.setUserSignExpireTime(DateUtils.addYears(new Date(), TencentConstant.SIGN_EXPIRE_TIME));
                    }
                    virtualUserInfoRepository.updateByPrimaryKeySelective(updateVirtualUserInfo);
                }else{
                    // 新增虚拟账号注册信息
                    VirtualUserInfo newVirtualUserInfo = new VirtualUserInfo();
                    newVirtualUserInfo.setImId(seatInfo.getTenantId());
                    newVirtualUserInfo.setOuterUserId(registerUserRespBo.getOuterUserId());
                    newVirtualUserInfo.setRegisterTime(new Date());
                    newVirtualUserInfo.setUserSign(registerUserRespBo.getUserSign());
                    newVirtualUserInfo.setUserSignExpireTime(DateUtils.addYears(new Date(), TencentConstant.SIGN_EXPIRE_TIME));
                    newVirtualUserInfo.setStatus(VirtualStatusEnum.USED.type);
                    virtualUserInfoRepository.insertSelective(newVirtualUserInfo);
                }

                // 是否取消绑定
                if ( BindStatusEnum.OVER.type.equals(seatVirtualRelation.getBindStatus()) ){
                    // 更新绑定关系
                    SeatVirtualRelation updateSeatVirtualRelation = new SeatVirtualRelation();
                    updateSeatVirtualRelation.setSeatVirtualRelationId(seatVirtualRelation.getSeatVirtualRelationId());
                    updateSeatVirtualRelation.setVirtualUserId(registerUserRespBo.getOuterUserId());
                    updateSeatVirtualRelation.setBindTime(new Date());
                    updateSeatVirtualRelation.setBindStatus(BindStatusEnum.BINDING.type);
                    seatVirtualRelationRepository.updateByPrimaryKeySelective(updateSeatVirtualRelation);
                }

                returnSeatVirtualRelation = seatVirtualRelation;
            }else {
                // 获取坐席和im关联关系
                UserRegisterInfo userRegisterInfo = userRegisterInfoRepository.findByUserId(seatInfo.getSeatId(), OuterUserClassTypeEnum.SEAT.type, 0);

                // 新增虚拟账号注册信息
                VirtualUserInfo newVirtualUserInfo = new VirtualUserInfo();
                newVirtualUserInfo.setImId(seatInfo.getTenantId());
                newVirtualUserInfo.setOuterUserId(registerUserRespBo.getOuterUserId());
                newVirtualUserInfo.setRegisterTime(new Date());
                newVirtualUserInfo.setUserSign(registerUserRespBo.getUserSign());
                newVirtualUserInfo.setUserSignExpireTime(DateUtils.addYears(new Date(), TencentConstant.SIGN_EXPIRE_TIME));
                newVirtualUserInfo.setStatus(VirtualStatusEnum.USED.type);
                virtualUserInfoRepository.insertSelective(newVirtualUserInfo);

                SeatVirtualRelation newSeatVirtualRelation = new SeatVirtualRelation();
                newSeatVirtualRelation.setSeatId(seatInfo.getSeatId());
                // 坐席外部用户id
                newSeatVirtualRelation.setOuterUserId(userRegisterInfo.getOuterUserId());
                // 坐席虚拟账号id
                newSeatVirtualRelation.setVirtualUserId(registerUserRespBo.getOuterUserId());
                newSeatVirtualRelation.setBindTime(new Date());
                newSeatVirtualRelation.setBindStatus(BindStatusEnum.BINDING.type);
                seatVirtualRelationRepository.insertSelective(newSeatVirtualRelation);

                returnSeatVirtualRelation = newSeatVirtualRelation;
            }

            // 提交事务
            platformTransactionManager.commit(status);
            return returnSeatVirtualRelation;
        }catch (Exception e){
            log.error("注册虚拟账号失败 seatInfo:{}", JSONObject.toJSONString(seatInfo), e);
            platformTransactionManager.rollback(status);
            return null;
        }
    }

    /**
     * 更新坐席登录缓存
     * @param accountId
     * @param seatInfo
     * @param groupIdList
     */
    private void updateSeatLoginCache(Long accountId, SeatInfo seatInfo, List<Integer> groupIdList) {
        SetLoginAuthResp setLoginAuthResp = new SetLoginAuthResp();
        Integer status = StatusEnum.DISABLE.type;
        // 只有启用的时候才缓存信息，禁用的时候设置为null
        if ( Objects.nonNull(seatInfo) ){
            status = seatInfo.getStatus();
            LoginSeatAuthInfoResp loginSeatAuthInfoResp = new LoginSeatAuthInfoResp();
            BeanUtils.copyProperties(seatInfo, loginSeatAuthInfoResp);
            loginSeatAuthInfoResp.setGroupIdList(groupIdList);
            // 兼容异常情况，只有离线状态时，将上线状态赋给当前状态
            if ( StringUtils.isEmpty(loginSeatAuthInfoResp.getCurrentSeatStatusEn())
                    || CommonConstant.DEFAULT_OFFLINE_SEAT_STATUS.equals(loginSeatAuthInfoResp.getCurrentSeatStatusEn()) ){
                loginSeatAuthInfoResp.setCurrentSeatStatusEn( StringUtils.isNotEmpty(loginSeatAuthInfoResp.getLoginSeatStatusEn()) ? loginSeatAuthInfoResp.getLoginSeatStatusEn() : CommonConstant.DEFAULT_ONLINE_SEAT_STATUS );
            }
            setLoginAuthResp.setSeatInfo(loginSeatAuthInfoResp);
        }
        authService.updateSeatLoginCache(accountId, setLoginAuthResp, status);
    }

    /**
     * 校验关联是否有效
     * @param req
     */
    private void verifySeatMappingReq(VerifySeatReq req) {

        // 校验分组状态
        if ( CollectionUtils.isNotEmpty(req.getGroupIdList()) ){
            Integer enableGroupCount = groupInfoRepository.selectEnableCountByGroupIdList(req.getGroupIdList());
            if (enableGroupCount != req.getGroupIdList().stream().distinct().count()){
                throw new BusinessException("选择关联分组不存在或已禁用删除");
            }
        }

        // 校验标签状态
        if ( req.getTagId() != null ){
            Integer enableTagsCount = tagsRepository.selectExistById(req.getTagId());
            if ( enableTagsCount == null || enableTagsCount == 0 ){
                throw new BusinessException("标签不存在或已禁用删除");
            }
        }
    }

    /**
     * 编辑坐席
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer edit(SeatEditReq req){
        // 校验昵称是否重复
        VerifyRepeatNameReq verifyRepeatNameReq = new VerifyRepeatNameReq();
        verifyRepeatNameReq.setVerifyName(req.getSeatName());
        verifyRepeatNameReq.setVerifyType(VerifyRepeatNameTypeEnum.SEAT.type);
        verifyRepeatNameReq.setExcludeId(req.getSeatId());
        if(!super.baseVerifyRepeatName(verifyRepeatNameReq)){
            throw new BusinessException("该昵称已被使用");
        }

        // 检验入参
        VerifySeatReq verifySeatReq = new VerifySeatReq();
        verifySeatReq.setGroupIdList(req.getGroupIdList());
        verifySeatReq.setTagId(req.getTagId());
        this.verifySeatMappingReq(verifySeatReq);

        SeatInfo seatInfo = new SeatInfo();
        seatInfo.setSeatId(req.getSeatId());
        seatInfo.setSeatName(req.getSeatName());
        seatInfo.setFaceUrl(req.getFaceUrl());
        seatInfo.setSeatType(req.getSeatType());
        seatInfo.setTagId(req.getTagId());
        seatInfo.setLoginSeatStatusEn(req.getLoginSeatStatusEn());
        seatInfo.setMaxWiringQuantity(req.getMaxWiringQuantity());
        seatInfo.setStatus(req.getStatus());
        seatInfo.setOperatorId(req.getOperatorId());

        try{
            Assert.isTrue(seatInfoRepository.updateByPrimaryKeySelective(seatInfo) > 0, "编辑坐席失败，请稍后重试");

            // 逻辑删除旧映射
            seatGroupMappingRepository.deleteMappingBySeatIdList(Collections.singletonList(req.getSeatId()));

            // 写入坐席分组映射关系
            req.getGroupIdList().forEach(groupId -> {
                SeatGroupMapping mapping = new SeatGroupMapping();
                mapping.setSeatId(seatInfo.getSeatId());
                mapping.setGroupId(groupId);
                mapping.setStatus(req.getStatus());
                mapping.setOperatorId(req.getOperatorId());
                seatGroupMappingRepository.insertSelective(mapping);
            });

            // 写入日志
            super.insertOperateLog(seatInfo.getSeatId(), SeatInfo.class, OperateLogFromBusinessEnum.MODIFY.type, JSONObject.toJSONString(req), req.getOperatorId());

            // 更新账号坐席缓存
            SeatInfo latestSeatInfo = seatInfoRepository.selectByPrimaryKey(seatInfo.getSeatId());
            if ( StatusEnum.ENABLE.type.equals(req.getStatus()) ){
                this.updateSeatLoginCache(latestSeatInfo.getAccountId(), latestSeatInfo, req.getGroupIdList());
            }else{
                this.updateSeatLoginCache(latestSeatInfo.getAccountId(), null, null);
            }


        }catch (Exception e){
            log.error("编辑坐席失败 req:{}", JSONObject.toJSONString(req), e);
            throw new BusinessException("编辑坐席失败，请稍后重试");
        }

        SeatInfo newSeatInfo = seatInfoRepository.selectByPrimaryKey(seatInfo.getSeatId());

        // 注册im账号
        this.seatReigisterImAccount(newSeatInfo);

        // 注册虚拟账号
        this.seatRegisterVirtualAccount(newSeatInfo);

        // 非离线的情况下，更新坐席在线信息
        if (!CommonConstant.DEFAULT_OFFLINE_SEAT_STATUS.equals(newSeatInfo.getCurrentSeatStatusEn())){
            SendImWebSocketMessageBo sendImWebSocketMessageBo = new SendImWebSocketMessageBo();
            sendImWebSocketMessageBo.setSeatId(seatInfo.getSeatId());
            sendImWebSocketMessageBo.setBusinessType(SocketBaseManagerImpl.UPDATE_SEAT_INFO);
            sendImWebSocketMessageProducer.sendImWebSocketMessage(sendImWebSocketMessageBo);
        }

        return 1;
    }

    /**
     * 获取当前账号最新的坐席工号
     * @param tenantId
     * @return
     */
    private Integer getLastSeatNo(Long tenantId) {
        String cacheKey = String.format(CommonRedisConstant.SEAT_LAST_SEAT_NO_KEY, tenantId);
        String seatNoString = redisHelper.get(cacheKey);

        Integer LastSeatNo;
        if(StringUtils.isNotEmpty(seatNoString)){
            // 缓存
            LastSeatNo = Integer.parseInt(seatNoString);
        }else{
            // 查表数据
            LastSeatNo = seatInfoRepository.getLastSeatNo(tenantId);
            if ( LastSeatNo == null ){
                LastSeatNo = CommonConstant.SEAT_SEAT_NO_START;
            }
        }

        LastSeatNo = LastSeatNo + 1;
        redisHelper.set(cacheKey, LastSeatNo.toString(), CommonRedisConstant.SEAT_LAST_SEAT_NO_TIME);
        return LastSeatNo;
    }

    /**
     * 获取标签列表
     * */
    @Override
    public List<GetTagsListResp> tagsList(GetTagsListReq req){
        List<Tags> tagsList = tagsService.getTagsList(req);
        if (CollectionUtils.isEmpty(tagsList)){
            return new ArrayList<>();
        }

        return tagsList.stream().map(tags -> {
            GetTagsListResp getTagsListResp = new GetTagsListResp();
            getTagsListResp.setTagId(tags.getTagId());
            getTagsListResp.setTagName(tags.getTagName());
            getTagsListResp.setTagDesc(tags.getTagDesc());
            getTagsListResp.setStatus(tags.getStatus());
            return getTagsListResp;
        }).collect(Collectors.toList());
    }

    /**
     * 添加坐席标签
     * */
    @Override
    public Integer createTags(CreateTagsReq req){
        return tagsService.createTags(req);
    }

    /**
     * 添加坐席标签
     * */
    @Override
    public Integer editTags(CreateTagsReq req){
        return tagsService.editTags(req);
    }

    /**
     * 切换标签状态
     */
    @Override
    public Integer switchTagsStatus(SwitchTagsStatusReq req){
        return tagsService.switchStatus(req);
    }

    /**
     * 获取坐席状态
     */
    @Override
    public List<GetSeatStatusListResp> seatStatusList(){
        // 获取所有坐席状态
        List<SeatStatus> seatStatusList = seatStatusRepository.getAll();
        if (CollectionUtils.isEmpty(seatStatusList)){
            return new ArrayList<>();
        }

        // 获取坐席状态功能
        List<SeatStatusFunctionMapping> mappingList = seatStatusfunctionMappingRepository.batchGetMappingBySeatStatusId(seatStatusList.stream().map(SeatStatus::getSeatStatusId).collect(Collectors.toList()));

        // 获取坐席功能信息
        List<SeatFunction> functionList = seatFunctionRepository.batchGetInfoByseatFunctionIdList(mappingList.stream().map(SeatStatusFunctionMapping::getSeatFunctionId).collect(Collectors.toList()));

        return seatStatusList.stream().map(seatStatus -> {
            GetSeatStatusListResp getSeatStatusListResp = new GetSeatStatusListResp();
            getSeatStatusListResp.setSeatStatusId(seatStatus.getSeatStatusId());
            getSeatStatusListResp.setSeatStatusEn(seatStatus.getSeatStatusEn());
            getSeatStatusListResp.setSeatStatusCn(seatStatus.getSeatStatusCn());
            getSeatStatusListResp.setSeatStatusDesc(seatStatus.getSeatStatusDesc());
            getSeatStatusListResp.setIsReckonInManHour(seatStatus.getIsReckonInManHour());

            List<SeatFunctionResp> seatFunctionRespList = mappingList.stream().filter(mapping ->
                     mapping.getSeatStatusId().equals(seatStatus.getSeatStatusId())
                             && !CommonConstant.SEAT_STATUS_ONLINE_SWITCH_TYPE.equals(mapping.getSeatFunctionEn())
                    ).map(mapping -> {
                        SeatFunctionResp seatFunctionResp = new SeatFunctionResp();
                        seatFunctionResp.setSeatFunctionId(mapping.getSeatFunctionId());
                        seatFunctionResp.setSeatFunctionEn(mapping.getSeatFunctionEn());
                        seatFunctionResp.setSeatFunctionType(mapping.getSeatFunctionType());
                        seatFunctionResp.setSeatFunctionTypeName(SeatFunctionTypeEnum.getNameByType(mapping.getSeatFunctionType()));

                        functionList.stream().filter(function -> function.getSeatFunctionId().equals(mapping.getSeatFunctionId())).findFirst().ifPresent(function -> {
                            seatFunctionResp.setSeatFunctionCn("可否"+function.getSeatFunctionCn());
                        });

                        return seatFunctionResp;
                    }).collect(Collectors.toList());

            getSeatStatusListResp.setSeatFunctionList(seatFunctionRespList);
            return getSeatStatusListResp;
        }).collect(Collectors.toList());
    }

    /**
     * 修改坐席状态配置
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer editSeatStatus(EditSeatStatusReq req){

        if ( CollectionUtils.isEmpty(req.getSeatFunctionList()) ){
            throw new BusinessException("坐席状态编辑异常功能");
        }

        if ( StringUtils.isNotEmpty(req.getSeatStatusDesc()) && req.getSeatStatusDesc().length() > 500 ){
            throw new BusinessException("状态说明不能超过500个字符");
        }

        SeatStatus seatStatus = seatStatusRepository.selectByPrimaryKey(req.getSeatStatusId());
        if (Objects.isNull(seatStatus)){
            throw new BusinessException("坐席状态不存在");
        }

        // 坐席状态更新
        SeatStatus updateSeatStatus = new SeatStatus();
        updateSeatStatus.setSeatStatusId(req.getSeatStatusId());
        updateSeatStatus.setIsReckonInManHour(req.getIsReckonInManHour());
        updateSeatStatus.setSeatStatusDesc(req.getSeatStatusDesc());
        updateSeatStatus.setOperatorId(req.getOperatorId() == null ? 0 : req.getOperatorId());
        int u = seatStatusRepository.updateByPrimaryKeySelective(updateSeatStatus);
        Assert.isTrue(u > 0, "坐席状态更新失败");

        // 坐席功能更新 先删除旧的，再添加新的
        List<Integer> seatFunctionIdList = req.getSeatFunctionList().stream().map(SeatFunctionResp::getSeatFunctionId).collect(Collectors.toList());
        seatStatusfunctionMappingRepository.deleteAllBySeatStatusId(req.getSeatStatusId(), seatFunctionIdList, req.getOperatorId() == null ? 0 : req.getOperatorId());

        req.getSeatFunctionList().forEach(seatFunctionResp -> {
            SeatStatusFunctionMapping seatStatusFunctionMapping = new SeatStatusFunctionMapping();
            seatStatusFunctionMapping.setSeatStatusId(req.getSeatStatusId());
            seatStatusFunctionMapping.setSeatStatusEn(seatStatus.getSeatStatusEn());
            seatStatusFunctionMapping.setSeatFunctionId(seatFunctionResp.getSeatFunctionId());
            seatStatusFunctionMapping.setSeatFunctionEn(seatFunctionResp.getSeatFunctionEn());
            seatStatusFunctionMapping.setSeatFunctionType(seatFunctionResp.getSeatFunctionType());
            seatStatusFunctionMapping.setOperatorId(req.getOperatorId() == null ? 0 : req.getOperatorId());
            seatStatusfunctionMappingRepository.insertSelective(seatStatusFunctionMapping);
        });

        // 记录操作日志
        super.insertOperateLog(req.getSeatStatusId().longValue(), SeatStatus.class, OperateLogFromBusinessEnum.MODIFY.type, JSONObject.toJSONString(req), req.getOperatorId());

        return u;
    }

    /**
     * 获取坐席功能枚举
     * @return
     */
    @Override
    public List<SeatFunctionResp> seatFunctionEnums(){
        // 获取坐席功能信息
        List<SeatFunction> functionList = seatFunctionRepository.getAll();
        return functionList.stream().filter(function -> !CommonConstant.SEAT_STATUS_ONLINE_SWITCH_TYPE.equals(function.getSeatFunctionEn())).map(function -> {
            SeatFunctionResp seatFunctionResp = new SeatFunctionResp();
            seatFunctionResp.setSeatFunctionId(function.getSeatFunctionId());
            seatFunctionResp.setSeatFunctionCn("可否"+function.getSeatFunctionCn());
            seatFunctionResp.setSeatFunctionEn(function.getSeatFunctionEn());
            return seatFunctionResp;
        }).collect(Collectors.toList());
    }


    /**
     * 关闭会话
     *
     * @param req
     */
    @Override
    public Integer closeConversation(CloseConversationReq req) {
        String outerGroupId = req.getOuterGroupId();
        Long accountId = req.getAccountId();

        ImGroup imGroupUsedByOutGroupId = imGroupRepository.getImGroupUsedByOutGroupId(outerGroupId);
        if(imGroupUsedByOutGroupId==null){
            log.error("群不存在，群id={}",outerGroupId);
            return 0;
        }
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(imGroupUsedByOutGroupId.getConversationId());
        if(imConversation==null){
            log.error("会话不存在，群id={}",outerGroupId);
            return 0;
        }

        SeatInfo seatInfo = seatInfoRepository.selectEnableInfoByAccountId(accountId);
        if(seatInfo==null){
            log.error("坐席不存在，accountId={}",accountId);
            return 0;
        }

        UserRegisterInfo userRegisterInfo = userRegisterInfoRepository.selectOuterUserIdByUser(seatInfo.getSeatId(), OuterUserClassTypeEnum.SEAT.type);
        if(userRegisterInfo==null){
            log.error("坐席注册信息不存在, seatId="+seatInfo.getSeatId());
            return 0;
        }
        if (!OuterUserClassTypeEnum.SEAT.type.equals(imConversation.getToOuterUserType())
                || !userRegisterInfo.getOuterUserId().equals(imConversation.getToOuterUserId())) {
            log.error("不是"+req.getAccountId()+"坐席会话，不能关闭会话，外部群组id+"+req.getOuterGroupId());
            return 0;
        }
        /**
         * 1、发送关闭会话提示
         *      取会话的配置 或者全局配置
         * 2、解除虚拟账号绑定关系
         * 3、解散群聊，需要调接口
         * 4、会话状态改成完成
         * 5、停止xxljob执行
         * */

        //1
        this.sendConversationCloseTip(imConversation);
        //手动回滚
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        try{
            //2
            String fromOuterUserId = imConversation.getFromOuterUserId();
            String agentOuterUserId = imConversation.getAgentOuterUserId();
            userVirtualRelationRepository.closeConversationUpdateBind(fromOuterUserId, agentOuterUserId);

            //4
            ImConversation imConversationUpdate = new ImConversation();
            imConversationUpdate.setConversationId(imConversation.getConversationId());
            imConversationUpdate.setCompleteTime(new Date());
            imConversationUpdate.setConversationType(ImConversationTypeEnum.history.type);
            imConversationUpdate.setConversationStatus(ConversationStatusEnum.COMPLETE.type);
            imConversationRepository.updateByPrimaryKeySelective(imConversationUpdate);
            platformTransactionManager.commit(status);
        }catch (Exception e){
            platformTransactionManager.rollback(status);
        }

        try{
            //3
            DestroyGroupBo destroyGroupBo = new DestroyGroupBo();
            destroyGroupBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            destroyGroupBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
            destroyGroupBo.setGroupId(outerGroupId);
            Integer i = tencentManager.destroyGroup(destroyGroupBo);
            if(i==null){
                throw new BusException("解散群聊异常，请求参数="+JSON.toJSONString(destroyGroupBo));
            }
            //5删除好友关系
            DeleteFriendShipBo deleteFriendShipBo = new DeleteFriendShipBo();
            deleteFriendShipBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            deleteFriendShipBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
            deleteFriendShipBo.setImConversationId(imConversation.getConversationId());

            tencentManager.deleteFriendShip(deleteFriendShipBo);
            //停止xxljob执行
            xxlJobTemplate.stopJob(Integer.valueOf(imConversation.getTimeoutXxlId()+""),1);

        }catch (Exception e){
            log.error("关闭会话异常",e);
        }

        // 发送关闭会话socket
        SendImWebSocketMessageBo sendImWebSocketMessageBo = new SendImWebSocketMessageBo();
        sendImWebSocketMessageBo.setConversationId(imConversation.getConversationId());
        sendImWebSocketMessageBo.setBusinessType(SocketBaseManagerImpl.CONVERSATION_CLOSE);
        sendImWebSocketMessageProducer.sendImWebSocketMessage(sendImWebSocketMessageBo);

        // 发送更新坐席状态mq
        ConversationFlowSeatStatusUpdateReq conversationFlowSeatStatusUpdateReq = new ConversationFlowSeatStatusUpdateReq();
        conversationFlowSeatStatusUpdateReq.setSeatId(seatInfo.getSeatId());
        conversationFlowSeatStatusUpdateProducer.sendMessage(conversationFlowSeatStatusUpdateReq);

        return 1;
    }


    /**
     * 1、发送会话结束提示
     * */
    private void sendConversationCloseTip(ImConversation imConversation){
        ChannelConfig channelConfigByChannelEn = channelConfigRepository.getChannelConfigByChannelEn(ChannelConstant.CONVERSATION_GLOBAL_CHANNEL_EN);
        List<Long> channelIdList = new ArrayList<>();
        if(channelConfigByChannelEn!=null){
            //全局渠道id
            channelIdList.add(channelConfigByChannelEn.getChannelId());
        }
        channelIdList.add(imConversation.getChannelConfigId());

        List<ConversationAutoReplyConfig> configAutoReplyListByChannelIds = conversationAutoReplyConfigRepository.getConfigListByChannelIds(channelIdList);
        if(CollectionUtils.isEmpty(configAutoReplyListByChannelIds)){
            return ;
        }
        List<Long> allAutoReplyConfigIdList = configAutoReplyListByChannelIds.stream().map(ConversationAutoReplyConfig::getConversationAutoReplyConfigId).collect(Collectors.toList());

        List<ConversationAutoReplyDetailConfig> listByAutoReplyConfigIdList = conversationAutoReplyDetailConfigRepository.getListByAutoReplyConfigIdAndMsgSubTypeList(allAutoReplyConfigIdList);


        if(CollectionUtils.isEmpty(listByAutoReplyConfigIdList)){
            log.error("不存在全局配置和渠道配置的超时配置，channelIdList={}", JSON.toJSONString(channelIdList));
            return ;
        }
        List<ConversationAutoReplyDetailConfig> timeoutSecondDetailConfigList = listByAutoReplyConfigIdList.stream().filter(f -> StringUtils.isNotEmpty(f.getTimeoutCostSecond())).collect(Collectors.toList());

        //全局配置和渠道配置， 如果有渠道配置拿渠道配置，如果没有渠道配置拿全局配置
        //会话配置的全局配置id集合
        List<Long> conversationAutoReplyConfigIdList = configAutoReplyListByChannelIds.stream().filter(f -> f.getChannelId().equals(imConversation.getChannelConfigId()))
                .map(ConversationAutoReplyConfig::getConversationAutoReplyConfigId).collect(Collectors.toList());
        //全局配置的自动回复配置id集合
        List<Long> globalAutoReplyConfigIdList = new ArrayList<>();
        if(channelConfigByChannelEn!=null){
            globalAutoReplyConfigIdList = configAutoReplyListByChannelIds.stream().filter(f -> f.getChannelId().equals(imConversation.getChannelConfigId()))
                    .map(ConversationAutoReplyConfig::getConversationAutoReplyConfigId).collect(Collectors.toList());
        }
        List<ConversationAutoReplyDetailConfig> realAutoReplyDetailConfigList = super.getRealAutoReplyDetailConfigList(conversationAutoReplyConfigIdList, globalAutoReplyConfigIdList, listByAutoReplyConfigIdList);
        if(CollectionUtils.isEmpty(realAutoReplyDetailConfigList)){
            log.error("不存在超时配置，channelIdList={}", JSON.toJSONString(channelIdList));
            return;
        }
        ConversationAutoReplyDetailConfig conversationAutoReplyDetailConfig = realAutoReplyDetailConfigList.get(0);

        Integer i = super.sendMsgToGroup(imConversation.getConversationId(), imConversation.getToOuterUserId(), conversationAutoReplyDetailConfig.getAutoReplyContent());

        ConversationAutoReplyRecord conversationAutoReplyRecord = new ConversationAutoReplyRecord();
        conversationAutoReplyRecord.setConversationId(imConversation.getConversationId());
        conversationAutoReplyRecord.setConversationItemId(0L);
        conversationAutoReplyRecord.setConversationAutoReplyDetailConfigId(conversationAutoReplyDetailConfig.getConversationAutoReplyConfigId());
        conversationAutoReplyRecord.setMsgSubType(conversationAutoReplyDetailConfig.getMsgSubType());
        conversationAutoReplyRecord.setRecordStatus(SuccessAndFailEnum.FAIL.type);
        if(i==1){
            conversationAutoReplyRecord.setRecordStatus(SuccessAndFailEnum.SUCCESS.type);
        }
        conversationAutoReplyRecord.setContent(conversationAutoReplyDetailConfig.getAutoReplyContent());
        conversationAutoReplyRecord.setIsDelete(0);
        conversationAutoReplyRecord.setUpdateTime(new Date());
        conversationAutoReplyRecord.setCreateTime(new Date());
        conversationAutoReplyRecordRepository.insertSelective(conversationAutoReplyRecord);
    }

    /**
     * 坐席列表-转接坐席列表
     */
    @Override
    public List<ConversationTransferServiceResp> transferConversation() {
        List<ConversationTransferServiceResp> result = new ArrayList<>();
        List<GroupInfo> groupInfos = groupInfoRepository.groupInfoList(CommonConstant.DEFAULT_TENANT_ID);
        if(CollectionUtils.isEmpty(groupInfos)){
            return result;
        }

        List<Integer> groupIdList = groupInfos.stream().map(GroupInfo::getGroupId).collect(Collectors.toList());

        List<SeatGroupMapping> seatGroupMappings = seatGroupMappingRepository.selectListByGroupIds(groupIdList);
        List<Long> seatIdList = seatGroupMappings.stream().map(SeatGroupMapping::getSeatId).distinct().collect(Collectors.toList());

        if(CollectionUtils.isEmpty(seatIdList)){
            return result;
        }

        //坐席基本信息
        List<SeatInfo> seatInfos = seatInfoRepository.selectListBySeatIdList(seatIdList);

        List<UserRegisterInfo> byUserIdAndUserClassList = userRegisterInfoRepository.findByUserIdAndUserClass(seatIdList, UserClassEnum.SEAT.type);
        if(CollectionUtils.isEmpty(byUserIdAndUserClassList)){
            return result;
        }
        List<String> seatOuterUserIdList = byUserIdAndUserClassList.stream().map(UserRegisterInfo::getOuterUserId).collect(Collectors.toList());
        //查询会话记录，统计进行中的会话
        List<ImConversation> imConversations = imConversationRepository.selectByGroupIdsAndToOuterUserIds(groupIdList, seatOuterUserIdList);

        for (GroupInfo groupInfo : groupInfos) {
            ConversationTransferServiceResp conversationTransferServiceResp = new ConversationTransferServiceResp();
            conversationTransferServiceResp.setGroupName(groupInfo.getGroupName());
            conversationTransferServiceResp.setGroupId(groupInfo.getGroupId());
            List<ConversationTransferServiceResp.GroupMemberInfo> groupMemberInfoList = new ArrayList<>();

            List<SeatGroupMapping> groupMappingList = seatGroupMappings.stream().filter(f -> f.getGroupId().equals(groupInfo.getGroupId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(groupMappingList)){
                result.add(conversationTransferServiceResp);
                continue;
            }

            List<Long> seatIdListGroup = groupMappingList.stream().map(SeatGroupMapping::getSeatId).collect(Collectors.toList());
            for (SeatInfo seatInfo : seatInfos) {
                if(!seatIdListGroup.contains(seatInfo.getSeatId())){
                    continue;
                }
                ConversationTransferServiceResp.GroupMemberInfo groupMemberInfo = new ConversationTransferServiceResp.GroupMemberInfo();
                groupMemberInfo.setSeatId(seatInfo.getSeatId());
                groupMemberInfo.setSeatName(seatInfo.getSeatName());
                //客服配置的最大接线量
                groupMemberInfo.setMaxLineNum(seatInfo.getMaxWiringQuantity());
                //客服进行中的会话量
                Optional<UserRegisterInfo> first = byUserIdAndUserClassList.stream().filter(f -> f.getUserId().equals(seatInfo.getSeatId())).findFirst();
                if(!first.isPresent()){
                    continue;
                }
                long count = imConversations.stream().filter(f -> f.getGroupId().toString().equals(groupInfo.getGroupId() + "") && f.getToOuterUserId().equals(first.get().getOuterUserId())).count();
                groupMemberInfo.setCurrentLineNum((int) count);
                groupMemberInfo.setCurrentSeatStatusEn(seatInfo.getCurrentSeatStatusEn());
                groupMemberInfoList.add(groupMemberInfo);
            }
            conversationTransferServiceResp.setGroupMemberInfoList(groupMemberInfoList);
            result.add(conversationTransferServiceResp);
        }

        return result;
    }

    /**
     * 会话转接
     * 校验：
     *   坐席是否空闲
     * 步骤：
     * 如果没有传seatId, 走分配逻辑
     *
     * 如果有传seatId,只做检验更新表数据操作即可
     *
     *   1、将坐席拉进群，将原来的坐席移除群，调腾讯接口
     *   2、更新群组成员关系表
     *   3、更新会话toOuterUserId;
     *   4、更新会话记录表
     * @param req
     */
    @Override
    public Integer transfer(TransferServiceReq req) {

        ImGroup imGroupByOutGroupId = imGroupRepository.getImGroupByOutGroupId(CommonConstant.DEFAULT_TENANT_ID, req.getOuterGroupId());
        if(ObjectUtils.isEmpty(imGroupByOutGroupId)){
            log.error("找不到群记录信息，无法转接");
            return null;
        }
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(imGroupByOutGroupId.getConversationId());
        if(ObjectUtils.isEmpty(imConversation)){
            log.error("找不到会话记录信息，无法转接");
            return null;
        }

        UserRegisterInfo byUserId = userRegisterInfoRepository.findByUserId(req.getSeatId(), OuterUserClassTypeEnum.SEAT.type, CommonConstant.ONE);
        if(byUserId==null){
            throw new BusException("坐席对应的账号没有注册im用户"+JSON.toJSONString(req));
        }
        if(req.getSeatId()!=null){
            SeatInfo seatInfo = seatInfoRepository.selectByPrimaryKey(req.getSeatId());
            if(seatInfo==null || !CurrentSeatStatusEnum.ONLINE.type.equals(seatInfo.getCurrentSeatStatusEn())){
                throw new BusException("坐席不是待接线状态，不能转接");
            }
        }

        List<ImGroupUserRelation> groupUserRelationsByImGroupId = imGroupUserRelationRepository.getGroupUserRelationsByImGroupId(imGroupByOutGroupId.getImGroupId(), OuterUserClassTypeEnum.SEAT.type);
        if(CollectionUtils.isEmpty(groupUserRelationsByImGroupId)){
            throw new BusException("群聊中没有坐席账号无法转接");
        }

        ChannelConfig channelConfig = channelConfigRepository.selectByPrimaryKey(imConversation.getChannelConfigId());

        List<UserProblemClass> userProblemClassByChannelId = userProblemClassRepository.getUserProblemClassByChannelId(channelConfig.getChannelId());

        if(req.getSeatId()==null){
            //会话转接记录表
            ImConversationTransferLog imConversationTransferLog = new ImConversationTransferLog();
            imConversationTransferLog.setConversationId(imConversation.getConversationId());
            imConversationTransferLog.setOriginGroupInfoId(imConversation.getGroupId());
            imConversationTransferLog.setOriginOuterUserId(imConversation.getToOuterUserId());
            imConversationTransferLog.setNewGroupInfoId(Long.valueOf(req.getGroupId()));
            imConversationTransferLog.setNewOuterUserId("");
            imConversationTransferLog.setTransferTime(new Date());
            imConversationTransferLog.setNote(req.getNote());
            imConversationTransferLog.setIsDelete(0);
            imConversationTransferLog.setUpdateTime(new Date());
            imConversationTransferLog.setCreateTime(new Date());
            imConversationTransferLogRepository.insertSelective(imConversationTransferLog);

            //走自动分配逻辑
            DistributeEnGroupReq distributeEnGroupReq = new DistributeEnGroupReq();
            distributeEnGroupReq.setConversationId(imConversation.getConversationId());
            distributeEnGroupReq.setOuterUserId(imConversation.getFromOuterUserId());
            distributeEnGroupReq.setClientCategoryId(channelConfig.getClientCategoryId());
            distributeEnGroupReq.setChannelId(imConversation.getChannelConfigId());
            distributeEnGroupReq.setUserProblemClassId(userProblemClassByChannelId.get(0).getProblemId());
            distributeEnGroupReq.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            distributeEnGroupReq.setGroupInfoId(req.getGroupId());
            distributeService.distributeEnGroup(distributeEnGroupReq);
            return 1;
        }

        //将新的客服添加到群组
        AddMemberToGroupBo addMemberToGroupBo = new AddMemberToGroupBo();
        addMemberToGroupBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        addMemberToGroupBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        addMemberToGroupBo.setGroupId(imGroupByOutGroupId.getImGroupId());
        addMemberToGroupBo.setMembers(Collections.singletonList(byUserId.getOuterUserId()));
        Boolean addGroupSuccess = tencentManager.addMemberToGroup(addMemberToGroupBo);
        if(!addGroupSuccess){
            throw new BusException("添加客服到群组失败,req="+JSON.toJSONString(req));
        }
        //移除群聊
        DelMemberFromGroupBo delMemberFromGroupBo = new DelMemberFromGroupBo();
        delMemberFromGroupBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        delMemberFromGroupBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        delMemberFromGroupBo.setGroupId(imGroupByOutGroupId.getImGroupId());
        delMemberFromGroupBo.setMembers(Collections.singletonList(groupUserRelationsByImGroupId.get(0).getOuterUserId()));
        Boolean removeGroupMember = tencentManager.delMemberFromGroup(delMemberFromGroupBo);
        if(!removeGroupMember){
            throw new BusinessException("转接失败，添加新坐席成功，移除原有坐席失败,req="+JSON.toJSONString(req));
        }

        //手动回滚
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        try{
            ImGroupUserRelation imGroupUserRelation = groupUserRelationsByImGroupId.get(0);
            imGroupUserRelation.setMemberStatus(MemberStatusEnum.OUT.type);
            imGroupUserRelationRepository.updateByPrimaryKeySelective(imGroupUserRelation);

            ImGroupUserRelation imGroupUserRelationNew = new ImGroupUserRelation();
            imGroupUserRelationNew.setImGroupId(imGroupByOutGroupId.getImGroupId());
            imGroupUserRelationNew.setOuterUserId(byUserId.getOuterUserId());
            imGroupUserRelationNew.setOuterClassType(OuterUserClassTypeEnum.SEAT.type);
            imGroupUserRelationNew.setMemberStatus(MemberStatusEnum.IN.type);
            imGroupUserRelationNew.setIsDelete(0);
            imGroupUserRelationNew.setUpdateTime(new Date());
            imGroupUserRelationNew.setCreateTime(new Date());
            imGroupUserRelationRepository.insertSelective(imGroupUserRelationNew);

            String originalOuterUserId = imConversation.getToOuterUserId();
            Long originGroupId = imConversation.getGroupId();
            //更新会话
            imConversation.setToOuterUserId(byUserId.getOuterUserId());
            imConversation.setGroupId(Long.valueOf(req.getGroupId()));
            imConversationRepository.updateByPrimaryKeySelective(imConversation);

            //插入会话日志
            ImConversationLog imConversationLog = new ImConversationLog();
            BeanUtils.copyProperties(imConversation, imConversationLog);
            imConversationLog.setToOuterUserId(byUserId.getOuterUserId());
            imConversationLogRepository.insertSelective(imConversationLog);
            platformTransactionManager.commit(status);

            //会话转接记录表
            ImConversationTransferLog imConversationTransferLog = new ImConversationTransferLog();
            imConversationTransferLog.setConversationId(imConversation.getConversationId());
            imConversationTransferLog.setOriginGroupInfoId(originGroupId);
            imConversationTransferLog.setOriginOuterUserId(originalOuterUserId);
            imConversationTransferLog.setNewGroupInfoId(Long.valueOf(req.getGroupId()));
            imConversationTransferLog.setNewOuterUserId(byUserId.getOuterUserId());
            imConversationTransferLog.setTransferTime(new Date());
            imConversationTransferLog.setNote(req.getNote());
            imConversationTransferLog.setIsDelete(0);
            imConversationTransferLog.setUpdateTime(new Date());
            imConversationTransferLog.setCreateTime(new Date());
            imConversationTransferLogRepository.insertSelective(imConversationTransferLog);

        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusException("会话转接请求腾讯接口成功，操作表失败，req="+JSON.toJSONString( req));
        }

        return 1;
    }

    /**
     * 会话流转更新坐席状态
     * 新增会话，认领留言，激活历史
     * 关闭会话
     */
    @Override
    public Integer conversationFlowSeatStatusUpdate(ConversationFlowSeatStatusUpdateReq req){
        req.checkParams();
        GetSeatRegisterInfoRespBo seatRegisterInfoRespBo;

        if ( Objects.nonNull(req.getSeatId()) ){
            BaseOnlineSeatReq baseOnlineSeatReq = new BaseOnlineSeatReq();
            baseOnlineSeatReq.setSeatId(req.getSeatId());
            baseOnlineSeatReq.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            seatRegisterInfoRespBo = super.getSeatRegisterInfo(baseOnlineSeatReq);
        }else{
            seatRegisterInfoRespBo = super.getSeatInfoByOutUserId(req.getSeatOuterUserId());
        }
        UserRegisterInfo seatRegisterInfo = seatRegisterInfoRespBo.getUserRegisterInfo();
        SeatInfo seatInfo = seatRegisterInfoRespBo.getSeatInfo();


        // 如果坐席当前状态不是在接待中，不进行状态更新
        if ( !Arrays.asList(CommonConstant.SEAT_STATUS_ONLINE, CommonConstant.SEAT_STATUS_FULL_LOAD).contains(seatInfo.getCurrentSeatStatusEn()) ){
            log.info("坐席当前状态非在线或满负荷，不进行状态更新，当前状态：{}", seatInfo.getCurrentSeatStatusEn());
            return 1;
        }

        // 获取坐席当前的状态
        Integer waitingCount = imConversationRepository.selectWaitingCountByToOuterUserId(seatRegisterInfo.getOuterUserId());

        /**
         * 当前坐席状态是在线
         * waitingCount 大于等于 坐席最大接线量 则需要修改成满负荷
         */
        if ( CommonConstant.SEAT_STATUS_ONLINE.equals(seatInfo.getCurrentSeatStatusEn())
                && waitingCount >= seatInfo.getMaxWiringQuantity() ){
            // 修改坐席状态为繁忙
            super.seatStatusSwitch(seatInfo, CommonConstant.SEAT_STATUS_FULL_LOAD);
        }

        /**
         * 当前坐席状态是满负荷
         * waitingCount 小于 坐席最大接线量 则需要修改成在线
         */
        if ( CommonConstant.SEAT_STATUS_FULL_LOAD.equals(seatInfo.getCurrentSeatStatusEn())
                && waitingCount < seatInfo.getMaxWiringQuantity() ){
            // 修改坐席状态为在线
            super.seatStatusSwitch(seatInfo, CommonConstant.SEAT_STATUS_ONLINE);
        }

        return 1;
    }
}
