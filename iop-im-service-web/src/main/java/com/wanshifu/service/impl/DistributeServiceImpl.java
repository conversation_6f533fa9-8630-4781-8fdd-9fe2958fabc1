package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.CommonRedisConstant;
import com.wanshifu.factory.action.SeatHandle;
import com.wanshifu.filter.GroupRuleFilter;
import com.wanshifu.filter.GroupRuleFilterChain;
import com.wanshifu.filter.action.ChannelFilter;
import com.wanshifu.filter.action.ClientCategoryFilter;
import com.wanshifu.filter.action.UserProblemClassFilter;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.iop.im.api.req.group.DistributeEnGroupReq;
import com.wanshifu.iop.im.api.req.group.DistributeGroupSeatReq;
import com.wanshifu.iop.im.api.req.group.QueueGroupEnQueueReq;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.domain.bo.CommonFilterResultBo;
import com.wanshifu.iop.im.domain.bo.channel.msg.ToArtificialTypesBo;
import com.wanshifu.iop.im.domain.bo.groupManage.GroupMaxFreeRateBo;
import com.wanshifu.iop.im.domain.bo.groupManage.SeatGroupDistributeRuleConfigValueBo;
import com.wanshifu.iop.im.domain.bo.groupManage.SeatMaxFreeRateBo;
import com.wanshifu.iop.im.domain.bo.queue.BatchGetQueueInfoBo;
import com.wanshifu.iop.im.domain.bo.queue.ConversationDistributeEnGroupFilterBo;
import com.wanshifu.iop.im.domain.bo.queue.DistributeGroupQueueInfoBo;
import com.wanshifu.iop.im.domain.bo.queue.DistributeGroupSeatBo;
import com.wanshifu.iop.im.domain.bo.queue.GroupConfigRuleAttributeReqBo;
import com.wanshifu.iop.im.domain.bo.queue.XxlDistributeMqReqBo;
import com.wanshifu.iop.im.domain.bo.seat.MaxOnlineCountByGroupIdListRespBo;
import com.wanshifu.iop.im.domain.bo.seat.SeatOutUserIdMappingRespBo;
import com.wanshifu.iop.im.domain.dto.distribute.SelectOnlineNumDto;
import com.wanshifu.iop.im.domain.enums.CurrentSeatStatusEnum;
import com.wanshifu.iop.im.domain.enums.ImConversationDistributeStatusEnum;
import com.wanshifu.iop.im.domain.enums.ModulesTypeEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserClassTypeEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricFromTypeEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricObjectEnum;
import com.wanshifu.iop.im.domain.enums.TriggerModeEnum;
import com.wanshifu.iop.im.domain.enums.UserClassEnum;
import com.wanshifu.iop.im.domain.po.*;
import com.wanshifu.iop.work.order.domain.api.request.SelectKaUserInfoByUserIdsReq;
import com.wanshifu.iop.work.order.domain.api.response.UserKaByUserIdsResp;
import com.wanshifu.iop.work.order.service.api.UserKaApi;
import com.wanshifu.manager.queueManager.QueueManager;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.mq.producer.ToArtificialWelcomeMsgProducer;
import com.wanshifu.mq.producer.XxlDistributeProducer;
import com.wanshifu.repository.*;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.DistributeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 鉴权服务
 *
 * <AUTHOR>
 * @date： 2025-06-04 15:27:44
 */
@Service
@Slf4j
public class DistributeServiceImpl extends AbstractService implements DistributeService {

    /**
     * 全局队列最大长度
     */
    @Value("${wanshifu.globalQueueConfig.length}")
    private Long globalQueueConfigLength;

    /**
     * 全局队列成员间隔时间（分钟）
     */
    @Value("${wanshifu.globalQueueConfig.memberIntervalMinute}")
    private Long globalQueueConfigMemberIntervalMinute;

    @Resource
    private SeatInfoRepository seatInfoRepository;

    @Resource
    private SeatStatusRepository seatStatusRepository;

    @Resource
    private SeatGroupMappingRepository seatGroupMappingRepository;

    @Resource
    private ImConversationDistributeRepository imConversationDistributeRepository;

    @Resource
    private GroupInfoRepository groupInfoRepository;

    @Resource
    private GroupRuleRepository groupRuleRepository;

    @Resource
    private RuleMetricConfigRepository ruleMetricConfigRepository;

    @Resource
    private ImConversationRepository imConversationRepository;

    @Resource
    private UserRegisterInfoRepository userRegisterInfoRepository;

    @Resource
    private QueueConfigRepository queueConfigRepository;

    @Resource
    private QueueManager queueManager;

    @Resource
    private UserKaApi userKaApi;
    @Autowired
    private SeatHandle seat;
    @Autowired
    private SeatGroupDistributeRuleRepository seatGroupDistributeRuleRepository;

    @Resource
    private ToArtificialWelcomeMsgProducer toArtificialWelcomeMsgProducer;

    /**
     * 分配进组
     *
     * @param req
     * @return
     */
    @Override
    public Integer distributeEnGroup(DistributeEnGroupReq req) {
        req.checkParams();
        /**
         * 预演：
         * 1，会话携带会话渠道，会话入口，咨询类型进线
         * 2，根据会话渠道，会话入口，咨询类型，查询符合的会话组
         * 3，根据根据符合的会话，按几种特殊条件去进组 如：
         * 会话只满足一个会话组的进线逻辑时，直接进入该组分配或排队
         * 当会话满足多个会话组的进线逻辑时，当仅有一个会话组无需排队时，则会话直接进入该会话组；
         * 当会话满足多个会话组的进线逻辑时，所有会话组都需要排队，则会话进入吞吐量最大的会话组（当前分组吞吐能力值 = 当前分组所有坐席在线接线总数/当前分组队列排队人数（排队人数为0的情况默认为1）），如果吞吐量一致，则按分组排序分给最小的会话组
         * 当会话满足多个会话组的进线逻辑时，所有会话组都无需排队，则会话进入空闲程度最大的会话组；（空闲程度最大=该组内的所有坐席的当前可接待会话量/该组内所有坐席的最大接线量）
         */

        // 查询是否已经分配
        ImConversationDistribute exist = imConversationDistributeRepository.selectInfoByConversationIdAndDistributeStatus(req.getConversationId(), ImConversationDistributeStatusEnum.ENGROUP.type);
        if (Objects.nonNull(exist)) {
            log.info("会话已经进组 req:{}", JSONObject.toJSONString(req));
            return 1;
        }

        // 获取所有分组, 剔除兜底组
        List<GroupInfo> groupInfoList = groupInfoRepository.selectAllByEnable(req.getTenantId(), minimumGuaranteeGroupId);
        if (CollectionUtils.isEmpty(groupInfoList)) {
            // 进入兜底组
            log.error("没有可用的分组 进入兜底分组：{}，req:{}", minimumGuaranteeGroupId, JSONObject.toJSONString(req));
            return minimumGuaranteeGroupId;
        }
        List<Integer> enGroupIdList = Collections.singletonList(req.getGroupInfoId());

        if(req.getGroupInfoId()==null){
            // 获取符合分配规则的分组
            enGroupIdList = this.returnGroupIdListByGroupRuleFilter(req, groupInfoList);
            log.info("符合的分组：{}", JSONObject.toJSONString(enGroupIdList));

            if(enGroupIdList!=null && CollectionUtils.isEmpty(enGroupIdList)){
                enGroupIdList.add(minimumGuaranteeGroupId);
            }
        }

        // 分配进组
        DistributeGroupQueueInfoBo enGroupInfo = this.distributeEnGroupByGroupIdList(req.getTenantId(), enGroupIdList, groupInfoList);

        // 分配进组
        ImConversationDistribute imConversationDistribute = new ImConversationDistribute();
        imConversationDistribute.setConversationId(req.getConversationId());
        imConversationDistribute.setGroupId(enGroupInfo.getGroupId());
        imConversationDistribute.setOuterUserId(req.getOuterUserId());
        imConversationDistribute.setDistributeStatus(ImConversationDistributeStatusEnum.ENGROUP.type);
        imConversationDistribute.setDistributeGroupTime(new Date());
        imConversationDistributeRepository.insertSelective(imConversationDistribute);

        if (enGroupInfo.getNeedQueue()) {
            // 这里后面改用mq
            this.enterQueue(enGroupInfo, req, imConversationDistribute);

        } else {
            // 直接分配
            DistributeGroupSeatReq distributeGroupSeatReq = new DistributeGroupSeatReq();
            distributeGroupSeatReq.setConversationId(req.getConversationId());
            distributeGroupSeatReq.setOuterUserId(req.getOuterUserId());
            distributeGroupSeatReq.setGroupId(enGroupInfo.getGroupId());
            DistributeGroupSeatBo seatOuterUserId = this.distributeGroupSeat(distributeGroupSeatReq);

            if (seatOuterUserId == null) {
                //分配失败，进队
                this.enterQueue(enGroupInfo, req, imConversationDistribute);
                return enGroupInfo.getGroupId();
            }

            Integer i = commonBaseManager.distributeUpdateConversation(req.getConversationId(), enGroupInfo.getGroupId(), seatOuterUserId.getToOuterUserId(), OuterUserClassTypeEnum.SEAT.type);
            if (i == 0) {
                //分配失败，进队
                this.enterQueue(enGroupInfo, req, imConversationDistribute);
                return enGroupInfo.getGroupId();
            }
            Long conversationDistributeId = imConversationDistribute.getConversationDistributeId();
            ImConversationDistribute distribute = new ImConversationDistribute();
            distribute.setConversationDistributeId(conversationDistributeId);
            distribute.setSeatId(seatOuterUserId.getSeatId());
            distribute.setDistributeStatus(ImConversationDistributeStatusEnum.COMPLETED.type);
            distribute.setDistributeSeatTime(new Date());
            imConversationDistributeRepository.updateByPrimaryKeySelective(distribute);

            ToArtificialTypesBo toArtificialTypesBo = new ToArtificialTypesBo();
            toArtificialTypesBo.setImConversationId(req.getConversationId());
            toArtificialTypesBo.setTriggerMode(TriggerModeEnum.SEAT_ENTER.type);
            toArtificialTypesBo.setSeatOuterUserId(seatOuterUserId.getToOuterUserId());
            toArtificialTypesBo.setPreToOuterUserType(OuterUserClassTypeEnum.SEAT.type);
            toArtificialWelcomeMsgProducer.sendMessage(toArtificialTypesBo);

        }
        return enGroupInfo.getGroupId();
    }

    @Resource
    public RedisHelper redisHelper;

    @Resource
    private XxlDistributeProducer xxlDistributeProducer;

    /**
     * 队列分配
     * 1、获取所有分组
     * 2、判断分组对应的队列中是否有值
     * 3、如果有值，执行下面流程，如果没有值跳过
     * 4、分配流程：
     * 1、每个分组下取队列第一个排队的用户，并且获取用户是否是ka用户（用作分配使用）
     * 2、获取所有分组下空闲坐席的进行中咨询数
     * 3、取最小的咨询数
     */
    @Override
    public Integer distribute() {

        // 获取所有分组, 剔除兜底组
        List<GroupInfo> groupInfoList = groupInfoRepository.selectAllByEnable(CommonConstant.DEFAULT_TENANT_ID, null);
        if (CollectionUtils.isEmpty(groupInfoList)) {
            // 进入兜底组
            log.info("查询不到分组信息");
            return 0;
        }
        //查询队列中有排队成员的队列对应的群组信息
        List<BatchGetQueueInfoBo> batchGetQueueInfoBos = queueManager.batchGetQueueInfo(groupInfoList);
        if (CollectionUtils.isEmpty(batchGetQueueInfoBos)) {
            log.info("所有队列中都没有排队的成员");
            return 0;
        }

        List<Long> conversationIdList = batchGetQueueInfoBos.stream().map(BatchGetQueueInfoBo::getConversationId).collect(Collectors.toList());
        List<ImConversation> imConversationList = imConversationRepository.selectListByConversationIdList(conversationIdList);
        if (CollectionUtils.isEmpty(imConversationList)) {
            log.error("会话查询异常，没有查到对应的会话记录");
            return 0;
        }
        List<String> fromOuterUserIdList = imConversationList.stream().map(ImConversation::getFromOuterUserId).collect(Collectors.toList());
        List<UserRegisterInfo> byOuterUserIds = userRegisterInfoRepository.findByOuterUserIds(fromOuterUserIdList);

        //ka用户列表
        List<String> kaOuterUserIdList = this.returnUserKaByUserIds(byOuterUserIds);

        //待分配的分组id
        List<Integer> groupIdList = batchGetQueueInfoBos.stream().map(BatchGetQueueInfoBo::getGroupId).collect(Collectors.toList());

        List<SeatGroupMapping> seatGroupMappings = seatGroupMappingRepository.selectByGroupIdList(groupIdList);
        if (CollectionUtils.isEmpty(seatGroupMappings)) {
            log.info("没有查询到分组对应的坐席信息，分组id=" + JSONObject.toJSONString(groupIdList));
            return 0;
        }
        //分组下所有的坐席id
        List<Long> seatIdList = seatGroupMappings.stream().map(SeatGroupMapping::getSeatId).collect(Collectors.toList());
        //查询在线的坐席
        List<SeatInfo> seatInfos = seatInfoRepository.selectListBySeatIdListAndCurrentSeatStatusEn(seatIdList, Collections.singletonList(CurrentSeatStatusEnum.ONLINE.type));

        //在线的坐席id
        List<Long> onlineSeatIdList = seatInfos.stream().map(SeatInfo::getSeatId).collect(Collectors.toList());

        List<UserRegisterInfo> byUserIdAndUserClass = userRegisterInfoRepository.findByUserIdAndUserClass(onlineSeatIdList, OuterUserClassTypeEnum.SEAT.type);
        List<String> onlineSeatOuterUserIdList = byUserIdAndUserClass.stream().map(UserRegisterInfo::getOuterUserId).collect(Collectors.toList());
        //查询坐席的正在咨询的数量
        List<SelectOnlineNumDto> selectOnlineNumDtos = imConversationRepository.selectOnlineNum(onlineSeatOuterUserIdList);

        int i = 1;
        for (BatchGetQueueInfoBo batchGetQueueInfoBo : batchGetQueueInfoBos) {
            XxlDistributeMqReqBo xxlDistributeMqReqBo = new XxlDistributeMqReqBo();
            xxlDistributeMqReqBo.setGroupId(batchGetQueueInfoBo.getGroupId());
            xxlDistributeMqReqBo.setConversationId(batchGetQueueInfoBo.getConversationId());
            Optional<ImConversation> first = imConversationList.stream().filter(f -> f.getConversationId().equals(batchGetQueueInfoBo.getConversationId())).findFirst();
            if (!first.isPresent()) {
                continue;
            }
            xxlDistributeMqReqBo.setOuterUserId(first.get().getFromOuterUserId());
            xxlDistributeMqReqBo.setIsUserKa(kaOuterUserIdList.contains(first.get().getFromOuterUserId()));

            List<XxlDistributeMqReqBo.GroupSeatInfo> groupSeatInfoList = new ArrayList<>();
            //在线的坐席映射关系
            List<SeatGroupMapping> onlineGroupMappingList = seatGroupMappings.stream().filter(f -> f.getGroupId().equals(batchGetQueueInfoBo.getGroupId()) && onlineSeatIdList.contains(f.getSeatId())).collect(Collectors.toList());
            for (SeatGroupMapping seatGroupMapping : onlineGroupMappingList) {
                XxlDistributeMqReqBo.GroupSeatInfo groupSeatInfo = new XxlDistributeMqReqBo.GroupSeatInfo();
                groupSeatInfo.setSeatId(seatGroupMapping.getSeatId());
                Optional<UserRegisterInfo> first1 = byUserIdAndUserClass.stream().filter(f -> f.getUserId().equals(seatGroupMapping.getSeatId())).findFirst();
                if (!first1.isPresent()) {
                    continue;
                }
                groupSeatInfo.setSeatOuterUserId(first1.get().getOuterUserId());

                Optional<SeatInfo> optionalSeatInfo = seatInfos.stream().filter(f -> f.getSeatId().equals(seatGroupMapping.getSeatId())).findFirst();
                if (!optionalSeatInfo.isPresent()) {
                    continue;
                }
                groupSeatInfo.setMaxOnlineCount(optionalSeatInfo.get().getMaxWiringQuantity());
                Optional<SelectOnlineNumDto> onlineNumDtoOptional = selectOnlineNumDtos.stream().filter(f -> f.getToOuterUserId().equals(first1.get().getOuterUserId())).findFirst();
                if (!onlineNumDtoOptional.isPresent()) {
                    continue;
                }
                groupSeatInfo.setOnlineCount(onlineNumDtoOptional.get().getNum());
                groupSeatInfoList.add(groupSeatInfo);
            }
            xxlDistributeMqReqBo.setGroupSeatInfoList(groupSeatInfoList);
            this.distributeXxl(xxlDistributeMqReqBo);
//            xxlDistributeProducer.sendMessage(xxlDistributeMqReqBo, MqConfig.DELAY_DISTRIBUTE_TIME * i);
        }
        return 1;
    }



    /**
     * xxl 消息分配
     *
     * @param req
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer distributeXxl(XxlDistributeMqReqBo req) {

        List<XxlDistributeMqReqBo.GroupSeatInfo> groupSeatInfoList = req.getGroupSeatInfoList();

        if(CollectionUtils.isEmpty(groupSeatInfoList)){
            return 1;
        }

// 查询是否已经分配
        ImConversationDistribute exist = imConversationDistributeRepository.selectInfoByConversationIdAndDistributeStatus(req.getConversationId(), null);
        if(exist==null){
            log.error("会话没有进分组，无法分配");
            return 0;
        }

        if (exist.getDistributeStatus().equals(ImConversationDistributeStatusEnum.COMPLETED.type)) {
            log.info("会话已经已经分配，无需分配 req:{}", JSONObject.toJSONString(req));
            return 1;
        }

        //计算最大空闲程度
        for (XxlDistributeMqReqBo.GroupSeatInfo groupSeatInfo : groupSeatInfoList) {
            BigDecimal bigDecimal = new BigDecimal(groupSeatInfo.getMaxOnlineCount() - groupSeatInfo.getOnlineCount());
            BigDecimal bigDecimal1 = new BigDecimal(groupSeatInfo.getMaxOnlineCount());
            groupSeatInfo.setRate(bigDecimal.divide(bigDecimal1));
        }
        //所有坐席id
        List<Long> seatIdList = groupSeatInfoList.stream().map(XxlDistributeMqReqBo.GroupSeatInfo::getSeatId).collect(Collectors.toList());

        //ka坐席id
        List<Long> kaSeatIds = Collections.emptyList();
        List<SeatGroupDistributeRule> seatGroupDistributeRules = seatGroupDistributeRuleRepository.selectListByGroupId(req.getGroupId(), seatIdList);
        if(CollectionUtils.isEmpty(seatGroupDistributeRules)){
            kaSeatIds = seatIdList;
        }else{
            if(req.getIsUserKa()){
                //如果是ka用户需要查询分组 对应的坐席中能够处理ka用户的客服
                //获取坐席分配规则

                //过滤出能否接待ka用户的坐席
                kaSeatIds = seatGroupDistributeRules.stream().filter(f -> {
                    SeatGroupDistributeRuleConfigValueBo seatGroupDistributeRuleConfigValueBo = JSONObject.parseObject(f.getRuleConfigValue(), SeatGroupDistributeRuleConfigValueBo.class);
                    if (seatGroupDistributeRuleConfigValueBo != null) {
                        return seatGroupDistributeRuleConfigValueBo.getMultipleValue().contains(CommonConstant.RULE_KA);
                    }
                    return false;
                }).map(SeatGroupDistributeRule::getSeatId).collect(Collectors.toList());
            } else {
                kaSeatIds = seatGroupDistributeRules.stream().filter(f -> {
                    SeatGroupDistributeRuleConfigValueBo seatGroupDistributeRuleConfigValueBo = JSONObject.parseObject(f.getRuleConfigValue(), SeatGroupDistributeRuleConfigValueBo.class);
                    if (seatGroupDistributeRuleConfigValueBo != null) {
                        return seatGroupDistributeRuleConfigValueBo.getMultipleValue().contains(CommonConstant.RULE_NOT_KA);
                    }
                    return false;
                }).map(SeatGroupDistributeRule::getSeatId).collect(Collectors.toList());
            }
        }

        //过滤ka 并且取空闲程度最大的坐席
        List<Long> finalKaSeatIds = kaSeatIds;
        Optional<XxlDistributeMqReqBo.GroupSeatInfo> max = groupSeatInfoList.stream().filter(f -> finalKaSeatIds.contains(f.getSeatId())).max(Comparator.comparing(XxlDistributeMqReqBo.GroupSeatInfo::getRate)
                .thenComparing(XxlDistributeMqReqBo.GroupSeatInfo::getSeatId, Comparator.reverseOrder()));
        if(!max.isPresent()){
            log.error("不满足分配条件");
            return 0;
        }
        Long seatId = max.get().getSeatId();
        String seatOuterUserId = max.get().getSeatOuterUserId();


        Integer i = commonBaseManager.distributeUpdateConversation(req.getConversationId(), req.getGroupId(), seatOuterUserId, OuterUserClassTypeEnum.SEAT.type);
        if (i == 0) {
            //分配失败，进队
            return 0;
        }
        Long conversationDistributeId = exist.getConversationDistributeId();
        ImConversationDistribute distribute = new ImConversationDistribute();
        distribute.setConversationDistributeId(conversationDistributeId);
        distribute.setSeatId(seatId);
        distribute.setDistributeStatus(ImConversationDistributeStatusEnum.COMPLETED.type);
        distribute.setDistributeSeatTime(new Date());
        imConversationDistributeRepository.updateByPrimaryKeySelective(distribute);
        //移除队列
        String format = String.format(CommonRedisConstant.TENANT_GROUP_QUEUE_KEY, CommonConstant.DEFAULT_TENANT_ID, req.getGroupId());

        // 获取 redis
        Jedis jedis = redisHelper.getResource();
        jedis.zrem(format, req.getConversationId().toString());

        ToArtificialTypesBo toArtificialTypesBo = new ToArtificialTypesBo();
        toArtificialTypesBo.setImConversationId(req.getConversationId());
        toArtificialTypesBo.setTriggerMode(TriggerModeEnum.SEAT_ENTER.type);
        toArtificialTypesBo.setSeatOuterUserId(seatOuterUserId);
        toArtificialTypesBo.setPreToOuterUserType(OuterUserClassTypeEnum.SEAT.type);
        toArtificialWelcomeMsgProducer.sendMessage(toArtificialTypesBo);
        return 1;
    }

    /**
     * 查询是否是ka用户，如果是，就返回用户id
     */
    public List<String> returnUserKaByUserIds(List<UserRegisterInfo> byOuterUserIds) {
        SelectKaUserInfoByUserIdsReq selectKaUserInfoByUserIdsReq = new SelectKaUserInfoByUserIdsReq();
        selectKaUserInfoByUserIdsReq.setUserIds(byOuterUserIds.stream().map(UserRegisterInfo::getUserId).collect(Collectors.toList()));
        List<UserKaByUserIdsResp> userKaByUserIdsResps = userKaApi.selectKaInfoByUserIds(selectKaUserInfoByUserIdsReq);
        List<Long> kaUserIdList = userKaByUserIdsResps.stream().map(UserKaByUserIdsResp::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(kaUserIdList)) {
            return new ArrayList<>();
        }
        List<UserRegisterInfo> byUserIdAndUserClass = userRegisterInfoRepository.findByUserIdAndUserClass(kaUserIdList, UserClassEnum.USER.type);

        return byUserIdAndUserClass.stream().map(UserRegisterInfo::getOuterUserId).collect(Collectors.toList());
    }


    /**
     * 进入队列
     */
    private void enterQueue(DistributeGroupQueueInfoBo enGroupInfo, DistributeEnGroupReq req, ImConversationDistribute imConversationDistribute) {
        QueueConfig queueConfig = queueConfigRepository.selectConfigByFromId(enGroupInfo.getGroupId(), ModulesTypeEnum.GROUP.type);
        if (Objects.isNull(queueConfig)) {
            log.error("分组队列配置不存在 设置为全局配置 groupId:{}", enGroupInfo.getGroupId());
            queueConfig = new QueueConfig();
            queueConfig.setFromId(enGroupInfo.getGroupId().longValue());
            queueConfig.setFromType(ModulesTypeEnum.GROUP.type);
            queueConfig.setMaxLength(globalQueueConfigLength);
            queueConfig.setMemberIntervalMinute(globalQueueConfigMemberIntervalMinute);
            queueConfig.setMsgTtl(CommonConstant.ONE.longValue());
            queueConfig.setOperatorId(0L);
            queueConfigRepository.insertSelective(queueConfig);
        }

        // 进入队列
        QueueGroupEnQueueReq queueGroupEnQueueReq = new QueueGroupEnQueueReq();
        queueGroupEnQueueReq.setTenantId(req.getTenantId());
        queueGroupEnQueueReq.setConversationDistributeId(imConversationDistribute.getConversationDistributeId());
        queueGroupEnQueueReq.setGroupId(enGroupInfo.getGroupId());
        queueGroupEnQueueReq.setQueueConfig(queueConfig);
        queueGroupEnQueueReq.setOuterUserId(req.getOuterUserId());
        queueGroupEnQueueReq.setConversationId(req.getConversationId());
        queueManager.groupEnQueue(queueGroupEnQueueReq);
    }

    /**
     * 分配进组
     *
     * @param tenantId
     * @param enGroupIdList
     * @param groupInfoList
     * @return
     */
    private DistributeGroupQueueInfoBo distributeEnGroupByGroupIdList(Long tenantId, List<Integer> enGroupIdList, List<GroupInfo> groupInfoList) {
        DistributeGroupQueueInfoBo enGroupQueueInfoBo = new DistributeGroupQueueInfoBo();

        // 获取每个组的队列信息 todo 这个地方需要优化一下，改成查当前分组的是否需要排队 + 排队长度
        List<DistributeGroupQueueInfoBo> groupQueueInfoList = this.getGroupQueueInfoListByGroupIdList(tenantId, enGroupIdList);

        /**
         * 1，会话只满足一个会话组的进线逻辑时，直接进入该组分配或排队
         * 2，当会话满足多个会话组的进线逻辑时，当仅有一个会话组无需排队时，则会话直接进入该会话组；
         * 3，当会话满足多个会话组的进线逻辑时，所有会话组都需要排队，则会话进入吞吐量最大的会话组（当前分组吞吐能力值 = 当前分组所有坐席在线接线总数/当前分组队列排队人数（排队人数为0的情况默认为1）），如果吞吐量一致，则按分组排序分给最小的会话组
         * 4，当会话满足多个会话组的进线逻辑时，所有会话组都无需排队，则会话进入空闲程度最大的会话组；（空闲程度最大=该组内的所有坐席的当前可接待会话量/该组内所有坐席的最大接线量）
         */

        // 1，会话只满足一个会话组的进线逻辑时，直接进入该组分配或排队
        if (groupQueueInfoList.size() == 1) {
            return groupQueueInfoList.get(0);
        } else {

            // 查询分组所有坐席的最大接线量
            List<MaxOnlineCountByGroupIdListRespBo> maxOnlineCountList = seatInfoRepository.selectMaxOnlineCountByGroupIdList(enGroupIdList);
            if (CollectionUtils.isEmpty(maxOnlineCountList)) {
                log.error("符合规则的所有分组都没有坐席或最大接线量为0 进入兜底分组：{}，enGroupIdList:{}", minimumGuaranteeGroupId, JSONObject.toJSONString(enGroupIdList));
                return this.returnGroupQueueInfo(tenantId, minimumGuaranteeGroupId);
            }

            // 3，当会话满足多个会话组的进线逻辑时，所有会话组都需要排队，则会话进入吞吐量最大的会话组（当前分组吞吐能力值 = 当前分组所有坐席在线接线总数/当前分组队列排队人数（排队人数为0的情况默认为1）），如果吞吐量一致，则按分组排序分给最小的会话组
            List<GroupMaxFreeRateBo> maxFreeRateList = new ArrayList<>();
            String rateName = "";
            if (groupQueueInfoList.stream().allMatch(DistributeGroupQueueInfoBo::getNeedQueue)) {

                // 遍历计算（当前分组吞吐能力值 = 当前分组所有坐席在线接线总数/当前分组队列排队人数（排队人数为0的情况默认为1））
                rateName = "吞吐量";
                for (DistributeGroupQueueInfoBo idleGroup : groupQueueInfoList) {

                    // 获取当前分组最大接线量
                    Long maxOnlineCount = maxOnlineCountList.stream().filter(f -> f.getGroupId().equals(idleGroup.getGroupId()))
                            .map(MaxOnlineCountByGroupIdListRespBo::getMaxOnlineCount).findFirst().orElse(null);
                    if (Objects.isNull(maxOnlineCount)) {
                        log.error("分组最大接线量为0 groupId:{}", idleGroup.getGroupId());
                        continue;
                    }

                    // 当前分组队列排队人数（被除数不能为0，但是考虑排队人数为0和为1的优先级是不一样的，因此整体加一再计算）
                    long queueLength = idleGroup.getQueueLength() + 1;

                    // 计算当前分组 空闲程度最大
                    long nextRate = maxOnlineCount / queueLength;

                    // 分组的空闲程度和排序一起放入
                    GroupMaxFreeRateBo bo = new GroupMaxFreeRateBo();
                    bo.setGroupId(idleGroup.getGroupId());
                    groupInfoList.stream().filter(f -> f.getGroupId().equals(idleGroup.getGroupId())).findFirst().ifPresent(f -> bo.setSort(f.getSort()));
                    bo.setRate(nextRate);
                    bo.setNeedQueue(idleGroup.getNeedQueue());
                    maxFreeRateList.add(bo);
                }

            } else {
                // 存在不用排队的

                // 2，当会话满足多个会话组的进线逻辑时，当仅有一个会话组无需排队时，则会话直接进入该会话组；
                List<DistributeGroupQueueInfoBo> idleGroupQueueInfoList = groupQueueInfoList.stream().filter(f -> !f.getNeedQueue()).collect(Collectors.toList());
                if (idleGroupQueueInfoList.size() == 1) {
                    return groupQueueInfoList.get(0);
                }

                // 4，当会话满足多个会话组的进线逻辑时，多个会话组都无需排队，则会话进入空闲程度最大的会话组；（空闲程度最大=该组内的所有坐席的当前可接待会话量/该组内所有坐席的最大接线量）

                // 获取分组所有坐席的虚拟账号 todo 没有虚拟账号的坐席能否计算在内？
                List<SeatOutUserIdMappingRespBo> seatOutUserIdMappingRespBos = userRegisterInfoRepository.selectOuterUserIdListByGroupIdList(enGroupIdList);
                if (CollectionUtils.isEmpty(maxOnlineCountList)) {
                    log.error("符合规则的所有分组都没有坐席虚拟账号 进入兜底分组：{}，enGroupIdList:{}", minimumGuaranteeGroupId, JSONObject.toJSONString(enGroupIdList));
                    return this.returnGroupQueueInfo(tenantId, minimumGuaranteeGroupId);
                }

                // 查询分组所有坐席的当前会话量
                List<String> outUserIdList = seatOutUserIdMappingRespBos.stream().map(SeatOutUserIdMappingRespBo::getOuterUserId).collect(Collectors.toList());
                List<CommonLabelValueResp> countByToOuterUserIdList = imConversationRepository.selectCountByToOuterUserId(outUserIdList, ModulesTypeEnum.SEAT.type);

                // 计算每个坐席的当前可接待会话量:（空闲程度最大=该组内的所有坐席的当前可接待会话量/该组内所有坐席的最大接线量）
                rateName = "空闲程度";
                for (DistributeGroupQueueInfoBo idleGroup : idleGroupQueueInfoList) {

                    // 获取当前分组最大接线量
                    Long maxOnlineCount = maxOnlineCountList.stream().filter(f -> f.getGroupId().equals(idleGroup.getGroupId()))
                            .map(MaxOnlineCountByGroupIdListRespBo::getMaxOnlineCount).findFirst().orElse(null);
                    if (Objects.isNull(maxOnlineCount)) {
                        log.error("分组最大接线量为0 groupId:{}", idleGroup.getGroupId());
                        continue;
                    }

                    // 获取当前分组的当前会话量
                    Long nextOnlineCount = countByToOuterUserIdList.stream().filter(f -> f.getValue().equals(idleGroup.getGroupId().toString())).map(i -> Long.valueOf(i.getValue())).findFirst().orElse(null);
                    if (Objects.isNull(nextOnlineCount)) {
                        nextOnlineCount = 0L;
                    }

                    // 计算当前分组 空闲程度最大
                    long nextRate = (maxOnlineCount - nextOnlineCount) / maxOnlineCount;

                    // 分组的空闲程度和排序一起放入
                    GroupMaxFreeRateBo bo = new GroupMaxFreeRateBo();
                    bo.setGroupId(idleGroup.getGroupId());
                    groupInfoList.stream().filter(f -> f.getGroupId().equals(idleGroup.getGroupId())).findFirst().ifPresent(f -> bo.setSort(f.getSort()));
                    bo.setRate(nextRate);
                    bo.setNeedQueue(idleGroup.getNeedQueue());
                    maxFreeRateList.add(bo);
                }
            }

            // 获取空闲程度/吞吐能力最大的分组，如果最大的有多个，则获取序号最小的分组
            Optional<GroupMaxFreeRateBo> maxFreeRateBo = maxFreeRateList.stream()
                    .max(Comparator.comparing(GroupMaxFreeRateBo::getRate)
                            .thenComparing(GroupMaxFreeRateBo::getSort, Comparator.reverseOrder()));
            if (!maxFreeRateBo.isPresent()) {
                log.error("异常场景，没有符合{}最大的分组 进入兜底分组 enGroupIdList：{}，maxFreeRateList：{}", rateName, JSONObject.toJSONString(enGroupIdList), JSONObject.toJSONString(maxFreeRateList));
                return this.returnGroupQueueInfo(tenantId, minimumGuaranteeGroupId);
            }

            log.info("{}最大的分组 groupId:{} maxFreeRateList：{}", rateName, maxFreeRateBo.get().getGroupId(), JSONObject.toJSONString(maxFreeRateList));
            enGroupQueueInfoBo.setGroupId(maxFreeRateBo.get().getGroupId());
            enGroupQueueInfoBo.setNeedQueue(maxFreeRateBo.get().getNeedQueue());
            return enGroupQueueInfoBo;
        }
    }

    /**
     * 获取单个分组的队列信息
     *
     * @param tenantId
     * @param groupId
     * @return
     */
    private DistributeGroupQueueInfoBo returnGroupQueueInfo(Long tenantId, Integer groupId) {
        DistributeGroupQueueInfoBo distributeGroupQueueInfoBo = new DistributeGroupQueueInfoBo();
        distributeGroupQueueInfoBo.setGroupId(groupId);
        // 获取 redis
        Jedis jedis = redisHelper.getResource();

        try {
            // 获取队列长度
            String queueKey = String.format(CommonRedisConstant.TENANT_GROUP_QUEUE_KEY, tenantId, groupId);
            Long length = jedis.zcard(queueKey);
            if (length != null && length > 0) {
                distributeGroupQueueInfoBo.setNeedQueue(true);
            }

            return distributeGroupQueueInfoBo;
        } finally {
            // 确保关闭Redis资源
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 获取分组的队列信息
     *
     * @param tenantId
     * @param enGroupIdList
     * @return
     */
    private List<DistributeGroupQueueInfoBo> getGroupQueueInfoListByGroupIdList(Long tenantId, List<Integer> enGroupIdList) {
        List<DistributeGroupQueueInfoBo> groupQueueInfoList = new ArrayList<>();

        // 获取 redis
        Jedis jedis = redisHelper.getResource();

        try {
            // 遍历获取队列信息
            for (Integer groupId : enGroupIdList) {
                DistributeGroupQueueInfoBo groupQueueInfoBo = new DistributeGroupQueueInfoBo();
                groupQueueInfoBo.setGroupId(groupId);

                // 获取队列长度
                String queueKey = String.format(CommonRedisConstant.TENANT_GROUP_QUEUE_KEY, tenantId, groupId);
                Long length = jedis.zcard(queueKey);
                if (length != null && length > 0) {
                    groupQueueInfoBo.setNeedQueue(true);
                    groupQueueInfoBo.setQueueLength(length);
                }
                groupQueueInfoList.add(groupQueueInfoBo);
            }

            return groupQueueInfoList;
        } finally {
            // 确保关闭Redis资源
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 根据分组过滤条件，返回分组id
     *
     * @param req
     * @param groupInfoList
     * @return
     */
    public List<Integer> returnGroupIdListByGroupRuleFilter(DistributeEnGroupReq req, List<GroupInfo> groupInfoList) {
        // 获取分组分配规则指标
        List<RuleMetricConfig> ruleMetricConfigs = ruleMetricConfigRepository.selectRuleMetricByObject(RuleMetricObjectEnum.GROUP.type, RuleMetricFromTypeEnum.KF.type);
        if (CollectionUtils.isEmpty(ruleMetricConfigs)) {
            // 进入兜底组
            log.error("没有可用的分组（规则指标配置为空） 进入兜底分组：{}，req:{}", minimumGuaranteeGroupId, JSONObject.toJSONString(req));
            return Collections.singletonList(minimumGuaranteeGroupId);
        }

        // 获取分组规则
        List<GroupRule> groupRuleList = groupRuleRepository.selectByGroupIdList(groupInfoList.stream().map(GroupInfo::getGroupId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(ruleMetricConfigs)) {
            // 进入兜底组
            log.error("没有可用的分组（分组规则配置为空） 进入兜底分组：{}，req:{}", minimumGuaranteeGroupId, JSONObject.toJSONString(req));
            return Collections.singletonList(minimumGuaranteeGroupId);
        }

        // 格式化过滤器
        List<GroupRuleFilter> groupRuleFilterList = Arrays.asList(new ClientCategoryFilter(), new ChannelFilter(), new UserProblemClassFilter());

        // 过滤符合的分组
        List<Integer> conformGroupIdList = new ArrayList<>();
        for (GroupInfo groupInfo : groupInfoList) {

            // 获取当前分组规则
            List<GroupRule> nextGroupRuleList = groupRuleList.stream().filter(groupRule -> groupRule.getGroupId().equals(groupInfo.getGroupId())
            ).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(nextGroupRuleList)) {
                log.error("分组：{}没有分组规则 req:{}", groupInfo.getGroupName(), JSONObject.toJSONString(req));
                continue;
            }

            // 获取分组配置属性
            GroupConfigRuleAttributeReqBo groupAttributeReqBo = new GroupConfigRuleAttributeReqBo();
            groupAttributeReqBo.setGroupId(groupInfo.getGroupId());
            groupAttributeReqBo.setGroupRuleList(nextGroupRuleList);
            groupAttributeReqBo.setRuleMetricConfigs(ruleMetricConfigs);

            // 初始化过滤器
            GroupRuleFilterChain groupRuleFilterChain = new GroupRuleFilterChain(
                    groupRuleFilterList,
                    new ConversationDistributeEnGroupFilterBo(req.getClientCategoryId(), req.getChannelId(),
                            req.getUserProblemClassId()),
                    groupAttributeReqBo
            );

            CommonFilterResultBo commonFilterResultBo = groupRuleFilterChain.doFilter();
            if (commonFilterResultBo.isResult()) {
                conformGroupIdList.add(groupInfo.getGroupId());
            }
        }

        // 没有符合条件的分组
        if (CollectionUtils.isEmpty(conformGroupIdList)) {
            log.error("没有符合条件的分组 进入兜底分组：{}，req:{}", minimumGuaranteeGroupId, JSONObject.toJSONString(req));
            return Collections.singletonList(minimumGuaranteeGroupId);
        }

        return conformGroupIdList;
    }

    /**
     * 分组坐席分配
     */
    @Override
    public DistributeGroupSeatBo distributeGroupSeat(DistributeGroupSeatReq req) {
        // 查询会话是否已经分配
        ImConversationDistribute exist = imConversationDistributeRepository.selectInfoByConversationIdAndDistributeStatus(req.getConversationId(), ImConversationDistributeStatusEnum.COMPLETED.type);
        if (Objects.nonNull(exist)) {
            log.info("会话已经分配坐席 req:{}", JSONObject.toJSONString(req));
            return null;
        }

        /**
         * 预演：
         * 获取当前分组是否有空闲坐席，没有的话，进入队列（已在队列中则不用动）
         * 获取符合的分配的坐席
         * 按饱和度均衡分配分配给坐席
         * （在队列中的，移出队列）
         * 触发排队更新事件 todo
         */

        // 获取分组是否有空闲坐席
        List<SeatInfo> seatInfoList = this.getFreeSeatByGroupId(req.getGroupId());
        if(CollectionUtils.isEmpty(seatInfoList)){
            log.error("直接分配没有空闲坐席");
            return null;
        }

        // 获取坐席配置虚拟账号
        List<UserRegisterInfo> registerInfoList = userRegisterInfoRepository.selectOuterUserIdListBySeatIdList(seatInfoList.stream().map(SeatInfo::getSeatId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(registerInfoList)) {
            log.error("关联坐席没有配置虚拟账号 registerInfoList为null groupId:{}", req.getGroupId());
            return null;
        }

        // 获取坐席的当前在线接线数
        List<CommonLabelValueResp> seatOnlineCount = imConversationRepository.selectCountByToOuterUserId(registerInfoList.stream().map(UserRegisterInfo::getOuterUserId).collect(Collectors.toList()), ModulesTypeEnum.SEAT.type);

        //查询用户ka信息
        UserRegisterInfo byOuterUserId = userRegisterInfoRepository.findByOuterUserId(req.getOuterUserId());
        SelectKaUserInfoByUserIdsReq selectKaUserInfoByUserIdsReq = new SelectKaUserInfoByUserIdsReq();
        selectKaUserInfoByUserIdsReq.setUserIds(Collections.singletonList(byOuterUserId.getUserId()));
        List<UserKaByUserIdsResp> userKaByUserIdsResps = userKaApi.selectKaInfoByUserIds(selectKaUserInfoByUserIdsReq);
        //用户是否是ka
        Boolean isUserKa = !CollectionUtils.isEmpty(userKaByUserIdsResps);

        //坐席id
        List<Long> seatIdList = seatInfoList.stream().map(SeatInfo::getSeatId).collect(Collectors.toList());

        //获取坐席分配规则
        List<SeatGroupDistributeRule> seatGroupDistributeRules = seatGroupDistributeRuleRepository.selectListByGroupId(req.getGroupId(), seatIdList);
        //过滤出能否接待ka用户的坐席
        List<Long> kaSeatIds = seatGroupDistributeRules.stream().filter(f -> {
            SeatGroupDistributeRuleConfigValueBo seatGroupDistributeRuleConfigValueBo = JSONObject.parseObject(f.getRuleConfigValue(), SeatGroupDistributeRuleConfigValueBo.class);
            if (isUserKa && seatGroupDistributeRuleConfigValueBo != null) {
                return seatGroupDistributeRuleConfigValueBo.getMultipleValue().contains(CommonConstant.RULE_KA);
            }
            return false;
        }).map(SeatGroupDistributeRule::getSeatId).collect(Collectors.toList());


        // 计算客服 空闲程度最大=当前可接待会话量/最大接线量
        List<SeatMaxFreeRateBo> maxFreeRateList = new ArrayList<>();
        for (SeatInfo seatInfo : seatInfoList) {
            if(isUserKa && !kaSeatIds.contains(seatInfo.getSeatId())){
                continue;
            }

            UserRegisterInfo registerInfo = registerInfoList.stream().filter(f -> f.getUserId().equals(seatInfo.getSeatId())).findFirst().orElse(null);
            if (Objects.isNull(registerInfo)) {
                log.error("关联坐席没有配置虚拟账号 registerInfoList为null seatId:{}", seatInfo.getSeatId());
                continue;
            }

            // 获取当前坐席最大接线量
            Long maxOnlineCount = seatInfo.getMaxWiringQuantity().longValue();

            // 获取当前坐席的当前会话量
            Long nextOnlineCount = seatOnlineCount.stream().filter(f -> f.getValue().equals(registerInfo.getOuterUserId())).map(i -> Long.valueOf(i.getValue())).findFirst().orElse(null);
            if (Objects.isNull(nextOnlineCount)) {
                nextOnlineCount = 0L;
            }

            // 计算当前坐席 空闲程度最大=当前可接待会话量/最大接线量
            BigDecimal bigDecimal = new BigDecimal((maxOnlineCount - nextOnlineCount));
            BigDecimal bigDecimal1 = new BigDecimal(maxOnlineCount);

            BigDecimal divide = bigDecimal.divide(bigDecimal1, 2, RoundingMode.HALF_UP);

            if (divide.compareTo(BigDecimal.ZERO)<=0) {
                log.error("分配坐席{}已满线 nextRate:{}", seatInfo.getSeatId(), divide);
                continue;
            }

            SeatMaxFreeRateBo seatMaxFreeRateBo = new SeatMaxFreeRateBo();
            seatMaxFreeRateBo.setSeatId(seatInfo.getSeatId());
            seatMaxFreeRateBo.setRate(divide);
            seatMaxFreeRateBo.setSort(seatInfo.getSeatId().intValue());
            maxFreeRateList.add(seatMaxFreeRateBo);
        }

        // 获取空闲程度最大的坐席，如果最大的有多个，则获取序号最小的坐席
        Optional<SeatMaxFreeRateBo> maxFreeRateBo = maxFreeRateList.stream()
                .max(Comparator.comparing(SeatMaxFreeRateBo::getRate)
                        .thenComparing(SeatMaxFreeRateBo::getSort, Comparator.reverseOrder()));
        if (!maxFreeRateBo.isPresent()) {
            log.error("异常场景，没有符合的坐席 回到队列 groupId：{}，maxFreeRateList：{}", req.getGroupId(), JSONObject.toJSONString(maxFreeRateList));
            return null;
        }

        log.info("空闲最大最大的坐席 seatId:{} maxFreeRateList：{}", maxFreeRateBo.get().getSeatId(), JSONObject.toJSONString(maxFreeRateList));

        Optional<UserRegisterInfo> first = registerInfoList.stream().filter(f -> f.getUserId().equals(maxFreeRateBo.get().getSeatId()) && f.getUserClass().equals(OuterUserClassTypeEnum.SEAT.type)).findFirst();
        if (!first.isPresent()) {
            log.error("坐席找不到注册信息");
            return null;
        }
        DistributeGroupSeatBo distributeGroupSeatBo = new DistributeGroupSeatBo();
        distributeGroupSeatBo.setSeatId(first.get().getUserId());
        distributeGroupSeatBo.setToOuterUserId(first.get().getOuterUserId());

        return distributeGroupSeatBo;
    }

    /**
     * 获取空闲坐席
     *
     * @param groupId
     * @return
     */
    private List<SeatInfo> getFreeSeatByGroupId(Integer groupId) {

        // 获取分组坐席
        List<SeatGroupMapping> seatMappingList = seatGroupMappingRepository.selectListByGroupId(groupId);
        if (CollectionUtils.isEmpty(seatMappingList)) {
            log.error("分组没有坐席 seatMappingList为null groupId:{}", groupId);
            return new ArrayList<>();
        }

        // 获取支持接线的坐席状态
        List<SeatStatus> canOnlineStatus = seatStatusRepository.selectStatusByFunctionEn(CommonConstant.SEAT_STATUS_FUNCTION_AUTO_RECEIVE_QUEUE_CONVERSATION);
        if (CollectionUtils.isEmpty(canOnlineStatus)) {
            log.error("没有可用的坐席状态 canOnlineStatus为null groupId:{}", groupId);
            return new ArrayList<>();
        }

        // 获取空闲坐席 todo 这里是取的状态，考虑是否要通过实时查询数据去控制
        List<SeatInfo> seatInfoList = seatInfoRepository.selectListBySeatIdListAndStatusList(
                seatMappingList.stream().map(SeatGroupMapping::getSeatId).collect(Collectors.toList())
                , canOnlineStatus.stream().map(SeatStatus::getSeatStatusEn).collect(Collectors.toList())
        );
        List<SeatInfo> freeSeatList = seatInfoList.stream().filter(f -> f.getMaxWiringQuantity() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(freeSeatList)) {
            log.error("没有空闲的坐席 freeSeatList为null groupId:{}", groupId);
            return new ArrayList<>();
        }

        // todo 这里后面需要加个坐席优先规则过滤器

        return freeSeatList;
    }
}
