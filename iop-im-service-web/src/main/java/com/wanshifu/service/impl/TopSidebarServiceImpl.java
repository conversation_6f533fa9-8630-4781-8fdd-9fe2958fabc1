package com.wanshifu.service.impl;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.topSidebar.*;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.api.resp.topSidebar.*;
import com.wanshifu.iop.im.domain.bo.seat.GetSeatRegisterInfoRespBo;
import com.wanshifu.iop.im.domain.bo.topSidebar.GroupSeatMergeInfoRespBo;
import com.wanshifu.iop.im.domain.enums.SeatTypeEnum;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.enums.UserClassEnum;
import com.wanshifu.iop.im.domain.po.*;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.TopSidebarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 顶边栏服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:44
 */
@Service
@Slf4j
public class TopSidebarServiceImpl extends AbstractService implements TopSidebarService {

    /**
     * 在线坐席接线情况列表
     */
    @Override
    public List<SeatConnectionInfoListResp> seatConnectionInfoList(SeatConnectionInfoListReq req) {

        // 获取当前坐席信息
        GetSeatRegisterInfoRespBo seatRegisterInfo = super.getSeatRegisterInfo(req);

        // 获取当前坐席的分组详情
        GroupSeatMergeInfoRespBo groupSeatMergeInfoRespBo = this.getGroupSeatMergeInfoList(seatRegisterInfo.getSeatInfo(), req.getQueryType());
        if ( CollectionUtils.isEmpty(groupSeatMergeInfoRespBo.getGroupInfoList()) ){
            log.info("当前坐席没有分组信息，无法获取接线情况");
            return new ArrayList<>();
        }

        // 查询当前坐席的接线会话情况
        List<CommonLabelValueResp> countByToOuterUserId = imConversationRepository.selectCountByToOuterUserId(groupSeatMergeInfoRespBo.getSeatRegisterInfoList().stream().map(UserRegisterInfo::getOuterUserId).collect(Collectors.toList()), UserClassEnum.SEAT.type);

        // 查询坐席最近切换状态
        List<SeatStatusSwitchLog> seatStatusSwitchLogs = seatStatusSwitchLogRepository.selectLastSwitchStatusBySeatIdList(groupSeatMergeInfoRespBo.getSeatInfoList().stream().map(SeatInfo::getSeatId).collect(Collectors.toList()));

        // 获取坐席状态
        List<SeatStatus> seatStatusList = seatStatusRepository.selectAll();

        return groupSeatMergeInfoRespBo.getGroupInfoList().stream().map(groupInfo -> {
            SeatConnectionInfoListResp listResp = new SeatConnectionInfoListResp();
            listResp.setGroupName(groupInfo.getGroupName());
            listResp.setFreeSeatCount(0L);
            listResp.setTotalSeatCount(CommonConstant.ZERO);

            // 获取分组下的坐席
            List<Long> groupSeatIdList = groupSeatMergeInfoRespBo.getSeatGroupMappingList().stream()
                    .filter(sgm -> sgm.getGroupId().equals(groupInfo.getGroupId())).map(SeatGroupMapping::getSeatId).collect(Collectors.toList());
            List<SeatInfo> seatInfoList = groupSeatMergeInfoRespBo.getSeatInfoList().stream()
                    .filter(f -> groupSeatIdList.contains(f.getSeatId())).collect(Collectors.toList());
            if ( CollectionUtils.isEmpty(seatInfoList) ){
                return listResp;
            }

            List<SeatConnectionInfoItemResp> itemRespList = seatInfoList.stream().map(seatInfo -> {
                SeatConnectionInfoItemResp itemResp = new SeatConnectionInfoItemResp();
                itemResp.setSeatId(seatInfo.getSeatId());
                itemResp.setSeatName(seatInfo.getSeatName());
                itemResp.setSeatStatus(seatInfo.getCurrentSeatStatusEn());
                itemResp.setSeatStatusName(seatInfo.getCurrentSeatStatusEn());
                itemResp.setMaxWiringQuantity(seatInfo.getMaxWiringQuantity());
                seatStatusList.stream().filter(ss -> ss.getSeatStatusEn().equals(seatInfo.getCurrentSeatStatusEn())).findFirst().ifPresent(ss -> {
                    itemResp.setSeatStatusName(ss.getSeatStatusCn());
                });

                // 获取当前坐席的接线量
                UserRegisterInfo userRegisterInfo = groupSeatMergeInfoRespBo.getSeatRegisterInfoList().stream().filter(sri -> sri.getUserId().equals(seatInfo.getSeatId())).findFirst().orElse(null);
                if (Objects.nonNull(userRegisterInfo)) {
                    countByToOuterUserId.stream().filter(c -> c.getLabel().equals(userRegisterInfo.getOuterUserId())).findFirst().ifPresent(c -> {
                        itemResp.setCurrentWiringQuantity(Integer.parseInt(c.getValue()));
                        if (itemResp.getMaxWiringQuantity() > 0) {
                            itemResp.setSortRate(new BigDecimal(itemResp.getCurrentWiringQuantity())
                                    .divide(new BigDecimal(itemResp.getMaxWiringQuantity()), 2, RoundingMode.HALF_UP));
                        }
                    });
                }

                // 获取当前坐席的当前状态的持续时间
                seatStatusSwitchLogs.stream().filter(s -> s.getSeatId().equals(seatInfo.getSeatId())).findFirst().ifPresent(seatStatusSwitchLog -> {
                    Date switchTime = null;
                    if (Arrays.asList(CommonConstant.SEAT_STATUS_ONLINE, CommonConstant.SEAT_STATUS_FULL_LOAD).contains(seatStatusSwitchLog.getSeatStatusEnAfter())) {
                        switchTime = Objects.nonNull(seatStatusSwitchLog.getFirstOnlineTime()) ? seatStatusSwitchLog.getFirstOnlineTime() : seatStatusSwitchLog.getCreateTime();
                    } else {
                        switchTime = seatStatusSwitchLog.getCreateTime();
                    }
                    itemResp.setSeatStatusDuration(((System.currentTimeMillis() - switchTime.getTime()) / 1000));
                });

                return itemResp;
            }).sorted(Comparator.comparing(SeatConnectionInfoItemResp::getSortRate, Comparator.nullsLast(BigDecimal::compareTo))
                    .thenComparing(SeatConnectionInfoItemResp::getMaxWiringQuantity, Comparator.reverseOrder())).collect(Collectors.toList());
            // 坐席排序
            // 1, 按sortRate越小越靠前
            // 2, 如果sortRate相同，则按MaxWiringQuantity越大越靠前
            // 上面不加Comparator.nullsLast(BigDecimal::compareTo)) 这个会识别出字符串排序

            // 获取分组下的空闲坐席数
            listResp.setFreeSeatCount(itemRespList.stream().filter(f ->
                    f.getMaxWiringQuantity() > 0 && f.getCurrentWiringQuantity() < f.getMaxWiringQuantity()
            ).count());
            // 获取分组下的坐席总数
            listResp.setTotalSeatCount(seatInfoList.size());
            listResp.setSeatConnectionInfoItem(itemRespList);
            return listResp;
        }).collect(Collectors.toList());
    }

    /**
     * 获取分组坐席合并信息
     *
     * @param seatInfo
     * @param queryType
     * @return
     */
    private GroupSeatMergeInfoRespBo getGroupSeatMergeInfoList(SeatInfo seatInfo, Integer queryType) {
        GroupSeatMergeInfoRespBo groupSeatMergeInfoRespBo = new GroupSeatMergeInfoRespBo();

        /**
         * 获取当前坐席所在分组
         * 当前坐席如果是管理员，则获取所有分组
         * 否则，获取当前坐席所在的分组
         */
        List<GroupInfo> groupInfoList = new ArrayList<>();

        if (SeatTypeEnum.GROUP_LEADER.type.equals(seatInfo.getSeatType())){
            groupInfoList.addAll(groupInfoRepository.getAll(StatusEnum.ENABLE.type));
        }else{
            groupInfoList.addAll(groupInfoRepository.selectEnableBySeatId(seatInfo.getSeatId()));
        }

        if ( CollectionUtils.isEmpty(groupInfoList) ){
            return groupSeatMergeInfoRespBo;
        }

        // 获取分组下的坐席关联
        List<SeatGroupMapping> seatGroupMappingList = seatGroupMappingRepository.selectByGroupIdList(groupInfoList.stream().map(GroupInfo::getGroupId).collect(Collectors.toList()));

        // 获取分组下的坐席
        List<SeatInfo> seatInfoList = seatInfoRepository.selectListBySeatIdListAndCurrentSeatStatusEn(
                seatGroupMappingList.stream().map(SeatGroupMapping::getSeatId).collect(Collectors.toList()),
                CommonConstant.ONE.equals(queryType) ? Arrays.asList(CommonConstant.SEAT_STATUS_ONLINE, CommonConstant.SEAT_STATUS_FULL_LOAD) : null
        );

        // 获取坐席的注册信息
        List<UserRegisterInfo> seatRegisterInfoList = userRegisterInfoRepository.batchSelectByUserIdListAndUserClass(seatGroupMappingList.stream().map(SeatGroupMapping::getSeatId).collect(Collectors.toList()), UserClassEnum.SEAT.type);

        groupSeatMergeInfoRespBo.setGroupInfoList(groupInfoList);
        groupSeatMergeInfoRespBo.setSeatInfoList(seatInfoList);
        groupSeatMergeInfoRespBo.setSeatGroupMappingList(seatGroupMappingList);
        groupSeatMergeInfoRespBo.setSeatRegisterInfoList(seatRegisterInfoList);
        return groupSeatMergeInfoRespBo;
    }

}
