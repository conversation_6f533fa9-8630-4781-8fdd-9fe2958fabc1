package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.wanshifu.constant.ChannelConstant;
import com.wanshifu.factory.timeout.TimeoutFactory;
import com.wanshifu.factory.timeout.TimeoutHandleI;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.bo.channel.reply.TimeoutStrategyReqBo;
import com.wanshifu.iop.im.domain.enums.OuterUserClassTypeEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserTypeEnum;
import com.wanshifu.iop.im.domain.enums.UserClassEnum;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyMsgSubTypeEnum;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyDetailConfig;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.XxlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/23 11:10
 * @description TODO
 */
@Service
@Slf4j
public class XxlServiceImpl extends AbstractService implements XxlService {
    /**
     * 渠道配置、会话配置
     *
     * @param conversationId
     */
    @Override
    public Integer channelAndConversationTimeout(Long conversationId) {
        log.info("定时任务，超时脚本执行，参数：id={}", conversationId);
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if (imConversation == null) {
            log.error("该会话不存在，id={}", conversationId);
            return 0;
        }

        ChannelConfig channelConfigByChannelEn = channelConfigRepository.getChannelConfigByChannelEn(ChannelConstant.CONVERSATION_GLOBAL_CHANNEL_EN);
        List<Long> channelIdList = new ArrayList<>();
        if(channelConfigByChannelEn!=null){
            //全局渠道id
            channelIdList.add(channelConfigByChannelEn.getChannelId());
        }

        if (imConversation.getChannelConfigId()>0L) {
            channelIdList.add(imConversation.getChannelConfigId());
        }
        List<ConversationAutoReplyConfig> configAutoReplyListByChannelIds = conversationAutoReplyConfigRepository.getConfigListByChannelIds(channelIdList);
        if(CollectionUtils.isEmpty(configAutoReplyListByChannelIds)){
            return 1;
        }
        List<Long> allAutoReplyConfigIdList = configAutoReplyListByChannelIds.stream().map(ConversationAutoReplyConfig::getConversationAutoReplyConfigId).collect(Collectors.toList());

        List<ConversationAutoReplyDetailConfig> listByAutoReplyConfigIdList = conversationAutoReplyDetailConfigRepository.getListByAutoReplyConfigIdList(allAutoReplyConfigIdList);

        if(CollectionUtils.isEmpty(listByAutoReplyConfigIdList)){
            log.error("不存在全局配置和渠道配置的超时配置，channelIdList={}", JSON.toJSONString(channelIdList));
            return 1;
        }
        List<ConversationAutoReplyDetailConfig> timeoutSecondDetailConfigList = listByAutoReplyConfigIdList.stream().filter(f -> StringUtils.isNotEmpty(f.getTimeoutCostSecond())).collect(Collectors.toList());

        //全局配置和渠道配置， 如果有渠道配置拿渠道配置，如果没有渠道配置拿全局配置
        //会话配置的全局配置id集合
        List<Long> conversationAutoReplyConfigIdList = configAutoReplyListByChannelIds.stream().filter(f -> f.getChannelId().equals(imConversation.getChannelConfigId()))
                .map(ConversationAutoReplyConfig::getConversationAutoReplyConfigId).collect(Collectors.toList());
        //全局配置的自动回复配置id集合
        List<Long> globalAutoReplyConfigIdList = new ArrayList<>();
        if(channelConfigByChannelEn!=null){
            globalAutoReplyConfigIdList = configAutoReplyListByChannelIds.stream().filter(f -> f.getChannelId().equals(imConversation.getChannelConfigId()))
                    .map(ConversationAutoReplyConfig::getConversationAutoReplyConfigId).collect(Collectors.toList());
        }
        List<ConversationAutoReplyDetailConfig> realAutoReplyDetailConfigList = super.getRealAutoReplyDetailConfigList(conversationAutoReplyConfigIdList, globalAutoReplyConfigIdList, listByAutoReplyConfigIdList);

        log.info(JSON.toJSONString(timeoutSecondDetailConfigList));

        //查询访客和坐席发送的最后一条消息
        List<ImConversationItem> imConversationItems = imConversationItemRepository.selectLastListByConversationIdList(conversationId);

        TimeoutStrategyReqBo timeoutStrategyReqBo = new TimeoutStrategyReqBo();
        timeoutStrategyReqBo.setConversation(imConversation);
        //坐席发送的消息
        Optional<ImConversationItem> seatImConversationOptional = imConversationItems.stream().filter(f -> StringUtils.isNotEmpty(f.getResponseOuterUserId())).findFirst();
        if(seatImConversationOptional.isPresent()){
            timeoutStrategyReqBo.setSeatConversationItem(seatImConversationOptional.get());
        }

        //访客发送的消息
        Optional<ImConversationItem> fromImConversationOptional = imConversationItems.stream().filter(f ->
                Arrays.asList(UserClassEnum.CLIENT.type,UserClassEnum.ENTERPRISE.type,UserClassEnum.MASTER.type,UserClassEnum.USER.type).contains(f.getFromOuterUserType())
                ).findFirst();
        if(fromImConversationOptional.isPresent()){
            timeoutStrategyReqBo.setFromConversationItem(fromImConversationOptional.get());
        }

        //机器人或系统发送的最后一条消息
        Optional<ImConversationItem> robotImConversationOptional = imConversationItems.stream().filter(f -> StringUtils.isEmpty(f.getResponseOuterUserId()) && Arrays.asList(OuterUserTypeEnum.ROBOT.type, OuterUserTypeEnum.SYSTEM.type).contains(f.getFromOuterUserType()) ).findFirst();
        if(robotImConversationOptional.isPresent()){
            timeoutStrategyReqBo.setFromConversationItem(robotImConversationOptional.get());
        }

        for (ConversationAutoReplyDetailConfig conversationAutoReplyDetailConfig : realAutoReplyDetailConfigList) {
            timeoutStrategyReqBo.setConversationAutoReplyDetailConfig(conversationAutoReplyDetailConfig);
            timeoutStrategyProducer.sendMessage(timeoutStrategyReqBo);
        }
        return 0;
    }


}
