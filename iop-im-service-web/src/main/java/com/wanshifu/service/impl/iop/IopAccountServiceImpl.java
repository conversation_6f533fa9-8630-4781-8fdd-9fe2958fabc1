package com.wanshifu.service.impl.iop;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.account.api.AccountApi;
import com.wanshifu.iop.account.domain.po.AccountUserMapping;
import com.wanshifu.iop.account.domain.req.account.AccountAllListReq;
import com.wanshifu.iop.account.domain.req.account.AccountDetailReq;
import com.wanshifu.iop.account.domain.req.account.AccountInfoListReq;
import com.wanshifu.iop.account.domain.req.account.GetAccountByNameReq;
import com.wanshifu.iop.account.domain.resp.account.AccountAllResp;
import com.wanshifu.iop.account.domain.resp.account.AccountDetailResp;
import com.wanshifu.iop.account.domain.resp.account.AccountInfoListResp;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.iop.IopAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 账号中台服务
 * <AUTHOR>
 * @date： 2025-05-29 15:27:02
 */
@Service
@Slf4j
public class IopAccountServiceImpl extends AbstractService implements IopAccountService {

    /**
     * 账号中台系统类型
     */
    private final static Integer PRODUCT_TYPE = 4;

    @Resource
    private AccountApi accountApi;

    /**
     * 通过username获取 账号信息
     */
    @Override
    public AccountDetailResp getUserInfoByUsername(String username) {
        try{
            GetAccountByNameReq getAccountByNameReq = new GetAccountByNameReq();
            getAccountByNameReq.setProductType(PRODUCT_TYPE);
            getAccountByNameReq.setLike(CommonConstant.ZERO);
            getAccountByNameReq.setUsername(username);
            List<AccountDetailResp> userInfoByChineseName = accountApi.getUserInfoByChineseName(getAccountByNameReq);
            if (CollectionUtils.isEmpty(userInfoByChineseName)){
                return null;
            }
            return userInfoByChineseName.get(0);
        }catch (Exception e){
            log.error("通过username获取 账号信息失败",e);
            return null;
        }
    }

    /**
     * 批量获取 账号信息
     */
    @Override
    public List<AccountInfoListResp> batchGetInfoListByAccountIds(List<Long> accountIdList) {
        if ( CollectionUtils.isEmpty(accountIdList) ){
            return new ArrayList<>();
        }

        try{
            AccountInfoListReq accountInfoListReq = new AccountInfoListReq();
            accountInfoListReq.setProductType(PRODUCT_TYPE);
            accountInfoListReq.setAccountIds(accountIdList);
            return accountApi.getInfoListByAccountIds(accountInfoListReq);
        }catch (Exception e){
            log.error("批量通过账号id获取账号信息失败",e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有账号信息
     */
    @Override
    public List<AccountAllResp> getAllAccount(Integer accountStatus) {
        try{
            AccountAllListReq accountInfoListReq = new AccountAllListReq();
            accountInfoListReq.setProductType(PRODUCT_TYPE);
            accountInfoListReq.setAccountStatus(accountStatus);
            return accountApi.getAllAccount(accountInfoListReq);
        }catch (Exception e){
            log.error("获取所有账号信息失败",e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询账号状态
     * @param accountId
     * @return
     */
    @Override
    public AccountUserMapping selectByAccountIdAndProductType(Long accountId){
        try{
            AccountDetailReq accountDetailReq = new AccountDetailReq();
            accountDetailReq.setProductType(PRODUCT_TYPE);
            accountDetailReq.setAccountId(accountId);
            return accountApi.selectByAccountIdAndProductType(accountDetailReq);
        }catch (Exception e){
            log.error("获取账号信息失败 accountId:{}", accountId,e);
            return null;
        }
    }
}
