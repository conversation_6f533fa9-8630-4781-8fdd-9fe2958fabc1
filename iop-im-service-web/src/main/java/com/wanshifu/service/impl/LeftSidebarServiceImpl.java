package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.constant.ChannelConstant;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.CommonRedisConstant;
import com.wanshifu.constant.TencentConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.inter.GetVirtualUserInfoReq;
import com.wanshifu.iop.im.api.req.leftSidebar.*;
import com.wanshifu.iop.im.api.req.seat.ConversationFlowSeatStatusUpdateReq;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.api.resp.LoginSeatAuthInfoResp;
import com.wanshifu.iop.im.api.resp.SetLoginAuthResp;
import com.wanshifu.iop.im.api.resp.leftSidebar.*;
import com.wanshifu.iop.im.domain.bo.*;
import com.wanshifu.iop.im.domain.bo.groupManage.GroupRuleConfigValueBo;
import com.wanshifu.iop.im.domain.bo.leftSidebar.FormatSearchConversationListBo;
import com.wanshifu.iop.im.domain.bo.leftSidebar.GetVisitorInnerInfoReqBo;
import com.wanshifu.iop.im.domain.bo.leftSidebar.VisitorInnerInfoBo;
import com.wanshifu.iop.im.domain.bo.seat.GetSeatRegisterInfoRespBo;
import com.wanshifu.iop.im.domain.bo.seat.SeatStatusFunctionMappingRespBo;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.AddMemberToGroupBo;
import com.wanshifu.iop.im.domain.enums.*;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyMsgSubTypeEnum;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyTypeEnum;
import com.wanshifu.iop.im.domain.enums.leftSidebar.ConversationTimeoutStatusEnum;
import com.wanshifu.iop.im.domain.enums.leftSidebar.VisitorLabelEnum;
import com.wanshifu.iop.im.domain.po.*;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyConfig;
import com.wanshifu.manager.socketBase.SocketBaseManager;
import com.wanshifu.manager.socketBase.impl.SocketBaseManagerImpl;
import com.wanshifu.master.information.domain.api.response.common.BatchGetMasterBaseInfoResp;
import com.wanshifu.merchant.domain.resp.EnterpriseBaseInfoResp;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.mq.producer.ImportHistoryConversationProducer;
import com.wanshifu.mq.producer.ImportMsgGroupProducer;
import com.wanshifu.mq.producer.ImportMsgSingleProducer;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.LeftSidebarService;
import com.wanshifu.service.SeatService;
import com.wanshifu.user.domain.po.UserInfo;
import com.wanshifu.utils.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


/**
 * 左侧边栏服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:44
 */
@Service
@Slf4j
public class LeftSidebarServiceImpl extends AbstractService implements LeftSidebarService {

    @Resource
    private ImInterServiceImpl imInterService;

    @Resource
    private SeatService seatService;

    @Resource
    private ImportMsgGroupProducer importMsgGroupProducer;

    @Resource
    private ImportMsgSingleProducer importMsgSingleProducer;

    /**
     * 公共枚举接口
     * @return
     */
    @Override
    public LeftSidebarEnumsResp enums() {
        LeftSidebarEnumsResp resp = new LeftSidebarEnumsResp();

        // 进线属性配置类型枚举
        List<CommonLabelValueResp> visitorLabelEnum = Arrays.stream(VisitorLabelEnum.values()).map(item -> {
            CommonLabelValueResp labelValueResp = new CommonLabelValueResp();
            labelValueResp.setLabel(item.name);
            labelValueResp.setValue(item.type);
            return labelValueResp;
        }).collect(Collectors.toList());

        resp.setVisitorLabelEnum(visitorLabelEnum);
        return resp;
    }

    /**
     * 获取在线坐席信息
     * @param req
     * @return
     */
    @Override
    public OnlineSeatInfoResp onlineSeatInfo(OnlineSeatInfoReq req) {
        OnlineSeatInfoResp resp = new OnlineSeatInfoResp();
        req.checkParams();

        // 获取坐席信息
        String cacheKey = String.format(CommonRedisConstant.IOP_IM_TOKEN_KEY, req.getImToken());
        String seatInfoJson = redisHelper.get(cacheKey);
        if ( StringUtils.isEmpty(seatInfoJson) ){
            throw new BusinessException("获取坐席信息失败, 请重新登录");
        }

        SetLoginAuthResp setLoginAuthResp = JSONObject.parseObject(seatInfoJson, SetLoginAuthResp.class);
        if ( Objects.isNull(setLoginAuthResp.getSeatInfo()) ){
            throw new BusinessException("获取坐席信息失败, 请检查客服是否配置坐席");
        }

        LoginSeatAuthInfoResp seatInfo = setLoginAuthResp.getSeatInfo();

        // 获取坐席状态名称
        SeatStatus seatStatusInfo = seatStatusRepository.selectInfoBySeatStatusEn(seatInfo.getCurrentSeatStatusEn());

        // 获取坐席可切换的状态
        List<SeatStatusFunctionMappingRespBo> seatStatusList = seatStatusFunctionMappingRepository.getSeatStatusByFunctionEnAndFunctionType(CommonConstant.SEAT_STATUS_ONLINE_SWITCH_TYPE, SeatFunctionTypeEnum.SUPPORT.type);
        List<CommonLabelValueResp> seatStatusMappingList = seatStatusList.stream().map(seatStatus -> {
            CommonLabelValueResp commonLabelValueResp = new CommonLabelValueResp();
            commonLabelValueResp.setLabel(seatStatus.getSeatStatusCn());
            commonLabelValueResp.setValue(seatStatus.getSeatStatusEn());
            return commonLabelValueResp;
        }).collect(Collectors.toList());

        resp.setSeatId(seatInfo.getSeatId());
        resp.setSeatName(seatInfo.getSeatName());
        resp.setFaceUrl(seatInfo.getFaceUrl());
        // 如果是满负荷的情况下，放到子状态中
        if ( CommonConstant.SEAT_STATUS_FULL_LOAD.equals(seatInfo.getCurrentSeatStatusEn()) ){
            resp.setSeatStatus(CommonConstant.SEAT_STATUS_ONLINE);
            resp.setSeatStatusName("在线");
            resp.setSeatSubStatus(seatInfo.getCurrentSeatStatusEn());
            resp.setSeatSubStatusName(Objects.nonNull(seatStatusInfo) ? seatStatusInfo.getSeatStatusCn() : seatInfo.getCurrentSeatStatusEn());
        }else{
            resp.setSeatStatus(seatInfo.getCurrentSeatStatusEn());
            resp.setSeatStatusName(Objects.nonNull(seatStatusInfo) ? seatStatusInfo.getSeatStatusCn() : seatInfo.getCurrentSeatStatusEn());
        }
        resp.setSeatType(seatInfo.getSeatType());
        resp.setFaceUrl(seatInfo.getFaceUrl());
        resp.setMaxWiringQuantity(seatInfo.getMaxWiringQuantity());
        resp.setSupportSwitchStatusList(seatStatusMappingList);
        return resp;
    }

    /**
     * 坐席状态切换
     * @param req
     * @return
     */
    @Override
    public Integer onlineSeatStatusSwitch(OnlineSeatStatusSwitchReq req) {
        req.checkParams();

        // 获取坐席信息
        SeatInfo seatInfo = super.getVerifySeatInfo(req);

        // 获取坐席可切换的状态
        List<SeatStatusFunctionMappingRespBo> seatStatusList = seatStatusFunctionMappingRepository.getSeatStatusByFunctionEnAndFunctionType(CommonConstant.SEAT_STATUS_ONLINE_SWITCH_TYPE, SeatFunctionTypeEnum.SUPPORT.type);
        if ( seatStatusList.stream().noneMatch(seatStatus -> Objects.equals(seatStatus.getSeatStatusEn(), req.getSwitchStatus()) ) ){
            log.error("当前坐席不支持切换到该状态, seatStatusList: {}, switchStatus: {}", seatStatusList, req.getSwitchStatus());
            throw new BusinessException("当前坐席不支持切换到该状态");
        }

        // 如果当前是满负荷状态，不能切换到在线状态
        if ( CommonConstant.SEAT_STATUS_FULL_LOAD.equals(seatInfo.getCurrentSeatStatusEn())
                && CommonConstant.SEAT_STATUS_ONLINE.equals(req.getSwitchStatus()) ){
            throw new BusinessException("当前坐席满负荷，不能切换到在线状态");
        }

        Integer integer = super.seatStatusSwitch(seatInfo, req.getSwitchStatus());

        // 发送更新坐席状态mq
        if ( CommonConstant.SEAT_STATUS_ONLINE.equals(req.getSwitchStatus()) ){
            ConversationFlowSeatStatusUpdateReq conversationFlowSeatStatusUpdateReq = new ConversationFlowSeatStatusUpdateReq();
            conversationFlowSeatStatusUpdateReq.setSeatId(seatInfo.getSeatId());
            conversationFlowSeatStatusUpdateProducer.sendMessage(conversationFlowSeatStatusUpdateReq);
        }

        return integer;
    }

    /**
     * 待处理列表
     * @param req
     * @return
     */
    @Override
    public List<GetWaitProcessConversationListResp> waitProcessConversationList(GetWaitProcessConversationListReq req) {

        // 格式化搜索入参
        FormatSearchConversationListReq reqBo = new FormatSearchConversationListReq();
        BeanUtils.copyProperties(req, reqBo);
        FormatSearchConversationListBo searchBo = this.formatSearchConversationListReq(reqBo);
        searchBo.setConversationType(ImConversationTypeEnum.online.type);
        if ( CommonConstant.ONE.equals(searchBo.getNoData()) ){
            return new ArrayList<>();
        }

        // 查询待处理的会话
        List<ImConversation> imConversations = imConversationRepository.selectWaitProcessList(searchBo);
        if ( CollectionUtils.isEmpty(imConversations) ){
            return new ArrayList<>();
        }

        // 获取坐席的外部账号id
        UserRegisterInfo seatRegisterInfo = userRegisterInfoRepository.selectOuterUserIdByUser(req.getSeatId(), UserClassEnum.SEAT.type);
        if ( Objects.isNull(seatRegisterInfo) ){
            log.error("获取坐席的外部账号id失败, seatId: {}", reqBo.getSeatId());
            throw new BusinessException("坐席关联的外部账号不存在");
        }

        // 转换会话列表
        List<ListConversationMsgResp> transfromConversationList = this.transfromConversationList(req.getSeatId(), imConversations, seatRegisterInfo);
        if ( CollectionUtils.isEmpty(transfromConversationList) ){
            return new ArrayList<>();
        }

        List<Long> conversationIdList = transfromConversationList.stream().map(ListConversationMsgResp::getConversationId).collect(Collectors.toList());

        // 获取坐席会话未读消息数
        List<CommonLabelValueResp> unreadCountList = imConversationItemRepository.selectUnreadCountByConversationIdList(conversationIdList);

        // 获取会话超时状态
        List<Long> channelIdList = imConversations.stream().map(ImConversation::getChannelConfigId).collect(Collectors.toList());

        // 获取全局配置
        ChannelConfig globalChannel = channelConfigRepository.getChannelConfigByChannelEn(ChannelConstant.CONVERSATION_GLOBAL_CHANNEL_EN);
        ConversationAutoReplyConfig globalConfigReplyConfig;
        if ( Objects.nonNull(globalChannel) ){
            channelIdList.add(globalChannel.getChannelId());
            globalConfigReplyConfig = conversationAutoReplyConfigRepository.selectByChannelId(globalChannel.getChannelId());
        } else {
            globalConfigReplyConfig = null;
        }

        SelectConversationAutoReplyDetailConfigReqBo replyReqBo = new SelectConversationAutoReplyDetailConfigReqBo();
        replyReqBo.setChannelIdList(channelIdList);
        replyReqBo.setMsgTypeList(Collections.singletonList(AutoReplyTypeEnum.TIMEOUT_AUTO_REPLY.type));
        replyReqBo.setMsgSubTypeList(Collections.singletonList(AutoReplyMsgSubTypeEnum.SEAT_REPLY_TIMEOUT_REMIND.type));
        List<SelectConversationAutoReplyDetailConfigRespBo> replyRespBo = conversationAutoReplyDetailConfigRepository.batchSelectConfig(replyReqBo);

        // 获取最新发送消息信息（访客和坐席） 查询配置了超时提醒的会话
        List<ImConversationItem> lastMessageItemList = imConversationItemRepository.selectLastMessageByConversationIdListAndVisitorAndAgent(conversationIdList);

        // 获取im外部群组id
        List<ImGroup> imGroupList = imGroupRepository.selectImGroupByConversationIds(conversationIdList);

        return transfromConversationList.stream().map(listConversationMsgResp -> {
            GetWaitProcessConversationListResp resp = new GetWaitProcessConversationListResp();
            BeanUtils.copyProperties(listConversationMsgResp, resp);

            ImConversation imConversation = imConversations.stream().filter(f -> listConversationMsgResp.getConversationId().equals(f.getConversationId())).findFirst().orElse(null);
            if ( Objects.isNull(imConversation) ){
                log.error("获取会话信息失败, conversationId: {}", listConversationMsgResp.getConversationId());
                return resp;
            }

            // 获取坐席会话未读消息数
            CommonLabelValueResp unreadCount = unreadCountList.stream().filter(f -> listConversationMsgResp.getConversationId().toString().equals(f.getLabel())).findFirst().orElse(null);
            if ( Objects.nonNull(unreadCount) ){
                resp.setUnreadCount(Long.valueOf(unreadCount.getValue()));
            }
            resp.setHasUnread(resp.getUnreadCount() > 0 ? CommonConstant.ONE : CommonConstant.ZERO);

            /**
             * 有最后一条消息，且发送人不是坐席
             * 计算当前会话是否即将超时或已超时
             */
            ImConversationItem lastMessageItem = lastMessageItemList.stream().filter(f -> listConversationMsgResp.getConversationId().equals(f.getConversationId())).findFirst().orElse(null);

            resp.setTimeoutStatus(ConversationTimeoutStatusEnum.NOT.type);
            if ( Objects.nonNull(lastMessageItem)
                    && Objects.nonNull(lastMessageItem.getMsgSendTime())
                    && !OuterUserTypeEnum.SEAT.type.equals(lastMessageItem.getResponseOuterUserType())
            ){
                resp.setTimeoutStatus(this.getConversationTimeoutStatus(lastMessageItem.getMsgSendTime(), imConversation, replyRespBo, globalConfigReplyConfig));
            }

            // 获取im群组id
            ImGroup imGroup = imGroupList.stream().filter(f -> listConversationMsgResp.getConversationId().equals(f.getConversationId())).findFirst().orElse(null);
            if ( Objects.nonNull(imGroup) ){
                resp.setImOuterGroupId(CommonConstant.DEFAULT_IM_GROUP_PREFIX+imGroup.getOuterGroupId());
            }

            return resp;

            // 排序规则
            // 未读消息 > 没有未读消息
            // 再按最后一条消息时间升序排序, 最后一条消息有可能为null，需要兼容
        }).sorted(Comparator.comparing(GetWaitProcessConversationListResp::getHasUnread, Comparator.reverseOrder())
                .thenComparing(GetWaitProcessConversationListResp::getLatestMsgTime, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
    }

    /**
     * 获取会话超时状态
     *
     * @param imConversation
     * @param replyRespBo
     * @param globalConfigReplyConfig
     * @return
     */
    public String getConversationTimeoutStatus(Date latestMsgTime, ImConversation imConversation, List<SelectConversationAutoReplyDetailConfigRespBo> replyRespBo, ConversationAutoReplyConfig globalConfigReplyConfig) {

        // 获取超时配置
        ConversationAutoReplyDetailConfigExtraJsonRespBo config = this.getConversationTimeoutConfig(imConversation.getChannelConfigId(), replyRespBo);
        if ( Objects.isNull(config) ){
            log.error("获取会话超时配置失败(渠道配置配置为空), conversationId: {}", imConversation.getConversationId());
            config = this.getConversationTimeoutGlobalConfig(replyRespBo.stream().filter(f -> Objects.nonNull(globalConfigReplyConfig) &&
                    globalConfigReplyConfig.getChannelId().equals(f.getChannelId())
            ).findFirst().orElse(null));
            if ( Objects.isNull(config) ){
                log.error("获取会话超时配置失败(全局配置为空), conversationId: {}", imConversation.getConversationId());
                return ConversationTimeoutStatusEnum.NOT.type;
            }
        }

        // 按顺序检测，超时->即将超时->未超时
        for (ConversationTimeoutStatusEnum timeoutStatusEnum : ConversationTimeoutStatusEnum.values()) {

            // 未超时
            if ( timeoutStatusEnum.equals(ConversationTimeoutStatusEnum.NOT) ){
                return timeoutStatusEnum.type;
            }

            // 已经超时
            if ( timeoutStatusEnum.equals(ConversationTimeoutStatusEnum.ALREADY) ){
                if ( this.isTimeout(latestMsgTime, config.getResponseTimeoutReminder()) ){
                    return timeoutStatusEnum.type;
                }
            }

            // 即将超时
            if ( timeoutStatusEnum.equals(ConversationTimeoutStatusEnum.APPROACH) ){
                if ( this.isTimeout(latestMsgTime, config.getImmediatelyResponseTimeoutReminder()) ){
                    return timeoutStatusEnum.type;
                }
            }
        }

        return ConversationTimeoutStatusEnum.NOT.type;
    }

    /**
     * 获取会话超时配置
     * @param channelConfigId
     * @param replyRespBo
     * @return
     */
    private ConversationAutoReplyDetailConfigExtraJsonRespBo getConversationTimeoutConfig(Long channelConfigId
            , List<SelectConversationAutoReplyDetailConfigRespBo> replyRespBo) {

        // 计算是否超时
        SelectConversationAutoReplyDetailConfigRespBo config = replyRespBo.stream()
                .filter(f ->
                        channelConfigId.equals(f.getChannelId())
                                && AutoReplyMsgSubTypeEnum.SEAT_REPLY_TIMEOUT_REMIND.type.equals(f.getMsgSubType())
                ).findFirst().orElse(null);
        if ( Objects.isNull(config) ){
            return null;
        }

        JSONObject jsonObject = JSONObject.parseObject(config.getExtraJson());
        if ( Objects.isNull(jsonObject) ){
            return null;
        }

        Long responseTimeoutReminder = jsonObject.getLong(ChannelConstant.RESPONSE_TIMEOUT_REMINDER);
        Long immediatelyResponseTimeoutReminder = jsonObject.getLong(ChannelConstant.IMMEDIATELY_RESPONSE_TIMEOUT_REMINDER);
        if ( Objects.isNull(responseTimeoutReminder) || Objects.isNull(immediatelyResponseTimeoutReminder) ){
            return null;
        }

        ConversationAutoReplyDetailConfigExtraJsonRespBo respBo = new ConversationAutoReplyDetailConfigExtraJsonRespBo();
        respBo.setResponseTimeoutReminder(responseTimeoutReminder);
        respBo.setImmediatelyResponseTimeoutReminder(immediatelyResponseTimeoutReminder);
        return respBo;
    }

    /**
     * 获取会话超时全局配置
     * @param config
     * @return
     */
    private ConversationAutoReplyDetailConfigExtraJsonRespBo getConversationTimeoutGlobalConfig(SelectConversationAutoReplyDetailConfigRespBo config){
        if ( Objects.isNull(config) ){
            return null;
        }

        // 全局配置
        JSONObject jsonObject = JSONObject.parseObject(config.getExtraJson());
        if ( Objects.isNull(jsonObject) ){
            return null;
        }

        Long responseTimeoutReminder = jsonObject.getLong(ChannelConstant.RESPONSE_TIMEOUT_REMINDER);
        Long immediatelyResponseTimeoutReminder = jsonObject.getLong(ChannelConstant.IMMEDIATELY_RESPONSE_TIMEOUT_REMINDER);
        if ( Objects.isNull(responseTimeoutReminder) || Objects.isNull(immediatelyResponseTimeoutReminder) ){
            return null;
        }

        ConversationAutoReplyDetailConfigExtraJsonRespBo respBo = new ConversationAutoReplyDetailConfigExtraJsonRespBo();
        respBo.setResponseTimeoutReminder(responseTimeoutReminder);
        respBo.setImmediatelyResponseTimeoutReminder(immediatelyResponseTimeoutReminder);
        return respBo;
    }

    private Boolean isTimeout(Date latestMsgTime, Long configSeconds) {
        if ( Objects.isNull(configSeconds) ){
            return false;
        }
        // 消息最后发送时间+配置时间 < 当前时间
        return latestMsgTime.getTime() + configSeconds*1000 < System.currentTimeMillis();
    }

    /**
     * 转换会话列表
     * @param imConversations
     * @param seatRegisterInfo
     * @return
     */
    public List<ListConversationMsgResp> transfromConversationList(Long seatId, List<ImConversation> imConversations, UserRegisterInfo seatRegisterInfo) {
        List<Long> conversationIdList = imConversations.stream().map(ImConversation::getConversationId).collect(Collectors.toList());
        List<String> visitorOuterUserIdList = imConversations.stream().map(ImConversation::getFromOuterUserId).collect(Collectors.toList());
//        List<String> seatOuterUserIdList = imConversations.stream().map(ImConversation::getToOuterUserId).collect(Collectors.toList());

        // 获取最新发送消息信息
        List<ImConversationItem> imConversationItems = imConversationItemRepository.selectLastMessageByConversationIdList(conversationIdList);
        List<String> itemVisitorId = imConversationItems.stream().filter(f ->
                Arrays.asList(OuterUserTypeEnum.MERCHANT.type, OuterUserTypeEnum.CLIENT.type, OuterUserTypeEnum.MASTER.type, OuterUserTypeEnum.ENTERPRISE.type).contains(f.getFromOuterUserType())
                        || ( OuterUserTypeEnum.VIRTUAL.type.equals(f.getFromOuterUserType())
                        && OuterUserTypeEnum.SEAT.type.equals(f.getResponseOuterUserType())
                        && !seatRegisterInfo.getOuterUserId().equals(f.getResponseOuterUserId()) )
        ).map(item -> {
            if ( OuterUserTypeEnum.VIRTUAL.type.equals(item.getFromOuterUserType()) && OuterUserTypeEnum.SEAT.type.equals(item.getResponseOuterUserType()) ){
                return item.getResponseOuterUserId();
            } else {
                return item.getFromOuterUserId();
            }
        }).distinct().collect(Collectors.toList());
        visitorOuterUserIdList.addAll(itemVisitorId);

        // 获取访客标签
        List<VisitorMark> visitorMarkList = visitorMarkRepository.selectListByOuterUserIdAndAccount(visitorOuterUserIdList, seatId.toString(), UserClassEnum.SEAT.type);

        // 获取访客内部信息
        visitorOuterUserIdList.add(seatRegisterInfo.getOuterUserId());
        GetVisitorInnerInfoReqBo getVisitorInnerInfoReqBo = new GetVisitorInnerInfoReqBo();
        getVisitorInnerInfoReqBo.setOuterUserIdList(visitorOuterUserIdList);
        List<VisitorInnerInfoBo> visitorInnerInfoBoList = this.getVisitorInnerInfoBoList(getVisitorInnerInfoReqBo);

        return imConversations.stream().map(imConversation -> {
            ListConversationMsgResp resp = new ListConversationMsgResp();
            resp.setConversationId(imConversation.getConversationId());

            // 会话访客信息
            VisitorInnerInfoBo visitorInnerInfoBo = visitorInnerInfoBoList.stream().filter(f -> imConversation.getFromOuterUserId().equals(f.getOuterUserId())).findFirst().orElse(null);
            if ( Objects.nonNull(visitorInnerInfoBo) ){
                resp.setVisitorName(visitorInnerInfoBo.getUserName());
                resp.setVisitorFaceUrl(visitorInnerInfoBo.getUserFaceUrl());
                resp.setVisitorOnlineState(StringUtils.isEmpty(visitorInnerInfoBo.getOnlineState()) ? CommonConstant.DEFAULT_OFFLINE_SEAT_STATUS : visitorInnerInfoBo.getOnlineState());
            }

            // 访客标签
            List<CommonLabelValueResp> visitorLabelList = visitorMarkList.stream().filter(f -> imConversation.getFromOuterUserId().equals(f.getOuterUserId())).map(visitorMark -> {
                CommonLabelValueResp commonLabelValueResp = new CommonLabelValueResp();
                commonLabelValueResp.setLabel(visitorMark.getLabelName());
                commonLabelValueResp.setValue(visitorMark.getLabelValue());
                return commonLabelValueResp;
            }).collect(Collectors.toList());
            resp.setVisitorLabelNameList(visitorLabelList);

            // 最新发送的消息
            ImConversationItem imConversationItem = imConversationItems.stream().filter(f -> imConversation.getConversationId().equals(f.getConversationId())).findFirst().orElse(null);
            if ( Objects.nonNull(imConversationItem) ){
                resp.setLatestMsgTime(imConversationItem.getMsgSendTime());
                resp.setLatestMsgContent(this.getListMsgContent(imConversationItem.getMsgContent()));
                // 发送人信息
                VisitorInnerInfoBo conversationMsgSender = this.getConversationMsgSender(imConversationItem, visitorInnerInfoBoList, seatRegisterInfo.getOuterUserId());
                resp.setLatestMsgSender(conversationMsgSender.getUserName());
            }

            return resp;
        }).collect(Collectors.toList());
    }

    /**
     * 获取消息发送人
     *
     * @param imConversationItem
     * @param visitorInnerInfoBoList
     * @param currentOuterUserId
     * @return
     */
    public VisitorInnerInfoBo getConversationMsgSender(ImConversationItem imConversationItem, List<VisitorInnerInfoBo> visitorInnerInfoBoList, String currentOuterUserId) {
        VisitorInnerInfoBo visitorInfo = new VisitorInnerInfoBo();
        visitorInfo.setOuterUserId(imConversationItem.getFromOuterUserId());
        visitorInfo.setUserName(imConversationItem.getFromOuterUserId());
        visitorInfo.setUserClass(imConversationItem.getFromOuterUserType());

        OuterUserTypeEnum enumByType = OuterUserTypeEnum.getEnumByType(imConversationItem.getFromOuterUserType());
        if ( Objects.isNull(enumByType) ){
            log.error("会话发送消息人类型不支持，imConversationItem: {}", JSONObject.toJSONString(imConversationItem));
            return visitorInfo;
        }

        switch (enumByType) {
            case VIRTUAL:
                if ( OuterUserTypeEnum.SEAT.type.equals(imConversationItem.getResponseOuterUserType()) ){
                    // 获取坐席信息
                    VisitorInnerInfoBo seatInfoBo = visitorInnerInfoBoList.stream()
                            .filter(f -> imConversationItem.getResponseOuterUserId().equals(f.getOuterUserId())).findFirst().orElse(null);
                    if ( Objects.nonNull(seatInfoBo) ){
                        visitorInfo.setOuterUserId(seatInfoBo.getOuterUserId());
                        visitorInfo.setInnerUserId(seatInfoBo.getInnerUserId());
                        visitorInfo.setUserClass(seatInfoBo.getUserClass());
                        visitorInfo.setUserFaceUrl(seatInfoBo.getUserFaceUrl());
                        if ( StringUtils.isNoneBlank(currentOuterUserId) && currentOuterUserId.equals(imConversationItem.getResponseOuterUserId()) ){
                            visitorInfo.setUserName("我");
                        }else{
                            visitorInfo.setUserName(seatInfoBo.getUserName());
                        }
                        return visitorInfo;
                    }
                }

                log.error("会话发送消息人类型是虚拟账号，但是群聊接待人不是坐席，imConversationItem: {}", JSONObject.toJSONString(imConversationItem));
                return visitorInfo;
            case MERCHANT:
            case CLIENT:
            case MASTER:
            case ENTERPRISE:
                VisitorInnerInfoBo merchantInfoBo = visitorInnerInfoBoList.stream()
                        .filter(f -> imConversationItem.getFromOuterUserId().equals(f.getOuterUserId())).findFirst().orElse(null);
                if ( Objects.nonNull(merchantInfoBo) ){
                    visitorInfo.setOuterUserId(merchantInfoBo.getOuterUserId());
                    visitorInfo.setInnerUserId(merchantInfoBo.getInnerUserId());
                    visitorInfo.setUserClass(merchantInfoBo.getUserClass());
                    visitorInfo.setUserName(merchantInfoBo.getUserName());
                    return visitorInfo;
                }

                log.error("会话发送消息人类型是"+enumByType.name+"，但是"+enumByType.name+"信息不存在，imConversationItem: {}", JSONObject.toJSONString(imConversationItem));
                return visitorInfo;
            case SYSTEM:
            case ROBOT:
                visitorInfo.setUserName(enumByType.name);
                return visitorInfo;
            default:
                log.error("会话发送消息人类型不支持，imConversationItem: {}", JSONObject.toJSONString(imConversationItem));
                return visitorInfo;
        }
    }

    /**
     * 获取访客内部信息
     * @param getVisitorInnerInfoReqBo
     * @return
     */
    public List<VisitorInnerInfoBo> getVisitorInnerInfoBoList(GetVisitorInnerInfoReqBo getVisitorInnerInfoReqBo) {
        if ( CollectionUtils.isEmpty(getVisitorInnerInfoReqBo.getOuterUserIdList()) ){
            return new ArrayList<>();
        }

        // 获取访客内部注册信息
        List<UserRegisterInfo> byOuterUserIds = userRegisterInfoRepository.findByOuterUserIds(getVisitorInnerInfoReqBo.getOuterUserIdList());

        List<Long> userIdList = byOuterUserIds.stream().filter(f -> UserClassEnum.USER.type.equals(f.getUserClass())).map(UserRegisterInfo::getUserId).collect(Collectors.toList());
        List<Long> masterIdList = byOuterUserIds.stream().filter(f -> UserClassEnum.MASTER.type.equals(f.getUserClass())).map(UserRegisterInfo::getUserId).collect(Collectors.toList());
        List<Long> enterpriseIdList = byOuterUserIds.stream().filter(f -> UserClassEnum.ENTERPRISE.type.equals(f.getUserClass())).map(UserRegisterInfo::getUserId).collect(Collectors.toList());
        List<Long> seatIdList = byOuterUserIds.stream().filter(f -> UserClassEnum.SEAT.type.equals(f.getUserClass())).map(UserRegisterInfo::getUserId).collect(Collectors.toList());

        List<UserInfo> userInfos = super.batchGetUserInfo(userIdList);
        List<BatchGetMasterBaseInfoResp> masterInfos = super.batchGetMasterInfo(masterIdList);
        List<EnterpriseBaseInfoResp> enterpriseInfos = super.batchGetEnterpriseInfo(enterpriseIdList);
        List<SeatInfo> seatInfoList = seatInfoRepository.selectListBySeatIdList(seatIdList);

        return getVisitorInnerInfoReqBo.getOuterUserIdList().stream().map(outerUserId -> {
            VisitorInnerInfoBo visitorInnerInfoBo = new VisitorInnerInfoBo();
            visitorInnerInfoBo.setOuterUserId(outerUserId);
            visitorInnerInfoBo.setUserName(outerUserId);

            UserRegisterInfo userRegisterInfo = byOuterUserIds.stream().filter(f -> outerUserId.equals(f.getOuterUserId())).findFirst().orElse(null);
            if ( Objects.isNull(userRegisterInfo) ){
                return visitorInnerInfoBo;
            }
            visitorInnerInfoBo.setInnerUserId(userRegisterInfo.getUserId());
            visitorInnerInfoBo.setUserClass(userRegisterInfo.getUserClass());
            visitorInnerInfoBo.setOnlineState(userRegisterInfo.getOnlineState());

            UserClassEnum userClassEnum = UserClassEnum.getEnumByType(userRegisterInfo.getUserClass());
            if (Objects.isNull(userClassEnum)) {
                return visitorInnerInfoBo;
            }

            switch (userClassEnum) {
                case USER:
                    UserInfo userInfo = userInfos.stream().filter(f -> visitorInnerInfoBo.getInnerUserId().equals(f.getUserId())).findFirst().orElse(null);
                    if (Objects.isNull(userInfo)) {
                        return visitorInnerInfoBo;
                    }
                    visitorInnerInfoBo.setUserName(userInfo.getAccount());
                    break;
                case MASTER:
                    BatchGetMasterBaseInfoResp masterBaseInfoResp = masterInfos.stream().filter(f -> visitorInnerInfoBo.getInnerUserId().equals(f.getMasterId())).findFirst().orElse(null);
                    if (Objects.isNull(masterBaseInfoResp)) {
                        return visitorInnerInfoBo;
                    }
                    visitorInnerInfoBo.setUserName(masterBaseInfoResp.getContact());
                    break;
                case ENTERPRISE:
                    EnterpriseBaseInfoResp enterpriseBaseInfoResp = enterpriseInfos.stream().filter(f -> visitorInnerInfoBo.getInnerUserId().equals(f.getEnterpriseId())).findFirst().orElse(null);
                    if (Objects.isNull(enterpriseBaseInfoResp)) {
                        return visitorInnerInfoBo;
                    }
                    visitorInnerInfoBo.setUserName(enterpriseBaseInfoResp.getEnterpriseName());
                    break;
                case SEAT:
                    SeatInfo seatInfo = seatInfoList.stream().filter(f -> visitorInnerInfoBo.getInnerUserId().equals(f.getSeatId())).findFirst().orElse(null);
                    if (Objects.isNull(seatInfo)) {
                        return visitorInnerInfoBo;
                    }
                    visitorInnerInfoBo.setUserName(seatInfo.getSeatName());
                    visitorInnerInfoBo.setUserFaceUrl(seatInfo.getFaceUrl());
                    break;
            }

            return visitorInnerInfoBo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取会话列表中的消息内容
     * @param msgContentStr
     * @return
     */
    public String getListMsgContent(String msgContentStr) {
        List<MsgBodyItemBo> msgBodyItemBos = super.getMsgContent(msgContentStr);
        if ( CollectionUtils.isEmpty(msgBodyItemBos) ){
            return null;
        }
        MsgBodyItemBo msgBodyItemBo = msgBodyItemBos.get(0);
        if ( Objects.isNull(msgBodyItemBo.getMsgType()) ){
            return null;
        }

        MsgTypeEnum msgTypeEnum = MsgTypeEnum.getEnumByType(msgBodyItemBo.getMsgType());
        if ( Objects.isNull(msgTypeEnum) ){
            return null;
        }

        switch (msgTypeEnum) {
            case TEXT:
                MsgContentCustomElemBo msgContentCustomElemBo = JSONObject.parseObject(msgBodyItemBo.getMsgContent().toString(), MsgContentCustomElemBo.class);
                return msgContentCustomElemBo.getText();
            case LOCATION:
            case FACE:
            case CUSTOMER:
            case SOUND:
            case IMAGE:
            case FILE:
            case VIDEO_FILE:
                return "["+msgTypeEnum.name+"]";
        }

        return null;
    }

    /**
     * 格式化搜索入参
     * @param reqBo
     */
    private FormatSearchConversationListBo formatSearchConversationListReq(FormatSearchConversationListReq reqBo) {
        FormatSearchConversationListBo formatSearchConversationListBo = new FormatSearchConversationListBo();
        BeanUtils.copyProperties(reqBo, formatSearchConversationListBo);

        // 获取坐席的外部账号id
        UserRegisterInfo userRegisterInfo = userRegisterInfoRepository.selectOuterUserIdByUser(reqBo.getSeatId(), ModulesTypeEnum.SEAT.type);
        if ( Objects.isNull(userRegisterInfo) ){
            log.error("获取坐席的外部账号id失败, seatId: {}", reqBo.getSeatId());
            throw new BusinessException("坐席关联的外部账号不存在");
        }
        formatSearchConversationListBo.setToOuterUserId(userRegisterInfo.getOuterUserId());
        formatSearchConversationListBo.setToOuterUserType(userRegisterInfo.getUserClass());

        // 不搜索打标，直接放回
        if ( CollectionUtils.isEmpty(reqBo.getVisitorLabelList()) ){
            return formatSearchConversationListBo;
        }

        /**
         * 搜索打标访客会话
         */
        // 获取坐席打标的访客
        List<VisitorMark> seatVisitorMark = visitorMarkRepository.selectListBySeatId(reqBo.getSeatId().toString(), ModulesTypeEnum.SEAT.type);

        // 坐席没有打过搜索的标, 但是查询打标的数据，且查询不包含未打标，返回空数据
        if ( seatVisitorMark.stream().noneMatch(f->reqBo.getVisitorLabelList().contains(f.getLabelValue()))
                && !reqBo.getVisitorLabelList().contains(VisitorLabelEnum.notMark.type)
        ){
            formatSearchConversationListBo.setNoData(CommonConstant.ONE);
            return formatSearchConversationListBo;
        }

        // 搜索打标的数据
        formatSearchConversationListBo.setSearchMarkOuterUserIdList(seatVisitorMark.stream().filter(f->reqBo.getVisitorLabelList().contains(f.getLabelValue())).map(VisitorMark::getOuterUserId).collect(Collectors.toList()));

        // 如果筛选了为空，则需要查询未打标的数据
        if ( reqBo.getVisitorLabelList().contains(VisitorLabelEnum.notMark.type) ){
            formatSearchConversationListBo.setAllMarkOuterUserIdList(seatVisitorMark.stream().map(VisitorMark::getOuterUserId).collect(Collectors.toList()));
        }
        return formatSearchConversationListBo;
    }

    /**
     * 坐席标记访客
     * @param req
     * @return
     */
    @Override
    public Integer seatMarkForVisitor(SeatMarkForVisitorReq req) {
        req.checkParams();

        // 获取坐席信息
        SeatInfo seatInfo = super.getVerifySeatInfo(req);

        // 校验打标内容 是否 为 Arrays.asList(VisitorLabelEnum.alreadyFeedback.type, VisitorLabelEnum.needFeedback.type, VisitorLabelEnum.needLeave.type) 之中
        if ( req.getMarkLabelList().stream().anyMatch(f -> !Arrays.asList(VisitorLabelEnum.alreadyFeedback.type, VisitorLabelEnum.needFeedback.type, VisitorLabelEnum.needLeave.type).contains(f)) ){
            log.error("非法操作, 存在不支持打标内容, req:{}", JSONObject.toJSONString(req));
            throw new BusinessException("非法操作，存在不支持打标内容");
        }

        // 获取会话信息
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getConversationId());
        if ( Objects.isNull(imConversation) ){
            log.error("会话不存在, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话不存在");
        }

        //手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        try{
            // 删除历史打标
            visitorMarkRepository.deleteByOuterUserIdAndAccount(imConversation.getFromOuterUserId(), req.getSeatId().toString(), UserClassEnum.SEAT.type);

            // 批量打标
            List<VisitorMark> visitorMarks = req.getMarkLabelList().stream().map(labelValue -> {
                VisitorMark visitorMark = new VisitorMark();
                visitorMark.setAccountId(req.getSeatId());
                visitorMark.setAccountClass(UserClassEnum.SEAT.type);
                visitorMark.setOuterUserId(imConversation.getFromOuterUserId());
                visitorMark.setLabelValue(labelValue);
                visitorMark.setLabelName(VisitorLabelEnum.getNameByType(labelValue));
                visitorMark.setIsDelete(CommonConstant.ZERO);
                visitorMark.setCreateTime(new Date());
                visitorMark.setUpdateTime(new Date());
                return visitorMark;
            }).collect(Collectors.toList());
            visitorMarkRepository.insertList(visitorMarks);

            platformTransactionManager.commit(status);
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }

        // 发送会话标签刷新消息
        SendImWebSocketMessageBo sendImWebSocketMessageBo = new SendImWebSocketMessageBo();
        sendImWebSocketMessageBo.setConversationId(imConversation.getConversationId());
        sendImWebSocketMessageBo.setBusinessType(SocketBaseManagerImpl.REFRESH_SEAT_VISITOR_MARK);
        sendImWebSocketMessageProducer.sendImWebSocketMessage(sendImWebSocketMessageBo);
        return 1;
    }

    /**
     * 获取留言列表
     * @param req
     * @return
     */
    @Override
    public SimplePageInfo<GetLeaveConversationListResp> leaveConversationList(GetLeaveConversationListReq req) {
        SimplePageInfo<GetLeaveConversationListResp> resp = new SimplePageInfo<>();

        // 格式化搜索入参
        FormatSearchGetLeaveConversationListReq searchBo = this.getFormatSearchGetLeaveConversationListReq(req);
        if ( Objects.isNull(searchBo) || ( CollectionUtils.isEmpty(searchBo.getChannelIdList()) && CommonConstant.ZERO.equals(searchBo.getIsBelongToGuaranteeGroup()) ) ){
            return resp;
        }

        // 查询留言中的会话
        Page<Object> page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<ImConversation> imConversations = imConversationRepository.selectLeaveList(searchBo);
        if ( CollectionUtils.isEmpty(imConversations) ){
            return resp;
        }

        // 获取坐席的外部账号id
        UserRegisterInfo seatRegisterInfo = userRegisterInfoRepository.selectOuterUserIdByUser(req.getSeatId(), UserClassEnum.SEAT.type);
        if ( Objects.isNull(seatRegisterInfo) ){
            log.error("获取坐席的外部账号id失败, seatId: {}", req.getSeatId());
            throw new BusinessException("坐席关联的外部账号不存在");
        }

        // 转换会话列表
        List<ListConversationMsgResp> transfromConversationList = this.transfromConversationList(req.getSeatId(), imConversations, seatRegisterInfo);
        if ( CollectionUtils.isEmpty(transfromConversationList) ){
            return resp;
        }

        List<GetLeaveConversationListResp> list = transfromConversationList.stream().map(listConversationMsgResp -> {
            GetLeaveConversationListResp item = new GetLeaveConversationListResp();
            BeanUtils.copyProperties(listConversationMsgResp, item);
            return item;
        }).collect(Collectors.toList());

        resp.setList(list);
        resp.setPages(page.getPages());
        resp.setTotal(page.getTotal());
        resp.setPageNum(page.getPageNum());
        resp.setPageSize(page.getPageSize());
        return resp;
    }

    /**
     * 格式化搜索入参
     * @param req
     * @return
     */
    private FormatSearchGetLeaveConversationListReq getFormatSearchGetLeaveConversationListReq(GetLeaveConversationListReq req) {
        /**
         * 留言列表，获取当前坐席所在分组关联的渠道所属的留言
         */
        // 获取当前坐席所在分组
        List<Integer> groupIdList = seatGroupMappingRepository.selectGroupIdBySeatId(req.getSeatId());
        if ( CollectionUtils.isEmpty(groupIdList) ){
            log.info("当前坐席未关联生效分组, seatId:{}", req.getSeatId());
            return null;
        }

        // 是否属于兜底组
        if ( groupIdList.contains(minimumGuaranteeGroupId) ){
            FormatSearchGetLeaveConversationListReq searchBo = new FormatSearchGetLeaveConversationListReq();
            searchBo.setIsBelongToGuaranteeGroup(CommonConstant.ONE);
            return searchBo;
        }

        // 获取分组关联渠道入口配置id
        RuleMetricConfig ruleMetricConfigByType = ruleMetricConfigRepository.selectRuleMetricByObjectAndFromTypeAndEn(RuleMetricObjectEnum.GROUP.type, RuleMetricFromTypeEnum.KF.type, RuleMetricConfigEnum.CONVERSATION_ENTRY.type);
        if ( Objects.isNull(ruleMetricConfigByType) ){
            log.error("分组渠道入口规则配置异常, ruleMetricConfigByType:{}", RuleMetricConfigEnum.CONVERSATION_ENTRY.type);
            throw new BusinessException("分组渠道入口规则配置异常");
        }

        // 获取分组关联的所有渠道
        List<GroupRule> groupRules = groupRuleRepository.selectByGroupIdListAndRuleMetricId(groupIdList, ruleMetricConfigByType.getRuleMetricId());
        if ( CollectionUtils.isEmpty(groupRules) ){
            log.info("当前坐席未关联渠道, groupIdList:{}", groupIdList);
            return null;
        }

        List<Long> channelIdList = new ArrayList<>();
        groupRules.forEach(groupRule -> {
            GroupRuleConfigValueBo groupRuleConfigValueBo = JSONObject.parseObject(groupRule.getRuleConfigValue(), GroupRuleConfigValueBo.class);
            if ( StringUtils.isBlank(groupRuleConfigValueBo.getSelectiveType()) ||
                    ( CollectionUtils.isEmpty(groupRuleConfigValueBo.getMultipleValue()) && StringUtils.isBlank(groupRuleConfigValueBo.getSingleValue()) ) ){
                return;
            }
            // 多选值
            if (RuleConfigValueSelectiveTypeEnum.MULTIPLE.type.equals(groupRuleConfigValueBo.getSelectiveType())){
                channelIdList.addAll(groupRuleConfigValueBo.getMultipleValue().stream().map(Long::valueOf).collect(Collectors.toList()));
            }
            // 单选值
            if (RuleConfigValueSelectiveTypeEnum.SINGLE.type.equals(groupRuleConfigValueBo.getSelectiveType())){
                channelIdList.add(Long.valueOf(groupRuleConfigValueBo.getSingleValue()));
            }
        });
        if ( CollectionUtils.isEmpty(channelIdList) ){
            log.info("当前坐席未关联渠道, groupIdList:{}", groupIdList);
            return null;
        }

        FormatSearchGetLeaveConversationListReq searchBo = new FormatSearchGetLeaveConversationListReq();
        searchBo.setSeatId(req.getSeatId());
        searchBo.setChannelIdList(channelIdList);
        return searchBo;
    }

    /**
     * 移除打标
     * @param req
     * @return
     */
    @Override
    public Integer seatUnmarkForVisitor(SeatUnmarkForVisitorReq req) {
        req.checkParams();

        // 获取校验坐席
        SeatInfo seatInfo = super.getVerifySeatInfo(req);

        // 校验打标内容 是否 为 Arrays.asList(VisitorLabelEnum.alreadyFeedback.type, VisitorLabelEnum.needFeedback.type, VisitorLabelEnum.needLeave.type) 之中
        if ( !Arrays.asList(VisitorLabelEnum.alreadyFeedback.type, VisitorLabelEnum.needFeedback.type, VisitorLabelEnum.needLeave.type).contains(req.getRemoveLabel()) ){
            log.error("移除标记不合法, req:{}", JSONObject.toJSONString(req));
            throw new BusinessException("移除标记不合法");
        }

        // 获取会话信息
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getConversationId());
        if ( Objects.isNull(imConversation) ){
            log.error("会话不存在, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话不存在");
        }

        return visitorMarkRepository.deleteSingleByOuterUserIdAndAccount(imConversation.getFromOuterUserId(), seatInfo.getSeatId().toString(), UserClassEnum.SEAT.type, req.getRemoveLabel());
    }

    /**
     * 获取历史会话列表
     * @param req
     * @return
     */
    @Override
    public SimplePageInfo<GetHistoryConversationListResp> historyConversationList(GetHistoryConversationListReq req) {
        SimplePageInfo<GetHistoryConversationListResp> resp = new SimplePageInfo<>();

        // 格式化搜索入参
        FormatSearchConversationListReq reqBo = new FormatSearchConversationListReq();
        BeanUtils.copyProperties(req, reqBo);
        FormatSearchConversationListBo searchBo = this.formatSearchConversationListReq(reqBo);
        searchBo.setConversationType(ImConversationTypeEnum.online.type);
        if ( CommonConstant.ONE.equals(searchBo.getNoData()) ){
            return resp;
        }

        // 查询待处理的会话
        Page<Object> page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<ImConversation> imConversations = imConversationRepository.selectHistoryList(searchBo);
        if ( CollectionUtils.isEmpty(imConversations) ){
            return resp;
        }

        // 获取坐席的外部账号id
        UserRegisterInfo seatRegisterInfo = userRegisterInfoRepository.selectOuterUserIdByUser(req.getSeatId(), UserClassEnum.SEAT.type);
        if ( Objects.isNull(seatRegisterInfo) ){
            log.error("获取坐席的外部账号id失败, seatId: {}", reqBo.getSeatId());
            throw new BusinessException("坐席关联的外部账号不存在");
        }

        // 转换会话列表
        List<ListConversationMsgResp> transfromConversationList = this.transfromConversationList(req.getSeatId(), imConversations, seatRegisterInfo);
        if ( CollectionUtils.isEmpty(transfromConversationList) ){
            return resp;
        }

        List<GetHistoryConversationListResp> list = transfromConversationList.stream().map(listConversationMsgResp -> {
            GetHistoryConversationListResp respBo = new GetHistoryConversationListResp();
            BeanUtils.copyProperties(listConversationMsgResp, respBo);
            return respBo;
        }).collect(Collectors.toList());

        resp.setList(list);
        resp.setPages(page.getPages());
        resp.setTotal(page.getTotal());
        resp.setPageNum(page.getPageNum());
        resp.setPageSize(page.getPageSize());
        return resp;
    }

    /**
     * 认领留言
     * @param req
     * @return
     */
    @Override
    public Integer claimLeaveConversation(ClaimLeaveConversationReq req) {
        req.checkParams();

        // 获取校验坐席
        GetSeatRegisterInfoRespBo seatRegisterInfo = super.getSeatRegisterInfo(req);

        // 校验坐席状态是否可以认领留言
        List<SeatStatusFunctionMappingRespBo> seatStatusList = seatStatusFunctionMappingRepository.getSeatStatusByFunctionEnAndFunctionType(CommonConstant.SEAT_STATUS_FUNCTION_CLAIM_LEAVE, SeatFunctionTypeEnum.SUPPORT.type);
        if ( seatStatusList.stream().noneMatch(f -> f.getSeatStatusEn().equals(seatRegisterInfo.getSeatInfo().getCurrentSeatStatusEn())) ){
            log.error("当前坐席状态不支持认领留言, seatId:{}", req.getSeatId());
            throw new BusinessException("当前坐席状态不支持认领留言");
        }

        // 获取坐席能否认领留言
        Integer waitingCount = imConversationRepository.selectWaitingCountByToOuterUserId(seatRegisterInfo.getUserRegisterInfo().getOuterUserId());
        if ( waitingCount + req.getConversationIdList().size() > seatRegisterInfo.getSeatInfo().getMaxWiringQuantity() ){
            log.error("当前领取留言+坐席待处理会话已超过坐席接线上限，无法继续领取留言, seatId:{} waitingCount:{} claimCount:{}", req.getSeatId(), waitingCount, req.getConversationIdList().size());
            throw new BusinessException("您已达最大接线会话数，暂时无法重启会话");
        }

        // 校验会话
        List<ImConversation> imConversationList = imConversationRepository.selectListByConversationIdListAndConversationType(
                req.getConversationIdList(), ImConversationTypeEnum.leave.type, ConversationStatusEnum.COMPLETE.type);
        if ( CollectionUtils.isEmpty(imConversationList) ){
            log.error("认领留言不存在, conversationIdList:{}", JSONObject.toJSONString(req.getConversationIdList()));
            throw new BusinessException("认领留言不存在");
        }
        if ( imConversationList.size() != req.getConversationIdList().size() ){
            log.error("非法操作，存在异常的留言, conversationIdList:{}", JSONObject.toJSONString(req.getConversationIdList()));
            throw new BusinessException("非法操作，存在异常的留言");
        }

        for (ImConversation imConversation : imConversationList) {
            // 认领留言 todo 改成mq
            CoreActiveConversationRespBo coreActiveConversationRespBo = this.coreActiveConversation(imConversation, seatRegisterInfo.getUserRegisterInfo(), ImConversationTypeEnum.leave);
            if ( Objects.nonNull(coreActiveConversationRespBo) ){
                // 导入历史消息会话
                this.initImportHistoryConversation(coreActiveConversationRespBo);
            }
        }

        // 监听会话状态
        ConversationFlowSeatStatusUpdateReq conversationFlowSeatStatusUpdateReq = new ConversationFlowSeatStatusUpdateReq();
        conversationFlowSeatStatusUpdateReq.setSeatId(seatRegisterInfo.getSeatInfo().getSeatId());
        conversationFlowSeatStatusUpdateProducer.sendMessage(conversationFlowSeatStatusUpdateReq);

        return 1;
    }

    /**
     * 核心认领留言方法
     * @param imConversation
     * @param seatRegisterInfo
     */
    private CoreActiveConversationRespBo coreActiveConversation(ImConversation imConversation, UserRegisterInfo seatRegisterInfo, ImConversationTypeEnum conversationTypeEnum) {
        /**
         * 预演：
         * 获取会话信息中的访客信息和坐席信息，判断当前访客的代理账号是否还是同一个，不是的话，需要更新代理账号，
         * 更新会话的类型和状态（处理中）
         * im部分：
         * 拉一个新群，加入坐席和代理账号（访客）
         * 然后将当前访客关联的历史会话导入到新群中
         */
        //检测用户是否已经绑定代理账号
        UserVirtualRelation bindVirtual = userVirtualRelationRepository.getRelationByOuterUserId(imConversation.getFromOuterUserId(), BindStatusEnum.BINDING.type);

        //绑定关系
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            if (ObjectUtils.isEmpty(bindVirtual)){
                //如果没有绑定关系，就需要重新绑定关系
                VirtualUserInfo notUsedVirtualInfo = virtualUserInfoRepository.getNotUsedVirtualInfo();
                if (ObjectUtils.isEmpty(notUsedVirtualInfo)) {
                    throw new BusException("没有空闲虚拟账号");
                }

                GetVirtualUserInfoReq.OuterUserInfoIem from = new GetVirtualUserInfoReq.OuterUserInfoIem();
                from.setOuterUserId(imConversation.getFromOuterUserId());
                from.setOuterUserType(imConversation.getFromOuterUserType());
                UserVirtualRelation userVirtualRelation = imInterService.returnUserVirtualRelationBean(from, notUsedVirtualInfo);
                //绑定关系，用户和聊天对象的关系
                userVirtualRelationRepository.insertSelective(userVirtualRelation);
                //更新虚拟用户状态
                virtualUserInfoRepository.updateVirtualStatus(notUsedVirtualInfo.getVirtualInfoId(), VirtualStatusEnum.USED.type);
                bindVirtual = userVirtualRelation;
            }

            // 代理账号
            String agentOuterUserId = bindVirtual.getVirtualUserId();
            // 会话对象
            String toOuterUserType = imConversation.getToOuterUserType();

            // 根据激活场景，生成会话
            ImConversation insertConversation = this.setConversationByConversationTypeEnum(imConversation, agentOuterUserId, toOuterUserType, conversationTypeEnum, seatRegisterInfo);

            //创建群聊
            String outGroupId = imInterService.returnGroupIdStr(insertConversation.getConversationId(), agentOuterUserId, seatRegisterInfo.getOuterUserId(), OuterUserClassTypeEnum.VIRTUAL.type);

            platformTransactionManager.commit(status);
            return new CoreActiveConversationRespBo(insertConversation.getConversationId(), outGroupId, bindVirtual.getVirtualUserId(), insertConversation.getFromOuterUserId(), insertConversation.getToOuterUserId(), null);
        } catch (Exception e) {
            log.error("会话处理异常，imConversation: {}", JSONObject.toJSONString(imConversation), e);
            platformTransactionManager.rollback(status);
            return null;
        }
    }

    private ImConversation setConversationByConversationTypeEnum(
            ImConversation imConversation,
            String agentOuterUserId,
            String toOuterUserType,
            ImConversationTypeEnum conversationTypeEnum,
            UserRegisterInfo seatRegisterInfo
    ) {
        ImConversation insertConversation = new ImConversation();

        switch (conversationTypeEnum) {
            case history:
                // 历史激活是新创建会话
                insertConversation = imInterService.returnImConversationBean(
                        imConversation.getFromOuterUserId(),
                        imConversation.getFromOuterUserType(),
                        agentOuterUserId,
                        toOuterUserType,
                        imConversation.getChannelConfigId());
                // 写入认领坐席
                insertConversation.setToOuterUserId(seatRegisterInfo.getOuterUserId());
                insertConversation.setConversationType(ImConversationTypeEnum.online.type);
                imConversationRepository.insertSelective(insertConversation);
                break;
            case leave:
                // 留言是更新当前会话类型
                imConversation.setConversationStatus(ConversationStatusEnum.PROCESSING.type);
                imConversation.setToOuterUserId(seatRegisterInfo.getOuterUserId());
                imConversation.setToOuterUserType(OuterUserTypeEnum.SEAT.type);
                imConversation.setAgentOuterUserId(agentOuterUserId);
                imConversation.setConversationType(ImConversationTypeEnum.online.type);
                imConversation.setUpdateTime(new Date());
                imConversationRepository.updateByPrimaryKeySelective(imConversation);
                insertConversation = imConversation;
                break;
            default:
                log.error("会话类型不支持，imConversation: {}", JSONObject.toJSONString(imConversation));
                throw new BusinessException("会话类型不支持");
        }

        //日志
        ImConversationLog imConversationLog = new ImConversationLog();
        BeanUtils.copyProperties(imConversation, imConversationLog);
        imConversationLogRepository.insertSelective(imConversationLog);

        // 写入激活记录
        ImConversationActiveLog imConversationActiveLog = new ImConversationActiveLog();
        imConversationActiveLog.setFromConversationId(imConversation.getConversationId());
        imConversationActiveLog.setToConversationId(insertConversation.getConversationId());
        imConversationActiveLog.setActivateScene(conversationTypeEnum.type);
        imConversationActiveLogRepository.insertSelective(imConversationActiveLog);

        return insertConversation;
    }

    /**
     * 历史留言认领
     * @param req
     * @return
     */
    @Override
    public Integer historyConversationActive(HistoryConversationActiveReq req) {
        req.checkParams();
        // 获取校验坐席
        GetSeatRegisterInfoRespBo seatRegisterInfo = super.getSeatRegisterInfo(req);

        // 校验坐席状态是否可以认领留言
        List<SeatStatusFunctionMappingRespBo> seatStatusList = seatStatusFunctionMappingRepository.getSeatStatusByFunctionEnAndFunctionType(CommonConstant.SEAT_STATUS_FUNCTION_INITIATE_CONVERSATION, SeatFunctionTypeEnum.SUPPORT.type);
        if ( seatStatusList.stream().noneMatch(f -> f.getSeatStatusEn().equals(seatRegisterInfo.getSeatInfo().getCurrentSeatStatusEn())) ){
            log.error("当前坐席状态不支持激活历史留言, seatId:{}", req.getSeatId());
            throw new BusinessException("当前坐席状态不支持激活历史留言");
        }


        // 获取坐席能否认领留言
        Integer waitingCount = imConversationRepository.selectWaitingCountByToOuterUserId(seatRegisterInfo.getUserRegisterInfo().getOuterUserId());
        if ( waitingCount >= seatRegisterInfo.getSeatInfo().getMaxWiringQuantity() ){
            log.error("坐席待处理会话已超过坐席接线上限，无法继续领取留言, seatId:{} waitingCount:{}", req.getSeatId(), waitingCount);
            throw new BusinessException("您已达最大接线会话数，暂时无法重启会话");
        }

        // 校验会话
        ImConversation imConversation = imConversationRepository.selectFirstByConversationIdAndConversationType(
                req.getConversationId(), ImConversationTypeEnum.leave.type, ConversationStatusEnum.COMPLETE.type);
        if ( Objects.isNull(imConversation) ){
            log.error("激活的历史会话不存在, conversationId:{}", req.getConversationId());
            throw new BusinessException("认领留言不存在");
        }

        // 校验是否已经激活过
        ImConversationActiveLog imConversationActiveLog = imConversationActiveLogRepository.selectByFromConversationId(req.getConversationId());
        if ( Objects.nonNull(imConversationActiveLog) ){
            log.error("历史会话正在激活中, conversationId:{}", req.getConversationId());
            throw new BusinessException("历史会话正在激活中");
        }

        // 激活历史留言
        CoreActiveConversationRespBo coreActiveConversationRespBo = this.coreActiveConversation(imConversation, seatRegisterInfo.getUserRegisterInfo(), ImConversationTypeEnum.history);
        if (Objects.nonNull(coreActiveConversationRespBo)){
            this.initImportHistoryConversation(coreActiveConversationRespBo);
        }

        // 监听会话状态
        ConversationFlowSeatStatusUpdateReq conversationFlowSeatStatusUpdateReq = new ConversationFlowSeatStatusUpdateReq();
        conversationFlowSeatStatusUpdateReq.setSeatId(seatRegisterInfo.getSeatInfo().getSeatId());
        conversationFlowSeatStatusUpdateProducer.sendMessage(conversationFlowSeatStatusUpdateReq);

        return 1;
    }

    @Resource
    private ImportHistoryConversationProducer importHistoryConversationProducer;

    /**
     * 初始化导入历史消息
     * @param coreActiveConversationRespBo
     */
    private void initImportHistoryConversation(CoreActiveConversationRespBo coreActiveConversationRespBo){
        coreActiveConversationRespBo.checkParams();

        // 初始化导入任务
        imConversationMessageHistoryImportLogRepository.insertInit(coreActiveConversationRespBo.getNewConversationId(), ConversationMessageHistoryImportStatusEnum.PROCESSING.type);

        // 导入核心方法
//        this.importHistoryConversationByVisitor(coreActiveConversationRespBo);
        importHistoryConversationProducer.sendMessage(coreActiveConversationRespBo);
    }

    /**
     * 会话历史消息导入
     * @param coreActiveConversationRespBo
     */
    @Override
    public Integer importHistoryConversationByVisitor(CoreActiveConversationRespBo coreActiveConversationRespBo) {
        // 腾讯群组id
        String outGroupId = coreActiveConversationRespBo.getOutGroupId();
        // 新会话id
        Long conversationId = coreActiveConversationRespBo.getNewConversationId();

        /**
         * 查询历史消息是否导入过
         */
        ImConversationMessageHistoryImportLog importLog = imConversationMessageHistoryImportLogRepository.selectByConversationId(conversationId);
        if ( Objects.isNull(importLog) ){
            log.info("没有会话历史消息导入任务, conversationId:{}", conversationId);
            return 1;
        }
        if (ConversationMessageHistoryImportStatusEnum.COMPLETE.type.equals(importLog.getImportStatus())){
            log.info("会话历史消息已导入, conversationId:{}", conversationId);
            return 1;
        }

        // 查询会话
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if ( Objects.isNull(imConversation) ){
            log.error("消息导入会话不存在, conversationId:{}", conversationId);
            throw new BusinessException("消息导入会话不存在");
        }
        coreActiveConversationRespBo.setVirtualUserId(imConversation.getAgentOuterUserId());
        coreActiveConversationRespBo.setFromOutUserId(imConversation.getFromOuterUserId());
        coreActiveConversationRespBo.setToOutUserId(imConversation.getToOuterUserId());

        // 获取im群组信息
        if ( StringUtils.isBlank(outGroupId) ){
            ImGroup imGroup = imGroupRepository.selectImGroupByConversationIdAndGroupStatus(conversationId);
            if ( Objects.isNull(imGroup) ){
                log.error("当前会话im群组信息异常 conversationId：{}", conversationId);
                throw new BusinessException("当前会话im群组信息异常");
            }
            outGroupId = imGroup.getOuterGroupId();
            coreActiveConversationRespBo.setOutGroupId(outGroupId);
        }

        // 代理账号id
        String virtualUserId = coreActiveConversationRespBo.getVirtualUserId();
        // 访客外部id
        String fromOutUserId = coreActiveConversationRespBo.getFromOutUserId();
        // 坐席外部id
        String toOutUserId = coreActiveConversationRespBo.getToOutUserId();

        // 历史发送成员关联信息
        List<HistoryConversationItemSenderInfoRespBo> historySender = coreActiveConversationRespBo.getHistorySender();
        if ( CollectionUtils.isEmpty(historySender) ){
            // 不传入历史发送成员，查询获取
            coreActiveConversationRespBo.setHistorySender(this.addHistorySenderMemberToGroup(coreActiveConversationRespBo));
        }

        /**
         * 预演
         * 查询访客的所有历史会话消息，格式化消息，分批多线程导入历史会话到新群中
         */
        GetMsgListByVisitorIdReqBo getMsgListByVisitorIdReqBo = new GetMsgListByVisitorIdReqBo();
        getMsgListByVisitorIdReqBo.setVisitorOutUserId(fromOutUserId);
        getMsgListByVisitorIdReqBo.setSort("asc");
        getMsgListByVisitorIdReqBo.setPageNum(1);
        getMsgListByVisitorIdReqBo.setPageSize(1);
        SimplePageInfo<ImConversationItem> msgList = this.getMsgListByVisitorId(getMsgListByVisitorIdReqBo);
        if ( CollectionUtils.isEmpty(msgList.getList()) ){
            log.info("访客没有历史会话消息, fromOutUserId:{}", fromOutUserId);
            return 1;
        }

        // 会话当前群组
        ImportMsgGroupReqBo importMsgGroupReqBo = new ImportMsgGroupReqBo();
        importMsgGroupReqBo.setGroupId(outGroupId);

        // 分批导入
        long total = msgList.getTotal();
        int pageSize = 7;
        Long pageNum = total / pageSize + (total % pageSize == 0 ? 0 : 1);
        ExecutorService executorService = new ThreadPoolExecutor(
                5, 5, 20, TimeUnit.MINUTES,
                new LinkedBlockingQueue<>(5),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        try{
            List<CompletableFuture<Integer>> totalFutureList = new ArrayList();

            for (int i = 1; i <= pageNum; i++) {
                int finalI = i;

                getMsgListByVisitorIdReqBo.setPageNum(finalI);
                getMsgListByVisitorIdReqBo.setPageSize(pageSize);
                SimplePageInfo<ImConversationItem> conversationItemList = this.getMsgListByVisitorId(getMsgListByVisitorIdReqBo);
                if ( CollectionUtils.isEmpty(conversationItemList.getList()) ){
                    log.info("没有历史会话，导入结束, fromOutUserId:{}", fromOutUserId);
                    continue;
                }

                // 单聊消息一次只能导入一条
                // 先不导入单聊消息
//                for (ImConversationItem imConversationItem : conversationItemList.getList()) {
//                    CompletableFuture<Integer> singleFuture =  CompletableFuture.supplyAsync(() -> {
//
//                        // 导入单聊消息
//                        ImportMsgToSingleBo importMsgToSingleBo = this.returnImportMsgToSingleBean(imConversationItem, fromOutUserId, virtualUserId);
//                        // 改成mq todo
//                        importMsgSingleProducer.sendMessageByImportMsgToSingleBo(importMsgToSingleBo, MqConfig.IMPORT_MSG_SINGLE_DELAY_TIME * (finalI + 1));
////                        tencentManager.importMsgToSingle(importMsgToSingleBo);
//
//                        return 1;
//                    }, executorService);
//                    totalFutureList.add(singleFuture);
//                }

                // 未分配进线会话，不需要导入群聊消息
                if ( OuterUserTypeEnum.SEAT.type.equals(imConversation.getToOuterUserType()) ){
                    // 多线程导入，群聊消息一次导7条
                    CompletableFuture<Integer> groupFuture = CompletableFuture.supplyAsync(() -> {
                        // 导入群聊消息
                        importMsgGroupReqBo.setImConversationItems(conversationItemList.getList());
                        ImportMsgToGroupBo importMsgToGroupBo = this.returnImportMsgToGroupBean(importMsgGroupReqBo, coreActiveConversationRespBo);
                        // 改成mq todo
                         importMsgGroupProducer.sendMessageByImportMsgToGroupBo(importMsgToGroupBo, MqConfig.IMPORT_MSG_SINGLE_DELAY_TIME * (finalI + 1));
//                        tencentManager.importMsgToGroup(importMsgToGroupBo);

                        return 1;
                    }, executorService);
                    totalFutureList.add(groupFuture);
                }

                // 每两百次睡1秒
                if (i % 200 == 0){
                    Thread.sleep(1000);
                }
            }

            CompletableFuture.allOf(totalFutureList.toArray(new CompletableFuture[0])).join();
            log.info("所有的子线程都结束了！");

            imConversationMessageHistoryImportLogRepository.updateImportStatus(importLog.getConversationMessageHistoryImportLogId(), ConversationMessageHistoryImportStatusEnum.COMPLETE.type, null);
            return 1;
        }catch (Exception e){
            imConversationMessageHistoryImportLogRepository.updateImportStatus(importLog.getConversationMessageHistoryImportLogId(), ConversationMessageHistoryImportStatusEnum.FAIL.type, e.getMessage());
            log.error("历史会话消息导入异常, fromOutUserId:{}", fromOutUserId, e);
            throw new BusinessException("历史会话消息导入异常");
        } finally {
            executorService.shutdown();
        }
    }

    /**
     * 会话历史成员加入群聊
     * @param coreActiveConversationRespBo
     * @return
     */
    @Override
    public List<HistoryConversationItemSenderInfoRespBo> addHistorySenderMemberToGroup(CoreActiveConversationRespBo coreActiveConversationRespBo) {
        // 查询访客的所有的进线历史会话消息
        List<ImConversation> imConversationList = imConversationRepository.selectListByFromOutUserId(coreActiveConversationRespBo.getFromOutUserId(), null);
        if ( CollectionUtils.isEmpty(imConversationList) ){
            log.info("没有历史会话，导入结束, fromOutUserId:{}", coreActiveConversationRespBo.getFromOutUserId());
            return new ArrayList<>();
        }

        // 查询历史会话关联的im群组
        List<ImGroup> imGroupList = imGroupRepository.batchSelectByConversationIdList(imConversationList.stream().map(ImConversation::getConversationId).collect(Collectors.toList()));
        if ( CollectionUtils.isEmpty(imGroupList) ){
            log.error("历史会话没有关联的im群组, conversationIdList:{}", imConversationList.stream().map(ImConversation::getConversationId).collect(Collectors.toList()));
            return new ArrayList<>();
        }

        ImGroup currentImGroup = imGroupList.stream().filter(imGroup -> imGroup.getOuterGroupId().equals(coreActiveConversationRespBo.getOutGroupId())).findFirst().orElse(null);
        if ( Objects.isNull(currentImGroup) ){
            log.error("当前会话没有关联的im群组, newConversationId:{}", coreActiveConversationRespBo.getNewConversationId());
            return new ArrayList<>();
        }

        // 获取需要加进组的虚拟账号id
        GetNeedToGroupSeatVirtualIdListRespBo needToGroupSeatVirtualIdList = this.getNeedToGroupSeatVirtualIdList(coreActiveConversationRespBo, imGroupList, currentImGroup);
        List<SeatVirtualRelation> seatVirtualRelations = needToGroupSeatVirtualIdList.getSeatVirtualRelations();
        List<String> needSeatVirtualIdList = needToGroupSeatVirtualIdList.getNeedSeatVirtualIdList();
        Boolean hasRobot = needToGroupSeatVirtualIdList.getHasRobot();

        // 是否需要添加成员
        if ( CollectionUtils.isEmpty(needSeatVirtualIdList) && hasRobot ){
            log.info("当前会话群组不需要加成员, newConversationId:{}", coreActiveConversationRespBo.getNewConversationId());
            return this.returnSenderInfoBySeatVirtualRelation(seatVirtualRelations, needToGroupSeatVirtualIdList.getRobotVirtualId());
        }

        log.info("当前会话群组需要加成员, newConversationId:{}, needSeatVirtualIdList:{}, hasRobot:{}", coreActiveConversationRespBo.getNewConversationId(), needSeatVirtualIdList, hasRobot);

        // 手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        VirtualUserInfo robotVirtualUserInfo = null;

        try{
            // 加一个机器人
            if( !hasRobot ){
                robotVirtualUserInfo = this.getRobotVirtualUserInfo(CommonConstant.DEFAULT_TENANT_ID);
                needSeatVirtualIdList.add(robotVirtualUserInfo.getOuterUserId());
            }

            // 添加群成员
            AddMemberToGroupBo addMemberToGroupBo = new AddMemberToGroupBo();
            addMemberToGroupBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            addMemberToGroupBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
            addMemberToGroupBo.setGroupId(currentImGroup.getImGroupId());
            addMemberToGroupBo.setMembers(needSeatVirtualIdList);
            Boolean result = tencentManager.addMemberToGroup(addMemberToGroupBo);
            if ( !result ){
                throw new BusinessException("向群中添加用户失败");
            }

            // 添加分组中
            List<ImGroupUserRelation> insertList = new ArrayList<>();
            for (String virtualUserId : needSeatVirtualIdList) {
                ImGroupUserRelation imGroupUserRelation = new ImGroupUserRelation();
                imGroupUserRelation.setImGroupId(currentImGroup.getImGroupId());
                imGroupUserRelation.setOuterUserId(virtualUserId);
                if ( Objects.nonNull(robotVirtualUserInfo) && robotVirtualUserInfo.getOuterUserId().equals(virtualUserId) ){
                    imGroupUserRelation.setOuterClassType(OuterUserClassTypeEnum.ROBOT.type);
                }else{
                    imGroupUserRelation.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);
                }
                imGroupUserRelation.setMemberStatus(MemberStatusEnum.IN.type);
                imGroupUserRelation.setIsDelete(CommonConstant.ZERO);
                imGroupUserRelation.setCreateTime(new Date());
                imGroupUserRelation.setUpdateTime(new Date());
                insertList.add(imGroupUserRelation);
            }
            imGroupUserRelationRepository.insertList(insertList);

            platformTransactionManager.commit(status);
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }

        // 格式化数据
        return this.returnSenderInfoBySeatVirtualRelation(seatVirtualRelations, Objects.nonNull(robotVirtualUserInfo) ? robotVirtualUserInfo.getOuterUserId() : null);
    }

    /**
     * 通过坐席机器人关系格式化关联关系
     * @param seatVirtualRelations
     * @param robotVirtualId
     * @return
     */
    private List<HistoryConversationItemSenderInfoRespBo> returnSenderInfoBySeatVirtualRelation(List<SeatVirtualRelation> seatVirtualRelations, String robotVirtualId) {
        List<HistoryConversationItemSenderInfoRespBo> historySender = new ArrayList<>();

        if( CollectionUtils.isNotEmpty(seatVirtualRelations) ){
            historySender.addAll(seatVirtualRelations.stream().map(seatVirtualRelation -> {
                HistoryConversationItemSenderInfoRespBo item = new HistoryConversationItemSenderInfoRespBo();
                item.setOuterUserId(seatVirtualRelation.getOuterUserId());
                item.setOuterUserType(OuterUserTypeEnum.SEAT.type);
                item.setVirtualUserId(seatVirtualRelation.getVirtualUserId());
                return item;
            }).collect(Collectors.toList()));
        }

        if( StringUtils.isNotBlank(robotVirtualId) ){
            HistoryConversationItemSenderInfoRespBo robotItem = new HistoryConversationItemSenderInfoRespBo();
            robotItem.setOuterUserType(OuterUserTypeEnum.ROBOT.type);
            robotItem.setVirtualUserId(robotVirtualId);
            historySender.add(robotItem);
        }

        return historySender;
    }

    /**
     * 获取需要加进组的虚拟账号id
     * @param coreActiveConversationRespBo
     * @return
     */
    private GetNeedToGroupSeatVirtualIdListRespBo getNeedToGroupSeatVirtualIdList(CoreActiveConversationRespBo coreActiveConversationRespBo, List<ImGroup> imGroupList, ImGroup currentImGroup) {
        GetNeedToGroupSeatVirtualIdListRespBo resp = new GetNeedToGroupSeatVirtualIdListRespBo();

        // 查询群组关联的坐席
        List<ImGroupUserRelation> seatRelations = imGroupUserRelationRepository.batchSelectAllByGroupIdListAndOuterClassType(
                imGroupList.stream().map(ImGroup::getImGroupId).collect(Collectors.toList()), OuterUserTypeEnum.SEAT.type);
        if ( CollectionUtils.isEmpty(seatRelations) ){
            log.info("历史会话群组没有关联坐席信息, imGroupIdList:{}", imGroupList.stream().map(ImGroup::getImGroupId).collect(Collectors.toList()));
            return resp;
        }

        // 获取历史会话坐席是否绑定虚拟账号
        List<SeatVirtualRelation> seatVirtualRelations = this.getSeatVirtualRelation(seatRelations.stream().map(ImGroupUserRelation::getOuterUserId)
                .collect(Collectors.toList()));
        if ( CollectionUtils.isEmpty(seatVirtualRelations) ){
            log.info("历史会话坐席虚拟账号查询异常, seatRelations:{}", JSONObject.toJSONString(seatRelations.stream().map(ImGroupUserRelation::getOuterUserId).collect(Collectors.toList())));
            return resp;
        }

        // 查询当前分组关联的成员
        List<ImGroupUserRelation> currentGroupSeatRelations = imGroupUserRelationRepository.getGroupUserRelationsByImGroupId(currentImGroup.getImGroupId(), null);
        // 当前会话群组是否已经存在机器人，如果有就不需要再添加
        ImGroupUserRelation robotVirtual = currentGroupSeatRelations.stream().filter(f -> OuterUserClassTypeEnum.ROBOT.type.equals(f.getOuterClassType())).findFirst().orElse(null);
        if ( Objects.nonNull(robotVirtual) ){
            resp.setHasRobot(true);
            resp.setRobotVirtualId(robotVirtual.getOuterUserId());
        }

        // 历史会话坐席虚拟账号id，剔除当前会话的坐席
        List<String> needSeatVirtualIdList = seatVirtualRelations.stream().filter(f ->
                StringUtils.isNoneBlank(coreActiveConversationRespBo.getToOutUserId()) && !coreActiveConversationRespBo.getToOutUserId().equals(f.getOuterUserId())
        ).map(SeatVirtualRelation::getVirtualUserId).collect(Collectors.toList());
        if ( CollectionUtils.isNotEmpty(needSeatVirtualIdList) ){
            // 查询当前会话群组是否已经存在坐席的虚拟账号，如果有就不需要再添加
            List<String> alreadyVirtualIdList = currentGroupSeatRelations.stream().map(ImGroupUserRelation::getOuterUserId).collect(Collectors.toList());
            needSeatVirtualIdList.removeAll(alreadyVirtualIdList);
        }

        resp.setNeedSeatVirtualIdList(needSeatVirtualIdList);
        resp.setSeatVirtualRelations(seatVirtualRelations);
        return resp;
    }

    /**
     * 获取坐席绑定的虚拟账号
     * @param seatOuterUserIdList
     * @return
     */
    private List<SeatVirtualRelation> getSeatVirtualRelation(List<String> seatOuterUserIdList) {
        List<SeatVirtualRelation> seatVirtualRelations = seatVirtualRelationRepository.batchSelectByOuterUserIdList(seatOuterUserIdList, BindStatusEnum.BINDING.type);

        // 是否存在没有绑定的坐席
        List<String> notRegisterSeat = seatOuterUserIdList.stream().filter(f ->
                !seatVirtualRelations.stream().map(SeatVirtualRelation::getOuterUserId).collect(Collectors.toList()).contains(f)
        ).collect(Collectors.toList());
        if ( CollectionUtils.isEmpty(notRegisterSeat) ){
            // 不需要绑定
            return seatVirtualRelations;
        }

        // 获取坐席注册信息
        List<UserRegisterInfo> seatRegisterInfoList = userRegisterInfoRepository.findByOuterUserIds(notRegisterSeat, UserClassEnum.SEAT.type);

        // 获取坐席信息
        List<SeatInfo> seatInfos = seatInfoRepository.selectListBySeatIdList(seatRegisterInfoList.stream().map(UserRegisterInfo::getUserId).collect(Collectors.toList()));
        if ( CollectionUtils.isEmpty(seatInfos) ){
            log.error("获取坐席信息失败, notRegisterSeat:{}", notRegisterSeat);
            throw new BusinessException("获取坐席信息失败");
        }

        // 坐席生成虚拟账号
        for (SeatInfo seatInfo : seatInfos) {
            SeatVirtualRelation seatVirtualRelation = seatService.seatRegisterVirtualAccount(seatInfo);
            seatVirtualRelations.add(seatVirtualRelation);
        }

        return seatVirtualRelations;
    }

    /**
     * 获取机器人虚拟账号
     * @return
     */
    private VirtualUserInfo getRobotVirtualUserInfo(Long tenantId) {

        // 注册im账号
        RegisterUserReqBo registerUserReqBo = new RegisterUserReqBo();
        registerUserReqBo.setTenantId(tenantId);
        registerUserReqBo.setImType(ImTypeEnum.CHAT.type);
        RegisterUserRespBo registerUserRespBo = tencentManager.registerUser(registerUserReqBo);
        if (Objects.isNull(registerUserRespBo)){
            log.error("机器人虚拟账号注册im账号失败");
            throw new BusinessException("机器人虚拟账号注册im账号失败");
        }

        // 保存虚拟账号信息
        VirtualUserInfo newVirtualUserInfo = new VirtualUserInfo();
        newVirtualUserInfo.setImId(tenantId);
        newVirtualUserInfo.setOuterUserId(registerUserRespBo.getOuterUserId());
        newVirtualUserInfo.setRegisterTime(new Date());
        newVirtualUserInfo.setUserSign(registerUserRespBo.getUserSign());
        newVirtualUserInfo.setUserSignExpireTime(DateUtils.addYears(new Date(), TencentConstant.SIGN_EXPIRE_TIME));
        newVirtualUserInfo.setStatus(VirtualStatusEnum.USED.type);
        virtualUserInfoRepository.insertSelective(newVirtualUserInfo);

        return newVirtualUserInfo;
    }

    /**
     * 组装bean
     * */
    public ImportMsgToSingleBo returnImportMsgToSingleBean(ImConversationItem imConversationItem, String fromOutUserId, String newVirtualUserId){
        ImportMsgToSingleBo importMsgToSingleBo = new ImportMsgToSingleBo();
        importMsgToSingleBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        importMsgToSingleBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        importMsgToSingleBo.setSyncFromOldSystem(5);
        // 发送方是访客（用户，师傅等等）, 挂到访客外部id上
        if ( fromOutUserId.equals(imConversationItem.getFromOuterUserId()) ){
            importMsgToSingleBo.setFromAccount(fromOutUserId);
            importMsgToSingleBo.setToAccount(newVirtualUserId);
        }else{
            // 接收方是访客，发送方是虚拟账号
            importMsgToSingleBo.setFromAccount(newVirtualUserId);
            importMsgToSingleBo.setToAccount(fromOutUserId);
        }
        importMsgToSingleBo.setMsgSeq(imConversationItem.getMsgSeq());
        importMsgToSingleBo.setMsgRandom(imConversationItem.getMsgSeq());
        importMsgToSingleBo.setMsgTimeStamp(imConversationItem.getMsgSendTime().getTime()/1000L);

        List<ImportMsgToSingleBo.MsgBodyItem> msgBodyItems = JSONArray.parseArray(imConversationItem.getMsgContent(), ImportMsgToSingleBo.MsgBodyItem.class);

        importMsgToSingleBo.setMsgBody(msgBodyItems);

        return importMsgToSingleBo;
    }

    /**
     * 组装bean
     * */
    public ImportMsgToGroupBo returnImportMsgToGroupBean(ImportMsgGroupReqBo ImportMsgGroupReqBo, CoreActiveConversationRespBo conversationRespBo){
        // 新会话id
        Long conversationId = conversationRespBo.getNewConversationId();
        // 代理账号id
        String virtualUserId = conversationRespBo.getVirtualUserId();
        // 坐席外部id
        String toOutUserId = conversationRespBo.getToOutUserId();
        // 历史发送成员关联信息
        List<HistoryConversationItemSenderInfoRespBo> historySender = conversationRespBo.getHistorySender();

        String robotVirtualId = historySender.stream().filter(f -> OuterUserClassTypeEnum.ROBOT.type.equals(f.getOuterUserType()))
                .map(HistoryConversationItemSenderInfoRespBo::getVirtualUserId).findFirst().orElse(null);
        if ( Objects.isNull(robotVirtualId) ){
            log.error("历史会话没有机器人虚拟账号, conversationId:{}", conversationId);
            robotVirtualId = toOutUserId;
        }

        ImportMsgToGroupBo importMsgToGroupBo = new ImportMsgToGroupBo();
        importMsgToGroupBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        importMsgToGroupBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        importMsgToGroupBo.setGroupId(ImportMsgGroupReqBo.getGroupId());
        importMsgToGroupBo.setRecentContactFlag(1);
        List<ImportMsgToGroupBo.MsgListItem> MsgList = new ArrayList<>();
        for (ImConversationItem imConversationItem : ImportMsgGroupReqBo.getImConversationItems()) {
            ImportMsgToGroupBo.MsgListItem MsgListItem = new ImportMsgToGroupBo.MsgListItem();

            OuterUserTypeEnum enumByType = OuterUserTypeEnum.getEnumByType(imConversationItem.getFromOuterUserType());
            if (enumByType != null) {
                switch (enumByType){
                    case MERCHANT:
                    case MASTER:
                    case CLIENT:
                        // 发送方是访客（用户，师傅等等）, 挂到代理账号id上
                        MsgListItem.setFrom_Account(virtualUserId);
                        break;
                    case VIRTUAL:
                        /**
                         * 发送方是代理账号（virtual）
                         * 先判断是否是会话接线坐席
                         * 是 挂导接线坐席外部id上
                         * 否 挂导消息坐席的虚拟账号id上
                         */
                        if ( toOutUserId.equals(imConversationItem.getResponseOuterUserId()) ){
                            MsgListItem.setFrom_Account(toOutUserId);
                        }else{

                            HistoryConversationItemSenderInfoRespBo historySenderItem = historySender.stream().filter(f -> imConversationItem.getResponseOuterUserId().equals(f.getOuterUserId())).findFirst().orElse(null);
                            if ( Objects.nonNull(historySenderItem) ){
                                // 挂到历史会话坐席的虚拟账号id上
                                MsgListItem.setFrom_Account(historySenderItem.getVirtualUserId());
                            }else{
                                log.error("历史会话没有坐席虚拟账号, imConversationItem:{}, responseOuterUserId:{}", JSONObject.toJSONString(imConversationItem), imConversationItem.getResponseOuterUserId());
                                // 异常场景，先挂到坐席身上
                                MsgListItem.setFrom_Account(toOutUserId);
                            }
                        }

                        break;
                    default:
                        // 发送方是机器人robot、系统 挂到机器人虚拟账号上
                        MsgListItem.setFrom_Account(robotVirtualId);
                        break;
                }
            }else{
                log.error("历史会话消息发送方类型异常, imConversationItem:{}", JSONObject.toJSONString(imConversationItem));
                MsgListItem.setFrom_Account(robotVirtualId);
            }
            // 群聊时间有限制，设置为建群时间
//            MsgListItem.setSendTime(createGroupTime.getTime()/1000L + 1);
            // 群聊时间有限制，只能为当前时间
            MsgListItem.setSendTime((new Date()).getTime()/1000L);
//            MsgListItem.setSendTime(imConversationItem.getMsgSendTime().getTime()/1000L);
            MsgListItem.setRandom(MathUtil.generateCustomerLength(32));
            MsgListItem.setMsgSeq(imConversationItem.getMsgSeq());
            List<ImportMsgToGroupBo.MsgBodyItem> MsgBody = JSONArray.parseArray(imConversationItem.getMsgContent(), ImportMsgToGroupBo.MsgBodyItem.class);
            MsgListItem.setMsgBody(MsgBody);
            MsgList.add(MsgListItem);
        }

        importMsgToGroupBo.setMsgList(MsgList);

        return importMsgToGroupBo;
    }

    /**
     * 历史会话历史消息记录
     */
    @Override
    public SimplePageInfo<GetHistoryConversationItemResp> getHistoryConversationItemLogs(GetHistoryConversationItemListReq req) {
        SimplePageInfo<GetHistoryConversationItemResp> resp = new SimplePageInfo<>();

        // 查询会话信息
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getConversationId());
        if ( Objects.isNull(imConversation) ){
            log.error("历史会话不存在, conversationId:{}", req.getConversationId());
            throw new BusinessException("历史会话不存在");
        }

        // 查询访客的所有的历史会话消息总数
        GetMsgListByVisitorIdReqBo getMsgListByVisitorIdReqBo = new GetMsgListByVisitorIdReqBo();
        getMsgListByVisitorIdReqBo.setVisitorOutUserId(imConversation.getFromOuterUserId());
        getMsgListByVisitorIdReqBo.setPageNum(1);
        getMsgListByVisitorIdReqBo.setPageSize(1);
        SimplePageInfo<ImConversationItem> msgList = this.getMsgListByVisitorId(getMsgListByVisitorIdReqBo);
        if ( CollectionUtils.isEmpty(msgList.getList()) ){
            log.info("访客没有历史会话消息, fromOutUserId:{}", imConversation.getFromOuterUserId());
            return resp;
        }

        List<String> visitorOuterUserIdList = new ArrayList<>();
        visitorOuterUserIdList.addAll(msgList.getList().stream().map(ImConversationItem::getFromOuterUserId).filter(StringUtils::isNoneBlank).collect(Collectors.toList()));
        visitorOuterUserIdList.addAll(msgList.getList().stream().map(ImConversationItem::getToOuterUserId).filter(StringUtils::isNoneBlank).collect(Collectors.toList()));
        visitorOuterUserIdList.addAll(msgList.getList().stream().map(ImConversationItem::getResponseOuterUserId).filter(StringUtils::isNoneBlank).collect(Collectors.toList()));

        // 获取访客内部信息
        GetVisitorInnerInfoReqBo getVisitorInnerInfoReqBo = new GetVisitorInnerInfoReqBo();
        getVisitorInnerInfoReqBo.setOuterUserIdList(visitorOuterUserIdList);
        List<VisitorInnerInfoBo> visitorInnerInfoBoList = this.getVisitorInnerInfoBoList(getVisitorInnerInfoReqBo);

        List<GetHistoryConversationItemResp> list = msgList.getList().stream().map(imConversationItem -> {
            GetHistoryConversationItemResp item = new GetHistoryConversationItemResp();
            item.setSendTime(imConversationItem.getMsgSendTime());

            // 获取发送人信息
            VisitorInnerInfoBo conversationMsgSender = this.getConversationMsgSender(imConversationItem, visitorInnerInfoBoList, null);
            item.setSendObjectFrom(Arrays.asList(OuterUserTypeEnum.SEAT.type, OuterUserTypeEnum.ROBOT.type, OuterUserTypeEnum.SYSTEM.type).contains(conversationMsgSender.getUserClass())
                    ? OuterUserTypeEnum.SEAT.type : CommonConstant.SEND_OBJECT_VISITOR);
            item.setSendOutUserId(conversationMsgSender.getOuterUserId());
            item.setSendOutUserName(conversationMsgSender.getUserName());
            item.setSendOutUserType(conversationMsgSender.getUserClass());
            item.setSendOutUserFaceUrl(conversationMsgSender.getUserFaceUrl());

            // 消息体
            List<MsgBodyItemBo> msgBodyItemBos = super.getMsgContent(imConversationItem.getMsgContent());
            item.setMsgBody(msgBodyItemBos);
            return item;
        }).collect(Collectors.toList());


        resp.setList(list);
        resp.setPages(msgList.getPages());
        resp.setTotal(msgList.getTotal());
        resp.setPageNum(msgList.getPageNum());
        resp.setPageSize(msgList.getPageSize());
        return resp;
    }

    /**
     * 获取访客会话消息记录
     * @param req
     * @return
     */
    public SimplePageInfo<ImConversationItem> getMsgListByVisitorId(GetMsgListByVisitorIdReqBo req) {
        SimplePageInfo<ImConversationItem> resp = new SimplePageInfo<>();

        // 查询访客的所有的进线历史会话消息
        List<ImConversation> imConversationList = imConversationRepository.selectListByFromOutUserId(req.getVisitorOutUserId(), null);
        if ( CollectionUtils. isEmpty(imConversationList) ){
            log.info("访客没有历史会话消息, fromOutUserId:{}", req.getVisitorOutUserId());
            return resp;
        }

        // 查询访客的所有的历史会话消息总数
        Page<Object> page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<ImConversationItem> msgList = imConversationItemRepository.selectListByConversationIdList(imConversationList.stream().map(ImConversation::getConversationId).collect(Collectors.toList()), req.getSort());

        resp.setList(msgList);
        resp.setPages(page.getPages());
        resp.setTotal(page.getTotal());
        resp.setPageNum(page.getPageNum());
        resp.setPageSize(page.getPageSize());
        return resp;
    }

    @Resource
    private SocketBaseManager socketBaseManager;

    /**
     * 测试关闭会话
     * @param req
     */
    public void closeConversation(SendImWebSocketMessageBo req){
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getConversationId());
        if ( Objects.isNull(imConversation) ){
            log.error("会话不存在, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话不存在");
        }

        if ( ConversationStatusEnum.COMPLETE.type.equals(imConversation.getConversationStatus()) ){
            log.info("会话已经完结, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话已经完结");
        }

        // 会话关闭
        ImConversation update = new ImConversation();
        update.setConversationId(req.getConversationId());
        update.setConversationStatus(ConversationStatusEnum.COMPLETE.type);
        update.setConversationType(ImConversationTypeEnum.history.type);
        update.setCompleteTime(new Date());
        imConversationRepository.updateByPrimaryKeySelective(update);

        // 查询激活日志是否有该会话
        imConversationActiveLogRepository.deleteLogByConversationId(req.getConversationId());

        // 获取会话群组信息
        ImGroup imGroup = imGroupRepository.selectImGroupByConversationIdAndGroupStatus(req.getConversationId());
        if ( Objects.nonNull(imGroup) ){
            // 关闭会话
            DestroyGroupBo destroyGroupBo = new DestroyGroupBo();
            destroyGroupBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            destroyGroupBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
            destroyGroupBo.setGroupId(imGroup.getOuterGroupId());
            tencentManager.destroyGroup(destroyGroupBo);
        }

        // 发送socketmq
        SendImWebSocketMessageBo sendImWebSocketMessageBo = new SendImWebSocketMessageBo();
        sendImWebSocketMessageBo.setConversationId(req.getConversationId());
        sendImWebSocketMessageBo.setBusinessType(SocketBaseManagerImpl.CONVERSATION_CLOSE);
        socketBaseManager.consumerMessage(sendImWebSocketMessageBo);

        // 发送更新坐席状态mq
        if ( OuterUserTypeEnum.SEAT.type.equals(imConversation.getToOuterUserType()) ){
            ConversationFlowSeatStatusUpdateReq conversationFlowSeatStatusUpdateReq = new ConversationFlowSeatStatusUpdateReq();
            conversationFlowSeatStatusUpdateReq.setSeatOuterUserId(imConversation.getToOuterUserId());
            conversationFlowSeatStatusUpdateProducer.sendMessage(conversationFlowSeatStatusUpdateReq);
        }
    }

    /**、
     * 历史会话变成留言会话
     */
    public void historyToLeaveConversation(SendImWebSocketMessageBo req){
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getConversationId());
        if ( !ConversationStatusEnum.COMPLETE.type.equals(imConversation.getConversationStatus()) ){
            log.info("会话已经关闭, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话未完成，不能转换成其他类型会话");
        }

        if ( req.getBusinessType().equals(ImConversationTypeEnum.leave.type) ){
            ImConversation update = new ImConversation();
            update.setConversationId(req.getConversationId());
            update.setConversationType(ImConversationTypeEnum.leave.type);
            imConversationRepository.updateByPrimaryKeySelective(update);
        }

        if ( req.getBusinessType().equals(ImConversationTypeEnum.history.type) ){
            ImConversation update = new ImConversation();
            update.setConversationId(req.getConversationId());
            update.setConversationType(ImConversationTypeEnum.history.type);
            imConversationRepository.updateByPrimaryKeySelective(update);
        }
    }

    /**
     * 会话分配
     * @param req
     */
    public void conversationAssign(ConversationAssignBo req){
        // 查询用户注册信息
        UserRegisterInfo userRegisterInfo = userRegisterInfoRepository.selectByOuterUserIdAndUserClass(req.getOuterUserId(), UserClassEnum.SEAT.type);
        if ( Objects.isNull(userRegisterInfo) ){
            log.error("会话分配失败，坐席未注册, outerUserId:{}", req.getOuterUserId());
            throw new BusinessException("会话分配失败，坐席未注册");
        }

        // 查询会话信息
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getConversationId());
        if ( Objects.isNull(imConversation) ){
            log.error("会话分配失败，会话不存在, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话分配失败，会话不存在");
        }

        if ( OuterUserTypeEnum.SEAT.type.equals(imConversation.getToOuterUserType()) ){
            // 会话已经分配给坐席，不需要再次分配
            log.info("会话已经分配坐席, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话已经分配坐席");
        }

        ImConversation update = new ImConversation();
        update.setConversationId(req.getConversationId());
        update.setToOuterUserType(OuterUserTypeEnum.SEAT.type);
        update.setToOuterUserId(req.getOuterUserId());
        imConversationRepository.updateByPrimaryKeySelective(update);
    }

    /**
     * 更新会话已读状态
     * @param req
     * @return
     */
    public Integer updateConversationRead(SendImWebSocketMessageBo req){

        // 查询会话信息
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getConversationId());
        if ( Objects.isNull(imConversation) ){
            log.error("会话不存在, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话不存在");
        }

        // 更新已读
        Integer integer = imConversationItemRepository.updateConversationRead(req.getConversationId());

        // 发送socketmq
        SendImWebSocketMessageBo sendImWebSocketMessageBo = new SendImWebSocketMessageBo();
        sendImWebSocketMessageBo.setConversationId(req.getConversationId());
        sendImWebSocketMessageBo.setBusinessType(SocketBaseManagerImpl.WAIT_CONVERSATION_UPDATE);
        socketBaseManager.consumerMessage(sendImWebSocketMessageBo);
//        socketBaseManager.sendWaitConversationUpdate(req.getConversationId());

        return integer;
    }
}
