package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.wanshifu.iop.im.api.resp.tencent.TencentCallBackResp;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.service.OpenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class OpenServiceImpl implements OpenService {

    @Resource
    private TencentManager tencentManager;



    @Override
    public TencentCallBackResp tencentCallBack(Object object, String callbackType, Integer sdkAppId) {
        log.info("tencentCallBack:{}", JSON.toJSONString(object));
        return tencentManager.tencentCallBack(object, callbackType);
    }
}
