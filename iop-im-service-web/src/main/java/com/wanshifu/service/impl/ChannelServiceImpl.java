package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.constant.ChannelConstant;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.CommonRedisConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.account.domain.resp.account.AccountInfoListResp;
import com.wanshifu.iop.im.api.req.channel.BatchEditChannelReq;
import com.wanshifu.iop.im.api.req.channel.ChannelEnumsReq;
import com.wanshifu.iop.im.api.req.channel.ChannelSearchListReq;
import com.wanshifu.iop.im.api.req.channel.ChannelUserProblemAddReq;
import com.wanshifu.iop.im.api.req.channel.ChannelUserProblemChangeStatusReq;
import com.wanshifu.iop.im.api.req.channel.ChannelUserProblemClassReq;
import com.wanshifu.iop.im.api.req.channel.ChannelUserProblemDelReq;
import com.wanshifu.iop.im.api.req.channel.ClientCategoryCreateReq;
import com.wanshifu.iop.im.api.req.channel.ClientCategoryUpdateReq;
import com.wanshifu.iop.im.api.req.channel.CreateChannelReq;
import com.wanshifu.iop.im.api.req.channel.CreateConversationStyleConfigRqt;
import com.wanshifu.iop.im.api.req.channel.CreateDraftReq;
import com.wanshifu.iop.im.api.req.channel.CreateOrEditAutoReplyConfigReq;
import com.wanshifu.iop.im.api.req.channel.CreateOrEditSatisfactionLevelConfigReq;
import com.wanshifu.iop.im.api.req.channel.CreateStyleAndSideBarReq;
import com.wanshifu.iop.im.api.req.channel.DeleteChannelReq;
import com.wanshifu.iop.im.api.req.channel.DeleteClientCategoryReq;
import com.wanshifu.iop.im.api.req.channel.DeployClientCategoryAndChannelReq;
import com.wanshifu.iop.im.api.req.channel.EditStyleAndSideBarReq;
import com.wanshifu.iop.im.api.req.channel.GetAutoReplyConfigReq;
import com.wanshifu.iop.im.api.req.channel.GetChannelConfigDraftListRqt;
import com.wanshifu.iop.im.api.req.channel.GetSatisfactionLevelConfigReq;
import com.wanshifu.iop.im.api.req.channel.GetStyleAndSideBarReq;
import com.wanshifu.iop.im.api.req.channel.IconUploadReq;
import com.wanshifu.iop.im.api.req.channel.PreviewClientCategoryAndChannelReq;
import com.wanshifu.iop.im.api.req.channel.SwitchChannelStatusReq;
import com.wanshifu.iop.im.api.req.channel.UpdateChannelReq;
import com.wanshifu.iop.im.api.req.channel.UpdateConversationStyleConfigRqt;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.api.resp.ImgUploadResp;
import com.wanshifu.iop.im.api.resp.channel.ChannelEnumsResp;
import com.wanshifu.iop.im.api.resp.channel.ChannelSearchListResp;
import com.wanshifu.iop.im.api.resp.channel.ChannelUserProblemResp;
import com.wanshifu.iop.im.api.resp.channel.GetAutoReplyConfigResp;
import com.wanshifu.iop.im.api.resp.channel.GetChannelConfigDraftListResp;
import com.wanshifu.iop.im.api.resp.channel.GetSatisfactionLevelConfigResp;
import com.wanshifu.iop.im.api.resp.channel.GetStyleAndSideBarResp;
import com.wanshifu.iop.im.api.resp.channel.PreviewClientCategoryAndChannelResp;
import com.wanshifu.iop.im.api.resp.channel.SwitchChannelStatusResp;
import com.wanshifu.iop.im.domain.bo.ChannelFlowBo;
import com.wanshifu.iop.im.domain.bo.ConversationStyleConfigV2Bo;
import com.wanshifu.iop.im.domain.bo.CreateConversationStyleConfigBo;
import com.wanshifu.iop.im.domain.bo.channel.AllConfigJsonBo;
import com.wanshifu.iop.im.domain.bo.channel.ChannelBo;
import com.wanshifu.iop.im.domain.bo.channel.ClientCategoryBo;
import com.wanshifu.iop.im.domain.bo.channel.ConversationAutoReplyConfigBo;
import com.wanshifu.iop.im.domain.bo.channel.ConversationAutoReplyConfigBoV2;
import com.wanshifu.iop.im.domain.bo.channel.ConversationAutoReplyDetailConfigBo;
import com.wanshifu.iop.im.domain.bo.channel.ConversationStyleConfigBo;
import com.wanshifu.iop.im.domain.bo.channel.ConversationThemeConfigBo;
import com.wanshifu.iop.im.domain.bo.channel.SatisfactionLevelConfigBo;
import com.wanshifu.iop.im.domain.builder.ChannelBoBuilder;
import com.wanshifu.iop.im.domain.enums.EnableStatusEnum;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.enums.TriggerModeEnum;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyMsgSubTypeEnum;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyTypeEnum;
import com.wanshifu.iop.im.domain.enums.channel.ChannelTypeEnum;
import com.wanshifu.iop.im.domain.enums.channel.ClientCategoryTypeEnum;
import com.wanshifu.iop.im.domain.enums.channel.TemplateInitJsonEnum;
import com.wanshifu.iop.im.domain.po.FlowDefine;
import com.wanshifu.iop.im.domain.po.UserProblemClass;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.iop.im.domain.po.channel.ClientCategoryConfig;
import com.wanshifu.iop.im.domain.po.channel.ClientChannelConfigDraft;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyDetailConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationStyleConfig;
import com.wanshifu.iop.im.domain.po.channel.SatisfactionEvaluateRecord;
import com.wanshifu.iop.im.domain.po.channel.TemplateInitJson;
import com.wanshifu.repository.channel.ChannelConfigRepository;
import com.wanshifu.repository.channel.ClientCategoryConfigRepository;
import com.wanshifu.repository.channel.ClientChannelConfigDraftRepository;
import com.wanshifu.repository.channel.ConversationAutoReplyConfigRepository;
import com.wanshifu.repository.channel.ConversationAutoReplyDetailConfigRepository;
import com.wanshifu.repository.channel.ConversationStyleConfigRepository;
import com.wanshifu.repository.channel.ConversationThemeConfigRepository;
import com.wanshifu.repository.channel.SatisfactionEvaluateRecordRepository;
import com.wanshifu.repository.channel.SatisfactionLevelConfigRepository;
import com.wanshifu.repository.channel.TemplateInitJsonRepository;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.ChannelService;
import com.wanshifu.utils.BaseUtil;
import com.wanshifu.utils.UploadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 端侧+渠道管理
 * <AUTHOR>
 * @date： 2025-06-04 13:40:16
 */
@Service
@Slf4j
public class ChannelServiceImpl extends AbstractService implements ChannelService {


    @Resource
    private ClientCategoryConfigRepository clientCategoryConfigRepository;

    @Resource
    private ChannelConfigRepository channelConfigRepository;

    @Resource
    private ClientChannelConfigDraftRepository clientChannelConfigDraftRepository;

    @Resource
    private ConversationAutoReplyConfigRepository conversationAutoReplyConfigRepository;

    @Resource
    private ConversationAutoReplyDetailConfigRepository conversationAutoReplyDetailConfigRepository;

    @Resource
    private ConversationStyleConfigRepository conversationStyleConfigRepository;

    @Resource
    private ConversationThemeConfigRepository conversationThemeConfigRepository;

    @Resource
    private SatisfactionEvaluateRecordRepository satisfactionEvaluateRecordRepository;

    @Resource
    private SatisfactionLevelConfigRepository satisfactionLevelConfigRepository;

    @Resource
    private TemplateInitJsonRepository templateInitJsonRepository;

    @Resource
    private UploadUtil uploadFileUtil;

    /**
     * 端侧+渠道枚举
     *
     */
    @Override
    public ChannelEnumsResp enums(ChannelEnumsReq req) {
        ChannelEnumsResp resp = new ChannelEnumsResp();
        List<CommonLabelValueResp> clientCategoryTypeEnumList = Arrays.stream(ClientCategoryTypeEnum.values()).map(item -> {
            CommonLabelValueResp labelValueResp = new CommonLabelValueResp();
            labelValueResp.setLabel(item.name);
            labelValueResp.setValue(item.type);
            return labelValueResp;
        }).collect(Collectors.toList());
        resp.setClientCategoryTypeEnumList(clientCategoryTypeEnumList);

        List<CommonLabelValueResp> channelTypeEnumList = Arrays.stream(ChannelTypeEnum.values()).map(item -> {
            CommonLabelValueResp labelValueResp = new CommonLabelValueResp();
            labelValueResp.setLabel(item.name);
            labelValueResp.setValue(item.type);
            return labelValueResp;
        }).collect(Collectors.toList());
        resp.setChannelTypeEnumList(channelTypeEnumList);

        List<CommonLabelValueResp> statusEnumList = Arrays.stream(StatusEnum.values()).map(item -> {
            CommonLabelValueResp labelValueResp = new CommonLabelValueResp();
            labelValueResp.setLabel(item.name);
            labelValueResp.setValue(""+item.type);
            return labelValueResp;
        }).collect(Collectors.toList());
        resp.setStatusEnumList(statusEnumList);
        return resp;
    }

    /**
     * 端侧+渠道列表 查询草稿表
     *
     *
     */
    @Override
    public ChannelSearchListResp searchList(ChannelSearchListReq req) {
        ChannelSearchListResp resp = new ChannelSearchListResp();
        // 查询端侧列表信息
        List<ClientChannelConfigDraft> clientChannelConfigDraftList = clientChannelConfigDraftRepository.selectClientChannelConfigDraftList();

        if(CollectionUtils.isEmpty(clientChannelConfigDraftList)) {
            return resp;
        }
        //特殊处理，移除会话全局配置展示
//        for(ClientChannelConfigDraft clientChannelConfigDraft : clientChannelConfigDraftList){
//            if(ChannelConstant.CONVERSATION_GLOBAL_CONFIG.equals(clientChannelConfigDraft.getClientCategoryEn())){
//                clientChannelConfigDraftList.removeIf(f-> f.getClientChannelConfigDraftId().equals(clientChannelConfigDraft.getClientChannelConfigDraftId()));
//            }
//        }
        // 特殊处理，移除会话全局配置展示
        clientChannelConfigDraftList = clientChannelConfigDraftList.stream()
                .filter(draft -> !ChannelConstant.CONVERSATION_GLOBAL_CONFIG.equals(draft.getClientCategoryEn()))
                .collect(Collectors.toList());

        //列表初始化选首个端侧
        Long clientCategoryId = BaseUtil.hasLongValue(req.getClientCategoryId()) ? req.getClientCategoryId(): clientChannelConfigDraftList.get(CommonConstant.ZERO).getClientChannelConfigDraftId();

        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftList.stream().filter(f-> f.getClientChannelConfigDraftId().equals(clientCategoryId)).findFirst().orElse(null);
        if(Objects.nonNull(clientChannelConfigDraft)) {
            AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(clientChannelConfigDraft.getAllConfigJson());
            if(Objects.nonNull(allConfigJsonBo)) {
                List<ChannelBo> channelConfigBoList = allConfigJsonBo.getChannelConfigBoList();
                // 填充操作人名字
                fillOperatorNameChannelConfigBoList(channelConfigBoList);
                if(CollectionUtils.isNotEmpty(channelConfigBoList)) {

                    List<Long> channelIdList = channelConfigBoList.stream().map(ChannelBo::getFlowDefineId).collect(Collectors.toList());

                    List<ChannelFlowBo> channelFlowBoList = this.getChannelFlowBoList(channelIdList);

                    List<ChannelBo> channelConfigBoList1 = this.getChannelConfigBoList(channelConfigBoList, channelFlowBoList);
                    resp.setChannelBoList(channelConfigBoList1);
                }
            }
        }
        List<ClientCategoryBo> clientCategoryBoList = ChannelBoBuilder.buildClientCategoryConfigBoList(clientChannelConfigDraftList);
        resp.setClientCategoryBoList(clientCategoryBoList);
        return resp;
    }

    /**
     * 组装返回数据，加上路由名称
     * */
    private List<ChannelBo> getChannelConfigBoList(List<ChannelBo> channelConfigBoList, List<ChannelFlowBo> channelFlowBoList){
        for (ChannelBo channelBo : channelConfigBoList) {
            Optional<ChannelFlowBo> first = channelFlowBoList.stream().filter(f -> f.getFlowDefineId().equals(channelBo.getFlowDefineId())).findFirst();
            if(first.isPresent()){
                channelBo.setFlowName(first.get().getFlowName());
                channelBo.setFlowDefineId(first.get().getFlowDefineId());
            }
        }
        return channelConfigBoList;
    }

    /**
     * 根据channelId查询关联的路由导航id
     * */
    private List<ChannelFlowBo> getChannelFlowBoList(List<Long> flowIdList){

        if(CollectionUtils.isEmpty(flowIdList)) {
            return new ArrayList<>();
        }


        List<FlowDefine> flowDefineByFlowIds = flowDefineRepository.getFlowDefineByFlowIds(flowIdList);

        List<ChannelFlowBo> result = new ArrayList<>();

        for (Long flowId : flowIdList) {
            ChannelFlowBo channelFlowBo = new ChannelFlowBo();


            Optional<FlowDefine> first = flowDefineByFlowIds.stream().filter(f -> f.getFlowDefineId().equals(flowId)).findFirst();
            if(first.isPresent()){
                channelFlowBo.setFlowDefineId(first.get().getFlowDefineId());
                channelFlowBo.setFlowName(first.get().getFlowName());
                result.add(channelFlowBo);
            }
        }
        return result;
    }




    /**
     * 新增端侧  先insert草稿表
     * 校验端侧标识和名称的重复在新增之前就调用
     */
    @Override
    public Long createClientCategory(ClientCategoryCreateReq req) {
        String key = String.format(CommonRedisConstant.CREATE_CLIENT_CATEGORY_PREFIX, req.getClientCategoryName());

        String s = redisHelper.get(key);
        if (StringUtils.isNotEmpty(s)) {
            throw new BusException("请勿短时间内重复操作");
        }
        redisHelper.set(key,"1", CommonRedisConstant.REPEAT_OPERATE_TIME);

        ClientChannelConfigDraft draftName = clientChannelConfigDraftRepository.checkClientChannelConfigName(req.getClientCategoryConfigId(), req.getClientCategoryName());
        if(Objects.nonNull(draftName)) {
            throw new BusinessException("该渠道名与系统内渠道名重复，不可添加");
        }
        ClientChannelConfigDraft draftEn = clientChannelConfigDraftRepository.checkClientChannelConfigEn(req.getClientCategoryConfigId(), req.getClientCategoryEn());
        if(Objects.nonNull(draftEn)) {
            throw new BusinessException("该渠道名与系统内渠道名重复，不可添加");
        }

        req.setClientCategoryConfigId(super.getGuid());
        ClientChannelConfigDraft clientChannelConfigDraft = ChannelBoBuilder.buildClientChannelConfigDraft(req);
        clientChannelConfigDraftRepository.insertSelective(clientChannelConfigDraft);
        return clientChannelConfigDraft.getClientChannelConfigDraftId();
    }

    /**
     * 编辑端侧  先更新草稿表
     *
     */
    @Override
    public Long editClientCategory(ClientCategoryUpdateReq req) {
        ClientChannelConfigDraft selfConfigDraft =  clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryConfigId());
        if(Objects.isNull(selfConfigDraft)) {
            throw new BusinessException("编辑的端侧ID不存在");
        }

        ClientChannelConfigDraft draftName = clientChannelConfigDraftRepository.checkClientChannelConfigName(null, req.getClientCategoryName());
        // 端侧名已有记录
        if(Objects.nonNull(draftName)) {
            // 端侧ID不一致属于重名
            if(!draftName.getClientChannelConfigDraftId().equals(req.getClientCategoryConfigId())) {
                throw new BusinessException("该渠道名与系统内渠道名重复，不可编辑");
            }
        }

//        ClientChannelConfigDraft draftEn = clientChannelConfigDraftRepository.checkClientChannelConfigEn(null, req.getClientCategoryEn());
//        if(Objects.nonNull(draftEn)) {
//            // 端侧ID不一致属于标识重复 标识重复就添加一个后缀
//            if(!draftEn.getClientChannelConfigDraftId().equals(req.getClientCategoryConfigId())) {
//
//            }
//        }

        ClientChannelConfigDraft clientChannelConfigDraft = ChannelBoBuilder.buildClientChannelConfigDraft(req);
        int updateCount = clientChannelConfigDraftRepository.updateByPrimaryKeySelective(clientChannelConfigDraft);
        assert updateCount > 0;
        return clientChannelConfigDraft.getClientChannelConfigDraftId();
    }

    /**
     * 删除端侧
     *
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long deleteClientCategory(DeleteClientCategoryReq req) {
        ClientChannelConfigDraft beforeDraft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(beforeDraft)) {
            throw new BusinessException("删除的端侧不存在！");
        }

        AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(beforeDraft.getAllConfigJson());
        if (Objects.nonNull(allConfigJsonBo) && CollectionUtils.isNotEmpty(allConfigJsonBo.getChannelConfigBoList())) {
            boolean anyActive = allConfigJsonBo.getChannelConfigBoList().stream()
                    .anyMatch(channelBo -> CommonConstant.ZERO.equals(channelBo.getStatus()));
            if (anyActive) {
                throw new BusinessException("所有入口都被禁用后，渠道才能被删除");
            }
        }
        ClientChannelConfigDraft updateClientChannelConfigDraft =  new ClientChannelConfigDraft();
        updateClientChannelConfigDraft.setClientChannelConfigDraftId(req.getClientCategoryId());
        updateClientChannelConfigDraft.setIsDelete(CommonConstant.ONE);
        updateClientChannelConfigDraft.setOperatorId(req.getOperatorId());
        updateClientChannelConfigDraft.setUpdateTime(new Date());
        int updateCount = clientChannelConfigDraftRepository.updateByPrimaryKeySelective(updateClientChannelConfigDraft);
        assert updateCount > 0;

        // 同步更新正式表状态
        ClientCategoryConfig updateClientCategoryConfig = new ClientCategoryConfig();
        updateClientCategoryConfig.setClientCategoryConfigId(req.getClientCategoryId());
        updateClientCategoryConfig.setIsDelete(CommonConstant.ONE);
        clientCategoryConfigRepository.updateByPrimaryKeySelective(updateClientCategoryConfig);
        return CommonConstant.LONG_ONE;
    }

    /**
     * 通过端侧ID查询渠道列表  查询草稿表
     *
     */
    @Override
    public List<ChannelBo> channelList(ChannelSearchListReq req) {
        List<ChannelBo> channelBoList = new ArrayList<>();

        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());

        // 没有数据
        if(Objects.isNull(clientChannelConfigDraft)) {
            return channelBoList;
        }

        String allConfigJson = clientChannelConfigDraft.getAllConfigJson();
        AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(allConfigJson);
        if(Objects.isNull(allConfigJsonBo)) {
            return channelBoList;
        }
        List<ChannelBo> channelConfigBoList = allConfigJsonBo.getChannelConfigBoList();
        if(CollectionUtils.isEmpty(channelConfigBoList)) {
            return channelBoList;
        }

        fillOperatorNameChannelConfigBoList(channelConfigBoList);

        return channelConfigBoList;
    }

    /**
     * 设置渠道列表操作人姓名
     */
    private void fillOperatorNameChannelConfigBoList(List<ChannelBo> channelConfigBoList) {
        List<Long> optionIdList = channelConfigBoList.stream()
                .filter(Objects::nonNull)
                .map(ChannelBo::getOperatorId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //渠道列表操作人姓名
        Map<Long, AccountInfoListResp> accountInfoMap = super.batchGetInfoMapByAccountIds(optionIdList);

        channelConfigBoList.forEach(channelBo -> {
            Long operatorId = channelBo.getOperatorId();

            AccountInfoListResp accountInfoListResp = accountInfoMap.get(operatorId);
            // 没查到设置id为兜底
            if (BaseUtil.hasLongValue(operatorId)) {
                channelBo.setOperatorName("" + operatorId);
            } else {
                channelBo.setOperatorName("" + operatorId);
            }
            if (Objects.nonNull(accountInfoListResp)) {
                channelBo.setOperatorName(accountInfoListResp.getUsername());
            }
        });
    }

    /**
     * 新增渠道
     * 校验端侧标识和名称的重复在新增之前就调用
     */
    @Override
    public Long createChannel(CreateChannelReq req) {
        String key = String.format(CommonRedisConstant.CREATE_CHANNEL_PREFIX, req.getChannelEn()+req.getChannelName());

        String s = redisHelper.get(key);
        if (StringUtils.isNotEmpty(s)) {
            throw new BusException("请勿短时间内重复操作");
        }
        redisHelper.set(key,"1", CommonRedisConstant.REPEAT_OPERATE_TIME);

        ClientChannelConfigDraft draft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(draft)) {
            throw new BusinessException("添加的端侧不存在！"+ req.getClientCategoryId());
        }


        String beforeAllConfigJsonString = draft.getAllConfigJson();
        AllConfigJsonBo beforeAllConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(beforeAllConfigJsonString);

        // 设置新渠道ID
        req.setChannelId(super.getGuid());
        // 新增的渠道
        List<ChannelBo> channelBoList = ChannelBoBuilder.buildChannelBoList(req);
        Assert.notEmpty(channelBoList, "入参错误！" + JSON.toJSONString(req));

        // 补充渠道的其他细节模板内容

        TemplateInitJsonEnum templateInitJsonEnum = TemplateInitJsonEnum.ALL;
        TemplateInitJson templateInitJson = templateInitJsonRepository.selectTemplateInitJsonByIndex(templateInitJsonEnum.tableTypeName, templateInitJsonEnum.columnTypeName, templateInitJsonEnum.type) ;
        if(Objects.isNull(templateInitJson) || StringUtils.isEmpty(templateInitJson.getContent())) {
            throw new BusinessException("初始化渠道模板配置不存在！"+ req.getChannelId());
        }
        AllConfigJsonBo templateAllConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(templateInitJson.getContent());
        Assert.notNull(templateAllConfigJsonBo, "初始化渠道模板配置不存在！请检查template_init_json表记录。");
        Assert.notEmpty(templateAllConfigJsonBo.getChannelConfigBoList(), "初始化渠道模板配置不存在！请检查template_init_json表的数据是否正确。");

        channelBoList.forEach(channelBo -> {
            // template_init_json 记录仅配置了一个 channelBo
            ChannelBo templateChannelBo = templateAllConfigJsonBo.getChannelConfigBoList().get(CommonConstant.ZERO);
            if(Objects.nonNull(templateChannelBo)) {
                List<ConversationAutoReplyConfigBo> conversationAutoReplyConfigBoList = templateChannelBo.getConversationAutoReplyConfigBoList();
                if(CollectionUtils.isNotEmpty(conversationAutoReplyConfigBoList)) {
                    conversationAutoReplyConfigBoList.forEach(item -> {
                        if(! BaseUtil.hasLongValue(item.getConversationAutoReplyConfigId())) {
                            item.setConversationAutoReplyConfigId(super.getGuid());
                        }
                        item.setChannelId(req.getChannelId());
                        item.setClientCategoryId(req.getClientCategoryId());
                        List<ConversationAutoReplyDetailConfigBo> autoReplyDetailConfigBoList = item.getConversationAutoReplyDetailConfigBoList();
                        autoReplyDetailConfigBoList.forEach(detailBo -> {
                            detailBo.setConversationAutoReplyConfigId(item.getConversationAutoReplyConfigId());
                            if(!BaseUtil.hasLongValue(detailBo.getConversationAutoReplyDetailConfigId())) {
                                detailBo.setConversationAutoReplyDetailConfigId(super.getGuid());
                            }
                        });
                    });
                }
                if(CollectionUtils.isNotEmpty(templateChannelBo.getConversationStyleConfigBoList())) {
                    templateChannelBo.getConversationStyleConfigBoList().forEach(item -> {
                        item.setChannelId(req.getChannelId());
                        if(!BaseUtil.hasLongValue(item.getConversationStyleConfigId())) {
                            item.setConversationStyleConfigId(super.getGuid());
                        }
                    });
                }
                channelBo.setConversationStyleConfigBoList(templateChannelBo.getConversationStyleConfigBoList());

                SatisfactionLevelConfigBo satisfactionLevelConfigBo = channelBo.getSatisfactionLevelConfigBo();
                if(Objects.nonNull(satisfactionLevelConfigBo)) {
                    if(!BaseUtil.hasLongValue(satisfactionLevelConfigBo.getSatisfactionLevelConfigId())) {
                        satisfactionLevelConfigBo.setSatisfactionLevelConfigId(super.getGuid());
                    }
                }
                channelBo.setSatisfactionLevelConfigBo(templateChannelBo.getSatisfactionLevelConfigBo());
                //添加明细
                if(CollectionUtils.isNotEmpty(templateChannelBo.getCreateConversationStyleConfigBos())) {
                    templateChannelBo.getCreateConversationStyleConfigBos().forEach(item -> {
                        item.setChannelId(req.getChannelId());
                        if(!BaseUtil.hasLongValue(item.getConversationStyleConfigId())) {
                            item.setConversationStyleConfigId(super.getGuid());
                        }
                    });
                }
                channelBo.setCreateConversationStyleConfigBos(templateChannelBo.getCreateConversationStyleConfigBos());

            }
            if(Objects.isNull(channelBo.getChannelSeq())) {
                channelBo.setChannelSeq(CommonConstant.ONE);
            }
            channelBo.setIsDelete(CommonConstant.ZERO);
            channelBo.setStatus(CommonConstant.ONE);
        });
        // 原先就有渠道，则把新增渠道设置道原有渠道里边，并且设置序号
        if(Objects.nonNull(beforeAllConfigJsonBo)) {
            List<ChannelBo> channelConfigBoList = beforeAllConfigJsonBo.getChannelConfigBoList();

            if(CollectionUtils.isNotEmpty(channelConfigBoList)) {
                // 重复校验
                channelConfigBoList.stream()
                        .filter(f -> f.getChannelEn().equals(req.getChannelEn()))
                        .findFirst()
                        .ifPresent(existingChannel -> {
                            throw new BusinessException("“该入口标识与系统内入口标识重复，不可添加");
                        });
                channelConfigBoList.stream()
                        .filter(f -> f.getChannelName().equals(req.getChannelName()))
                        .findFirst()
                        .ifPresent(existingChannel -> {
                            throw new BusinessException("该入口名与系统内入口名重复，不可添加");
                        });
                // 获取当前最大序号
                Optional<Integer> currentMaxSeq = channelConfigBoList.stream()
                        .filter(Objects::nonNull)
                        .map(ChannelBo::getChannelSeq)
                        .filter(Objects::nonNull)
                        .max(Integer::compareTo);
                // 设置新增渠道序号
                ChannelBo channelBo = channelBoList.get(0);
                if(Objects.isNull(channelBo.getChannelSeq())) {
                    channelBo.setChannelSeq(CommonConstant.ONE);
                }
                channelBo.setChannelSeq(currentMaxSeq.orElse(CommonConstant.ZERO) + 1);
                channelConfigBoList.addAll(channelBoList);
                channelBoList = channelConfigBoList;
            }
        }
        // 端侧下没有渠道，直接使用渠道待新增的channelBoList
        String updateAllConfigJson = ChannelBoBuilder.buildAllConfigJsonString(channelBoList);
        req.setAllConfigJson(updateAllConfigJson);
        ClientChannelConfigDraft clientChannelConfigDraftUpdate = ChannelBoBuilder.buildClientChannelConfigDraft(req);
        clientChannelConfigDraftRepository.updateByPrimaryKeySelective(clientChannelConfigDraftUpdate);
        return clientChannelConfigDraftUpdate.getClientChannelConfigDraftId();
    }

    /**
     * 编辑渠道
     *
     */
    @Override
    public Long editChannel(UpdateChannelReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            throw new BusinessException("编辑的端侧不存在！");
        }

        String allConfigJsonString = clientChannelConfigDraft.getAllConfigJson();
        AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(allConfigJsonString);

        // 新增的渠道
        List<ChannelBo> channelBoList = ChannelBoBuilder.buildChannelBoList(req);

        if(Objects.isNull(allConfigJsonBo) || CollectionUtils.isEmpty(allConfigJsonBo.getChannelConfigBoList())) {
            throw new BusinessException("编辑的渠道子对象不存在！");
        }
        //待更新的渠道
        ChannelBo updateChannelBo = allConfigJsonBo.getChannelConfigBoList().stream()
                .filter(channelBo -> channelBo.getChannelId().equals(req.getChannelId()))
                .findFirst().orElseThrow(() -> new BusinessException("编辑的渠道不存在！"));
        //替换渠道对象, 但不调整渠道的子对象
        ChannelBo paramChannelBo = channelBoList.get(CommonConstant.ZERO);
        // 属性更新
        updateChannelBo.overlayChannelBo(paramChannelBo);
        String updateAllConfigJson = JSON.toJSONString(allConfigJsonBo);

        ClientChannelConfigDraft clientChannelConfigDraftUpdate = ChannelBoBuilder.buildClientChannelConfigDraft(req, clientChannelConfigDraft);
        clientChannelConfigDraftUpdate.setAllConfigJson(updateAllConfigJson);
        clientChannelConfigDraftRepository.updateByPrimaryKeySelective(clientChannelConfigDraftUpdate);
        return clientChannelConfigDraftUpdate.getClientChannelConfigDraftId();
    }

    /**
     * 删除渠道
     *
     */
    @Override
    public Long deleteChannel(DeleteChannelReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            log.info("待删除的端侧不存在！");
            return CommonConstant.LONG_ZERO;
        }
        String allConfigJsonString = clientChannelConfigDraft.getAllConfigJson();
        AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(allConfigJsonString);
        if(Objects.isNull(allConfigJsonBo) || CollectionUtils.isEmpty(allConfigJsonBo.getChannelConfigBoList())) {
            log.info("待删除的渠道结构不存在！");
            return CommonConstant.LONG_ZERO;
        }

        ChannelBo deleteChannelBo = allConfigJsonBo.getChannelConfigBoList().stream()
                .filter(channelBo -> channelBo.getChannelId().equals(req.getChannelId()))
                .findFirst().orElse(null);

        if(Objects.isNull(deleteChannelBo)) {
            log.info("待删除的渠道不存在！");
            return CommonConstant.LONG_ZERO;
        }

        deleteChannelBo.setIsDelete(CommonConstant.ONE);

        ClientChannelConfigDraft clientChannelConfigDraftUpdate = ChannelBoBuilder.buildClientChannelConfigDraft(req, clientChannelConfigDraft);

        String updateAllConfigJson = JSON.toJSONString(allConfigJsonBo);
        clientChannelConfigDraftUpdate.setAllConfigJson(updateAllConfigJson);
        clientChannelConfigDraftRepository.updateByPrimaryKeySelective(clientChannelConfigDraftUpdate);
        return clientChannelConfigDraftUpdate.getClientChannelConfigDraftId();
    }

    /**
     * 修改渠道启用状态
     *
     */
    @Override
    @Transactional(rollbackFor =  Exception.class)
    public SwitchChannelStatusResp switchChannelStatus(SwitchChannelStatusReq req) {
        SwitchChannelStatusResp resp = new SwitchChannelStatusResp();
        Integer deployStatus = CommonConstant.ZERO;
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            throw new BusinessException("编辑的端侧不存在！");
        }
        ClientChannelConfigDraft clientChannelConfigDraftUpdate =  ChannelBoBuilder.buildClientChannelConfigDraft(req, clientChannelConfigDraft);
        clientChannelConfigDraftRepository.updateByPrimaryKeySelective(clientChannelConfigDraftUpdate);

        // 已发布的渠道 直接更新正式表启用状态
        ClientCategoryConfig clientCategoryConfig = clientCategoryConfigRepository.selectByPrimaryKey(req.getClientCategoryId());
        if(Objects.nonNull(clientCategoryConfig)) {
            ChannelConfig channelConfig = channelConfigRepository.selectByPrimaryKey(req.getChannelId());
            if(Objects.nonNull(req.getStatus()) && Objects.nonNull(channelConfig)) {
                channelConfig.setStatus(req.getStatus());
                channelConfigRepository.updateByPrimaryKeySelective(channelConfig);
                deployStatus = CommonConstant.ONE;
            }
        }
        resp.setClientCategoryId(req.getClientCategoryId());
        resp.setChannelId(req.getChannelId());
        resp.setDeployStatus(deployStatus);
        if (CommonConstant.ZERO.equals(deployStatus)) {
            resp.setDeployMsg("入口未发布过,请先去发布页发布！");
        }
        return resp;
    }

    /**
     * 批量编辑渠道
     *
     */
    @Override
    public Integer batchEditChannel(BatchEditChannelReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            throw new BusinessException("编辑的端侧不存在！");
        }
        req.getChannelBoList().forEach(channelBo -> {
            // 兼容新增的channel
            if(! BaseUtil.hasLongValue(channelBo.getChannelId())) {
                channelBo.setChannelId(super.getGuid());
            }
            channelBo.setUpdateTime(new Date());
            channelBo.setOperatorId(req.getOperationId());
        });
        ClientChannelConfigDraft clientChannelConfigDraftUpdate =  ChannelBoBuilder.buildClientChannelConfigDraft(req.getChannelBoList(), clientChannelConfigDraft);
        return clientChannelConfigDraftRepository.updateByPrimaryKeySelective(clientChannelConfigDraftUpdate);
    }

    /**
     * 预览端侧+渠道
     *
     */
    @Override
    public PreviewClientCategoryAndChannelResp previewDraft(PreviewClientCategoryAndChannelReq req) {
        //TODO 跟侧边栏调整
        PreviewClientCategoryAndChannelResp resp = new PreviewClientCategoryAndChannelResp();
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectByPrimaryKey(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            throw new BusinessException("端侧草稿不存在！");
        }
        String allConfigJson = clientChannelConfigDraft.getAllConfigJson();
        AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(allConfigJson);
        if(Objects.nonNull(allConfigJsonBo)) {
            // 获取预览的渠道
            ChannelBo channelBo = allConfigJsonBo.getChannelConfigBoList().stream().filter(item -> item.getChannelId().equals(req.getChannelId())).findFirst().orElse(null);
            if(Objects.isNull(channelBo)) {
                throw new BusinessException("渠道草稿不存在！");
            }
            List<ConversationStyleConfigBo> conversationStyleConfigBoList =  channelBo.getConversationStyleConfigBoList();
            resp.setConversationStyleConfigBoList(conversationStyleConfigBoList);

        }

        return resp;
    }

    /**
     * 发布端侧+渠道,配置发布完成后,才能在前端使用。 将草稿表内容发布到正式表(合计8张)
     *
     */
    @Override
    @Transactional(rollbackFor =  Exception.class)
    public Long deployConfig(DeployClientCategoryAndChannelReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectByPrimaryKey(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            throw new BusinessException("端侧草稿不存在！");
        }

        AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(clientChannelConfigDraft.getAllConfigJson());
        // 校验待发布配置是否完整
        checkAllConfigJsonBo(allConfigJsonBo, req);

        ClientCategoryConfig  clientCategoryConfig = ChannelBoBuilder.buildClientCategoryConfig(req, clientChannelConfigDraft);
        clientCategoryConfigRepository.insertOrUpdateClientCategoryConfig(clientCategoryConfig);

        if(Objects.nonNull(allConfigJsonBo)) {
            List<ChannelBo> channelBoList = allConfigJsonBo.getChannelConfigBoList();
            if(CollectionUtils.isNotEmpty(channelBoList)) {
                ChannelBo deploychannelBo = channelBoList.stream().filter(cb -> cb.getChannelId().equals(req.getChannelId()))
                        .findFirst().orElseThrow(() -> new BusinessException("待发布的渠道不存在"));
                ChannelConfig channelConfig = new ChannelConfig();
                BeanUtils.copyProperties(deploychannelBo, channelConfig);
                channelConfigRepository.insertOrUpdateChannelConfig(channelConfig);

                // 渠道样式
                List<ConversationStyleConfigBo> conversationStyleConfigBoList = deploychannelBo.getConversationStyleConfigBoList();
                if (CollectionUtils.isNotEmpty(conversationStyleConfigBoList)) {
                    conversationStyleConfigBoList.forEach(item -> {
                        item.setChannelId(deploychannelBo.getChannelId());
                        if (!BaseUtil.hasLongValue(item.getConversationStyleConfigId())) {
                            item.setConversationStyleConfigId(super.getGuid());
                        }
                    });
                    //conversationStyleConfigRepository.batchInsertOrUpdateConversationStyleConfig(conversationStyleConfigBoList);
                    conversationStyleConfigBoList.forEach(cscBo -> {
                        // 渠道主题
                        List<ConversationThemeConfigBo> conversationThemeConfigBoList = cscBo.getConversationThemeConfigBoList();
                        if (CollectionUtils.isNotEmpty(conversationThemeConfigBoList)) {
                            //conversationThemeConfigRepository.batchInsertOrUpdateConversationThemeConfigBo(conversationThemeConfigBoList);
                        }
                    });
                }
                //会话样式处理
                List<CreateConversationStyleConfigBo> createConversationStyleConfigBos = deploychannelBo.getCreateConversationStyleConfigBos();
                if (CollectionUtils.isNotEmpty(createConversationStyleConfigBos)) {
                    List<ConversationStyleConfigV2Bo> conversationStyleConfigV2BoList =  createConversationStyleConfigBos.stream().map(createConversationStyleConfigBo->{
                        //会话配置， 有就更新，没有就新增
                        ConversationStyleConfigV2Bo conversationStyleConfigV2Bo = new ConversationStyleConfigV2Bo();

                        BeanUtils.copyProperties(createConversationStyleConfigBo, conversationStyleConfigV2Bo);
                        String sidebarJson = JSONObject.toJSONString(createConversationStyleConfigBo.getConversationSidebarConfigBoList());
                        //设置侧边栏数据
                        conversationStyleConfigV2Bo.setSidebarJson(sidebarJson);
                        //设置其他配置
                        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(createConversationStyleConfigBo.getBulletinBoard());
                        //添加卡片等样式配置
                        jsonObject.put("orderCardThemeId", createConversationStyleConfigBo.getOrderCardThemeId());
                        jsonObject.put("workOrderCardThemeId", createConversationStyleConfigBo.getWorkOrderCardThemeId());
                        //设置侧边栏数据
                        conversationStyleConfigV2Bo.setOtherJson(jsonObject.toJSONString());

                        return conversationStyleConfigV2Bo;
                    }).collect(Collectors.toList());

                    conversationStyleConfigRepository.batchInsertOrUpdateConversationStyleConfigV2(conversationStyleConfigV2BoList);
                }
                //会话满意度
                SatisfactionLevelConfigBo satisfactionLevelConfigBo = deploychannelBo.getSatisfactionLevelConfigBo();
                if (Objects.nonNull(satisfactionLevelConfigBo)) {
                    satisfactionLevelConfigBo.setResultCallbackFormJson(JSONObject.toJSONString(satisfactionLevelConfigBo.getResultCallbackFormJsonBo()));
                    if (!BaseUtil.hasLongValue(satisfactionLevelConfigBo.getChannelId())) {
                        satisfactionLevelConfigBo.setChannelId(req.getChannelId());
                    }
                    if (!BaseUtil.hasLongValue(satisfactionLevelConfigBo.getSatisfactionLevelConfigId())) {
                        satisfactionLevelConfigBo.setSatisfactionLevelConfigId(super.getGuid());
                    }
                    satisfactionLevelConfigRepository.insertOrUpdateSatisfactionLevelConfigBo(satisfactionLevelConfigBo);
                    //会话满意度快照  每次都是新增 线上都用最新记录
                    SatisfactionEvaluateRecord satisfactionEvaluateRecord = ChannelBoBuilder.buildSatisfactionEvaluateRecord(satisfactionLevelConfigBo);
                    satisfactionEvaluateRecord.setSatisfactionEvaluateRecordId(super.getGuid());
                    if (!BaseUtil.hasLongValue(satisfactionEvaluateRecord.getChannelId())) {
                        satisfactionEvaluateRecord.setChannelId(req.getChannelId());
                    }
                    satisfactionEvaluateRecord.setSatisfactionLevelConfigId(satisfactionLevelConfigBo.getSatisfactionLevelConfigId());

                    satisfactionEvaluateRecordRepository.insertSelective(satisfactionEvaluateRecord);
                }
                // 渠道自动回复
                List<ConversationAutoReplyConfigBo> conversationAutoReplyConfigBoList = deploychannelBo.getConversationAutoReplyConfigBoList();
                if (CollectionUtils.isNotEmpty(conversationAutoReplyConfigBoList)) {
                    for (ConversationAutoReplyConfigBo conversationAutoReplyConfigBo : conversationAutoReplyConfigBoList) {
                        ConversationAutoReplyConfig conversationAutoReplyConfig = new ConversationAutoReplyConfig();
                        BeanUtils.copyProperties(conversationAutoReplyConfigBo, conversationAutoReplyConfig);
                        if (!BaseUtil.hasLongValue(conversationAutoReplyConfig.getConversationAutoReplyConfigId())) {
                            conversationAutoReplyConfig.setConversationAutoReplyConfigId(super.getGuid());
                        }
                        if (!BaseUtil.hasLongValue(conversationAutoReplyConfig.getChannelId())) {
                            conversationAutoReplyConfig.setChannelId(req.getChannelId());
                        }
                        if (Objects.isNull(conversationAutoReplyConfig.getCreateTime())) {
                            conversationAutoReplyConfig.setCreateTime(new Date());
                        }
                        conversationAutoReplyConfigRepository.insertOrUpdateConversationAutoReplyConfig(conversationAutoReplyConfig);
                    }
                    // 渠道自动回复明细
                    conversationAutoReplyConfigBoList.forEach(carBo -> {
                        List<ConversationAutoReplyDetailConfigBo> conversationAutoReplyDetailConfigBoList = carBo.getConversationAutoReplyDetailConfigBoList();
                        if (CollectionUtils.isNotEmpty(conversationAutoReplyDetailConfigBoList)) {
                            for (ConversationAutoReplyDetailConfigBo carDetailBo : conversationAutoReplyDetailConfigBoList) {
                                ConversationAutoReplyDetailConfig conversationAutoReplyDetailConfig = new ConversationAutoReplyDetailConfig();
                                BeanUtils.copyProperties(carDetailBo, conversationAutoReplyDetailConfig);
                                conversationAutoReplyDetailConfig.setConversationAutoReplyDetailConfig(carDetailBo.getConversationAutoReplyDetailConfigId());
                                conversationAutoReplyDetailConfig.setConversationAutoReplyConfigId(carBo.getConversationAutoReplyConfigId());
                                if (Objects.isNull(conversationAutoReplyDetailConfig.getSupportPlaceholder())) {
                                    conversationAutoReplyDetailConfig.setSupportPlaceholder(CommonConstant.ZERO);
                                }
                                if (!BaseUtil.hasLongValue(conversationAutoReplyDetailConfig.getConversationAutoReplyDetailConfig())) {
                                    conversationAutoReplyDetailConfig.setConversationAutoReplyDetailConfig(super.getGuid());
                                }
                                conversationAutoReplyDetailConfig.setExtraJson(carDetailBo.getIntroductionMode());

                                if(conversationAutoReplyDetailConfig.getTimeoutCostSecond()!=null &&
                                        Arrays.asList(TriggerModeEnum.SEAT_TIMEOUT.type,TriggerModeEnum.VISITOR_TIMEOUT.type,TriggerModeEnum.VISITOR_LEAVE.type).contains(conversationAutoReplyDetailConfig.getTimeoutCostSecond())){
                                    int i = Integer.parseInt(conversationAutoReplyDetailConfig.getTimeoutCostSecond()) * 60;
                                    conversationAutoReplyDetailConfig.setTimeoutCostSecond(i + "");
                                }
                                //特殊判断,坐席超时未回复  有多个时间，需要特殊处理
                                if(AutoReplyMsgSubTypeEnum.SEAT_REPLY_TIMEOUT_REMIND.type.equals(carDetailBo.getMsgSubType())){
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put(ChannelConstant.RESPONSE_TIMEOUT_REMINDER,carDetailBo.getTimeoutCostSecond());
                                    jsonObject.put(ChannelConstant.IMMEDIATELY_RESPONSE_TIMEOUT_REMINDER,carDetailBo.getExtendVal1());
                                    conversationAutoReplyDetailConfig.setExtraJson(jsonObject.toJSONString());

                                }

                                conversationAutoReplyDetailConfigRepository.insertOrUpdateConversationAutoReplyDetailConfig(conversationAutoReplyDetailConfig);
                            }
                            //conversationAutoReplyDetailConfigRepository.batchInsertOrUpdateConversationAutoReplyDetailConfigBo(conversationAutoReplyDetailConfigBoList);
                        }
                    });
                }
            }
        }

        // 更新在线状态
        clientChannelConfigDraft.setStatus(CommonConstant.ONE);
        clientChannelConfigDraftRepository.updateByPrimaryKeySelective(clientChannelConfigDraft);

        return clientCategoryConfig.getClientCategoryConfigId();
    }

    /**
     * 检验配置是否齐全
     */
    private void checkAllConfigJsonBo(AllConfigJsonBo allConfigJsonBo, DeployClientCategoryAndChannelReq req) {
        if(Objects.isNull(allConfigJsonBo) || CollectionUtils.isEmpty(allConfigJsonBo.getChannelConfigBoList())) {
            throw new BusinessException("入口配置缺失！");
        }
        ChannelBo channelBo = allConfigJsonBo.getChannelConfigBoList().stream().filter(f -> f.getChannelId().equals(req.getChannelId())).findFirst().orElseThrow(() -> new BusinessException("入口配置缺失！"));

        List<ConversationAutoReplyConfigBo> conversationAutoReplyConfigBoList = channelBo.getConversationAutoReplyConfigBoList();
        if (CollectionUtils.isEmpty(conversationAutoReplyConfigBoList)) {
            throw new BusinessException("系统自动回复消息未配置！");
        }

        ConversationAutoReplyConfigBo welcomeBo = conversationAutoReplyConfigBoList.stream()
                .filter(item -> item.getChannelId().equals(req.getChannelId()))
                .filter(item -> item.getMsgType().equals(AutoReplyTypeEnum.WELCOME_AUTO_REPLY.type))
                .findFirst()
                .orElseThrow(() -> new BusinessException("会话欢迎语缺失，请补充配置！"));
        List<ConversationAutoReplyDetailConfigBo> welcomeList = welcomeBo.getConversationAutoReplyDetailConfigBoList();
        if (CollectionUtils.isEmpty(welcomeList)) {
            throw new BusinessException("会话欢迎语缺失，请补充配置！");
        }
        welcomeList.forEach(item -> {
            if (Objects.isNull(item) || !BaseUtil.hasLongValue(item.getConversationAutoReplyDetailConfigId())) {
                throw new BusinessException("会话欢迎语缺失，请补充配置！");
            }
        });

        ConversationAutoReplyConfigBo waitBo = conversationAutoReplyConfigBoList.stream()
                .filter(item -> item.getChannelId().equals(req.getChannelId()))
                .filter(item -> item.getMsgType().equals(AutoReplyTypeEnum.WAIT_AUTO_REPLY.type))
                .findFirst()
                .orElseThrow(() -> new BusinessException("会话排队配置缺失，请补充配置！"));
        List<ConversationAutoReplyDetailConfigBo> waitBoList = waitBo.getConversationAutoReplyDetailConfigBoList();
        if (CollectionUtils.isEmpty(waitBoList)) {
            throw new BusinessException("会话排队配置缺失，请补充配置！");
        }
        waitBoList.forEach(item -> {
            if (Objects.isNull(item) || !BaseUtil.hasLongValue(item.getConversationAutoReplyDetailConfigId())) {
                throw new BusinessException("会话排队配置缺失，请补充配置！");
            }
        });

        ConversationAutoReplyConfigBo timeoutBo = conversationAutoReplyConfigBoList.stream()
                .filter(item -> item.getChannelId().equals(req.getChannelId()))
                .filter(item -> item.getMsgType().equals(AutoReplyTypeEnum.TIMEOUT_AUTO_REPLY.type))
                .findFirst()
                .orElseThrow(() -> new BusinessException("会话超时配置缺失，请补充配置！"));

        List<ConversationAutoReplyDetailConfigBo> timeoutBoList = timeoutBo.getConversationAutoReplyDetailConfigBoList();
        if (CollectionUtils.isEmpty(timeoutBoList)) {
            throw new BusinessException("会话超时配置缺失，请补充配置！");
        }
        timeoutBoList.forEach(item -> {
            if (Objects.isNull(item) || !BaseUtil.hasLongValue(item.getConversationAutoReplyDetailConfigId())) {
                throw new BusinessException("会话超时配置缺失，请补充配置！");
            }
        });

        ConversationAutoReplyConfigBo endBo = conversationAutoReplyConfigBoList.stream()
                .filter(item -> item.getChannelId().equals(req.getChannelId()))
                .filter(item -> item.getMsgType().equals(AutoReplyTypeEnum.END_AUTO_REPLY.type))
                .findFirst()
                .orElseThrow(() -> new BusinessException("会话结束配置缺失，请补充配置！"));

        List<ConversationAutoReplyDetailConfigBo> endBoList = endBo.getConversationAutoReplyDetailConfigBoList();
        if (CollectionUtils.isEmpty(endBoList)) {
            throw new BusinessException("会话结束配置缺失，请补充配置！");
        }
        endBoList.forEach(item -> {
            if (Objects.isNull(item) || !BaseUtil.hasLongValue(item.getConversationAutoReplyDetailConfigId())) {
                throw new BusinessException("会话结束配置缺失，请补充配置！");
            }
        });

        if (Objects.isNull(channelBo.getSatisfactionLevelConfigBo()) || !BaseUtil.hasLongValue(channelBo.getSatisfactionLevelConfigBo().getSatisfactionLevelConfigId())) {
            throw new BusinessException("会话满意度配置缺失！");
        }
//                if(CollectionUtils.isEmpty(channelBo.getConversationStyleConfigBoList())) {
//                    throw new RuntimeException("侧边栏未配置！");
//                }


    }

    /**
     * 保存端侧+渠道草稿
     *
     */
    @Override
    @Transactional(rollbackFor =  Exception.class)
    public Long saveConfigDraft(CreateDraftReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = ChannelBoBuilder.buildClientCategoryDraft(req);
        return clientChannelConfigDraftRepository.insertOrUpdateClientCategoryDraft(clientChannelConfigDraft);
    }

    /**
     * 会话主题样式和侧边栏查询
     *
     */
    @Override
    public GetStyleAndSideBarResp getStyleAndSideBar(GetStyleAndSideBarReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectByPrimaryKey(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            throw new BusinessException("端侧草稿不存在！");
        }
        GetStyleAndSideBarResp resp = new GetStyleAndSideBarResp();
        String allConfigJson = clientChannelConfigDraft.getAllConfigJson();
        if(StringUtils.isEmpty(allConfigJson)) {
            return resp;
        }
        AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(allConfigJson);
        if(Objects.isNull(allConfigJsonBo)) {
            return resp;
        }
        List<ChannelBo> channelConfigBoList = allConfigJsonBo.getChannelConfigBoList();
        if(CollectionUtils.isNotEmpty(channelConfigBoList)) {
            ChannelBo channelBo = channelConfigBoList.stream().filter(f -> f.getChannelId().equals(req.getChannelId())).findFirst().orElse(null);
            if(Objects.isNull(channelBo)) {
                return resp;
            }
            List<ConversationStyleConfigBo> conversationStyleConfigBoList = channelBo.getConversationStyleConfigBoList();
            if(CollectionUtils.isEmpty(conversationStyleConfigBoList)) {
                return resp;
            }
            resp.setConversationStyleConfigBoList(conversationStyleConfigBoList);
        }
        return resp;
    }

    /**
     * 编辑会话主题样式和侧边栏
     *
     */
    @Override
    public Long editStyleAndSideBar(UpdateConversationStyleConfigRqt req) {
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            throw new BusinessException("端侧草稿不存在！");
        }
        //查询值
        ConversationStyleConfig conversationStyleConfig = conversationStyleConfigRepository.selectByPrimaryKey(req.getConversationStyleConfigId());
        if(Objects.isNull(conversationStyleConfig)) {
            throw new BusinessException("会话配置样式不存在！");
        }
        //写入配置
        conversationStyleConfig.setChannelId(req.getChannelId());
        conversationStyleConfig.setConversationThemeId(req.getConversationThemeId());
        conversationStyleConfig.setClientCategoryId(req.getClientCategoryId());
        conversationStyleConfig.setSidebarJson(req.getSidebarJson());
        conversationStyleConfig.setOtherJson(req.getOtherJson());
        conversationStyleConfig.setStatus(req.getStatus());
        conversationStyleConfig.setOperatorId(req.getOperatorId());
        conversationStyleConfig.setIsDelete(0);
        conversationStyleConfig.setUpdateTime(new Date());
        conversationStyleConfigRepository.updateByPrimaryKey(conversationStyleConfig);
        return conversationStyleConfig.getConversationStyleConfigId();
    }

    /**
     * 新增会话主题样式和侧边栏
     *
     */
    @Override
    public Long createStyleAndSideBar(CreateConversationStyleConfigRqt req) {
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            throw new BusinessException("端侧草稿不存在！");
        }
        //写入配置
        ConversationStyleConfig conversationStyleConfig = new ConversationStyleConfig();
        BeanUtils.copyProperties(req, conversationStyleConfig);
        //设置默认值
        conversationStyleConfig.setIsDelete(0);
        conversationStyleConfig.setStatus(0);
        conversationStyleConfig.setCreateTime(new Date());
        conversationStyleConfig.setUpdateTime(new Date());
        int id = conversationStyleConfigRepository.insertSelective(conversationStyleConfig);

        return (long) id;
    }

    /**
     * 会话满意度配置查询
     *
     */
    @Override
    public GetSatisfactionLevelConfigResp getSatisfactionLevelConfig(GetSatisfactionLevelConfigReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            throw new BusinessException("端侧草稿不存在！");
        }
        if(! BaseUtil.hasLongValue(req.getSatisfactionLevelConfigId())) {
            req.setSatisfactionLevelConfigId(super.getGuid());
        }
        List<TemplateInitJson> templateInitJsonList = templateInitJsonRepository.selectAllTemplateInitJson();
        if(CollectionUtils.isEmpty(templateInitJsonList)) {
            throw new BusinessException("初始化模板配置为空！"+ JSONObject.toJSONString(req));
        }
        TemplateInitJson allTemplateInitJson = templateInitJsonList.stream().filter(f -> f.getInitJsonType().equals(TemplateInitJsonEnum.ALL.type)).findFirst().orElseThrow(() -> new BusinessException("初始化全量模板配置不存在！"+ JSONObject.toJSONString(req)));
        TemplateInitJson satisfactionTemplateInitJson = templateInitJsonList.stream().filter(f -> f.getInitJsonType().equals(TemplateInitJsonEnum.SATISFACTION.type)).findFirst().orElseThrow(() -> new BusinessException("初始化渠道满意度模板配置不存在！"+ JSONObject.toJSONString(req)));
        return ChannelBoBuilder.buildGetSatisfactionLevelConfigResp(req, clientChannelConfigDraft, satisfactionTemplateInitJson.getContent(), allTemplateInitJson.getContent());
    }

    /**
     * 新增或编辑会话满意度配置
     *
     */
    @Override
    public Long createOrEditSatisfactionLevelConfig(CreateOrEditSatisfactionLevelConfigReq req) {
        ClientChannelConfigDraft draft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(draft)) {
            throw new BusinessException("端侧草稿不存在！");
        }
        AllConfigJsonBo allConfigJsonBo = ChannelBoBuilder.buildAllConfigJsonBo(draft.getAllConfigJson());
        if(Objects.nonNull(allConfigJsonBo)) {
            if (CollectionUtils.isNotEmpty(allConfigJsonBo.getChannelConfigBoList())) {
                ChannelBo channelBo = allConfigJsonBo.getChannelConfigBoList().stream().filter(f -> f.getChannelId().equals(req.getChannelId())).findFirst().orElse(null);
                if (Objects.nonNull(channelBo)) {
                    // 渠道层的更新时间和操作人设置
                    channelBo.setOperatorId(req.getOperatorId());
                    channelBo.setUpdateTime(new Date());
                    //原先没有会话满意度配置 新增会话满意度
                    if(Objects.isNull(channelBo.getSatisfactionLevelConfigBo())) {
                        TemplateInitJsonEnum templateInitJsonEnum = TemplateInitJsonEnum.SATISFACTION;
                        TemplateInitJson templateInitJson = templateInitJsonRepository.selectTemplateInitJsonByIndex(templateInitJsonEnum.tableTypeName, templateInitJsonEnum.columnTypeName, templateInitJsonEnum.type) ;
                        if(Objects.isNull(templateInitJson) || StringUtils.isEmpty(templateInitJson.getContent())) {
                            throw new BusinessException("初始化渠道自动回复模板配置不存在！"+ req.getChannelId());
                        }
                        SatisfactionLevelConfigBo satisfactionLevelConfigBo = ChannelBoBuilder.buildSatisfactionLevelConfigBo(req, templateInitJson.getContent());
                        channelBo.setSatisfactionLevelConfigBo(satisfactionLevelConfigBo);
                    }
                }
            }
        }

        // 设置满意度主键
        if(Objects.nonNull(req.getSatisfactionLevelConfigBo())) {
            if(!BaseUtil.hasLongValue(req.getSatisfactionLevelConfigBo().getSatisfactionLevelConfigId())) {
                req.getSatisfactionLevelConfigBo().setSatisfactionLevelConfigId(super.getGuid());
            }
            if (!BaseUtil.hasLongValue(req.getSatisfactionLevelConfigBo().getChannelId())) {
                req.getSatisfactionLevelConfigBo().setChannelId(req.getChannelId());
            }
        }
        ClientChannelConfigDraft clientChannelConfigDraftUpdate = ChannelBoBuilder.buildClientChannelConfigDraft(req, draft);
        clientChannelConfigDraftRepository.updateByPrimaryKeySelective(clientChannelConfigDraftUpdate);
        return clientChannelConfigDraftUpdate.getClientChannelConfigDraftId();
    }

    /**
     * 会话系统自动回复消息配置查询
     *
     */
    @Override
    public GetAutoReplyConfigResp getAutoReplyConfig(GetAutoReplyConfigReq req) {
        ClientChannelConfigDraft clientChannelConfigDraft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(clientChannelConfigDraft)) {
            throw new BusinessException("端侧草稿不存在！");
        }
        List<TemplateInitJson> templateInitJsonList = templateInitJsonRepository.selectAllTemplateInitJson();
        if(CollectionUtils.isEmpty(templateInitJsonList)) {
            throw new BusinessException("初始化模板配置为空！"+ JSONObject.toJSONString(req));
        }
        TemplateInitJson allTemplateInitJson = templateInitJsonList.stream().filter(f -> f.getInitJsonType().equals(TemplateInitJsonEnum.ALL.type)).findFirst().orElseThrow(() -> new BusinessException("初始化全量模板配置不存在！"+ JSONObject.toJSONString(req)));
        //区分会话模板默认处理
        String initJsonType = TemplateInitJsonEnum.AUTO_REPLY.type;
        if(AutoReplyTypeEnum.CONVERSATION_STYLE_CONFIG.type.equals(req.getMsgType())){
            initJsonType = TemplateInitJsonEnum.CREATE_CONVERSATION_STYLE_CONFIG.type;
        }
        String finalInitJsonType = initJsonType;
        TemplateInitJson autoReplyTemplateInitJson = templateInitJsonList.stream().filter(f -> f.getInitJsonType().equals(finalInitJsonType)).findFirst().orElseThrow(() -> new BusinessException("初始化渠道自动回复模板配置不存在！"+ JSONObject.toJSONString(req)));
        GetAutoReplyConfigResp autoReplyConfigResp = ChannelBoBuilder.buildGetAutoReplyConfigRespV2(req, clientChannelConfigDraft, autoReplyTemplateInitJson.getContent(), allTemplateInitJson.getContent());
        ConversationAutoReplyConfigBoV2 conversationAutoReplyConfigBo = autoReplyConfigResp.getConversationAutoReplyConfigBo();
        if(Objects.nonNull(conversationAutoReplyConfigBo)) {
            List<ConversationAutoReplyDetailConfigBo> autoReplyDetailConfigBoList = conversationAutoReplyConfigBo.getConversationAutoReplyDetailConfigBoList();
            if( !BaseUtil.hasLongValue(conversationAutoReplyConfigBo.getConversationAutoReplyConfigId())) {
                conversationAutoReplyConfigBo.setConversationAutoReplyConfigId(super.getGuid());
            }
            if(CollectionUtils.isNotEmpty(autoReplyDetailConfigBoList)) {
                // 设置主键
                autoReplyDetailConfigBoList.forEach(detail -> {
                    if( !BaseUtil.hasLongValue(detail.getConversationAutoReplyDetailConfigId())) {
                        detail.setConversationAutoReplyDetailConfigId(super.getGuid());
                    }
                    if( !BaseUtil.hasLongValue(detail.getConversationAutoReplyConfigId())) {
                        detail.setConversationAutoReplyConfigId(conversationAutoReplyConfigBo.getConversationAutoReplyConfigId());
                    }
                });
            }
        }
        return autoReplyConfigResp;
    }

    /**
     * 新增或编辑会话满意度配置
     *
     */
    @Override
    public Long createOrEditAutoReplyConfig(CreateOrEditAutoReplyConfigReq req) {
        ClientChannelConfigDraft draft = clientChannelConfigDraftRepository.selectClientChannelConfigDraft(req.getClientCategoryId());
        if(Objects.isNull(draft)) {
            throw new BusinessException("端侧草稿不存在！");
        }
        // 校验消息类型一致性
        if(CollectionUtils.isNotEmpty(req.getConversationAutoReplyDetailConfigBoList())) {
            req.getConversationAutoReplyDetailConfigBoList().forEach(detail -> {
                if(!req.getMsgType().equals(detail.getMsgType())) {
                    throw new BusinessException("req.msgType和detail.msgType的消息类型不一致！" + req.getMsgType() + "!=" + detail.getMsgType());
                }
            });
        }
        if(!BaseUtil.hasLongValue(req.getAutoReplyConfigId())) {
            req.setAutoReplyConfigId(super.getGuid());
        }
        String autoReplyJson = JSON.toJSONString(req.getConversationAutoReplyDetailConfigBoList());
        req.setAutoReplyJson(autoReplyJson);
        // 2. 使用工具类合并配置
        AllConfigJsonBo updatedConfig = ChannelBoBuilder.buildAndMergeAutoReplyConfig(draft, req);

        updatedConfig.getChannelConfigBoList().stream().filter(channel -> channel.getChannelId().equals(req.getChannelId()))
                .forEach(channel -> {

            if(!BaseUtil.hasLongValue(channel.getChannelId())) {
                // 没有主键的时候设置主键
                channel.setChannelId(super.getGuid());
            }
            if(Objects.isNull(channel.getCreateTime())) {
                channel.setCreateTime(new Date());
            }
            channel.setUpdateTime(new Date());
            channel.setOperatorId(req.getOperatorId());

            ConversationAutoReplyConfigBo autoReply = channel.getConversationAutoReplyConfigBoList().stream().filter(f -> req.getMsgType().equals(f.getMsgType())).findFirst().orElseThrow(() -> new BusinessException("未找到该自动回复类型 " + req.getMsgType()));


                if(!BaseUtil.hasLongValue(autoReply.getConversationAutoReplyConfigId())) {
                    // 没有主键的时候设置主键
                    autoReply.setConversationAutoReplyConfigId(super.getGuid());
                }
                if(Objects.isNull(autoReply.getCreateTime())) {
                    autoReply.setCreateTime(new Date());
                }
                autoReply.setUpdateTime(new Date());
                autoReply.setOperatorId(req.getOperatorId());
                autoReply.getConversationAutoReplyDetailConfigBoList().forEach(detail -> {
                    if(!BaseUtil.hasLongValue(detail.getConversationAutoReplyDetailConfigId())) {
                        // 没有主键的时候设置主键
                        detail.setConversationAutoReplyConfigId(super.getGuid());
                    }
                    if(Objects.isNull(detail.getCreateTime())) {
                        detail.setCreateTime(new Date());
                    }
                    detail.setUpdateTime(new Date());
                    detail.setOperatorId(req.getOperatorId());
                });
            });


        // 3. 更新草稿表字段
        draft.setAllConfigJson(JSON.toJSONString(updatedConfig));

        if (draft.getClientChannelConfigDraftId() == null) {
            draft.setClientChannelConfigDraftId(req.getClientCategoryId());
        }
        return clientChannelConfigDraftRepository.insertOrUpdateClientCategoryDraft(draft);
    }

    /**
     * 按钮图标上传
     */
    @Override
    public ImgUploadResp iconUpload(IconUploadReq req, MultipartFile file) {
        if(!BaseUtil.hasLongValue(req.getSatisfactionLevelConfigId())) {
            throw new BusinessException("满意度配置ID不能为空！");
        }
        if(StringUtils.isEmpty(req.getButtonName())) {
            throw new BusinessException("图标名字不能为空！");
        }
        String iconRedisKey = String.format(CommonRedisConstant.PICTURE_URL_KEY, req.getSatisfactionLevelConfigId() + "_" + req.getButtonName());
        ImgUploadResp iconResp = redisHelper.getObject(iconRedisKey, ImgUploadResp.class);
        if(Objects.nonNull(iconResp)) {
            return iconResp;
        }
        try {
            iconResp = uploadFileUtil.uploadFileToCdn(file, file.getInputStream());
            if(Objects.nonNull(iconResp)
                    && StringUtils.isNotEmpty(iconResp.getPath())
                    && ! CommonConstant.NULL_STRING.equals(iconResp.getPath())) {
                redisHelper.setObject(iconRedisKey, iconResp, CommonRedisConstant.PICTURE_URL_KEY_CACHE_TIME);
            }
        } catch (IOException e) {
            log.error("按钮图标文件上传失败！", e);
            throw new BusinessException(e.getMessage());
        }
        return iconResp;
    }

    /**
     * 用户咨询问题列表
     *
     * @param req
     */
    @Override
    public SimplePageInfo<ChannelUserProblemResp> userProblemClassList(ChannelUserProblemClassReq req) {

        ChannelConfig channelConfig = channelConfigRepository.selectByPrimaryKey(req.getChannelId());
//        if(Objects.isNull(channelConfig)) {
//            log.error("渠道不存在：channelId:{}", req.getChannelId());
//            return null;
//        }

        Page<ChannelUserProblemResp> pageHelper = PageHelper.startPage(req.getPageNum(), req.getPageSize());

        List<UserProblemClass> userProblemClassByChannelIdList = userProblemClassRepository.getUserProblemClassByChannelId(req.getChannelId());
        if(CollectionUtils.isEmpty(userProblemClassByChannelIdList)) {
            return null;
        }

        return getChannelUserProblemRespSimplePageInfo(pageHelper, userProblemClassByChannelIdList);
    }

    private static SimplePageInfo<ChannelUserProblemResp> getChannelUserProblemRespSimplePageInfo(Page<ChannelUserProblemResp> pageHelper, List<UserProblemClass> userProblemClassByChannelIdList) {
        SimplePageInfo<ChannelUserProblemResp> result = new SimplePageInfo<>();
        result.setPageNum(pageHelper.getPageNum());
        result.setPageSize(pageHelper.getPageSize());
        result.setTotal(pageHelper.getTotal());
        result.setPages(pageHelper.getPages());
        result.setList(Lists.newArrayList());

        List<ChannelUserProblemResp> channelUserProblemRespList = new ArrayList<>();
        for(UserProblemClass userProblemClass : userProblemClassByChannelIdList) {
            ChannelUserProblemResp channelUserProblemResp = new ChannelUserProblemResp();
            channelUserProblemResp.setProblemId(userProblemClass.getProblemId());
            channelUserProblemResp.setProblemName(userProblemClass.getProblemName());
            channelUserProblemResp.setStatus(userProblemClass.getStatus());
            channelUserProblemRespList.add(channelUserProblemResp);
        }
        result.setList(channelUserProblemRespList);
        return result;
    }

    /**
     * 用户咨询问题分类-删除
     *
     * @param req
     */
    @Override
    public Integer userProblemClassDel(ChannelUserProblemDelReq req) {

        UserProblemClass userProblemClass1 = userProblemClassRepository.selectByPrimaryKey(req.getProblemId());
        if(Objects.isNull(userProblemClass1)){
            throw new BusException("找不到问题分类信息");
        }

        if(EnableStatusEnum.ENABLE.type.equals(userProblemClass1.getStatus())){
            throw new BusException("问题分类启用状态，不能删除");
        }


        UserProblemClass userProblemClass = new UserProblemClass();
        userProblemClass.setChannelId(req.getChannelId());
        userProblemClass.setProblemId(req.getProblemId());
        userProblemClass.setIsDelete(1);
        userProblemClass.setUpdateTime(new Date());
        userProblemClass.setUpdateAccountId(req.getAccountId());

        userProblemClassRepository.updateByPrimaryKeySelective(userProblemClass);

        return 1;
    }

    /**
     * 用户咨询问题分类-修改状态
     *
     * @param req
     */
    @Override
    public Integer userProblemClassChangeStatus(ChannelUserProblemChangeStatusReq req) {

        ChannelConfig channelConfig = channelConfigRepository.selectByPrimaryKey(req.getChannelId());
//        if(Objects.isNull(channelConfig)){
//            throw new BusinessException("找不到渠道信息");
//        }
        UserProblemClass userProblemClass = userProblemClassRepository.selectByPrimaryKey(req.getProblemId());
        if(Objects.isNull(userProblemClass)){
            throw new BusException("找不到问题分类信息");
        }
        String status = userProblemClass.getStatus();
        if(req.getStatus().equals(status)){
            throw new BusException("状态未改变");
        }

        UserProblemClass userProblemClass1 = new UserProblemClass();
        userProblemClass1.setProblemId(req.getProblemId());
        userProblemClass1.setStatus(req.getStatus());
        userProblemClass1.setUpdateTime(new Date());
        userProblemClass1.setUpdateAccountId(req.getAccountId());
        userProblemClass1.setChannelId(req.getChannelId());

        userProblemClassRepository.updateByPrimaryKeySelective(userProblemClass1);

        return 1;
    }

    /**
     * 用户咨询问题分类-新增
     *
     * @param req
     */
    @Override
    public Long userProblemClassAdd(ChannelUserProblemAddReq req) {

        List<UserProblemClass> userProblemClassByName = userProblemClassRepository.getUserProblemClassByName(req.getProblemName(), req.getChannelId());
        if(CollectionUtils.isNotEmpty(userProblemClassByName)){
            throw new BusException("存在未删除的用户问题分类重名");
        }

        UserProblemClass userProblemClass = new UserProblemClass();
        userProblemClass.setChannelId(req.getChannelId());
        userProblemClass.setProblemName(req.getProblemName());
        userProblemClass.setStatus(EnableStatusEnum.ENABLE.type);
        userProblemClass.setUpdateAccountId(req.getAccountId());
        userProblemClass.setCreateAccountId(req.getAccountId());
        userProblemClass.setIsDelete(0);
        userProblemClass.setUpdateTime(new Date());
        userProblemClass.setCreateTime(new Date());
        userProblemClassRepository.insertSelective(userProblemClass);
        return userProblemClass.getProblemId();
    }

    /**
     * 获取渠道信息列表
     * @param req
     * @return
     */
    @Override
    public List<GetChannelConfigDraftListResp> getChannelConfigDraftList(GetChannelConfigDraftListRqt req) {

        return channelConfigRepository.getChannelConfigDraftList();
    }

}
