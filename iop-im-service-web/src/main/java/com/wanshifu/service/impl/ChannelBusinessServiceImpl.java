package com.wanshifu.service.impl;

import com.wanshifu.iop.im.api.req.channel.GetConversationThemeInfoRqt;
import com.wanshifu.iop.im.api.req.channel.PreviewClientCategoryAndChannelReq;
import com.wanshifu.iop.im.api.req.channel.SaveSatisfactionDataRqt;
import com.wanshifu.iop.im.api.resp.channel.GetConversationThemeInfoResp;
import com.wanshifu.iop.im.api.resp.channel.PreviewClientCategoryAndChannelResp;
import com.wanshifu.iop.im.domain.po.SatisfactionEvaluateResultRecord;
import com.wanshifu.iop.im.domain.po.channel.ConversationStyleConfig;
import com.wanshifu.iop.im.domain.po.channel.SatisfactionEvaluateRecord;
import com.wanshifu.repository.channel.ConversationStyleConfigRepository;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.ChannelBusinessService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * @author:<EMAIL>
 * @create:2025-07-24 11:41:53
 * @Description ：
 **/
@Service
@Slf4j
public class ChannelBusinessServiceImpl extends AbstractService implements ChannelBusinessService {
    @Resource
    private ConversationStyleConfigRepository conversationStyleConfigRepository;
    /**
     * 预览渠道
     * @param req
     * @return
     */
    @Override
    public PreviewClientCategoryAndChannelResp previewDraft(PreviewClientCategoryAndChannelReq req) {
        return null;
    }

    /**
     * 获取会话主题信息
     * @param req
     * @return
     */
    @Override
    public GetConversationThemeInfoResp getConversationThemeInfo(GetConversationThemeInfoRqt req) {
        ConversationStyleConfig conversationStyleConfig = conversationStyleConfigRepository.selectByUniqueCondition(req.getChannelId(),req.getClientPortType());
        if(conversationStyleConfig==null){
            return null;
        }
        GetConversationThemeInfoResp getConversationThemeInfoResp = new GetConversationThemeInfoResp();
        BeanUtils.copyProperties(conversationStyleConfig,getConversationThemeInfoResp);

        return getConversationThemeInfoResp;
    }

    /**
     * 保存满意度数据
     * @param req
     * @return
     */
    @Override
    public Integer saveSatisfactionData(SaveSatisfactionDataRqt req) {
        SatisfactionEvaluateRecord satisfactionEvaluateRecord = satisfactionEvaluateRecordRepository.selectByPrimaryKey(req.getSatisfactionEvaluateRecordId());
        if(null == satisfactionEvaluateRecord){
            throw new RuntimeException("渠道记录不存在,请检查");
        }
        SatisfactionEvaluateResultRecord satisfactionEvaluateResultRecord = new SatisfactionEvaluateResultRecord();

        BeanUtils.copyProperties(req,satisfactionEvaluateResultRecord);
        satisfactionEvaluateResultRecord.setResultCallbackFormJson(satisfactionEvaluateRecord.getResultCallbackFormJson());
        int i = satisfactionEvaluateResultRecordRepository.insertSelective(satisfactionEvaluateResultRecord);
        return i;
    }
}