package com.wanshifu.service.impl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.components.xxl.AddXxlJob;
import com.wanshifu.components.xxl.XxlJobTemplate;
import com.wanshifu.constant.ChannelConstant;
import com.wanshifu.iop.im.api.req.group.DistributeEnGroupReq;
import com.wanshifu.iop.im.domain.bo.channel.msg.ToArtificialTypesBo;
import com.wanshifu.iop.im.domain.bo.channel.reply.SeatEnterExtraJsonBo;
import com.wanshifu.iop.im.domain.bo.conversation.FormCustomerMsgContentBo.FromData;
import com.wanshifu.iop.im.domain.bo.conversation.FormCustomerMsgContentBo;
import com.wanshifu.iop.im.domain.bo.conversation.FormMsgContentFormatBo;
import com.wanshifu.iop.im.domain.bo.conversation.TextMsgContentFormatBo.TextMsgContentFormatContent;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSON;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.ConversationConstant;
import com.wanshifu.constant.TencentConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.iop.im.api.req.inter.GetGroupListReq;
import com.wanshifu.iop.im.api.req.inter.GetSeatImInfoReq;
import com.wanshifu.iop.im.api.req.inter.GetVirtualUserInfoReq;
import com.wanshifu.iop.im.api.req.inter.RegisterUserReq;
import com.wanshifu.iop.im.api.req.inter.ToArtificialServiceReq;
import com.wanshifu.iop.im.api.resp.inter.GetGroupListResp;
import com.wanshifu.iop.im.api.resp.inter.GetVirtualUserInfoResp;
import com.wanshifu.iop.im.api.resp.inter.RegisterUserResp;
import com.wanshifu.iop.im.domain.bo.AIWorkflowExecuteResultMsgBo;
import com.wanshifu.iop.im.domain.bo.CreateGroupReqBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgGroupReqBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgTagBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToGroupBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToGroupRespBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToSingleBo;
import com.wanshifu.iop.im.domain.bo.RegisterUserReqBo;
import com.wanshifu.iop.im.domain.bo.RegisterUserRespBo;
import com.wanshifu.iop.im.domain.bo.SendWelcomeMsgBo;
import com.wanshifu.iop.im.domain.bo.ToArtificialMsgBo;
import com.wanshifu.iop.im.domain.bo.UserInfoBo;
import com.wanshifu.iop.im.domain.bo.conversation.TextMsgContentFormatBo;
import com.wanshifu.iop.im.domain.bo.conversation.XxlTimeoutReqBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.UpdateMsgSingleBo;
import com.wanshifu.iop.im.domain.enums.BindStatusEnum;
import com.wanshifu.iop.im.domain.enums.ConversationStatusEnum;
import com.wanshifu.iop.im.domain.enums.ConversationFromTypeEnum;
import com.wanshifu.iop.im.domain.enums.EnableStatusEnum;
import com.wanshifu.iop.im.domain.enums.IncomingControlTypeEnum;
import com.wanshifu.iop.im.domain.enums.MsgTypeEnum;
import com.wanshifu.iop.im.domain.enums.NodeTypeEnum;
import com.wanshifu.iop.im.domain.enums.OnlineStateEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserClassTypeEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserTypeEnum;
import com.wanshifu.iop.im.domain.enums.RegisterStatusEnum;
import com.wanshifu.iop.im.domain.enums.TriggerModeEnum;
import com.wanshifu.iop.im.domain.enums.UserClassEnum;
import com.wanshifu.iop.im.domain.enums.VirtualStatusEnum;
import com.wanshifu.iop.im.domain.po.FlowNode;
import com.wanshifu.iop.im.domain.po.ImConfig;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.ImConversationLog;
import com.wanshifu.iop.im.domain.po.ImGroup;
import com.wanshifu.iop.im.domain.po.IncomingPropertyConfig;
import com.wanshifu.iop.im.domain.po.IncomingSafetyConfig;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.UserProblemClass;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import com.wanshifu.iop.im.domain.po.UserVirtualRelation;
import com.wanshifu.iop.im.domain.po.VirtualUserInfo;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyDetailConfig;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.mq.producer.ImportMsgGroupProducer;
import com.wanshifu.mq.producer.ImportMsgProducer;
import com.wanshifu.mq.producer.ImportMsgSingleProducer;
import com.wanshifu.mq.producer.SendWelcomeMsgProducer;
import com.wanshifu.mq.producer.TimeoutAddXxlJobProducer;
import com.wanshifu.mq.producer.ToArtificialWelcomeMsgProducer;
import com.wanshifu.repository.ImConfigRepository;
import com.wanshifu.repository.ImConversationDistributeRepository;
import com.wanshifu.repository.ImConversationItemRepository;
import com.wanshifu.repository.ImConversationLogRepository;
import com.wanshifu.repository.ImConversationRepository;
import com.wanshifu.repository.ImGroupRepository;
import com.wanshifu.repository.SeatInfoRepository;
import com.wanshifu.repository.UserDeviceRelationRepository;
import com.wanshifu.repository.UserProblemClassRepository;
import com.wanshifu.repository.UserRegisterInfoRepository;
import com.wanshifu.repository.UserVirtualRelationRepository;
import com.wanshifu.repository.VirtualUserInfoRepository;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.DistributeService;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

import static com.wanshifu.constant.ChannelConstant.KF_NICK_NAME;

@Service
@Slf4j
public class ImInterServiceImpl extends AbstractService implements ImInterService {


    @Resource
    private TencentManager tencentManager;

    @Resource
    private UserRegisterInfoRepository userRegisterInfoRepository;

    @Resource
    private UserDeviceRelationRepository userDeviceRelationRepository;

    @Resource
    private VirtualUserInfoRepository virtualUserInfoRepository;

    @Resource
    private UserVirtualRelationRepository userVirtualRelationRepository;

    @Resource
    private PlatformTransactionManager platformTransactionManager;

    @Resource
    private ImConversationRepository imConversationRepository;

    @Resource
    private ImConversationLogRepository imConversationLogRepository;

    @Resource
    private ImConversationItemRepository imConversationItemRepository;

    @Resource
    private ImportMsgSingleProducer importMsgSingleProducer;

    @Resource
    private ImportMsgGroupProducer importMsgGroupProducer;

    @Resource
    private ImConfigRepository imConfigRepository;

    @Resource
    private SeatInfoRepository seatInfoRepository;

    @Resource
    private ImGroupRepository imGroupRepository;

    @Resource
    private SendWelcomeMsgProducer sendWelcomeMsgProducer;

    @Resource
    private ImConversationDistributeRepository imConversationDistributeRepository;

    @Resource
    private UserProblemClassRepository userProblemClassRepository;

    @Resource
    private TimeoutAddXxlJobProducer timeoutAddXxlJobProducer;


    /**
     * 注册用户
     *
     * @param req
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RegisterUserResp registerUser(RegisterUserReq req) {

        //参数校验
        Long userId = req.getUserId();
        String deviceId = req.getDeviceId();
        if (ObjectUtils.isEmpty(deviceId) && ObjectUtils.isEmpty(userId)) {
            throw new BusException("设备id和用户id必须有一个不为空");
        }

        //参数合法性校验
        if (!this.registerCheck(req)) {
            throw new BusException("用户参数校验不通过");
        }

        RegisterUserResp result = new RegisterUserResp();

        UserRegisterInfo userRegisterInfoExistCheck = this.checkRegisterStatus(req.getDeviceId(), req.getUserId(), req.getUserClass());

        ImConfig imConfigInfo = imConfigRepository.getImConfigInfo(CommonConstant.DEFAULT_TENANT_ID, CommonConstant.DEFAULT_IM_TYPE);
        if (imConfigInfo == null) {
            throw new BusException("IM配置不存在");
        }
        result.setSdkAppId(imConfigInfo.getImThirdId());
        if (!ObjectUtils.isEmpty(userRegisterInfoExistCheck)) {

            result.setOuterUserId(userRegisterInfoExistCheck.getOuterUserId());
            result.setUserSig(userRegisterInfoExistCheck.getUserSign());
            return result;
        }

        UserRegisterInfo userRegisterInfo = this.returnUserRegisterInfoBean(req);

        RegisterUserReqBo registerUserReqBo = new RegisterUserReqBo();
        registerUserReqBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        registerUserReqBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        registerUserReqBo.setNick("");
        registerUserReqBo.setFaceUrl("");

        RegisterUserRespBo registerUserRespBo = tencentManager.registerUser(registerUserReqBo);
        if (registerUserRespBo == null) {
            return null;
        }
        userRegisterInfo.setOuterUserId(registerUserRespBo.getOuterUserId());

        if (StringUtils.isEmpty(registerUserRespBo.getUserSign())) {
            //注册失败，用户注册记录表
            userRegisterInfoRepository.insertSelective(userRegisterInfo);
            return null;
        }

        userRegisterInfo.setRegisterStatus(RegisterStatusEnum.SUCCESS.type);
        userRegisterInfo.setUserSign(registerUserRespBo.getUserSign());
        userRegisterInfo.setUserSignExpireTime(DateUtils.addYears(new Date(), TencentConstant.SIGN_EXPIRE_TIME));
        userRegisterInfoRepository.insertSelective(userRegisterInfo);

        result.setOuterUserId(registerUserRespBo.getOuterUserId());
        result.setUserSig(registerUserRespBo.getUserSign());
        return result;
    }

    /**
     * 注册校验
     * true:校验通过，
     * false:校验不通过
     */
    private Boolean registerCheck(RegisterUserReq req) {

        //访问域名校验
        IncomingSafetyConfig incomingSafetyConfig = incomingSafetyConfigRepository.selectInfoByTenantId(CommonConstant.DEFAULT_TENANT_ID);
        if (incomingSafetyConfig == null) {
            log.info("安全域名未配置");
            return false;
        }
        String urlFroms = incomingSafetyConfig.getUrlFroms();
        if (StringUtils.isEmpty(urlFroms)) {
            log.info("安全域名未配置");
            return false;
        }
        String[] urlFromsArr = urlFroms.split(",");
        List<String> urlFromList = Arrays.stream(urlFromsArr).collect(Collectors.toList());
        //todo  域名先屏蔽
//        if (!urlFromList.contains(req.getSystemFromUrl())) {
//            log.info("访问域名非法：url={}",req.getSystemFromUrl());
//            return false;
//        }

        //访问ip校验，userId，设备id校验
        List<String> values = Stream.of(req.getUserId() + "", req.getDeviceId(), req.getIp()).collect(Collectors.toList());

        List<IncomingPropertyConfig> propertyConfigList = incomingPropertyConfigRepository.getPropertyConfigList(CommonConstant.DEFAULT_TENANT_ID, values, new ArrayList<>());
        if (CollectionUtils.isEmpty(propertyConfigList)) {
            return true;
        }

        List<IncomingPropertyConfig> blackIncomingPropertyConfigList = propertyConfigList.stream().filter(f -> IncomingControlTypeEnum.black.type.equals(f.getControlType())).collect(Collectors.toList());

        Optional<IncomingPropertyConfig> blackIncomingPropertyOptional = blackIncomingPropertyConfigList.stream().filter(f ->
                f.getPropertyValue().equals(req.getUserId() + "") || f.getPropertyValue().equals(req.getIp()) || f.getPropertyValue().equals(req.getDeviceId())).findFirst();
        if (blackIncomingPropertyOptional.isPresent()) {
            return false;
        }
        return true;
    }

    private UserRegisterInfo returnUserRegisterInfoBean(RegisterUserReq req) {
        UserRegisterInfo userRegisterInfo = new UserRegisterInfo();
        userRegisterInfo.setImId(CommonConstant.DEFAULT_TENANT_ID);
        userRegisterInfo.setUserId(req.getUserId());
        userRegisterInfo.setUserClass((req.getUserId() == null || req.getUserId() == 0L) ? UserClassEnum.TOURIST.type : req.getUserClass());
        userRegisterInfo.setOuterUserId("");
        userRegisterInfo.setDeviceId(req.getDeviceId());
        userRegisterInfo.setRegisterStatus(RegisterStatusEnum.FAIL.type);
        userRegisterInfo.setRegisterTime(new Date());
        userRegisterInfo.setUserSign("");
        userRegisterInfo.setStatus(0);
        userRegisterInfo.setIsDelete(0);
        userRegisterInfo.setUpdateTime(new Date());
        userRegisterInfo.setCreateTime(new Date());
        userRegisterInfo.setOnlineState(OnlineStateEnum.ONLINE.type);
        userRegisterInfo.setOnlineStateChangeTime(new Date());
        return userRegisterInfo;
    }

    /**
     * 验证当前用户是否已经注册过
     */
    private UserRegisterInfo checkRegisterStatus(String deviceId, Long userId, String userClass) {

        if (userId != null && userId != 0L) {
            UserRegisterInfo byUserId = userRegisterInfoRepository.findByUserIdAndDeviceId(userId, userClass, deviceId, CommonConstant.ONE);
            if (!ObjectUtils.isEmpty(byUserId)) {
                return byUserId;
            }
            return null;
        }

        if (!StringUtils.isEmpty(deviceId)) {
            //如果未登陆的情况
            return userRegisterInfoRepository.findByDeviceId(deviceId);
        }

        return null;
    }


    /**
     * 批量注册虚拟用户
     *
     * @param num
     */
    @Override
    public Integer batchRegisterUser(Integer num) {

        if (num > 50) {
            throw new BusException("批量注册用户数量不能超过50");
        }

        List<VirtualUserInfo> virtualUserInfoList = new ArrayList<>();

        for (int i = 0; i < num; i++) {

            RegisterUserReqBo registerUserReqBo = new RegisterUserReqBo();
            registerUserReqBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            registerUserReqBo.setImType(CommonConstant.DEFAULT_IM_TYPE);

            RegisterUserRespBo registerUserRespBo = tencentManager.registerUser(registerUserReqBo);
            if (registerUserRespBo == null) {
                continue;
            }

            VirtualUserInfo virtualUserInfo = new VirtualUserInfo();
            virtualUserInfo.setImId(CommonConstant.DEFAULT_TENANT_ID);
            virtualUserInfo.setOuterUserId(registerUserRespBo.getOuterUserId());
            virtualUserInfo.setRegisterTime(new Date());
            virtualUserInfo.setUserSign(registerUserRespBo.getUserSign());
            virtualUserInfo.setUserSignExpireTime(DateUtils.addYears(new Date(), TencentConstant.SIGN_EXPIRE_TIME));
            virtualUserInfo.setIsDelete(0);
            virtualUserInfo.setStatus(VirtualStatusEnum.NO_USED.type);
            virtualUserInfo.setUpdateTime(new Date());
            virtualUserInfo.setCreateTime(new Date());
            virtualUserInfoList.add(virtualUserInfo);
        }

        if (CollectionUtils.isNotEmpty(virtualUserInfoList)) {
            virtualUserInfoRepository.insertList(virtualUserInfoList);
            return 1;
        }
        return 0;
    }

    @Resource
    private ImportMsgProducer importMsgProducer;

    /**
     * 作用：获取虚拟用户信息，绑定关系，系统自动发送欢迎语消息
     *
     * @param req
     */
    @Override
    public GetVirtualUserInfoResp getVirtualInfo(GetVirtualUserInfoReq req) {

        GetVirtualUserInfoReq.OuterUserInfoIem from = req.getFrom();

        GetVirtualUserInfoReq.OuterUserInfoIem reqTo = req.getTo();

        //验证入参用户id是否已经注册
        UserRegisterInfo byOuterUserId = userRegisterInfoRepository.findByOuterUserId(from.getOuterUserId());
        if (ObjectUtils.isEmpty(byOuterUserId)) {
            throw new BusException("用户没有注册" + JSON.toJSONString(req));
        }

        String channelEn = req.getChannelEn();
        ChannelConfig channelConfigByChannelEn = channelConfigRepository.getChannelConfigByChannelEn(channelEn);
        if(channelConfigByChannelEn==null){
            throw new BusException("渠道不存在,channel_en="+channelEn);
        }

        GetVirtualUserInfoResp result = new GetVirtualUserInfoResp();
        result.setOuterUserId(from.getOuterUserId());
        if (reqTo != null && !StringUtils.isEmpty(reqTo.getOuterUserId())) {
            UserInfoBo userInfoBo = super.getUserInfoBo(Long.valueOf(reqTo.getOuterUserId()), reqTo.getOuterUserType());
            result.setVirtualUserNickname(userInfoBo.getUserName());
        } else {
            result.setVirtualUserNickname(CommonConstant.VIRTUAL_USER_NICKNAME);
        }
        //检测用户是否已经绑定关系
        UserVirtualRelation relationByOuterUserId = userVirtualRelationRepository.getRelationByOuterUserId(from.getOuterUserId(), BindStatusEnum.BINDING.type);

        if (!ObjectUtils.isEmpty(relationByOuterUserId)) {
            result.setVirtualUserId(relationByOuterUserId.getVirtualUserId());
            //发送欢迎语
            this.sendWelcomeMsg(result);
            return result;
        }
        //如果没有绑定关系，就需要创建用户会话
        VirtualUserInfo notUsedVirtualInfo = virtualUserInfoRepository.getNotUsedVirtualInfo();
        if (ObjectUtils.isEmpty(notUsedVirtualInfo)) {
            throw new BusException("没有空闲虚拟账号");
        }

        //绑定关系
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        Long conversationId = req.getCId();
        try {
            UserVirtualRelation userVirtualRelation = this.returnUserVirtualRelationBean(from, notUsedVirtualInfo);
            //绑定关系，用户和聊天对象的关系
            userVirtualRelationRepository.insertSelective(userVirtualRelation);
            //更新虚拟用户状态
            virtualUserInfoRepository.updateVirtualStatus(notUsedVirtualInfo.getVirtualInfoId(), VirtualStatusEnum.USED.type);
            //进入会话先写死用户类型, 初次进线，不设置to_outer_user_id
            String toOuterUserType = "";
            String toOuterUserId = notUsedVirtualInfo.getOuterUserId();

            if (req.getCId() != null && req.getCId() > 0L) {
                //如果有会话id，绑定关系已经结束，说明当前虚拟账号已取消绑定，需要生成新的虚拟账号，需要导入单聊消息+群聊消息
                //更新当前会话，并写入日志
                this.updateImConversation(req.getCId(), from.getOuterUserId(), notUsedVirtualInfo.getOuterUserId());

            } else {
                ImConversation imConversation = this.returnImConversationBean(byOuterUserId.getOuterUserId(), from.getOuterUserType(), toOuterUserId, toOuterUserType, req.getChannelId());
                imConversation.setChannelConfigId(channelConfigByChannelEn.getChannelId());
                imConversationRepository.insertSelective(imConversation);
                //创建会话，如果是用户和师傅之间的对话需要建群，此外不需要建群，在用户发消息后回调时才建群
                if (reqTo != null && !StringUtils.isEmpty(reqTo.getOuterUserId())) {
                    toOuterUserType = reqTo.getOuterUserType();

                    //注册单聊对象用户
                    UserRegisterInfo userRegisterInfoByIdAndType = super.getUserRegisterInfoByIdAndType(Long.valueOf(reqTo.getOuterUserId()), toOuterUserType);
                    //创建群聊
                    this.returnGroupIdStr(imConversation.getConversationId(), notUsedVirtualInfo.getOuterUserId(), userRegisterInfoByIdAndType.getOuterUserId(), OuterUserClassTypeEnum.VIRTUAL.type);
                }
                conversationId = imConversation.getConversationId();
            }

            platformTransactionManager.commit(status);
        } catch (Exception e) {
            platformTransactionManager.rollback(status);
        }

        if (req.getCId() != null && req.getCId() > 0L) {
            //如果有会话id，绑定关系已经结束，说明当前虚拟账号已取消绑定，需要生成新的虚拟账号，需要导入单聊消息+群聊消息
            ImportMsgTagBo importMsgTagBo = new ImportMsgTagBo();
            importMsgTagBo.setConversationId(req.getCId());
            importMsgProducer.sendMessage(JSON.toJSONString(importMsgTagBo));
        }
        result.setVirtualUserId(notUsedVirtualInfo.getOuterUserId());
        //发送欢迎语
        this.sendWelcomeMsg(result);

        //添加定时任务
        XxlTimeoutReqBo xxlTimeoutReqBo = new XxlTimeoutReqBo();
        xxlTimeoutReqBo.setConversationId(conversationId);
        timeoutAddXxlJobProducer.sendMessage(xxlTimeoutReqBo);

        return result;
    }


    /**
     * 发送机器人欢迎语
     */
    private void sendWelcomeMsg(GetVirtualUserInfoResp result) {
        SendWelcomeMsgBo sendWelcomeMsgBo = new SendWelcomeMsgBo();
        sendWelcomeMsgBo.setFromAccountId(result.getVirtualUserId());
        sendWelcomeMsgBo.setToAccountId(result.getOuterUserId());
        sendWelcomeMsgProducer.sendMessage(sendWelcomeMsgBo);

    }

    /**
     * 生成入参、建群
     */
    public String returnGroupIdStr(Long conversationId, String toAccount, String seatOuterUserId, String outerClassType) {
        //创建群聊成员：虚拟账号+指定测试账号对应的outerUserId
        CreateGroupReqBo createGroupReqBo = new CreateGroupReqBo();
        createGroupReqBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        createGroupReqBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        createGroupReqBo.setGroupName(CommonConstant.GROUP_NOT_SEAT_NAME);
        createGroupReqBo.setConversationId(conversationId);
        createGroupReqBo.setAdminOuterUserId(toAccount);
        createGroupReqBo.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);

        List<CreateGroupReqBo.CreateGroupReqBoItem> groupMembers = new ArrayList<>();
        CreateGroupReqBo.CreateGroupReqBoItem item = new CreateGroupReqBo.CreateGroupReqBoItem();
        item.setOuterUserId(toAccount);
        item.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);
        groupMembers.add(item);
        CreateGroupReqBo.CreateGroupReqBoItem itemSeat = new CreateGroupReqBo.CreateGroupReqBoItem();
        itemSeat.setOuterUserId(seatOuterUserId);
        itemSeat.setOuterClassType(OuterUserClassTypeEnum.SEAT.type);
        groupMembers.add(itemSeat);

        createGroupReqBo.setGroupMembers(groupMembers);
        try {
            String groupId = tencentManager.createGroup(createGroupReqBo);
            if (groupId == null) {
                throw new BusException("创建群聊失败");
            }
            return groupId;
        } catch (BusException e) {
            throw new BusException(e.getMessage());
        }
    }

    /**
     * 单聊导入+更新群消息+导入群消息
     *
     * @param req
     */
    @Override
    public Integer importMsgAction(ImportMsgTagBo req) {

        Long conversationId = req.getConversationId();
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if (imConversation == null) {
            return 0;
        }

        //创建群聊
        CreateGroupReqBo createGroupReqBo = new CreateGroupReqBo();
        createGroupReqBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        createGroupReqBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        createGroupReqBo.setGroupName(CommonConstant.GROUP_NAME);
        createGroupReqBo.setConversationId(conversationId);
        createGroupReqBo.setAdminOuterUserId(imConversation.getToOuterUserId());
        createGroupReqBo.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);

        List<CreateGroupReqBo.CreateGroupReqBoItem> groupMembers = new ArrayList<>();

        CreateGroupReqBo.CreateGroupReqBoItem item = new CreateGroupReqBo.CreateGroupReqBoItem();
        item.setOuterUserId(imConversation.getFromOuterUserId());
        item.setOuterClassType(imConversation.getFromOuterUserType());
        groupMembers.add(item);

        CreateGroupReqBo.CreateGroupReqBoItem itemTo = new CreateGroupReqBo.CreateGroupReqBoItem();
        itemTo.setOuterUserId(imConversation.getAgentOuterUserId());
        itemTo.setOuterClassType(OuterUserClassTypeEnum.VIRTUAL.type);
        groupMembers.add(itemTo);
        createGroupReqBo.setGroupMembers(groupMembers);
        String group = tencentManager.createGroup(createGroupReqBo);
        if (StringUtils.isEmpty(group)) {
            log.error("创建群聊失败" + JSON.toJSONString(req));
            return 0;
        }

        List<ImConversationItem> imConversationItemsByConversationId = imConversationItemRepository.getImConversationItemsByConversationId(conversationId);

        //导入单聊消息
        this.importSingleChatMsg(imConversationItemsByConversationId);

        //导入群消息
        List<List<ImConversationItem>> lists = super.splitList(imConversationItemsByConversationId, CommonConstant.SEVEN);

        for (int i = 0; i < lists.size(); i++) {
            ImportMsgGroupReqBo importMsgGroupReqBo = new ImportMsgGroupReqBo();
            importMsgGroupReqBo.setImConversationItems(lists.get(i));
            importMsgGroupReqBo.setGroupId(group);
            importMsgGroupProducer.sendMessage(importMsgGroupReqBo, MqConfig.IMPORT_MSG_GROUP_DELAY_TIME * (i + 1));
        }
        return 1;
    }

    /**
     * 单聊消息
     *
     * @param req
     */
    @Override
    public Integer importMsgSingleAction(ImportMsgToSingleBo req) {
        return tencentManager.importMsgToSingle(req);
    }

    /**
     * 导入群聊消息
     *
     * @param req
     */
    @Override
    public Integer importMsgGroupAction(ImportMsgToGroupBo req) {
        ImportMsgToGroupRespBo importMsgToGroupRespBo = tencentManager.importMsgToGroup(req);
        //todo 可做扩展，失败重试
        return 1;
    }

    /**
     * 客服初始化 坐席信息
     *
     * @param req
     */
    @Override
    public RegisterUserResp getSeatImInfo(GetSeatImInfoReq req) {

        SeatInfo seatInfo = seatInfoRepository.selectInfoByAccountId(req.getAccountId(), null);
        if (ObjectUtils.isEmpty(seatInfo)) {
            throw new BusException("坐席不存在");
        }

        Long seatId = seatInfo.getSeatId();
        UserRegisterInfo byUserId = userRegisterInfoRepository.findByUserId(seatId, OuterUserTypeEnum.SEAT.type, CommonConstant.ONE);
        if (byUserId == null) {
            //如果没有注册，需要注册，兜底 todo
//            RegisterUserReqBo registerUserReqBo = new RegisterUserReqBo();
//            registerUserReqBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
//            registerUserReqBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
//            registerUserReqBo.setNick(seatInfo.getSeatName());
//            registerUserReqBo.setFaceUrl(seatInfo.getFaceUrl());
//            RegisterUserRespBo registerUserRespBo = tencentManager.registerUser(registerUserReqBo);

            throw new BusException("坐席没有生成对应的注册信息");
        }
        RegisterUserResp registerUserResp = new RegisterUserResp();
        registerUserResp.setOuterUserId(byUserId.getOuterUserId());
        registerUserResp.setUserSig(byUserId.getUserSign());

        ImConfig imConfigInfo = imConfigRepository.getImConfigInfo(CommonConstant.DEFAULT_TENANT_ID, CommonConstant.DEFAULT_IM_TYPE);
        if (imConfigInfo == null) {
            throw new BusException("IM配置不存在");
        }
        registerUserResp.setSdkAppId(imConfigInfo.getImThirdId());
        return registerUserResp;
    }

    /**
     * 坐席端-获取群列表
     *
     * @param req
     */
    @Override
    public List<GetGroupListResp> getGroupList(GetGroupListReq req) {

        String outerUserId = req.getOuterUserId();
        UserRegisterInfo byOuterUserId = userRegisterInfoRepository.findByOuterUserId(outerUserId);
        if (ObjectUtils.isEmpty(byOuterUserId)) {
            throw new BusException("传入的外部用户ID不存在");
        }
        List<ImConversation> infoByFromIdAndToIds = imConversationRepository.getInfoByFromIdAndToId(outerUserId);
        if (CollectionUtils.isEmpty(infoByFromIdAndToIds)) {
            return new ArrayList<>();
        }

        List<Long> conversationIdList = infoByFromIdAndToIds.stream().map(ImConversation::getConversationId).collect(Collectors.toList());

        List<ImGroup> imGroups = imGroupRepository.selectImGroupByConversationIds(conversationIdList);

        List<GetGroupListResp> result = new ArrayList<>();

        for (ImGroup imGroup : imGroups) {
            GetGroupListResp getGroupListResp = new GetGroupListResp();
            getGroupListResp.setOuterGroupId(imGroup.getOuterGroupId());
            result.add(getGroupListResp);
        }
        return result;
    }


    /**
     * 导入单聊消息
     */
    public void importSingleChatMsg(List<ImConversationItem> imConversationItemsByConversationId) {
        //导入单聊消息


        for (int i = 0; i < imConversationItemsByConversationId.size(); i++) {
            importMsgSingleProducer.sendMessage(imConversationItemsByConversationId.get(i), MqConfig.IMPORT_MSG_SINGLE_DELAY_TIME * (i + 1));
        }
    }

    /**
     * 更新会话，需要判断会话的发起人在当前会话中是什么角色，如果与会话发起人相同，说明是A先给B发消息  之后B给A发消息
     */
    public void updateImConversation(Long cId, String fromOuterUserId, String newVirtualUserId) {
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(cId);
        if (imConversation == null) {
            throw new BusException("会话不存在");
        }
        int type = CommonConstant.ZERO;
        if (imConversation.getFromOuterUserId().equals(fromOuterUserId)) {
            //A先给B发消息，之后B给A发消息
            type = CommonConstant.ONE;
            imConversation.setFromOuterUserId(newVirtualUserId);
        } else {
            imConversation.setFromOuterUserId(newVirtualUserId);
        }
        ImConversation imConversationNew = imConversationRepository.updateVirtualUserId(imConversation, newVirtualUserId, type);

        //日志
        ImConversationLog imConversationLog = new ImConversationLog();
        BeanUtils.copyProperties(imConversationNew, imConversationLog);
        imConversationLogRepository.insertSelective(imConversationLog);
    }

    /**
     * 创建userVirtualRelation
     */
    public UserVirtualRelation returnUserVirtualRelationBean(GetVirtualUserInfoReq.OuterUserInfoIem from, VirtualUserInfo notUsedVirtualInfo) {
        UserVirtualRelation userVirtualRelation = new UserVirtualRelation();
        userVirtualRelation.setOuterUserId(from.getOuterUserId());
        userVirtualRelation.setVirtualUserId(notUsedVirtualInfo.getOuterUserId());
        userVirtualRelation.setBindTime(new Date());
        userVirtualRelation.setOverBindTime(null);
        userVirtualRelation.setBindStatus(BindStatusEnum.BINDING.type);
        userVirtualRelation.setIsDelete(0);
        userVirtualRelation.setUpdateTime(new Date());
        userVirtualRelation.setCreateTime(new Date());
        return userVirtualRelation;
    }

    /**
     * 创建会话bean
     */
    public ImConversation returnImConversationBean(String fromOuterUserId, String fromOuterUserType,
                                                   String toOuterUserId, String toOuterUserType, Long channelId) {
        ImConversation imConversation = new ImConversation();
        imConversation.setFromOuterUserId(fromOuterUserId);
        imConversation.setFromOuterUserType(fromOuterUserType);
        imConversation.setConversationStatus(ConversationStatusEnum.PROCESSING.type);
        imConversation.setChannelConfigId(channelId);
        imConversation.setToOuterUserType(toOuterUserType);
        imConversation.setToOuterUserId("");
        /**
         * 如果是对象是机器人，或者系统，需要生成toOuterUserId
         * */
        if (OuterUserTypeEnum.ROBOT.type.equals(toOuterUserType) || OuterUserTypeEnum.SYSTEM.type.equals(toOuterUserType)) {
            //如果没有绑定关系，就需要创建用户会话
            VirtualUserInfo notUsedVirtualInfo = virtualUserInfoRepository.getNotUsedVirtualInfo();
            if (ObjectUtils.isEmpty(notUsedVirtualInfo)) {
                throw new BusException("没有空闲客服");
            }
            imConversation.setToOuterUserId(notUsedVirtualInfo.getOuterUserId());
            //更新虚拟用户状态
            virtualUserInfoRepository.updateVirtualStatus(notUsedVirtualInfo.getVirtualInfoId(), VirtualStatusEnum.USED.type);
        }

        imConversation.setFromType(ConversationFromTypeEnum.NORMAL.type);
        imConversation.setFromId("");
        imConversation.setFromScene("");
        imConversation.setSceneValue("");
        imConversation.setConversationTagIds("");
        imConversation.setCompleteTime(null);
        imConversation.setIsDelete(0);
        imConversation.setUpdateTime(new Date());
        imConversation.setCreateTime(new Date());
        imConversation.setAgentOuterUserId(toOuterUserId);
        return imConversation;
    }


    /**
     * 机器人ai回调 向用户发送消息
     *
     * @param req
     */
    @Override
    public Integer sendAIMsgToPersonal(AIWorkflowExecuteResultMsgBo req) {

        //向用户转发消息，插入会话明细
        super.sendMsgTypeToPersonalText(Long.valueOf(req.getConversationId()),MsgTypeEnum.TEXT.type,req.getAnswer());
        return 1;

    }

    @Resource
    private DistributeService distributeService;


    /**
     * 接收询前表单提交数据，并转人工
     * 更新msgId查询的会话记录数据
     * 排队
     * @param req
     */
    @Override
    public Integer toArtificial(ToArtificialServiceReq req) {

        ImConversationItem imConversationItemsByMsgId = imConversationItemRepository.getImConversationItemsByMsgId(req.getMsgId());
        if(imConversationItemsByMsgId==null){
            log.error("消息id不存在，消息id={}", req.getMsgId());
            return 0;
        }

        String msgContent = imConversationItemsByMsgId.getMsgContent();

        if(StringUtils.isEmpty(msgContent)){
            log.error("表单数据空，消息id={}", req.getMsgId());
            return 0;
        }
        List<FormMsgContentFormatBo> formMsgContentFormatBos = JSONArray.parseArray(msgContent, FormMsgContentFormatBo.class);
        if(StringUtils.isEmpty(msgContent)){
            log.error("表单数据解析异常，消息id={}", req.getMsgId());
            return 0;
        }
        FormMsgContentFormatBo formMsgContentFormatBo = formMsgContentFormatBos.get(0);
        if(!formMsgContentFormatBo.getMsgType().equals(MsgTypeEnum.CUSTOMER.type)){
            log.error("表单数据类型不是自定义，消息id={}", req.getMsgId());
            return 0;
        }
        String msgContentData = formMsgContentFormatBo.getMsgContent();
        FormCustomerMsgContentBo formCustomerMsgContentBo = JSONObject.parseObject(msgContentData, FormCustomerMsgContentBo.class);
        //设置提交数据
        String data = formCustomerMsgContentBo.getData();
        FromData fromData = JSONObject.parseObject(data, FromData.class);
        fromData.setSubmit(1);
        FormCustomerMsgContentBo.ProblemItemResult problemItemResult = new FormCustomerMsgContentBo.ProblemItemResult();
        problemItemResult.setProblemId(req.getProblemId());
        problemItemResult.setProblemName(req.getProblemName());
        problemItemResult.setProblemDesc(req.getProblemDesc());
        fromData.setResult(problemItemResult);
        formCustomerMsgContentBo.setData(JSON.toJSONString(fromData));

        //更新表单数据
        List<FormMsgContentFormatBo> formMsgContentFormatUpdateBos = new ArrayList<>();
        FormMsgContentFormatBo formMsgContentFormatUpdateBo = new FormMsgContentFormatBo();
        formMsgContentFormatUpdateBo.setMsgContent(JSON.toJSONString(formCustomerMsgContentBo));
        formMsgContentFormatUpdateBo.setMsgType(MsgTypeEnum.CUSTOMER.type);
        formMsgContentFormatUpdateBos.add(formMsgContentFormatUpdateBo);
        imConversationItemRepository.updateMsgContentByKey(imConversationItemsByMsgId.getConversationItemId(), JSON.toJSONString(formMsgContentFormatUpdateBos));
        ImConversation imConversationOri = imConversationRepository.selectByPrimaryKey(imConversationItemsByMsgId.getConversationId());

        ChannelConfig channelConfig = channelConfigRepository.selectByPrimaryKey(imConversationOri.getChannelConfigId());
        DistributeEnGroupReq distributeEnGroupReq = new DistributeEnGroupReq();
        distributeEnGroupReq.setConversationId(imConversationOri.getConversationId());
        distributeEnGroupReq.setOuterUserId(imConversationOri.getFromOuterUserId());
        distributeEnGroupReq.setClientCategoryId(channelConfig.getClientCategoryId());
        distributeEnGroupReq.setChannelId(imConversationOri.getChannelConfigId());
        distributeEnGroupReq.setUserProblemClassId(req.getProblemId());
        distributeEnGroupReq.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        //进入排队
        if(OuterUserClassTypeEnum.SEAT.type.equals(imConversationOri.getToOuterUserType())){
            //走自动分配逻辑
            distributeService.distributeEnGroup(distributeEnGroupReq);

         }else{
            FlowNode nextNode = commonBaseManager.getNextNode(imConversationItemsByMsgId.getConversationId(), NodeTypeEnum.ROBOT.type);
            if(nextNode== null){
                log.error("没有下一个节点，会话id={}", imConversationItemsByMsgId.getConversationId());
                return 0;
            }
            if(NodeTypeEnum.ARTIFICIAL.type.equals(nextNode.getNodeType())){
                //如果是转人工
                distributeService.distributeEnGroup(distributeEnGroupReq);
            }
        }

        //修改表单状态
        UpdateMsgSingleBo updateMsgSingleBo = new UpdateMsgSingleBo();
        updateMsgSingleBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        updateMsgSingleBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        updateMsgSingleBo.setImConversationItemId(imConversationItemsByMsgId.getConversationItemId());
        tencentManager.updateMsgSingle(updateMsgSingleBo);

        //发送欢迎语
        super.sendArtificialTypeMsg(imConversationItemsByMsgId.getConversationId(),TriggerModeEnum.VISITOR_ENTER.type,null, null);
        return 1;
    }

    /**
     * 发送询前表单
     *
     * @param req
     */
    @Override
    public Integer sendPreForm(ToArtificialMsgBo req) {

        Long conversationId = req.getConversationId();
        String fromAccount = req.getFromAccount();

        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if(imConversation==null){
            log.error("会话id不存在，会话id={}", conversationId);
            return 0;
        }

        //发送表单信息 自定义消息类型
        List<UserProblemClass> userProblemClassByChannelIdAndStatus = userProblemClassRepository.getUserProblemClassByChannelIdAndStatus(imConversation.getChannelConfigId(), EnableStatusEnum.ENABLE.type);

        if(CollectionUtils.isEmpty(userProblemClassByChannelIdAndStatus)){
            log.error("没有配置表单数据，会话id={}", conversationId);
            return 1;
        }

        //发送基础信息 text类型消息
        try{
            Map<String, String> textMsgContentFormatBoMap = new HashMap<>();
            textMsgContentFormatBoMap.put("Text", ConversationConstant.PRE_FORM_GREETING);
            super.sendMsgTypeToPersonal(conversationId, MsgTypeEnum.TEXT.type,JSON.toJSONString(textMsgContentFormatBoMap));
        }catch (Exception e){
            log.error("发送信息失败，会话id={}", conversationId);
            return 0;
        }

        //发送基础信息 自定义消息类型
        try{
//
            FormCustomerMsgContentBo formCustomerMsgContentBo = new FormCustomerMsgContentBo();

            FromData fromData = new FromData();

            fromData.setBusinessID(ConversationConstant.BUSINESS_ID);

            List<FormCustomerMsgContentBo.FromData.FormCustomerMsgContent> problemList = new ArrayList<>();

            for (UserProblemClass problemClassByChannelIdAndStatus : userProblemClassByChannelIdAndStatus) {
                FormCustomerMsgContentBo.FromData.FormCustomerMsgContent problem = new FormCustomerMsgContentBo.FromData.FormCustomerMsgContent();
                problem.setProblemName(problemClassByChannelIdAndStatus.getProblemName());
                problem.setProblemId(problemClassByChannelIdAndStatus.getProblemId());
                problemList.add(problem);
            }

            fromData.setProblemList(problemList);
            fromData.setSubmit(0);
            formCustomerMsgContentBo.setData(JSON.toJSONString(fromData));

            super.sendMsgTypeToPersonal(conversationId, MsgTypeEnum.CUSTOMER.type,JSON.toJSONString(formCustomerMsgContentBo));
        }catch (Exception e){
            log.error("发送基础信息失败自定义消息，会话id={}", conversationId);
            return 0;
        }

        return 1;
    }

    /**
     * 发送欢迎语消息
     *
     * @param req
     */
    @Override
    public Integer toArtificialWelcomeMsg(ToArtificialTypesBo req) {

        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getImConversationId());
        if(imConversation==null){
            log.error("会话id不存在，会话id={}", req.getImConversationId());
            return 1;
        }

        ChannelConfig channelConfigByChannelEn = channelConfigRepository.getChannelConfigByChannelEn(ChannelConstant.CONVERSATION_GLOBAL_CHANNEL_EN);
        List<Long> channelIdList = new ArrayList<>();
        if(channelConfigByChannelEn!=null){
            //全局渠道id
            channelIdList.add(channelConfigByChannelEn.getChannelId());
        }

        //会话关联的渠道id
        Long channelConfigId = imConversation.getChannelConfigId();
        channelIdList.add(imConversation.getChannelConfigId());

        List<ConversationAutoReplyConfig> configAutoReplyListByChannelIds = conversationAutoReplyConfigRepository.getConfigListByChannelIds(channelIdList);
        if(CollectionUtils.isEmpty(configAutoReplyListByChannelIds)){
            log.error("会话id：{}，关联的渠道id：{}，没有配置:{}", req.getImConversationId(), channelConfigId, TriggerModeEnum.getNameByType(req.getTriggerMode()));
            return 1;
        }
        List<Long> conversationAutoReplyConfigIdList = configAutoReplyListByChannelIds.stream().map(ConversationAutoReplyConfig::getConversationAutoReplyConfigId).collect(Collectors.toList());

        List<ConversationAutoReplyDetailConfig> listByAutoReplyConfigIdList = conversationAutoReplyDetailConfigRepository.getListByAutoReplyConfigIdList(conversationAutoReplyConfigIdList);

        List<ConversationAutoReplyDetailConfig> autoReplyDetailConfigList = listByAutoReplyConfigIdList.stream().filter(f -> req.getTriggerMode().equals(f.getTriggerMode())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(autoReplyDetailConfigList)){
            log.error("会话id：{}，关联的渠道id：{}，没有配置:{}", req.getImConversationId(), channelConfigId, TriggerModeEnum.getNameByType(req.getTriggerMode())
            );
            return 1;
        }

        //使用全局渠道id或者会话关联的渠道id拿 自动回复配置；如果会话关联的渠道id有配置就拿配置，如果没有就拿全局配置
        List<ConversationAutoReplyDetailConfig> autoReplyDetailConfigWelcomeList = this.getAutoReplyDetailConfigList(configAutoReplyListByChannelIds, autoReplyDetailConfigList, channelConfigId);
        int change = 0;
        if(CollectionUtils.isNotEmpty(autoReplyDetailConfigWelcomeList) && TriggerModeEnum.SEAT_ENTER.type.equals(req.getTriggerMode())){
            ConversationAutoReplyDetailConfig conversationAutoReplyDetailConfig1 = autoReplyDetailConfigWelcomeList.get(0);

            List<SeatEnterExtraJsonBo> seatEnterExtraJsonConBos = JSONArray.parseArray(conversationAutoReplyDetailConfig1.getExtraJson(), SeatEnterExtraJsonBo.class);
            Optional<SeatEnterExtraJsonBo> first = seatEnterExtraJsonConBos.stream().filter(f -> f.getVarNameEn().equals(req.getPreToOuterUserType())).findFirst();
            if(first.isPresent() && first.get().getCheckStatus().equals(0)){
                change = 1;
            }
        }
        if(CollectionUtils.isEmpty(autoReplyDetailConfigList)){
            change = 1;
        }

        if(change==1 && channelConfigByChannelEn!=null){
            autoReplyDetailConfigWelcomeList = this.getAutoReplyDetailConfigList(configAutoReplyListByChannelIds, autoReplyDetailConfigList, channelConfigByChannelEn.getChannelId());
        }

        if(CollectionUtils.isEmpty(autoReplyDetailConfigWelcomeList)){
            //全局会话id和渠道配置id不存在欢迎语自动回复信息
            return 1;
        }
        //发送内容
        ConversationAutoReplyDetailConfig conversationAutoReplyDetailConfig = autoReplyDetailConfigWelcomeList.get(0);
        String autoReplyContent = conversationAutoReplyDetailConfig.getAutoReplyContent();
        if(StringUtils.isEmpty(autoReplyContent)){
            log.error("发送内容配置为空，autoReplyDetailConfigWelcomeList={}", JSON.toJSONString(autoReplyDetailConfigWelcomeList));
            return 1;
        }
        if(TriggerModeEnum.SEAT_ENTER.type.equals(req.getTriggerMode())
                        && !StringUtils.isEmpty(req.getSeatOuterUserId())
        && conversationAutoReplyDetailConfig.getSupportPlaceholder()>0
        ){

            String extraJson = conversationAutoReplyDetailConfig.getExtraJson();
            if(extraJson==null){
                return 1;
            }
            List<SeatEnterExtraJsonBo> seatEnterExtraJsonBos = JSONArray.parseArray(extraJson, SeatEnterExtraJsonBo.class);
            Optional<SeatEnterExtraJsonBo> first = seatEnterExtraJsonBos.stream().filter(f -> f.getVarNameEn().equals(OuterUserClassTypeEnum.ROBOT.type)).findFirst();
            if(first.isPresent() && first.get().getCheckStatus().equals(CommonConstant.ONE)){
                //查询坐席相关信息
                UserRegisterInfo byOuterUserId = userRegisterInfoRepository.findByOuterUserId(req.getSeatOuterUserId());
                if(byOuterUserId==null){
                    log.error("坐席：{}不存在", req.getSeatOuterUserId());
                    return 1;
                }
                SeatInfo seatInfo = seatInfoRepository.selectByPrimaryKey(byOuterUserId.getUserId());

                if(seatInfo==null){
                    log.error("坐席表不存在记录：{}不存在", req.getSeatOuterUserId());
                    return 1;
                }
                autoReplyContent = StringUtils.replace(autoReplyContent, KF_NICK_NAME, seatInfo.getSeatName());
                autoReplyContent = StringUtils.replace(autoReplyContent, ChannelConstant.KF_SEAT_ID, seatInfo.getSeatId()+"");
            }else{
                return 1;
            }
        }

        Map<String, String> textMsgContentFormatBoMap = new HashMap<>();
        textMsgContentFormatBoMap.put("Text", autoReplyContent);
        super.sendMsgTypeToPersonal(imConversation.getConversationId(), MsgTypeEnum.TEXT.type,JSON.toJSONString(textMsgContentFormatBoMap));
        return 1;
    }

    /**
     * 添加超时任务
     *
     * @param req
     */
    @Override
    public Integer addTimeoutTask(XxlTimeoutReqBo req) {

        AddXxlJob addXxlJob = new AddXxlJob();
        addXxlJob.setJobGroup(1);
        addXxlJob.setJobDesc(String.format(ConversationConstant.TASK_JOB_XXL_DESC_TEMPLATE,req.getConversationId()));
        addXxlJob.setExecutorRouteStrategy("");
        addXxlJob.setAuthor("柯贤运");
        addXxlJob.setJobCron("");
        addXxlJob.setExecutorHandler(ConversationConstant.HTTP_JOB_HANDLER);
        String param = "http://" + super.getPreFix() + "iop-im-service.wanshifu.com/xxl/timeout?id=" + req.getConversationId();
        addXxlJob.setExecutorParam(param);
        addXxlJob.setIntervalSecond(timeoutXxlCron);
        addXxlJob.setScheduleType("FIX_RATE");
        Integer result = xxlJobTemplate.addJobAndStart(addXxlJob);

        ImConversation imConversation = new ImConversation();
        imConversation.setConversationId(req.getConversationId());
        imConversation.setTimeoutXxlId(Long.valueOf(result));
        imConversationRepository.updateByPrimaryKeySelective(imConversation);

        return 1;
    }


    /**
     *
     * */
    public List<ConversationAutoReplyDetailConfig> getAutoReplyDetailConfigList(
            List<ConversationAutoReplyConfig> configAutoReplyListByChannelIds,
            List<ConversationAutoReplyDetailConfig> autoReplyDetailConfigList,
            Long channelConfigId){
        List<Long> channelAutoReplyConfigIdList = configAutoReplyListByChannelIds.stream().filter(f -> f.getChannelId().equals(channelConfigId)).map(ConversationAutoReplyConfig::getConversationAutoReplyConfigId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(channelAutoReplyConfigIdList)){
            return new ArrayList<>();
        }
        List<ConversationAutoReplyDetailConfig> collect = autoReplyDetailConfigList.stream().filter(f -> channelAutoReplyConfigIdList.contains(f.getConversationAutoReplyConfigId())).collect(Collectors.toList());
        return collect;
    }



    /**
     * 接收到表单后，实际是进入排队
     *
     * @param req
     */
    public Integer toArtificial1(ToArtificialServiceReq req) {

//        Long conversationId = req.getCid();
//        String outerUserId = req.getOuterUserId();
//
//        //校验会话是否存在
//        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
//        if (imConversation == null) {
//            throw new BusException("会话不存在,会话id=" + conversationId);
//        }
//        if(OuterUserTypeEnum.SEAT.type.equals(imConversation.getToOuterUserType())){
//            throw new BusException("会话已经是客服接待状态,会话id=" + conversationId);
//        }
//
//        //判断关联路由是否有转人工节点
//        if(imConversation.getChannelConfigId()==0){
//            throw new BusException("会话没有关联路由,会话id=" + conversationId);
//        }
//
//        Boolean b = commonBaseManager.checkFlowExistNodeByType(imConversation.getChannelConfigId(), NodeTypeEnum.ARTIFICIAL.type);
//        if(!b){
//            log.info("会话关联的路由没有转人工节点或当前时间没有转人工节点,会话id=" + conversationId);
//            return 0;
//        }
//
//        //查询会话+用户+（已分配/已进组）的会话分配记录
//        ImConversationDistribute imConversationDistribute = imConversationDistributeRepository.selectInfoByConversationId(conversationId, outerUserId, Arrays.asList(ImConversationDistributeStatusEnum.COMPLETED.type,ImConversationDistributeStatusEnum.ENGROUP.type));
//        if(imConversationDistribute!=null){
//            if(ImConversationDistributeStatusEnum.COMPLETED.type.equals(imConversationDistribute.getDistributeStatus())){
//                throw new BusException("会话已经分配完成,会话id=" + conversationId);
//            }
//            if(ImConversationDistributeStatusEnum.ENGROUP.type.equals(imConversationDistribute.getDistributeStatus())){
//                throw new BusException("会话正在排队中,会话id=" + conversationId);
//            }
//        }

        //进入排队 todo

        return 1;
    }


}
