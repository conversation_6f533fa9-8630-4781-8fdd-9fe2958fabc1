package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.CommonRedisConstant;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.account.domain.util.MD5Util;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.BatchLogoutSeatReq;
import com.wanshifu.iop.im.api.req.seat.ConversationFlowSeatStatusUpdateReq;
import com.wanshifu.iop.im.api.req.seat.DisableSeatStatusReq;
import com.wanshifu.iop.im.api.req.seat.SetLoginAuthReq;
import com.wanshifu.iop.im.api.resp.*;
import com.wanshifu.iop.im.domain.enums.SeatStatusDefaultTypeEnum;
import com.wanshifu.iop.im.domain.enums.StatusEnum;
import com.wanshifu.iop.im.domain.po.SeatGroupMapping;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.SeatStatus;
import com.wanshifu.iop.im.domain.po.SeatStatusSwitchLog;
import com.wanshifu.repository.SeatGroupMappingRepository;
import com.wanshifu.repository.SeatInfoRepository;
import com.wanshifu.repository.SeatStatusRepository;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.AuthService;
import com.wanshifu.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 鉴权服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:44
 */
@Service
@Slf4j
public class AuthServiceImpl extends AbstractService implements AuthService {

    @Resource
    private SeatInfoRepository seatInfoRepository;

    @Resource
    private SeatGroupMappingRepository seatGroupMappingRepository;

    @Resource
    private SeatStatusRepository seatStatusRepository;

    @Resource
    private CommonService  commonService;

    /**
     * 登录坐席
     * @param req
     * @return
     */
    @Override
    public SetLoginAuthResp loginSeat(SetLoginAuthReq req){
        log.info("登录坐席 req:{}", req);
        // 存储账号关联token key
        String accountTokenInfoKey = String.format(CommonRedisConstant.IOP_ACCOUNT_MAPPING_TOKEN_KEY, req.getLoginAccountId());

        // 已经登陆过直接返回信息
        String existImToken = redisHelper.get(accountTokenInfoKey);
        if(StringUtils.isNotEmpty(existImToken)){
            String seatInfo = redisHelper.get(String.format(CommonRedisConstant.IOP_IM_TOKEN_KEY, existImToken));
            return JSONObject.parseObject(seatInfo, SetLoginAuthResp.class);
        }

        // 生成token
        //根据当前时间+登录账号戳通过md5加密生成一个唯一token值
        String imToken = MD5Util.MD5Encode(String.valueOf(System.currentTimeMillis()+req.getLoginAccountId()), "utf-8");
        // 存储token key
        String seatTokenInfoKey = String.format(CommonRedisConstant.IOP_IM_TOKEN_KEY, imToken);
        // 缓存到第二天0点的秒数
        int secondsToMidnight = super.getSecondsToMidnight().intValue();

        // 初始化返回对象
        SetLoginAuthResp resp = new SetLoginAuthResp();
        resp.setImToken(imToken);

        // 获取坐席信息
        SeatInfo seatInfo = seatInfoRepository.selectEnableInfoByAccountId(req.getLoginAccountId());
        if(seatInfo == null){
            log.info("当前账号没有生效坐席 accountId:{}", req.getLoginAccountId());
            // 存储账号关联token key
            redisHelper.set(accountTokenInfoKey, imToken, secondsToMidnight);
            // 存储坐席信息
            redisHelper.set(seatTokenInfoKey, JSONObject.toJSONString(resp), secondsToMidnight);
            return resp;
        }

        // 获取坐席关联分组
        List<SeatGroupMapping> groupMappings = seatGroupMappingRepository.batchGetMappingBySeatIdList(Collections.singletonList(seatInfo.getSeatId()), StatusEnum.ENABLE.type);

        // 设置坐席当前状态
        //手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            // 更新坐席表
            seatInfoRepository.updateSeatOnlineStatus(seatInfo.getSeatId());

            // 更新日志表
            SeatStatusSwitchLog seatStatusSwitchLog = new SeatStatusSwitchLog();
            seatStatusSwitchLog.setSeatStatusEnBefore(seatInfo.getCurrentSeatStatusEn());
            seatStatusSwitchLog.setSeatStatusEnAfter(seatInfo.getLoginSeatStatusEn());
            seatStatusSwitchLog.setSeatId(seatInfo.getSeatId());
            // 不是满负荷的情况下切换成在线需要记录首次在线时间
            if ( !CommonConstant.SEAT_STATUS_FULL_LOAD.equals(seatStatusSwitchLog.getSeatStatusEnBefore()) && CommonConstant.SEAT_STATUS_ONLINE.equals(seatInfo.getLoginSeatStatusEn()) ){
                seatStatusSwitchLog.setFirstOnlineTime(new Date());
            }
            seatStatusSwitchLogRepository.insertSelective(seatStatusSwitchLog);

            platformTransactionManager.commit(status);
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }

        // 获取坐席信息
        LoginSeatAuthInfoResp loginSeatAuthInfoResp = new LoginSeatAuthInfoResp();
        BeanUtils.copyProperties(seatInfo, loginSeatAuthInfoResp);
        if (CollectionUtils.isNotEmpty(groupMappings)){
            loginSeatAuthInfoResp.setGroupIdList(groupMappings.stream().map(SeatGroupMapping::getGroupId).collect(Collectors.toList()));
        }
        // 兼容异常情况，只有离线状态时，将上线状态赋给当前状态
        if ( StringUtils.isEmpty(loginSeatAuthInfoResp.getCurrentSeatStatusEn())
                || CommonConstant.DEFAULT_OFFLINE_SEAT_STATUS.equals(loginSeatAuthInfoResp.getCurrentSeatStatusEn()) ){
            loginSeatAuthInfoResp.setCurrentSeatStatusEn( StringUtils.isNotEmpty(loginSeatAuthInfoResp.getLoginSeatStatusEn()) ? loginSeatAuthInfoResp.getLoginSeatStatusEn() : CommonConstant.DEFAULT_ONLINE_SEAT_STATUS );
        }
        resp.setSeatInfo(loginSeatAuthInfoResp);

        // 存储账号关联token key
        redisHelper.set(accountTokenInfoKey, imToken, secondsToMidnight);
        // 存储坐席信息
        redisHelper.set(seatTokenInfoKey, JSONObject.toJSONString(resp), secondsToMidnight);

        // 如果坐席登录状态是在线，监听会话状态
        if ( CommonConstant.SEAT_STATUS_ONLINE.equals(seatInfo.getLoginSeatStatusEn()) ){
            // 监听会话状态
            ConversationFlowSeatStatusUpdateReq conversationFlowSeatStatusUpdateReq = new ConversationFlowSeatStatusUpdateReq();
            conversationFlowSeatStatusUpdateReq.setSeatId(seatInfo.getSeatId());
            conversationFlowSeatStatusUpdateProducer.sendMessage(conversationFlowSeatStatusUpdateReq);
        }

        return resp;
    }

    /**
     * 批量登出坐席
     * @param req
     * @return
     */
    @Override
    public Integer batchLogoutSeat(BatchLogoutSeatReq req){
        log.info("登出坐席 req:{}", req);

        List<Long> accountIdList = new ArrayList<>();

        if ( CollectionUtils.isNotEmpty(req.getAccountIdList()) ){
            accountIdList.addAll(req.getAccountIdList());
        }

        // 保底校验 当时时间不在0点到6点之间 不能操作全部数据
        if ( CollectionUtils.isEmpty(accountIdList) && !this.isBetween0And6AM() && !req.getIsTest() ){
            log.info("当前时间不在0点到6点之间，不能操作全部数据");
            return 0;
        }

        // 批量设置坐席离线
        Integer integer = this.batchUpdateSeatOfflineStatus(accountIdList);
        if ( integer == 0 ){
            return 0;
        }

        // 批量删除登录redis
        if ( CollectionUtils.isNotEmpty(accountIdList) ){
            this.destroySeatLoginCache(accountIdList);
        }else{
            // 批量删除登录redis
            List<Long> allAccountList = seatInfoRepository.selectAccountIdListEnableSeatInfo();
            this.destroySeatLoginCache(allAccountList);
        }

        return 1;
    }

    /**
     * 批量设置坐席离线
     * @param accountIdList
     */
    private Integer batchUpdateSeatOfflineStatus(List<Long> accountIdList){
        // 批量设置坐席离线
        SeatStatus defaultStatus = seatStatusRepository.getStatusByDefaultType(SeatStatusDefaultTypeEnum.OFFLINE.type);
        String defaultSeatStatusEn = CommonConstant.DEFAULT_OFFLINE_SEAT_STATUS;
        if ( Objects.nonNull(defaultStatus) ){
            defaultSeatStatusEn = defaultStatus.getSeatStatusEn();
        }

        // 通过账号查询坐席信息
        List<SeatInfo> seatInfoList = seatInfoRepository.batchSelectNotOfflineListByAccountIdList(accountIdList);
        if ( CollectionUtils.isEmpty(seatInfoList) ){
            log.info("没有找到需要离线的坐席");
            return 0;
        }

        // 手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        try  {
            // 批量更新
            seatInfoRepository.updateSeatOfflineStatus(defaultSeatStatusEn, seatInfoList.stream().map(SeatInfo::getSeatId).collect(Collectors.toList()));

            // 批量写入日志
            List<SeatStatusSwitchLog> insertList = new ArrayList<>();
            for (SeatInfo seatInfo : seatInfoList) {
                SeatStatusSwitchLog seatStatusSwitchLog = new SeatStatusSwitchLog();
                seatStatusSwitchLog.setSeatId(seatInfo.getSeatId());
                seatStatusSwitchLog.setSeatStatusEnBefore(seatInfo.getCurrentSeatStatusEn());
                seatStatusSwitchLog.setSeatStatusEnAfter(defaultSeatStatusEn);
                seatStatusSwitchLog.setIsDelete(CommonConstant.ZERO);
                seatStatusSwitchLog.setCreateTime(new Date());
                seatStatusSwitchLog.setUpdateTime(new Date());
                insertList.add(seatStatusSwitchLog);
            }
            seatStatusSwitchLogRepository.insertList(insertList);
            platformTransactionManager.commit(status);
            return 1;
        } catch (Exception e) {
            log.error("批量设置坐席离线异常", e);
            platformTransactionManager.rollback(status);
            return 0;
        }
    }

    /**
     * 销毁坐席登录缓存
     */
    @Override
    public void destroySeatLoginCache(List<Long> accountIdList) {
        if (  CollectionUtils.isEmpty(accountIdList) ){
            return;
        }

        // 批量删除登录redis
        accountIdList.forEach(accountId -> {
            String format = String.format(CommonRedisConstant.IOP_ACCOUNT_MAPPING_TOKEN_KEY, accountId);
            String imToken = redisHelper.get(format);
            redisHelper.del(String.format(CommonRedisConstant.IOP_IM_TOKEN_KEY, imToken));
            redisHelper.del(format);
        });
    }

    /**
     * 更新坐席登录缓存
     */
    @Override
    public void updateSeatLoginCache(Long accountId, SetLoginAuthResp cacheResp, Integer status){
        // 存储账号关联token key
        String accountTokenInfoKey = String.format(CommonRedisConstant.IOP_ACCOUNT_MAPPING_TOKEN_KEY, accountId);

        // 批量设置坐席离线
        if ( StatusEnum.DISABLE.type.equals(status) ){
            this.batchUpdateSeatOfflineStatus(Collections.singletonList(accountId));
        }

        String existImToken = redisHelper.get(accountTokenInfoKey);
        if ( StringUtils.isEmpty(existImToken) ){
            // 没有登录不用管
            return;
        }

        // 设置坐席登录
        if ( StatusEnum.ENABLE.type.equals(status) && Objects.nonNull(cacheResp) && Objects.nonNull(cacheResp.getSeatInfo()) ){
            TransactionStatus transaction = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

            try{
                seatInfoRepository.updateSeatOnlineStatus(cacheResp.getSeatInfo().getSeatId());
                // 更新日志表
                SeatStatusSwitchLog seatStatusSwitchLog = new SeatStatusSwitchLog();
                seatStatusSwitchLog.setSeatStatusEnBefore(CommonConstant.DEFAULT_OFFLINE_SEAT_STATUS);
                seatStatusSwitchLog.setSeatStatusEnAfter(cacheResp.getSeatInfo().getLoginSeatStatusEn());
                seatStatusSwitchLog.setSeatId(cacheResp.getSeatInfo().getSeatId());
                // 不是满负荷的情况下切换成在线需要记录首次在线时间
                if ( !CommonConstant.SEAT_STATUS_FULL_LOAD.equals(seatStatusSwitchLog.getSeatStatusEnBefore()) && CommonConstant.SEAT_STATUS_ONLINE.equals(cacheResp.getSeatInfo().getLoginSeatStatusEn()) ){
                    seatStatusSwitchLog.setFirstOnlineTime(new Date());
                }
                seatStatusSwitchLogRepository.insertSelective(seatStatusSwitchLog);
                platformTransactionManager.commit(transaction);
            }catch (Exception e){
                platformTransactionManager.rollback(transaction);
                throw new BusinessException(e.getMessage());
            }
        }

        // 写入imToken
        cacheResp.setImToken(existImToken);

        // 已经登陆过，更新登录信息
        Long secondsToMidnight = super.getSecondsToMidnight();
        redisHelper.set(String.format(CommonRedisConstant.IOP_IM_TOKEN_KEY, existImToken), JSONObject.toJSONString(cacheResp), secondsToMidnight.intValue());
    }

    /**
     * 禁用坐席状态
     * @param req
     * @return
     */
    @Override
    public Integer disableSeatStatus(DisableSeatStatusReq req){
        log.info("禁用坐席状态 req:{}", req);

        // 查询坐席信息
        SeatInfo seatInfo = seatInfoRepository.selectEnableInfoByAccountId(req.getDisableAccountId());
        if ( Objects.isNull(seatInfo) ){
            return 0;
        }

        SwitchStatusReq switchStatusReq = new SwitchStatusReq();
        switchStatusReq.setSwitchId(seatInfo.getSeatId());
        switchStatusReq.setSwitchType("seat");
        switchStatusReq.setStatus(StatusEnum.DISABLE.type);
        switchStatusReq.setOperatorId(req.getOperatorAccountId());
        return commonService.switchStatus(switchStatusReq);
    }

                                     /**
     * 判断当前时间是否在0点到6点之间
     * @return
     */
    public boolean isBetween0And6AM() {
        LocalTime now = LocalTime.now(); // 获取当前时间（不包含日期）
        LocalTime start = LocalTime.of(0, 0);  // 0:00
        LocalTime end = LocalTime.of(6, 0);    // 6:00
        return !now.isBefore(start) && now.isBefore(end); // [0:00, 6:00)
    }
}
