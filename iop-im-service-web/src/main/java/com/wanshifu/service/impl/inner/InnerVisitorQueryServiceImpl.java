package com.wanshifu.service.impl.inner;

import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.domain.bo.innerAccountService.GetThirdPartyIpLocationRespBo;
import com.wanshifu.iop.im.domain.enums.UserClassEnum;
import com.wanshifu.master.information.api.CommonQueryServiceApi;
import com.wanshifu.master.information.domain.api.request.common.GetMasterInfoForKeFuRqt;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoForKeFuResp;
import com.wanshifu.orderconfig.api.ServerCategoryApi;
import com.wanshifu.orderconfig.domains.reqbean.ConfigServerCategoryReq;
import com.wanshifu.orderconfig.domains.respbean.ConfigServerCategoryResp;
import com.wanshifu.sdk.InnerAccountServiceApi;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.inner.InnerVisitorQueryService;
import com.wanshifu.user.api.UserInfoApi;
import com.wanshifu.user.domain.req.UserIdReq;
import com.wanshifu.user.domain.resp.user.GetUserInfoAllToIopResp;
import com.wanshifu.wallet.domains.po.WalletBase;
import com.wanshifu.wallet.service.api.MasterWalletServiceApi;
import com.wanshifu.wallet.service.api.UserWalletServiceApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 内部业务查询服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:44
 */
@Service
@Slf4j
public class InnerVisitorQueryServiceImpl extends AbstractService implements InnerVisitorQueryService {

    // 服务品类
    @Resource
    private ServerCategoryApi serverCategoryApi;

    // 用户信息服务
    @Resource
    private UserInfoApi userInfoApi;

    // 师傅钱包服务
    @Resource
    private MasterWalletServiceApi masterWalletServiceApi;

    // 用户钱包服务
    @Resource
    private UserWalletServiceApi userWalletServiceApi;

    // 师傅信息服务
    @Resource
    private CommonQueryServiceApi commonQueryServiceApi;

    // 内部账户服务
    @Resource
    private InnerAccountServiceApi innerAccountServiceApi;

    // 基础地址服务
    @Resource
    private AddressApi addressApi;

    /**
     * 获取服务品类列表
     * @param sourceType
     * @return
     */
    @Override
    public SimplePageInfo<ConfigServerCategoryResp> getListServerCategory(String sourceType) {
        try{
            ConfigServerCategoryReq configServerCategoryReq = new ConfigServerCategoryReq();
            configServerCategoryReq.setCategoryStatus(1);
            configServerCategoryReq.setSource(sourceType);
            return serverCategoryApi.listServerCategory(Optional.of(configServerCategoryReq));
        }catch (Exception e) {
            log.error("获取服务品类列表失败", e);
            return null;
        }
    }

    /**
     * 获取用户信息
     * @param userId
     * @return
     */
    @Override
    public GetUserInfoAllToIopResp getUserInfoAllToIop(Long userId) {
        try{
            UserIdReq req = new UserIdReq();
            req.setUserId(userId);
            return userInfoApi.getUserInfoAllToIop(req);
        }catch (Exception e) {
            log.error("获取用户信息失败", e);
            return null;
        }
    }

    /**
     * 获取用户钱包信息 getWalletInfo
     * @return
     */
    @Override
    public WalletBase getWalletInfo(Long accountId, String accountType)  {
        try{
            if (UserClassEnum.USER.type.equals(accountType)){
                return userWalletServiceApi.getWalletInfo(accountId);
            }else{
                return masterWalletServiceApi.getWalletInfo(accountId);
            }
        }catch (Exception e) {
            log.error("获取钱包信息失败", e);
            return null;
        }
    }

    /**
     * 获取师傅信息 getMasterInfoForKeFu
     */
    @Override
    public GetMasterInfoForKeFuResp getMasterInfoForKeFu(Long masterId) {
        try{
            GetMasterInfoForKeFuRqt req = new GetMasterInfoForKeFuRqt();
            req.setMasterId(masterId);
            return commonQueryServiceApi.getMasterInfoForKeFu(req);
        }catch (Exception e) {
            log.error("获取师傅信息失败", e);
            return null;
        }
    }

    /**
     * 通过ip地址获取地址信息
     */
    @Override
    public GetThirdPartyIpLocationRespBo getThirdPartyIpLocation(String ip) {
        try{
            return innerAccountServiceApi.getThirdPartyIpLocation(ip);
        }catch (Exception e) {
            log.error("获取ip地址信息失败", e);
            return null;
        }
    }

    /**
     * 根据divisionIds批量查询地址信息
     */
    @Override
    public List<Address> getDivisionInfoListByDivisionIds(List<Long> divisionIds) {
        if ( CollectionUtils.isEmpty(divisionIds) ){
            return new ArrayList<>();
        }

        try{
            return addressApi.getDivisionInfoListByDivisionIds(divisionIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }catch (Exception e) {
            log.error("根据divisionIds批量查询地址信息失败", e);
            return new ArrayList<>();
        }
    }
}
