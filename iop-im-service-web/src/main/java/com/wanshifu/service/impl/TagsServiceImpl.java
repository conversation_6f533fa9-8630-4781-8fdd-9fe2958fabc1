package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.req.tags.CreateTagsReq;
import com.wanshifu.iop.im.api.req.tags.GetTagsListReq;
import com.wanshifu.iop.im.api.req.tags.SwitchTagsStatusReq;
import com.wanshifu.iop.im.domain.enums.OperateLogFromBusinessEnum;
import com.wanshifu.iop.im.domain.enums.VerifyRepeatNameTypeEnum;
import com.wanshifu.iop.im.domain.po.Tags;
import com.wanshifu.repository.TagsRepository;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.TagsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * 标签服务
 * <AUTHOR>
 * @date： 2025-05-29 15:27:02
 */
@Service
@Slf4j
public class TagsServiceImpl extends AbstractService implements TagsService {

    @Resource
    private TagsRepository tagsRepository;

    /**
     * 获取标签
     * @param req
     * @return
     */
    @Override
    public List<Tags> getTagsList(GetTagsListReq req){
        return tagsRepository.getTagsList(req);
    }

    /**
     * 创建标签
     * @param req
     * @return
     */
    @Override
    public Integer createTags(CreateTagsReq req){
        if ( req.getTagDesc().length() > 500 ){
            throw new BusinessException("标签说明不能超过500个字符");
        }

        // 校验标签是否重复
        VerifyRepeatNameReq verifyRepeatNameReq = new VerifyRepeatNameReq();
        verifyRepeatNameReq.setVerifyName(req.getTagName());
        verifyRepeatNameReq.setVerifyType(VerifyRepeatNameTypeEnum.TAGS.type);
        if(!super.baseVerifyRepeatName(verifyRepeatNameReq)){
            throw new BusinessException("坐席标签名不可重复，请重新输入提交；");
        }

        Tags tags = new Tags();
        tags.setTagName(req.getTagName());
        tags.setTagDesc(req.getTagDesc());
        tags.setScene(req.getScene());
        tags.setOperatorId(req.getOperatorId());
        int i = tagsRepository.insertSelective(tags);
        Assert.isTrue(i > 0, "新增标签失败");

//        super.insertOperateLog(tags.getTagId().longValue(), Tags.class, OperateLogFromBusinessEnum.ADD.type, JSONObject.toJSONString(req), req.getOperatorId());
        return tags.getTagId();
    }

    /**
     * 创建标签
     * @param req
     * @return
     */
    @Override
    public Integer editTags(CreateTagsReq req){
        if ( req.getTagId() == null || req.getTagId() == 0 ){
            throw new BusinessException("标签ID不能为空");
        }

        if ( req.getTagDesc().length() > 500 ){
            throw new BusinessException("标签说明不能超过500个字符");
        }

        // 校验标签是否重复
        VerifyRepeatNameReq verifyRepeatNameReq = new VerifyRepeatNameReq();
        verifyRepeatNameReq.setVerifyName(req.getTagName());
        verifyRepeatNameReq.setVerifyType(VerifyRepeatNameTypeEnum.TAGS.type);
        verifyRepeatNameReq.setExcludeId(req.getTagId().longValue());
        if(!super.baseVerifyRepeatName(verifyRepeatNameReq)){
            throw new BusinessException("坐席标签名不可重复，请重新输入提交；");
        }

        Tags tags = new Tags();
        tags.setTagId(req.getTagId());
        tags.setTagName(req.getTagName());
        tags.setTagDesc(req.getTagDesc());
        tags.setOperatorId(req.getOperatorId());
        int u = tagsRepository.updateByPrimaryKeySelective(tags);
        Assert.isTrue(u > 0, "编辑标签失败");

//        super.insertOperateLog(tags.getTagId().longValue(), Tags.class, OperateLogFromBusinessEnum.MODIFY.type, JSONObject.toJSONString(req), req.getOperatorId());
        return u;
    }

    /**
     * 切换标签状态
     * @param req
     * @return
     */
    @Override
    public Integer switchStatus(SwitchTagsStatusReq req){
        Tags tags = new Tags();
        tags.setTagId(req.getTagId());
        tags.setStatus(req.getStatus());
        int u = tagsRepository.updateByPrimaryKeySelective(tags);
        Assert.isTrue(u > 0, "标签状态切换失败");

        super.insertOperateLog(tags.getTagId().longValue(), Tags.class, OperateLogFromBusinessEnum.CHANGE_STATUS.type, JSONObject.toJSONString(req), req.getOperatorId());
        return u;
    }
}
