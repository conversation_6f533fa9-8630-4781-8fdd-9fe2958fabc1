package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.factory.DataManipulationFactory;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.BatchGetEnumsReq;
import com.wanshifu.iop.im.api.req.ConversationThemeConfigRqt;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.*;
import com.wanshifu.iop.im.domain.enums.ModulesTypeEnum;
import com.wanshifu.iop.im.domain.enums.VerifyRepeatNameTypeEnum;
import com.wanshifu.iop.im.domain.po.channel.ConversationThemeConfig;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.CommonService;
import java.util.ArrayList;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 通用服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:44
 */
@Service
@Slf4j
public class CommonServiceImpl extends AbstractService implements CommonService {

    /**
     * 校验重复名称
     * @param req
     * @return
     */
    @Override
    public VerifyRepeatNameResp verifyRepeatName(VerifyRepeatNameReq req){
        if ( StringUtils.isEmpty(req.getVerifyName()) || StringUtils.isEmpty(req.getVerifyType()) ){
            throw new BusException("校验入参异常");
        }

        if ( VerifyRepeatNameTypeEnum.getEnumByType(req.getVerifyType()) == null ){
            throw new BusException("校验类型不存在");
        }

        // 校验策略执行
        ResultResp<CommonRunStrategyResp> resp = DataManipulationFactory.setFactoryType(req.getVerifyType()).verifyRepeatName(req);

        VerifyRepeatNameResp verifyRepeatNameResp = new VerifyRepeatNameResp();
        verifyRepeatNameResp.setVerifyResult(resp.isSuccess());
        if ( !resp.isSuccess() ){
            verifyRepeatNameResp.setMessageTips(resp.getMessage());
        }
        return verifyRepeatNameResp;
    }

    /**
     * 切换状态
     * @param req
     * @return
     */
    @Override
    public Integer switchStatus(SwitchStatusReq req){
        if ( req.getSwitchId() == null || req.getStatus() == null || req.getSwitchType() == null ){
            throw new BusException("入参异常");
        }

        if ( ModulesTypeEnum.getEnumByType(req.getSwitchType()) == null ){
            throw new BusException("切换类型不存在");
        }

        // 策略执行
        ResultResp<CommonRunStrategyResp> resp = DataManipulationFactory.setFactoryType(req.getSwitchType()).switchStatus(req);

        return resp.getCode();
    }

    /**
     * 获取枚举
     * @param req
     * @return
     */
    @Override
    public Map<String, List<CommonEnumsResp>> getEnums(BatchGetEnumsReq req){
        Map<String, List<CommonEnumsResp>> enumsMap = new HashMap<>();

        req.getEnumsItemList().forEach(item -> {
            item.setTenantId(req.getTenantId());
            List<CommonEnumsResp> enumsItem = this.getEnumsItem(item);
            if ( enumsItem != null ){
                enumsMap.put(item.getEnumsType(), enumsItem);
            }else{
                enumsMap.put(item.getEnumsType(), null);
            }
        });

        return enumsMap;
    }

    /**
     * 获取主题列表
     * @param req
     * @return
     */
    @Override
    public Map<String,List<ConversationThemeConfigResp>> getThemeConfigList(ConversationThemeConfigRqt req) {
        List<ConversationThemeConfig> conversationThemeConfigList = conversationThemeConfigRepository.selectListByTenantIdAndClientUserTypeAndClientPortType(req);
        if(CollectionUtils.isEmpty(conversationThemeConfigList)){
            return null;
        }
        List<ConversationThemeConfigResp> conversationThemeConfigResps =  conversationThemeConfigList.stream().map(item -> {
            ConversationThemeConfigResp conversationThemeConfigResp = new ConversationThemeConfigResp();
            BeanUtils.copyProperties(item, conversationThemeConfigResp);
            return conversationThemeConfigResp;
        }).collect(Collectors.toList());
        //分组
        Map<String,List<ConversationThemeConfigResp>> conversationThemeConfigRespsMap = conversationThemeConfigResps.stream().collect(Collectors.groupingBy(ConversationThemeConfigResp::getThemeType));
        return conversationThemeConfigRespsMap;
    }

    /**
     * 获取单项枚举
     * @param req
     * @return
     */
    public List<CommonEnumsResp> getEnumsItem(GetEnumsReq req){
        if ( req.getEnumsType() == null ){
            log.error("枚举类型为空 req:{}", JSONObject.toJSONString(req));
            return null;
        }

        if ( ModulesTypeEnum.getEnumByType(req.getEnumsType()) == null ){
            log.error("枚举类型不存在 req:{}", JSONObject.toJSONString(req));
            return null;
        }

        // 策略执行
        ResultResp<CommonRunEnumsResp> resp = DataManipulationFactory.setFactoryType(req.getEnumsType()).getEnums(req);
        if ( resp == null ){
            throw new BusException("获取枚举失败");
        }

        if ( resp.isSuccess() ){
            return resp.getData().getEnumsList();
        }else{
            throw new BusException(resp.getMessage());
        }

    }
}
