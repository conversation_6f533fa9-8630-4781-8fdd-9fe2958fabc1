package com.wanshifu.service.impl;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.wanshifu.iop.im.domain.bo.ConditionExpressionBo.ConditionExpressionBoItem;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sun.org.apache.xalan.internal.xsltc.compiler.FlowList;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.flow.FlowAddReq;
import com.wanshifu.iop.im.api.req.flow.FlowConfigReq;
import com.wanshifu.iop.im.api.req.flow.FlowDelReq;
import com.wanshifu.iop.im.api.req.flow.FlowDetailReq;
import com.wanshifu.iop.im.api.req.flow.FlowListReq;
import com.wanshifu.iop.im.api.req.flow.FlowModifyReq;
import com.wanshifu.iop.im.api.resp.flow.FlowAllListResp;
import com.wanshifu.iop.im.api.resp.flow.FlowDetailResp;
import com.wanshifu.iop.im.api.resp.flow.FlowListResp;
import com.wanshifu.iop.im.domain.bo.ConditionExpressionBo;
import com.wanshifu.iop.im.domain.bo.FlowNodeBo;
import com.wanshifu.iop.im.domain.bo.NodeConfigBo;
import com.wanshifu.iop.im.domain.enums.EnableStatusEnum;
import com.wanshifu.iop.im.domain.enums.NodeTypeEnum;
import com.wanshifu.iop.im.domain.po.FlowDefine;
import com.wanshifu.iop.im.domain.po.FlowNode;
import com.wanshifu.iop.im.domain.po.FlowTransition;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.repository.FlowDefineRepository;
import com.wanshifu.repository.FlowNodeRepository;
import com.wanshifu.repository.FlowTransitionRepository;
import com.wanshifu.repository.channel.ChannelConfigRepository;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.FlowService;
import lombok.extern.slf4j.Slf4j;
import org.omg.CORBA.TIMEOUT;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/1 16:24
 * @description 流程服务实现类
 */
@Service
@Slf4j
public class FlowServiceImpl extends AbstractService implements FlowService {


    /**
     * 路由导航列表
     *
     * @param req
     */
    @Override
    public SimplePageInfo<FlowListResp> flowList(FlowListReq req) {

        String flowName = req.getFlowName();

        Page<FlowListResp> objects = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<FlowDefine> flowDefineByNameList = flowDefineRepository.getFlowDefineByName(flowName, CommonConstant.ZERO);

        if(CollectionUtils.isEmpty(flowDefineByNameList)){
            return null;
        }

        List<Long> flowDefineIdList = flowDefineByNameList.stream().map(FlowDefine::getFlowDefineId).collect(Collectors.toList());

        List<ChannelConfig> channelConfigByFlowIds = channelConfigRepository.getChannelConfigByFlowIds(flowDefineIdList);

        SimplePageInfo<FlowListResp> result = new SimplePageInfo<>();
        result.setPageNum(objects.getPageNum());
        result.setPageSize(objects.getPageSize());
        result.setTotal(objects.getTotal());
        result.setPages(objects.getPages());
        result.setList(Lists.newArrayList());

        List<FlowListResp> list = new ArrayList<>();
        for (FlowDefine flowDefine : flowDefineByNameList) {
            FlowListResp flowListResp = new FlowListResp();
            flowListResp.setFlowId(flowDefine.getFlowDefineId());
            flowListResp.setFlowName(flowDefine.getFlowName());
            List<FlowListResp.FlowChannelItem> flowChannelItemList = new ArrayList<>();
            channelConfigByFlowIds.stream().filter(
                    f->f.getFlowDefineId().equals(flowDefine.getFlowDefineId()))
                    .forEach(k->{
                        FlowListResp.FlowChannelItem flowChannelItem = new FlowListResp.FlowChannelItem();
                        flowChannelItem.setChannelName(k.getChannelName());
                        flowChannelItem.setChannelId(k.getChannelId());
                        flowChannelItemList.add(flowChannelItem);
                    });
            flowListResp.setFlowChannels(flowChannelItemList);
            flowListResp.setCreateTime(flowDefine.getCreateTime());
            flowListResp.setUpdateTime(flowDefine.getUpdateTime());
            flowListResp.setCreateAccountId(flowDefine.getCreateAccountId());
            flowListResp.setUpdateAccountId(flowDefine.getUpdateAccountId());
            list.add(flowListResp);
        }
        result.setList(list);
        return result;
    }

    /**
     * 路由导航删除
     *
     * @param req
     */
    @Override
    public Integer flowDel(FlowDelReq req) {
        FlowDefine flowDefine = flowDefineRepository.selectByPrimaryKey(req.getFlowId());
        if(ObjectUtils.isEmpty(flowDefine)){
            log.error("删除流程异常，流程不存在；flowId: {}",req.getFlowId());
            return 0;
        }
        if(CommonConstant.ONE.equals(flowDefine.getIsDelete())){
            log.info("删除流程异常，流程已删除；flowId: {}",req.getFlowId());
            return 0;
        }

        FlowDefine flowDefineUpdate = new FlowDefine();
        flowDefineUpdate.setFlowDefineId(req.getFlowId());
        flowDefineUpdate.setUpdateAccountId(req.getAccountId());
        flowDefineUpdate.setIsDelete(CommonConstant.ONE);
        flowDefineRepository.updateByPrimaryKeySelective(flowDefineUpdate);
        return 1;
    }

    /**
     * 路由导航详情
     *
     * @param req
     */
    @Override
    public FlowDetailResp flowDetail(FlowDetailReq req) {

        FlowDefine flowDefine = flowDefineRepository.selectByPrimaryKey(req.getFlowId());
        if(ObjectUtils.isEmpty(flowDefine)){
            throw new BusException("路由详情不存在，flowDefineId:"+req.getFlowId());
        }

        FlowDetailResp flowDetailResp = new FlowDetailResp();
        flowDetailResp.setFlowId(flowDefine.getFlowDefineId());
        flowDetailResp.setCreateAccountId(flowDefine.getCreateAccountId());
        flowDetailResp.setUpdateAccountId(flowDefine.getUpdateAccountId());
        flowDetailResp.setCreateTime(flowDefine.getCreateTime());
        flowDetailResp.setUpdateTime(flowDefine.getUpdateTime());
        flowDetailResp.setFlowNodeList(Lists.newArrayList());


        List<FlowNode> flowNodeListByFlowId = flowNodeRepository.getFlowNodeListByFlowId(req.getFlowId());
        if(CollectionUtils.isEmpty(flowNodeListByFlowId)){
            return flowDetailResp;
        }

        List<FlowDetailResp.FlowDetailNode> flowNodeList = new ArrayList<>();

        for (FlowNode flowNode : flowNodeListByFlowId) {
            FlowDetailResp.FlowDetailNode flowDetailNode = new FlowDetailResp.FlowDetailNode();
            flowDetailNode.setNodeType(flowNode.getNodeType());
            flowDetailNode.setNodeName(flowNode.getNodeName());
            flowDetailNode.setNodeId(flowNode.getFlowNodeId());
            flowDetailNode.setNodeConfigJsonStr(flowNode.getConfigJson());
            flowDetailNode.setSort(flowNode.getSort());
            flowDetailNode.setFromNodeId(flowNode.getFlowNodeId());
            flowNodeList.add(flowDetailNode);
        }
        flowDetailResp.setFlowNodeList(flowNodeList);

        return flowDetailResp;
    }

    /**
     * 路由导航编辑
     *
     * @param req
     */
    @Override
    public Integer flowModify(FlowModifyReq req) {
        FlowDefine flowDefine = flowDefineRepository.selectByPrimaryKey(req.getFlowId());
        if(ObjectUtils.isEmpty(flowDefine)){
            log.error("编辑流程异常，流程不存在；flowId: {}",req.getFlowId());
            return 0;
        }
        if(CommonConstant.ONE.equals(flowDefine.getIsDelete())){
            log.info("编辑流程异常，流程已删除；flowId: {}",req.getFlowId());
            return 0;
        }
        List<FlowDefine> flowDefineByName = flowDefineRepository.getFlowDefineByNameAndId(req.getFlowId(),req.getFlowName(), CommonConstant.ZERO);
        if(CollectionUtils.isNotEmpty(flowDefineByName)){
            log.error("新增流程异常，流程名称已存在；flowName: {}",req.getFlowName());
            throw new BusException("新增流程异常，流程名称已存在；");
        }
        FlowDefine flowDefineUpdate = new FlowDefine();
        flowDefineUpdate.setFlowDefineId(req.getFlowId());
        flowDefineUpdate.setUpdateAccountId(req.getUpdateAccountId());
        flowDefineUpdate.setFlowName(req.getFlowName());
        flowDefineRepository.updateByPrimaryKeySelective(flowDefineUpdate);
        return 1;
    }

    /**
     * 路由导航新增
     *
     * @param req
     */
    @Override
    public Long flowAdd(FlowAddReq req) {

        List<FlowDefine> flowDefineByName = flowDefineRepository.getFlowDefineByName(req.getFlowName(), CommonConstant.ZERO);
        if(CollectionUtils.isNotEmpty(flowDefineByName)){
            log.error("新增流程异常，流程名称已存在；flowName: {}",req.getFlowName());
            throw new BusException("新增流程异常，流程名称已存在；");
        }

        FlowDefine flowDefine = new FlowDefine();
        flowDefine.setFlowName(req.getFlowName());
        flowDefine.setUpdateAccountId(req.getCreateAccountId());
        flowDefine.setCreateAccountId(req.getCreateAccountId());
        flowDefine.setStatus(EnableStatusEnum.ENABLE.type);
        flowDefine.setIsDelete(0);
        flowDefine.setUpdateTime(new Date());
        flowDefine.setCreateTime(new Date());
        flowDefineRepository.insertSelective(flowDefine);
        return flowDefine.getFlowDefineId();
    }

    /**
     * 路由导航新增
     * 需要更新三个表：
     * flow_define
     * flow_node
     * flow_transition
     *
     * @param req
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer flowConfig(FlowConfigReq req) {

        //更新flow_define
        FlowDefine flowDefine = new FlowDefine();
        flowDefine.setFlowDefineId(req.getFlowId());
        flowDefine.setUpdateAccountId(req.getUpdateAccountId());
        flowDefine.setUpdateTime(new Date());
        flowDefineRepository.updateByPrimaryKeySelective(flowDefine);

        //插入flow_node，需要判断两种情况，如果有关联节点id，节点改成删除状态，如果没有关联节点id，直接插入
        flowNodeRepository.deleteOldFlowNodeByFlowDefineId(req.getFlowId());
        
        //删除flow_transition is_delete=1
        flowTransitionRepository.updateSetIsDeleteDefineTransition(req.getFlowId());
        

        List<FlowNode> flowNodeList = new ArrayList<>();

        for (FlowConfigReq.FlowDetailNode flowDetailNode : req.getFlowNodeList()) {
            FlowNode flowNode = new FlowNode();
            flowNode.setFlowDefineId(req.getFlowId());
            flowNode.setNodeName(NodeTypeEnum.getNameByType(flowDetailNode.getNodeType()));
            flowNode.setNodeType(flowDetailNode.getNodeType());
            flowNode.setSort(flowDetailNode.getSort());
            flowNode.setConfigJson(flowDetailNode.getNodeConfigJsonStr());
            flowNode.setIsDelete(0);
            flowNode.setUpdateTime(new Date());
            flowNode.setCreateTime(new Date());
            flowNodeRepository.insertSelective(flowNode);
            flowNodeList.add(flowNode);
        }

        //插入flow_transition 节点关联关系表
        List<FlowTransition> flowTransitionBeans = this.returnFlowTransition( flowNodeList, req.getFlowNodeList());
        flowTransitionRepository.insertList(flowTransitionBeans);
        return 1;
    }

    /**
     * 返回路由节点关系
     * */
    public List<FlowTransition> returnFlowTransition(List<FlowNode> flowNodeList, List<FlowConfigReq.FlowDetailNode> flowNodeListReq){

        List<FlowTransition> result = new ArrayList<>();

        for (FlowNode flowNode : flowNodeList) {

            NodeTypeEnum nodeTypeEnum = NodeTypeEnum.getEnumByType(flowNode.getNodeType());
            if(nodeTypeEnum==null){
                continue;
            }
            FlowTransition flowTransition = new FlowTransition();
            Optional<FlowNode> nodeOptional = flowNodeList.stream().filter(f -> nodeTypeEnum.type.equals(f.getNodeType())).findFirst();
            if(!nodeOptional.isPresent()){
                continue;
            }
            FlowNode flowNodeByType = nodeOptional.get();
            flowTransition.setFlowDefineId(flowNodeByType.getFlowDefineId());
            flowTransition.setToNodeId(flowNodeByType.getFlowNodeId());
            flowTransition.setIsDelete(0);
            flowTransition.setUpdateTime(new Date());
            flowTransition.setCreateTime(new Date());
            switch (nodeTypeEnum){
                case START:
                        flowTransition.setFromNodeId(0L);
                        flowTransition.setConditionExpression("");
                    break;
                case ROBOT:
                case ARTIFICIAL:
                case LEAVE_WORD:
                case SYSTEM_MSG:
                case SYSTEM_REPLY:
                case TIME:
                    //找出节点的父级信息
                    Optional<FlowConfigReq.FlowDetailNode> first = flowNodeListReq.stream().filter(f -> f.getNodeType().equals(nodeTypeEnum.type)).findFirst();
                    if(!first.isPresent()){
                        continue;
                    }
                    FlowConfigReq.FlowDetailNode flowDetailNode = first.get();
                    String fromNodeId = flowDetailNode.getFromNodeId();
                    Optional<FlowConfigReq.FlowDetailNode> flowDetailFromOptional = flowNodeListReq.stream().filter(f -> f.getNodeId().equals(fromNodeId)).findFirst();
                    if (!flowDetailFromOptional.isPresent()) {
                        continue;
                    }
                    //父级节点对象
                    FlowConfigReq.FlowDetailNode flowDetailNodeParent = flowDetailFromOptional.get();
                    String nodeType = flowDetailNodeParent.getNodeType();

                    Optional<FlowNode> nodeOptional1 = flowNodeList.stream().filter(f -> f.getNodeType().equals(nodeType)).findFirst();
                    if(!nodeOptional1.isPresent()){
                        continue;
                    }
                    flowTransition.setFromNodeId(nodeOptional1.get().getFlowNodeId());

                    String nodeConfigJsonStr = flowDetailNode.getNodeConfigJsonStr();
                    NodeConfigBo nodeConfigBo = JSONObject.parseObject(nodeConfigJsonStr, NodeConfigBo.class);
                    if(!NodeTypeEnum.TIME.type.equals(flowNode.getNodeType()) && CollectionUtils.isNotEmpty(nodeConfigBo.getWorkTime())){
                        ConditionExpressionBo conditionExpressionBo = new ConditionExpressionBo();
                        conditionExpressionBo.setConditionType(NodeTypeEnum.TIME.type);
                        conditionExpressionBo.setCondition("in");
                        ConditionExpressionBoItem conditionExpressionBoItem = new ConditionExpressionBoItem();
                        conditionExpressionBoItem.setWorkDays(nodeConfigBo.getWorkDays());
                        conditionExpressionBoItem.setWorkTime(nodeConfigBo.getWorkTime());
                        conditionExpressionBo.setConditionValue(conditionExpressionBoItem);
                        flowTransition.setConditionExpression(JSON.toJSONString(conditionExpressionBo));
                    }
                    break;
            }
            result.add(flowTransition);
        }
        return result;
    }

    /**
     * 路由导航列表
     */
    @Override
    public List<FlowAllListResp> flowAllList() {

        List<FlowDefine> flowDefineByNameList = flowDefineRepository.getFlowDefineAll(EnableStatusEnum.ENABLE.type);
        if(CollectionUtils.isEmpty(flowDefineByNameList)){
            return Lists.newArrayList();
        }
        List<FlowAllListResp> result = new ArrayList<>();
        for (FlowDefine flowDefine : flowDefineByNameList) {
            FlowAllListResp flowAllListResp = new FlowAllListResp();
            flowAllListResp.setFlowId(flowDefine.getFlowDefineId());
            flowAllListResp.setFlowName(flowDefine.getFlowName());
            result.add(flowAllListResp);
        }
        return result;
    }
}
