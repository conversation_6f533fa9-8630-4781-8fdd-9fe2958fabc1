package com.wanshifu.service.impl;

import com.wanshifu.factory.visitor.VisitorFactory;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.rightSidebar.*;
import com.wanshifu.iop.im.api.resp.CommonLabelValueLevelResp;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.api.resp.rightSidebar.*;
import com.wanshifu.iop.im.domain.bo.conversation.GetConversationVisitorRegisterInfoRespBo;
import com.wanshifu.iop.im.domain.enums.rightSidebar.ProblemIsSolvedEnum;
import com.wanshifu.iop.im.domain.enums.rightSidebar.RelationWorkOrderTypeEnum;
import com.wanshifu.iop.im.domain.po.BusinessFromTemplate;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.Problem;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.RightSidebarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 右侧边栏服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:44
 */
@Service
@Slf4j
public class RightSidebarServiceImpl extends AbstractService implements RightSidebarService {

    /**
     * 访客信息
     * @param req
     * @return
     */
    @Override
    public List<GetVisitorInfoResp> visitorInfo(GetVisitorInfoReq req) {

        // 获取会话和访客注册信息
        GetConversationVisitorRegisterInfoRespBo conversationRegisterInfo = super.getVerityConversationVisitorRegisterInfo(req);

        // 获取访客信息
        return VisitorFactory.setFactoryType(conversationRegisterInfo.getUserRegisterInfo().getUserClass())
                .queryVisitorInfo(conversationRegisterInfo.getUserRegisterInfo(), req.getConversationId());
    }

    /**
     * 表单枚举
     * @param req
     * @return
     */
    @Override
    public GetFromEnumsResp fromEnums(BaseOnlineConversationReq req) {
        GetFromEnumsResp resp = new GetFromEnumsResp();

        // 获取会话信息
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getConversationId());
        if ( Objects.isNull(imConversation) ){
            log.error("会话不存在, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话不存在");
        }

        // 获取业务小结模板
        List<BusinessFromTemplate> businessFromTemplates  = businessFromTemplateRepository.selectAllByTenantIdAndUserClass(req.getTenantId(), imConversation.getFromOuterUserType());
        if (CollectionUtils.isNotEmpty(businessFromTemplates)){
            List<CommonLabelValueResp> controlTypeEnums = businessFromTemplates.stream().map(item -> {
                CommonLabelValueResp labelValueResp = new CommonLabelValueResp();
                labelValueResp.setLabel(item.getBusinessTemplateCn());
                labelValueResp.setValue(item.getBusinessFromTemplateId().toString());
                return labelValueResp;
            }).collect(Collectors.toList());
            resp.setBusinessSummaryTemplateEnums(controlTypeEnums);
        }

        // 获取问题是否已解决 0-未解决 1-已解决 枚举
        List<CommonLabelValueResp> problemIsSolvedEnums = Arrays.stream(ProblemIsSolvedEnum.values()).map(item -> {
            CommonLabelValueResp labelValueResp = new CommonLabelValueResp();
            labelValueResp.setLabel(item.name);
            labelValueResp.setValue(item.type.toString());
            return labelValueResp;
        }).collect(Collectors.toList());
        resp.setProblemIsSolvedEnums(problemIsSolvedEnums);

        // 获取创建工单类型枚举
        List<CommonLabelValueResp> workOrderTypeEnums = Arrays.stream(RelationWorkOrderTypeEnum.values()).map(item -> {
            CommonLabelValueResp labelValueResp = new CommonLabelValueResp();
            labelValueResp.setLabel(item.name);
            labelValueResp.setValue(item.type);
            return labelValueResp;
        }).collect(Collectors.toList());
        resp.setCreateWorkOrderTypeEnums(workOrderTypeEnums);

        return resp;
    }

    /**
     * 通过业务小结模板id获取问题类型枚举
     * @param req
     * @return
     */
    @Override
    public List<CommonLabelValueLevelResp> getProblemTypeEnums(GetProblemTypeEnumsReq req) {

        // 获取模板信息
        BusinessFromTemplate businessFromTemplate = businessFromTemplateRepository.selectByPrimaryKey(req.getBusinessFromTemplateId());
        if (Objects.isNull(businessFromTemplate)){
            log.error("业务小结模板不存在, businessFromTemplateId:{}", req.getBusinessFromTemplateId());
            return new ArrayList<>();
        }
        if ( Objects.isNull(businessFromTemplate.getProblemId()) || businessFromTemplate.getProblemId() == 0 ){
            return new ArrayList<>();
        }

        // 获取模板问题类型
        Problem mainProblem = problemRepository.selectByPrimaryKey(businessFromTemplate.getProblemId());
        if ( Objects.isNull(mainProblem) ){
            log.error("模板主问题类型不存在, problemId:{}", businessFromTemplate.getProblemId());
            return new ArrayList<>();
        }

        // 获取一级问题类型
        List<Problem> problemLv1List = problemRepository.selectAllByParentId(Collections.singletonList(mainProblem.getProblemId()));
        if ( CollectionUtils.isEmpty(problemLv1List) ){
            return new ArrayList<>();
        }

        // 获取二级问题类型
        List<Problem> problemLv2List = problemRepository.selectAllByParentId(problemLv1List.stream().map(Problem::getProblemId).collect(Collectors.toList()));

        // 获取三级问题类型
        List<Problem> problemLv3List = problemRepository.selectAllByParentId(problemLv2List.stream().map(Problem::getProblemId).collect(Collectors.toList()));

        return problemLv1List.stream().map(item -> {
            CommonLabelValueLevelResp labelValueLevelResp = new CommonLabelValueLevelResp();
            labelValueLevelResp.setLabel(item.getProblemName());
            labelValueLevelResp.setValue(item.getProblemId().toString());
            labelValueLevelResp.setLevel("1");
            labelValueLevelResp.setChildren(problemLv2List.stream().filter(problemLv2 -> problemLv2.getParentId().equals(item.getProblemId())).map(problemLv2 -> {
                CommonLabelValueLevelResp labelValueLevelRespLv2 = new CommonLabelValueLevelResp();
                labelValueLevelRespLv2.setLabel(problemLv2.getProblemName());
                labelValueLevelRespLv2.setValue(problemLv2.getProblemId().toString());
                labelValueLevelRespLv2.setLevel("2");
                if ( CollectionUtils.isEmpty(problemLv3List) ){
                    return labelValueLevelRespLv2;
                }

                labelValueLevelRespLv2.setChildren(problemLv3List.stream().filter(problemLv3 -> problemLv3.getParentId().equals(problemLv2.getProblemId())).map(problemLv3 -> {
                    CommonLabelValueLevelResp labelValueLevelRespLv3 = new CommonLabelValueLevelResp();
                    labelValueLevelRespLv3.setLabel(problemLv3.getProblemName());
                    labelValueLevelRespLv3.setValue(problemLv3.getProblemId().toString());
                    labelValueLevelRespLv3.setLevel("3");
                    return labelValueLevelRespLv3;
                    }).collect(Collectors.toList()));
                return labelValueLevelRespLv2;
            }).collect(Collectors.toList()));
            return labelValueLevelResp;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer businessSummarySubmitForm(BusinessSummarySubmitFormReq req) {
        return null;
    }

    @Override
    public Integer createOrderSubmitForm(CreateOrderSubmitFormReq req) {
        return null;
    }

    @Override
    public List<ConversationVisitorHistoryLogsResp> conversationVisitorHistoryLogs(ConversationVisitorHistoryLogsReq req) {
        return null;
    }

    @Override
    public ConversationHistoryMessageItemLogsResp conversationHistoryMessageItemLogs(ConversationHistoryMessageItemLogsReq req) {
        return null;
    }

    @Override
    public ConversationVisitInfoResp conversationVisitInfo(ConversationVisitInfoReq req) {
        return null;
    }

    @Override
    public ConversationBaseInfoResp conversationBaseInfo(ConversationBaseInfoReq req) {
        return null;
    }

    @Override
    public ConversationWorkOrderInfoResp conversationWorkOrderInfo(ConversationWorkOrderInfoReq req) {
        return null;
    }

    @Override
    public QueryOrderAndWorkOrderInfoResp queryOrderAndWorkOrderInfo(QueryOrderAndWorkOrderInfoReq req) {
        return null;
    }
}
