package com.wanshifu.service.impl;

import com.wanshifu.iop.im.api.req.knowledgeBase.*;
import com.wanshifu.iop.im.api.resp.knowledgeBase.KnowledgeBaseDataListResp;
import com.wanshifu.service.AbstractService;
import com.wanshifu.service.KnowledgeBaseManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 知识库管理
 * <AUTHOR>
 * @date： 2025-06-04 15:27:44
 */
@Service
@Slf4j
public class KnowledgeBaseManageServiceImpl extends AbstractService implements KnowledgeBaseManageService {

    @Override
    public KnowledgeBaseDataListResp knowledgeBaseDataList(KnowledgeBaseDataListReq req) {
        return null;
    }

    @Override
    public Integer addKnowledgeBaseCategory(AddKnowledgeBaseCategoryReq req) {
        return null;
    }

    @Override
    public Integer addKnowledgeBaseContent(AddKnowledgeBaseContentReq req) {
        return null;
    }

    @Override
    public Integer editKnowledgeBaseCategory(EditKnowledgeBaseCategoryReq req) {
        return null;
    }

    @Override
    public Integer editKnowledgeBaseContent(EditKnowledgeBaseContentReq req) {
        return null;
    }

    @Override
    public Integer deleteKnowledgeBaseData(DeleteKnowledgeBaseDataReq req) {
        return null;
    }
}
