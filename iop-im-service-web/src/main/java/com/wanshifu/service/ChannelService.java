package com.wanshifu.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.channel.*;
import com.wanshifu.iop.im.api.resp.ImgUploadResp;
import com.wanshifu.iop.im.api.resp.channel.*;
import com.wanshifu.iop.im.domain.bo.channel.ChannelBo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ChannelService {

    /**
     * 端侧+渠道枚举
     */
    ChannelEnumsResp enums(ChannelEnumsReq req);

    /**
     * 端侧+渠道列表
     */
    ChannelSearchListResp searchList(ChannelSearchListReq req);

    /**
     * 新增端侧
     */
    Long createClientCategory(ClientCategoryCreateReq req);

    /**
     * 编辑端侧
     */
    Long editClientCategory(ClientCategoryUpdateReq req);

    /**
     * 删除端侧
     */
    Long deleteClientCategory(DeleteClientCategoryReq req);

    /**
     * 通过端侧ID查询渠道列表
     */
    List<ChannelBo> channelList(ChannelSearchListReq req);

    /**
     * 新增渠道
     */
    Long createChannel(CreateChannelReq req);


    /**
     * 编辑渠道
     */
    Long editChannel(UpdateChannelReq req);

    /**
     * 删除渠道
     */
    Long deleteChannel(DeleteChannelReq req);

    /**
     * 修改渠道状态
     */
    SwitchChannelStatusResp switchChannelStatus(SwitchChannelStatusReq req);

    /**
     * 批量编辑渠道
     */
    Integer batchEditChannel(BatchEditChannelReq req);

    /**
     * 预览端侧+渠道
     */
    PreviewClientCategoryAndChannelResp previewDraft(PreviewClientCategoryAndChannelReq req);

    /**
     * 发布端侧+渠道
     */
    Long deployConfig(DeployClientCategoryAndChannelReq req);

    /**
     * 保存端侧+渠道草稿
     */
    Long saveConfigDraft(CreateDraftReq req);

    /**
     * 会话主题样式和侧边栏查询
     */
    GetStyleAndSideBarResp getStyleAndSideBar(GetStyleAndSideBarReq req);
    /**
     * 编辑会话主题样式和侧边栏
     *
     */
    Long editStyleAndSideBar(UpdateConversationStyleConfigRqt req);
    /**
     * 新增会话主题样式和侧边栏
     *
     */
    Long createStyleAndSideBar(CreateConversationStyleConfigRqt req);
    /**
     * 会话满意度配置查询
     *
     */
    GetSatisfactionLevelConfigResp getSatisfactionLevelConfig(GetSatisfactionLevelConfigReq req);
    /**
     * 新增或编辑会话满意度配置
     *
     */
    Long createOrEditSatisfactionLevelConfig(CreateOrEditSatisfactionLevelConfigReq req);
    /**
     * 会话系统自动回复消息配置查询
     *
     */
    GetAutoReplyConfigResp getAutoReplyConfig(GetAutoReplyConfigReq req);
    /**
     * 新增或编辑会话满意度配置
     *
     */
    Long createOrEditAutoReplyConfig(CreateOrEditAutoReplyConfigReq req);

    /**
     * 上传按钮图标
     */
    ImgUploadResp iconUpload(IconUploadReq req, MultipartFile file);


    /**
     * 用户咨询问题列表
     * */
    SimplePageInfo<ChannelUserProblemResp> userProblemClassList(ChannelUserProblemClassReq req);

    /**
     * 用户咨询问题分类-删除
     *
     * @param req
     */
    Integer userProblemClassDel(ChannelUserProblemDelReq req);

    /**
     * 用户咨询问题分类-修改状态
     *
     * @param req
     */
    Integer userProblemClassChangeStatus(ChannelUserProblemChangeStatusReq req);

    /**
     * 用户咨询问题分类-新增
     *
     * @param req
     */
    Long userProblemClassAdd(ChannelUserProblemAddReq req);

    List<GetChannelConfigDraftListResp> getChannelConfigDraftList(GetChannelConfigDraftListRqt req);
}
