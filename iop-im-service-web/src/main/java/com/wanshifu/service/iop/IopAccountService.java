package com.wanshifu.service.iop;

import com.wanshifu.iop.account.domain.po.AccountUserMapping;
import com.wanshifu.iop.account.domain.resp.account.AccountAllResp;
import com.wanshifu.iop.account.domain.resp.account.AccountDetailResp;
import com.wanshifu.iop.account.domain.resp.account.AccountInfoListResp;
import com.wanshifu.iop.im.api.req.tags.CreateTagsReq;
import com.wanshifu.iop.im.api.req.tags.GetTagsListReq;
import com.wanshifu.iop.im.api.req.tags.SwitchTagsStatusReq;
import com.wanshifu.iop.im.domain.po.Tags;

import java.util.List;

/**
 * 账号中台服务
 * <AUTHOR>
 * @date： 2025-06-03 10:40:46
 */
public interface IopAccountService {
    /**
     * 通过username获取 账号信息
     */
    AccountDetailResp getUserInfoByUsername(String username);

    /**
     * 批量获取 账号信息
     */
    List<AccountInfoListResp> batchGetInfoListByAccountIds(List<Long> accountIdList);

    /**
     * 获取所有账号信息
     */
    List<AccountAllResp> getAllAccount(Integer accountStatus);

    /**
     * 查询账号状态 账号信息
     */
    AccountUserMapping selectByAccountIdAndProductType(Long accountId);
}
