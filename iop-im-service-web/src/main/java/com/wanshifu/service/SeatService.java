package com.wanshifu.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.seat.*;
import com.wanshifu.iop.im.api.req.tags.CreateTagsReq;
import com.wanshifu.iop.im.api.req.tags.GetTagsListReq;
import com.wanshifu.iop.im.api.req.tags.SwitchTagsStatusReq;
import com.wanshifu.iop.im.api.resp.seat.ConversationTransferServiceResp;
import com.wanshifu.iop.im.api.resp.seat.GetSeatStatusListResp;
import com.wanshifu.iop.im.api.resp.seat.SeatFunctionResp;
import com.wanshifu.iop.im.api.resp.seat.SeatEnumsResp;
import com.wanshifu.iop.im.api.resp.seat.SeatSearchListResp;
import com.wanshifu.iop.im.api.resp.tags.GetTagsListResp;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.SeatVirtualRelation;
import java.util.List;

public interface SeatService {

    /***
     * 坐席列表枚举
     */
    SeatEnumsResp enums(SeatEnumsReq req);

    /**
     * 坐席列表
     */
    SimplePageInfo<SeatSearchListResp> searchList(SeatSearchListReq req);

    /**
     * 创建坐席
     */
    Integer create(SeatCreateReq req);

    /**
     * 坐席注册虚拟账号
     */
    SeatVirtualRelation seatRegisterVirtualAccount(SeatInfo seatInfo);

    /**
     * 编辑坐席
     */
    Integer edit(SeatEditReq req);

    /**
     * 获取标签列表
     * */
    List<GetTagsListResp> tagsList(GetTagsListReq req);

    /**
     * 添加标签
     */
    Integer createTags(CreateTagsReq req);

    /**
     * 编辑标签
     */
    Integer editTags(CreateTagsReq req);

    /**
     * 切换标签状态
     */
    Integer switchTagsStatus(SwitchTagsStatusReq req);

    /**
     * 坐席状态列表
     */
    List<GetSeatStatusListResp> seatStatusList();

    /**
     * 修改坐席状态配置
     */
    Integer editSeatStatus(EditSeatStatusReq req);

    /**
     * 获取坐席功能枚举
     */
    List<SeatFunctionResp> seatFunctionEnums();

    /**
     * 关闭会话
     * */
    Integer closeConversation(CloseConversationReq req);

    /**
     * 坐席列表-转接坐席列表
     * */
    List<ConversationTransferServiceResp> transferConversation();

    /**
     * 会话转接
     * */
    Integer transfer(TransferServiceReq req);

    /**
     * 会话流转更新坐席状态
     */
    Integer conversationFlowSeatStatusUpdate(ConversationFlowSeatStatusUpdateReq req);
}
