package com.wanshifu.service;

import com.wanshifu.iop.im.api.req.knowledgeBase.*;
import com.wanshifu.iop.im.api.resp.knowledgeBase.KnowledgeBaseDataListResp;

/**
 * 知识库管理服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:06
 */
public interface KnowledgeBaseManageService {
    KnowledgeBaseDataListResp knowledgeBaseDataList(KnowledgeBaseDataListReq req);

    Integer addKnowledgeBaseCategory(AddKnowledgeBaseCategoryReq req);

    Integer addKnowledgeBaseContent(AddKnowledgeBaseContentReq req);

    Integer editKnowledgeBaseCategory(EditKnowledgeBaseCategoryReq req);

    Integer editKnowledgeBaseContent(EditKnowledgeBaseContentReq req);

    Integer deleteKnowledgeBaseData(DeleteKnowledgeBaseDataReq req);
}
