package com.wanshifu.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.incomingConfig.*;
import com.wanshifu.iop.im.api.resp.incomingConfig.GetRegistrationFrequencyConfigResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.IncomingConfigEnumsResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchDomainListResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchWhiteAndBlackListResp;

import java.util.List;

/**
 * 进线配置服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:06
 */
public interface IncomingConfigService {

    IncomingConfigEnumsResp enums();

    List<SearchDomainListResp> domainList(SearchDomainListReq req);

    Integer addDomain(AddDomainReq req);

    Integer deleteDomain(DeleteDomainReq req);

    SimplePageInfo<SearchWhiteAndBlackListResp> whiteAndBlackList(SearchWhiteAndBlackListReq req);

    Integer addWhiteAndBlack(AddWhiteAndBlackReq req);

    Integer updateWhiteAndBlack(UpdateWhiteAndBlackReq req);

    GetRegistrationFrequencyConfigResp getRegistrationFrequencyConfig(GetRegistrationFrequencyConfigReq req);

    Integer updateRegistrationFrequency(UpdateRegistrationFrequencyReq req);
}
