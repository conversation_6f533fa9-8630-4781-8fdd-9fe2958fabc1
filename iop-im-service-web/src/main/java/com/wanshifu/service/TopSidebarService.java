package com.wanshifu.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.leftSidebar.*;
import com.wanshifu.iop.im.api.req.topSidebar.SeatConnectionInfoListReq;
import com.wanshifu.iop.im.api.resp.leftSidebar.*;
import com.wanshifu.iop.im.api.resp.topSidebar.SeatConnectionInfoListResp;
import com.wanshifu.iop.im.domain.bo.CoreActiveConversationRespBo;
import com.wanshifu.iop.im.domain.bo.HistoryConversationItemSenderInfoRespBo;

import java.util.List;

/**
 * 顶边栏服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:06
 */
public interface TopSidebarService  {

    List<SeatConnectionInfoListResp> seatConnectionInfoList(SeatConnectionInfoListReq req);
}
