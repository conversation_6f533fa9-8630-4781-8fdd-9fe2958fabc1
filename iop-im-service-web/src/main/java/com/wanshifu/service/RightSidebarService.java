package com.wanshifu.service;

import com.wanshifu.iop.im.api.req.rightSidebar.*;
import com.wanshifu.iop.im.api.resp.CommonLabelValueLevelResp;
import com.wanshifu.iop.im.api.resp.rightSidebar.*;

import java.util.List;

/**
 * 右侧边栏服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:06
 */
public interface RightSidebarService {

    List<GetVisitorInfoResp> visitorInfo(GetVisitorInfoReq req);

    GetFromEnumsResp fromEnums(BaseOnlineConversationReq req);

    List<CommonLabelValueLevelResp> getProblemTypeEnums(GetProblemTypeEnumsReq req);

    Integer businessSummarySubmitForm(BusinessSummarySubmitFormReq req);

    Integer createOrderSubmitForm(CreateOrderSubmitFormReq req);

    List<ConversationVisitorHistoryLogsResp> conversationVisitorHistoryLogs(ConversationVisitorHistoryLogsReq req);

    ConversationHistoryMessageItemLogsResp conversationHistoryMessageItemLogs(ConversationHistoryMessageItemLogsReq req);

    ConversationVisitInfoResp conversationVisitInfo(ConversationVisitInfoReq req);

    ConversationBaseInfoResp conversationBaseInfo(ConversationBaseInfoReq req);

    ConversationWorkOrderInfoResp conversationWorkOrderInfo(ConversationWorkOrderInfoReq req);

    QueryOrderAndWorkOrderInfoResp queryOrderAndWorkOrderInfo(QueryOrderAndWorkOrderInfoReq req);
}
