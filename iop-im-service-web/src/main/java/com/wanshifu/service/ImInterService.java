package com.wanshifu.service;

import com.wanshifu.iop.im.api.req.inter.GetGroupListReq;
import com.wanshifu.iop.im.api.req.inter.GetSeatImInfoReq;
import com.wanshifu.iop.im.api.req.inter.GetVirtualUserInfoReq;
import com.wanshifu.iop.im.api.req.inter.RegisterUserReq;
import com.wanshifu.iop.im.api.req.inter.ToArtificialServiceReq;
import com.wanshifu.iop.im.api.resp.inter.GetGroupListResp;
import com.wanshifu.iop.im.api.resp.inter.GetVirtualUserInfoResp;
import com.wanshifu.iop.im.api.resp.inter.RegisterUserResp;
import com.wanshifu.iop.im.domain.bo.AIWorkflowExecuteResultMsgBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgTagBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToGroupBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToSingleBo;
import com.wanshifu.iop.im.domain.bo.ToArtificialMsgBo;
import com.wanshifu.iop.im.domain.bo.channel.msg.ToArtificialTypesBo;
import com.wanshifu.iop.im.domain.bo.conversation.XxlTimeoutReqBo;

import java.util.List;

public interface ImInterService {

    /**
     * 注册用户
     * */
    RegisterUserResp registerUser(RegisterUserReq req);

    /**
     * 批量注册虚拟用户
     * */
    Integer batchRegisterUser(Integer num);

    /**
     * 作用：获取虚拟用户信息，绑定关系，系统自动发送欢迎语消息
     * */
    GetVirtualUserInfoResp getVirtualInfo(GetVirtualUserInfoReq req);

    /**
     * 单聊导入+更新群消息+导入群消息
     * */
    Integer importMsgAction(ImportMsgTagBo req);

    /**
     * 导入单聊消息
     * */
    Integer importMsgSingleAction(ImportMsgToSingleBo req);


    /**
     * 导入群聊消息
     * */
    Integer importMsgGroupAction(ImportMsgToGroupBo req);


    /**
     * 客服初始化 坐席信息
     * */
    RegisterUserResp getSeatImInfo(GetSeatImInfoReq req);


    /**
     * 坐席端-获取群列表
     * */
    List<GetGroupListResp> getGroupList(GetGroupListReq req);

    /**
     * 机器人ai回调 向用户发送消息
     * */
    Integer sendAIMsgToPersonal(AIWorkflowExecuteResultMsgBo req);

    /**
     * 转人工，实际是进入排队
     *
     * @param req
     */
    Integer toArtificial(ToArtificialServiceReq req);

    /**
     * 发送询前表单
     * */
    Integer sendPreForm(ToArtificialMsgBo req);

    /**
     * 发送欢迎语消息
     * */
    Integer toArtificialWelcomeMsg(ToArtificialTypesBo req);

    /**
     * 添加超时任务
     * */
    Integer addTimeoutTask(XxlTimeoutReqBo req);
}
