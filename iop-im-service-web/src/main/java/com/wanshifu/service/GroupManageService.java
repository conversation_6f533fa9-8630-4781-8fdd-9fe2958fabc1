package com.wanshifu.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.group.*;
import com.wanshifu.iop.im.api.req.incomingConfig.*;
import com.wanshifu.iop.im.api.resp.group.*;
import com.wanshifu.iop.im.api.resp.incomingConfig.GetRegistrationFrequencyConfigResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.IncomingConfigEnumsResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchDomainListResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchWhiteAndBlackListResp;

import java.util.List;

/**
 * 进线配置服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:06
 */
public interface GroupManageService {

    GroupConfigEnumsResp enums();

    SimplePageInfo<GroupListResp> list(GroupListReq req);

    Long add(AddGroupReq req);

    Integer rearrangement(GroupRearrangementReq req);

    SimplePageInfo<GroupSeatListResp> seatList(GroupSeatListReq req);

    Long addSeat(AddGroupSeatReq req);

    Integer removeSeat(RemoveGroupSeatReq req);

    GroupAccessRuleDetailResp ruleDetail(GroupAccessRuleDetailReq req);

    Integer editRule(EditGroupAccessRuleReq req);

    SeatPriorityDistributeRuleEnumsResp seatPriorityDistributeRuleEnums(GroupAccessRuleDetailReq req);

    SeatPriorityDistributeRuleDetailResp seatPriorityDistributeRuleList(SeatPriorityDistributeRuleDetailReq req);

    Integer editDistributeStrategy(EditDistributeStrategyReq req);

    Integer editSeatDistributeRule(EditGroupSeatDistributeRuleReq req);
}
