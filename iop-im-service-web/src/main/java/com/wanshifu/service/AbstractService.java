package com.wanshifu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.components.xxl.XxlJobTemplate;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.CommonRedisConstant;
import com.wanshifu.constant.TencentConstant;
import com.wanshifu.factory.DataManipulationFactory;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.account.domain.enums.AccountStatus;
import com.wanshifu.iop.account.domain.po.AccountUserMapping;
import com.wanshifu.iop.account.domain.resp.account.AccountInfoListResp;
import com.wanshifu.iop.im.api.req.leftSidebar.BaseOnlineSeatReq;
import com.wanshifu.iop.im.api.req.rightSidebar.BaseOnlineConversationReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.req.tencent.CallbackSingleSendMsgAfterReq;
import com.wanshifu.iop.im.api.resp.CommonRunStrategyResp;
import com.wanshifu.iop.im.api.resp.ResultResp;
import com.wanshifu.iop.im.api.resp.SetLoginAuthResp;
import com.wanshifu.iop.im.domain.bo.*;
import com.wanshifu.iop.im.domain.bo.AIWorkflowExecuteResultMsgBo;
import com.wanshifu.iop.im.domain.bo.ImConversationBeanBo;
import com.wanshifu.iop.im.domain.bo.RegisterUserReqBo;
import com.wanshifu.iop.im.domain.bo.RegisterUserRespBo;
import com.wanshifu.iop.im.domain.bo.SendMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgToPersonalResultBo;
import com.wanshifu.iop.im.domain.bo.UserInfoBo;
import com.wanshifu.iop.im.domain.bo.channel.msg.ToArtificialTypesBo;
import com.wanshifu.iop.im.domain.bo.channel.reply.TimeoutStrategyReqBo;
import com.wanshifu.iop.im.domain.bo.conversation.FormCustomerMsgContentBo;
import com.wanshifu.iop.im.domain.bo.conversation.FormMsgContentFormatBo;
import com.wanshifu.iop.im.domain.bo.conversation.FormMsgContentFormatObjBo;
import com.wanshifu.iop.im.domain.bo.conversation.GetConversationVisitorRegisterInfoRespBo;
import com.wanshifu.iop.im.domain.bo.conversation.TextMsgContentFormatBo;
import com.wanshifu.iop.im.domain.bo.groupManage.GroupRuleConfigValueBo;
import com.wanshifu.iop.im.domain.bo.seat.GetSeatRegisterInfoRespBo;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.iop.im.domain.enums.*;
import com.wanshifu.iop.im.domain.enums.AccountTypeEnum;
import com.wanshifu.iop.im.domain.enums.ConversationStatusEnum;
import com.wanshifu.iop.im.domain.enums.EnableStatusEnum;
import com.wanshifu.iop.im.domain.enums.MsgTypeEnum;
import com.wanshifu.iop.im.domain.enums.NodeTypeEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserClassTypeEnum;
import com.wanshifu.iop.im.domain.enums.OuterUserTypeEnum;
import com.wanshifu.iop.im.domain.enums.RegisterStatusEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricConfigEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricFromTypeEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricObjectEnum;
import com.wanshifu.iop.im.domain.enums.VirtualStatusEnum;
import com.wanshifu.iop.im.domain.po.*;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyDetailConfig;
import com.wanshifu.manager.commonBase.CommonBaseManager;
import com.wanshifu.manager.socketBase.impl.SocketBaseManagerImpl;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.master.information.api.CommonQueryServiceApi;
import com.wanshifu.master.information.domain.api.request.common.BatchGetMasterBaseInfoRqt;
import com.wanshifu.master.information.domain.api.request.common.GetMasterInfoRqt;
import com.wanshifu.master.information.domain.api.response.common.BatchGetMasterBaseInfoResp;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoResp;
import com.wanshifu.merchant.api.MerchantEnterpriseApi;
import com.wanshifu.merchant.domain.req.EnterpriseByIdsRqt;
import com.wanshifu.merchant.domain.resp.EnterpriseBaseInfoResp;
import com.wanshifu.mq.producer.ConversationFlowSeatStatusUpdateProducer;
import com.wanshifu.mq.producer.SendImWebSocketMessageProducer;
import com.wanshifu.mq.producer.TimeoutStrategyProducer;
import com.wanshifu.mq.producer.ToArtificialWelcomeMsgProducer;
import com.wanshifu.repository.*;
import com.wanshifu.repository.channel.ChannelConfigRepository;
import com.wanshifu.repository.channel.ConversationAutoReplyConfigRepository;
import com.wanshifu.repository.channel.ConversationAutoReplyDetailConfigRepository;
import com.wanshifu.repository.channel.ConversationThemeConfigRepository;
import com.wanshifu.repository.channel.SatisfactionEvaluateRecordRepository;
import com.wanshifu.sdk.GuidServiceApi;
import com.wanshifu.service.iop.IopAccountService;
import com.wanshifu.utils.MathUtil;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import com.wanshifu.user.api.UserInfoApi;
import com.wanshifu.user.domain.po.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.core.env.Environment;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.persistence.Table;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractService {

    @Value("${wanshifu.minimumGuarantee.groupId}")
    public Integer minimumGuaranteeGroupId;

    @Resource
    private GuidServiceApi guidServiceApi;

    @Resource
    private OperateLogRepository operateLogRepository;

    @Resource
    protected IopAccountService iopAccountService;

    @Resource
    protected RedisHelper redisHelper;

    @Resource
    public UserRegisterInfoRepository userRegisterInfoRepository;

    @Resource
    public TencentManager tencentManager;

    @Resource
    public VirtualUserInfoRepository virtualUserInfoRepository;

    @Resource
    private CommonQueryServiceApi commonQueryServiceApi;

    @Resource
    public UserProblemClassRepository userProblemClassRepository;

    @Resource
    public ChannelConfigRepository channelConfigRepository;

    @Resource
    public FlowDefineRepository flowDefineRepository;

    @Resource
    public FlowContextRepository flowContextRepository;

    @Resource
    public FlowInstanceRepository flowInstanceRepository;

    @Resource
    public FlowNodeRepository flowNodeRepository;

    @Resource
    public FlowTransitionRepository flowTransitionRepository;

    @Resource
    public IncomingSafetyConfigRepository incomingSafetyConfigRepository;

    @Resource
    public IncomingPropertyConfigRepository incomingPropertyConfigRepository;

    @Resource
    public RuleMetricConfigRepository ruleMetricConfigRepository;

    @Resource
    public GroupRuleRepository groupRuleRepository;

    @Resource
    public SeatInfoRepository seatInfoRepository;

    @Resource
    public SeatStatusRepository seatStatusRepository;

    @Resource
    public SeatStatusFunctionMappingRepository seatStatusFunctionMappingRepository;

    @Resource
    public SeatStatusSwitchLogRepository seatStatusSwitchLogRepository;

    @Resource
    public ImConversationRepository imConversationRepository;

    @Resource
    public ImConversationItemRepository imConversationItemRepository;

    @Resource
    public ConversationThemeConfigRepository conversationThemeConfigRepository;

    @Resource
    public ConversationAutoReplyDetailConfigRepository conversationAutoReplyDetailConfigRepository;

    @Resource
    public SeatGroupMappingRepository seatGroupMappingRepository;

    @Resource
    public CommonBaseManager commonBaseManager;

    @Resource
    public VisitorMarkRepository visitorMarkRepository;

    @Resource
    public UserVirtualRelationRepository userVirtualRelationRepository;

    @Resource
    public SeatVirtualRelationRepository seatVirtualRelationRepository;

    @Resource
    public ImConversationActiveLogRepository imConversationActiveLogRepository;

    @Resource
    public ImConversationLogRepository imConversationLogRepository;

    @Resource
    public ImGroupRepository imGroupRepository;

    @Resource
    public ImGroupUserRelationRepository imGroupUserRelationRepository;

    /**************************************** channel service ****************************************/
    @Resource
    public SatisfactionEvaluateResultRecordRepository satisfactionEvaluateResultRecordRepository;
    @Resource
    public SatisfactionEvaluateRecordRepository satisfactionEvaluateRecordRepository;



    @Resource
    public UserInfoApi userInfoApi;

    @Resource
    public MerchantEnterpriseApi merchantEnterpriseApi;

    @Resource
    public ConversationAutoReplyConfigRepository conversationAutoReplyConfigRepository;

    @Resource
    public ImConversationMessageHistoryImportLogRepository imConversationMessageHistoryImportLogRepository;

    @Resource
    private ToArtificialWelcomeMsgProducer toArtificialWelcomeMsgProducer;

    @Resource
    public ConversationAutoReplyRecordRepository conversationAutoReplyRecordRepository;

    @Resource
    public GroupInfoRepository groupInfoRepository;

    @Resource
    public VisitorInfoTemplateRepository visitorInfoTemplateRepository;

    @Resource
    public FieldAttributeRepository fieldAttributeRepository;

    @Resource
    public ConversationVisitorDetailRepository conversationVisitorDetailRepository;

    @Resource
    public BusinessFromTemplateRepository businessFromTemplateRepository;

    @Resource
    public ProblemRepository problemRepository;

    @Resource
    public TimeoutStrategyProducer timeoutStrategyProducer;

    @Resource
    public PlatformTransactionManager platformTransactionManager;

    @Resource
    public ConversationFlowSeatStatusUpdateProducer conversationFlowSeatStatusUpdateProducer;

    @Resource
    public SendImWebSocketMessageProducer sendImWebSocketMessageProducer;

    @Resource
    public XxlJobTemplate xxlJobTemplate;

    @Value("${wanshifu.conversation.timeout.xxl.cron}")
    public Integer timeoutXxlCron;

    @Resource
    private Environment environment;

    /**
     * 获取前缀
     * @return
     */
    public String getPreFix() {
        String preFix = "";
        if ("dev".equals(environment.getActiveProfiles()[0]) || "test".equals(environment.getActiveProfiles()[0])) {
            preFix = environment.getActiveProfiles()[0] + "-";
        }
        return preFix;
    }

    /**
     * 插入操作日志
     *
     * @param fromTableId
     * @param tableClass
     * @param fromBusiness
     * @param operateContent
     * @param operatorId
     * @return
     */
    public Integer insertOperateLog(Long fromTableId, Class<?> tableClass, String fromBusiness, String operateContent, Long operatorId) {
        OperateLog operateLog = new OperateLog();
        operateLog.setFromTableId(fromTableId);
        operateLog.setFromTableName(this.getTableName(tableClass));
        operateLog.setFromBusiness(fromBusiness);
        operateLog.setOperateContent(operateContent);
        operateLog.setOperatorId(operatorId);
        return operateLogRepository.insertSelective(operateLog);
    }

    /**
     * 获取表名
     *
     * @param clazz
     * @return
     */
    public String getTableName(Class<?> clazz) {
        // 检查类是否被 @Table 注解修饰
        if (clazz.isAnnotationPresent(Table.class)) {
            Table tableAnnotation = clazz.getAnnotation(Table.class);
            return tableAnnotation.name();
        }
        return "";
    }

    /**
     * 验证名称重复
     *
     * @param req
     * @return
     */
    public Boolean baseVerifyRepeatName(VerifyRepeatNameReq req) {
        ResultResp<CommonRunStrategyResp> resp = DataManipulationFactory.setFactoryType(req.getVerifyType()).verifyRepeatName(req);
        return resp.isSuccess();
    }

    /**
     * 获取guid
     */
    public Long getGuid() {
        return guidServiceApi.getGuid();
    }

    /**
     * 获取到今天24点剩余秒数
     *
     * @return
     */
    public Long getSecondsToMidnight() {
        LocalDateTime now = LocalDateTime.now();  // 当前日期时间
        LocalDateTime nextMidnight = LocalDate.now().plusDays(1)  // 跳到明天
                .atStartOfDay();  // 获取明天0点（即今天的24点）
        return ChronoUnit.SECONDS.between(now, nextMidnight);  // 计算秒数差
    }

    /**
     * 校验账号信息
     *
     * @param accountId
     * @return
     */
    public void verifyAccountStatus(Long accountId) {
        AccountUserMapping accountStatus = iopAccountService.selectByAccountIdAndProductType(accountId);
        if (Objects.isNull(accountStatus)) {
            log.error("创建坐席校验账号不存在 accountId:{}", accountId);
            throw new BusinessException("账号不存在");
        }

        if (AccountStatus.DISABLED.code.equals(accountStatus.getAccountStatus())) {
            log.info("创建坐席校验账号已禁用 accountId:{}", accountId);
            throw new BusinessException("账号已禁用");
        }
    }

    /**
     * 获取操作人账号列表
     *
     * @param accountIdList 操作人账号ID列表
     * @return 操作人账号列表
     */
    public List<AccountInfoListResp> batchGetInfoListByAccountIds(List<Long> accountIdList) {
        if (CollectionUtils.isEmpty(accountIdList)) {
            return new ArrayList<>();
        }
        return iopAccountService.batchGetInfoListByAccountIds(accountIdList);
    }


    /**
     * 获取操作人账号map
     *
     * @param accountIdList 操作人账号ID列表
     * @return 操作人账号列表
     */
    public Map<Long, AccountInfoListResp> batchGetInfoMapByAccountIds(List<Long> accountIdList) {
        Map<Long, AccountInfoListResp> accountInfoListRespMap = new HashMap<>();

        List<AccountInfoListResp> list = this.batchGetInfoListByAccountIds(accountIdList);
        if (CollectionUtils.isEmpty(list)) {
            return accountInfoListRespMap;
        }
        return list.stream().collect(Collectors.toMap(AccountInfoListResp::getAccountId, resp -> resp, (existing, replacement) -> existing // 可选：根据业务定义合并策略
        ));
    }

    /**
     * 指定用户id+类型查询注册信息，使用无设备id的情况
     */
    public UserRegisterInfo getUserRegisterInfoByIdAndType(Long userId, String userClass) {
        //查询是否已经注册，如果已经注册就返回，如果没有注册需要注册
        UserRegisterInfo byUserId = userRegisterInfoRepository.findByUserId(userId, userClass, CommonConstant.ZERO);

        if (ObjectUtils.isEmpty(byUserId)) {
            //注册
            RegisterUserReqBo registerUserReqBo = new RegisterUserReqBo();
            registerUserReqBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            registerUserReqBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
            RegisterUserRespBo registerUserRespBo = tencentManager.registerUser(registerUserReqBo);
            if (registerUserRespBo == null) {
                throw new BusException("注册用户异常");
            }
            UserRegisterInfo userRegisterInfo = new UserRegisterInfo();
            userRegisterInfo.setImId(CommonConstant.DEFAULT_TENANT_ID);
            userRegisterInfo.setUserId(userId);
            userRegisterInfo.setUserClass(userClass);
            userRegisterInfo.setOuterUserId(registerUserRespBo.getOuterUserId());
            userRegisterInfo.setRegisterStatus(RegisterStatusEnum.SUCCESS.type);
            userRegisterInfo.setRegisterTime(new Date());
            userRegisterInfo.setUserSign(registerUserRespBo.getUserSign());
            userRegisterInfo.setUserSignExpireTime(DateUtils.addYears(new Date(), TencentConstant.SIGN_EXPIRE_TIME));
            userRegisterInfo.setStatus(0);
            userRegisterInfo.setIsDelete(0);
            userRegisterInfo.setUpdateTime(new Date());
            userRegisterInfo.setCreateTime(new Date());
            userRegisterInfo.setOnlineState(OnlineStateEnum.ONLINE.type);
            userRegisterInfo.setOnlineStateChangeTime(new Date());
            userRegisterInfoRepository.insertSelective(userRegisterInfo);
            return userRegisterInfo;
        }

        if (byUserId.getUserSignExpireTime().before(new Date())) {
            //更新签名
            //注册
            RegisterUserReqBo registerUserReqBo = new RegisterUserReqBo();
            registerUserReqBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            registerUserReqBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
            registerUserReqBo.setOuterUserId(byUserId.getOuterUserId());
            RegisterUserRespBo registerUserRespBo = tencentManager.registerUser(registerUserReqBo);
            if (registerUserRespBo == null) {
                throw new BusException("注册用户异常");
            }
            UserRegisterInfo userRegisterInfo = new UserRegisterInfo();
            userRegisterInfo.setRegisterInfoId(byUserId.getRegisterInfoId());
            userRegisterInfo.setRegisterStatus(RegisterStatusEnum.SUCCESS.type);
            userRegisterInfo.setUserSign(registerUserRespBo.getUserSign());
            userRegisterInfo.setUserSignExpireTime(DateUtils.addYears(new Date(), TencentConstant.SIGN_EXPIRE_TIME));
            userRegisterInfo.setStatus(0);
            userRegisterInfo.setIsDelete(0);
            userRegisterInfo.setUpdateTime(new Date());
            userRegisterInfoRepository.updateByPrimaryKeySelective(userRegisterInfo);
            return userRegisterInfo;
        }

        return byUserId;
    }

    /**
     * 查询用户姓名、师傅姓名
     */
    public UserInfoBo getUserInfoBo(Long userId, String userClass) {

        OuterUserTypeEnum enumByType = OuterUserTypeEnum.getEnumByType(userClass);

        UserInfoBo userInfoBo = new UserInfoBo();
        userInfoBo.setUserId(userId);

        if (enumByType == null) {
            return userInfoBo;
        }

        switch (enumByType) {
            case MASTER:
                //师傅
                GetMasterInfoRqt getMasterInfoRqt = new GetMasterInfoRqt();
                getMasterInfoRqt.setMasterId(userId);
                GetMasterInfoResp masterInfo = commonQueryServiceApi.getMasterInfo(getMasterInfoRqt);
                if (masterInfo == null) {
                    log.error("查询师傅基础信息为空，userId={}", userId);
                    return userInfoBo;
                }
                userInfoBo.setUserName(masterInfo.getTeamName());
                break;
            case MERCHANT:
                //用户
                break;
        }
        return userInfoBo;

    }


    public <T> List<List<T>> splitList(List<T> list, int size) {
        List<List<T>> result = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            int end = Math.min(i + size, list.size());
            result.add(list.subList(i, end));
        }
        return result;
    }

    /**
     * 查询会话关联的路由导航的节点
     */
    public List<FlowNode> getFlowNodeByGroupId(Integer groupId) {
        //判断会话关联的路由是否配置了机器人节点
        RuleMetricConfig ruleMetricConfigByType = ruleMetricConfigRepository.selectRuleMetricByObjectAndFromTypeAndEn(RuleMetricObjectEnum.GROUP.type, RuleMetricFromTypeEnum.KF.type, RuleMetricConfigEnum.CONVERSATION_ENTRY.type);


        if (ruleMetricConfigByType == null) {
            return new ArrayList<>();
        }

        GroupRule groupRule = groupRuleRepository.selectByGroupIdAndRuleMetricId(groupId, ruleMetricConfigByType.getRuleMetricId());

        if (groupRule == null) {
            return new ArrayList<>();
        }

        if (StringUtils.isEmpty(groupRule.getRuleConfigValue())) {
            return new ArrayList<>();
        }

        GroupRuleConfigValueBo groupRuleConfigValueBo = JSON.parseObject(groupRule.getRuleConfigValue(), GroupRuleConfigValueBo.class);
        if (!groupRuleConfigValueBo.getMultipleValue().contains(groupId + "")) {
            return new ArrayList<>();
        }

        ChannelConfig channelConfig = channelConfigRepository.selectByPrimaryKey(Long.valueOf(groupId + ""));
        if (channelConfig == null) {
            return new ArrayList<>();
        }

        Long flowDefineId = channelConfig.getFlowDefineId();
        if (flowDefineId == null) {
            return new ArrayList<>();
        }

        FlowDefine flowDefine = flowDefineRepository.getFlowDefineById(flowDefineId, EnableStatusEnum.ENABLE.type);
        if (flowDefine == null) {
            return new ArrayList<>();
        }

        List<FlowNode> flowNodeListByFlowId = flowNodeRepository.getFlowNodeListByFlowId(flowDefine.getFlowDefineId());

        return flowNodeListByFlowId;

    }

    /**
     * 发送消息给个人， 消息类型，消息内容，如果是自定义需要传json内容
     */
    public Integer sendMsgTypeToPersonalText(Long conversationId, String msgType, String content) {

        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if (imConversation == null) {
            throw new BusException("会话不存在,会话id=" + conversationId);
        }
        if (!ConversationStatusEnum.PROCESSING.type.equals(imConversation.getConversationStatus())) {
            throw new BusException("会话不是进行中" + conversationId);
        }
        List<ImConversationBeanBo.MsgContentItem> msgContentItemList = new ArrayList<>();
        ImConversationBeanBo.MsgContentItem msgContentItem = new ImConversationBeanBo.MsgContentItem();
        msgContentItem.setMsgType(msgType);
        Map<String, String> textMsgContentFormatBoMap = new HashMap<>();
        textMsgContentFormatBoMap.put("Text", content);
        msgContentItem.setMsgContent(textMsgContentFormatBoMap);

        msgContentItemList.add(msgContentItem);
        //向用户转发消息，插入会话明细
        ImConversationBeanBo imConversationBeanBo = null;
        String msgId = System.currentTimeMillis()+"-"+MathUtil.generateCustomerLength(32);
        if(MsgTypeEnum.TEXT.type.equals(msgType)){
            List<TextMsgContentFormatBo> textMsgContentFormatBos = new ArrayList<>();
            TextMsgContentFormatBo textMsgContentFormatBo = new TextMsgContentFormatBo();
            TextMsgContentFormatBo.TextMsgContentFormatContent textMsgContentFormatContent = new TextMsgContentFormatBo.TextMsgContentFormatContent();
            textMsgContentFormatContent.setText(content);
            textMsgContentFormatBo.setMsgContent(textMsgContentFormatContent);

            textMsgContentFormatBo.setMsgType(msgType);
            textMsgContentFormatBos.add(textMsgContentFormatBo);

            imConversationBeanBo = ImConversationBeanBo.builder().MsgKey(null)
                    .MsgId(msgId)
                    .msgContentItemList(msgContentItemList)
                    .MsgSeq(MathUtil.generateCustomerLength(32))
                    .msgContentJson(JSON.toJSONString(textMsgContentFormatBos))
                    .fromAccount(imConversation.getAgentOuterUserId())
                    .fromAccountType(imConversation.getFromOuterUserType())
                    .toAccount(imConversation.getFromOuterUserId())
                    .toAccountType(imConversation.getFromOuterUserType()).build();
            if(imConversationBeanBo==null){
                return 0;
            }
            ImConversationItem imConversationItem = this.returnImConversationItemBean(imConversationBeanBo, conversationId);

            SendMsgToPersonalResultBo sendMsgToPersonalResultBo = this.sendMsgToPersonal(imConversationBeanBo);
            if (sendMsgToPersonalResultBo==null) {
                throw new BusException("向个人发送消息失败");
            }
            sendMsgToPersonalResultBo.setMsgId(sendMsgToPersonalResultBo.getMsgId());
            sendMsgToPersonalResultBo.setMsgKey(sendMsgToPersonalResultBo.getMsgKey());
            imConversationItemRepository.insertSelective(imConversationItem);
        }

        return 1;
    }




    /**
     * 发送消息给个人， 消息类型，消息内容，如果是自定义需要传json内容
     */
    public Integer sendMsgTypeToPersonal(Long conversationId, String msgType, String content) {

        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(conversationId);
        if (imConversation == null) {
            throw new BusException("会话不存在,会话id=" + conversationId);
        }
        if (!ConversationStatusEnum.PROCESSING.type.equals(imConversation.getConversationStatus())) {
            throw new BusException("会话不是进行中" + conversationId);
        }
        List<ImConversationBeanBo.MsgContentItem> msgContentItemList = new ArrayList<>();
        ImConversationBeanBo.MsgContentItem msgContentItem = new ImConversationBeanBo.MsgContentItem();
        msgContentItem.setMsgType(msgType);

        msgContentItem.setMsgContent(JSONObject.parseObject(content));

        msgContentItemList.add(msgContentItem);
        //向用户转发消息，插入会话明细
        ImConversationBeanBo imConversationBeanBo = null;
        String msgId = System.currentTimeMillis()+"-"+MathUtil.generateCustomerLength(32);
        if(MsgTypeEnum.TEXT.type.equals(msgType)){
            List<TextMsgContentFormatBo> textMsgContentFormatBos = new ArrayList<>();
            TextMsgContentFormatBo textMsgContentFormatBo = new TextMsgContentFormatBo();
            TextMsgContentFormatBo.TextMsgContentFormatContent textMsgContentFormatContent = new TextMsgContentFormatBo.TextMsgContentFormatContent();
            textMsgContentFormatContent.setText(content);
            textMsgContentFormatBo.setMsgContent(textMsgContentFormatContent);

            textMsgContentFormatBo.setMsgType(msgType);
            textMsgContentFormatBos.add(textMsgContentFormatBo);

            imConversationBeanBo = ImConversationBeanBo.builder().MsgKey(null)
                    .MsgId(msgId)
                    .msgContentItemList(msgContentItemList)
                    .MsgSeq(MathUtil.generateCustomerLength(32))
                    .msgContentJson(JSON.toJSONString(textMsgContentFormatBos))
                    .fromAccount(imConversation.getAgentOuterUserId())
                    .fromAccountType(imConversation.getFromOuterUserType())
                    .toAccount(imConversation.getFromOuterUserId())
                    .toAccountType(imConversation.getFromOuterUserType()).build();
            if(imConversationBeanBo==null){
                return 0;
            }
            ImConversationItem imConversationItem = this.returnImConversationItemBean(imConversationBeanBo, conversationId);

            SendMsgToPersonalResultBo sendMsgToPersonalResultBo = this.sendMsgToPersonal(imConversationBeanBo);
            if (sendMsgToPersonalResultBo==null) {
                throw new BusException("向个人发送消息失败");
            }
            sendMsgToPersonalResultBo.setMsgId(sendMsgToPersonalResultBo.getMsgId());
            sendMsgToPersonalResultBo.setMsgKey(sendMsgToPersonalResultBo.getMsgKey());
            imConversationItemRepository.insertSelective(imConversationItem);
        }

        if(MsgTypeEnum.CUSTOMER.type.equals(msgType)){
            List<FormMsgContentFormatObjBo> textMsgContentFormatBos = new ArrayList<>();

            FormMsgContentFormatObjBo.MsgBodyFromContent msgBodyFromContent = JSONObject.parseObject(content, FormMsgContentFormatObjBo.MsgBodyFromContent.class);
            FormMsgContentFormatObjBo formMsgContentFormatObjBo = new FormMsgContentFormatObjBo();
            formMsgContentFormatObjBo.setMsgContent(msgBodyFromContent);
            formMsgContentFormatObjBo.setMsgType(msgType);

            textMsgContentFormatBos.add(formMsgContentFormatObjBo);
            imConversationBeanBo = ImConversationBeanBo.builder().MsgKey(null)
                    .MsgId(msgId)
                    .msgContentItemList(msgContentItemList).MsgSeq(MathUtil.generateCustomerLength(32))
                    .msgContentJson(JSON.toJSONString(textMsgContentFormatBos)).fromAccount(imConversation.getAgentOuterUserId()).fromAccountType(OuterUserClassTypeEnum.ROBOT.type).toAccount(imConversation.getFromOuterUserId()).toAccountType(imConversation.getFromOuterUserType()).build();
            if(imConversationBeanBo==null){
                return 0;
            }
            ImConversationItem imConversationItem = this.returnImConversationItemBean(imConversationBeanBo, conversationId);

            for (ImConversationBeanBo.MsgContentItem contentItem : msgContentItemList) {
                String msgContentStr = JSONObject.toJSONString(contentItem.getMsgContent());
                FormCustomerMsgContentBo formCustomerMsgContentBo = JSON.parseObject(msgContentStr, FormCustomerMsgContentBo.class);
                FormCustomerMsgContentBo.FromData fromData = JSON.parseObject(formCustomerMsgContentBo.getData(), FormCustomerMsgContentBo.FromData.class);
                fromData.setMsgId(msgId);
                formCustomerMsgContentBo.setData(JSON.toJSONString(fromData));
                contentItem.setMsgContent(JSON.toJSONString(formCustomerMsgContentBo));
            }
            imConversationBeanBo.setMsgContentItemList(msgContentItemList);
            SendMsgToPersonalResultBo sendMsgToPersonalResultBo = this.sendCustomerMsgToPersonal(imConversationBeanBo);
            if (sendMsgToPersonalResultBo==null) {
                throw new BusException("向个人发送消息失败");
            }
            String msgKey = sendMsgToPersonalResultBo.getMsgKey();
            imConversationItem.setMsgKey(msgKey);
            imConversationItemRepository.insertSelective(imConversationItem);
        }
        return 1;
    }


    /**
     * 写入会话记录
     */
    public ImConversationItem returnImConversationItemBean(ImConversationBeanBo req, Long conversationId) {
        ImConversationItem imConversationItem = new ImConversationItem();
        imConversationItem.setConversationId(conversationId);
        imConversationItem.setFromOuterUserId(req.getFromAccount());
        imConversationItem.setFromOuterUserType(req.getFromAccountType());
        imConversationItem.setToOuterUserId(req.getToAccount());
        imConversationItem.setToOuterUserType(req.getToAccountType());
        imConversationItem.setResponseOuterUserId(req.getResponseAccount());
        imConversationItem.setResponseOuterUserType(req.getResponseAccountType());
        imConversationItem.setMsgSendTime(new Date());
        imConversationItem.setHasRead(0);
        imConversationItem.setMsgReadTime(new Date());
        imConversationItem.setMsgType(req.getMsgContentItemList().get(0).getMsgType());
        imConversationItem.setMsgContent(req.getMsgContentJson());
        imConversationItem.setMsgSeq(req.getMsgSeq());
        imConversationItem.setMsgKey(req.getMsgKey());
        imConversationItem.setMsgId(req.getMsgId());
        imConversationItem.setIsDelete(0);
        imConversationItem.setUpdateTime(new Date());
        imConversationItem.setCreateTime(new Date());
        return imConversationItem;
    }


    /**
     * 发送消息给个人
     */
    public SendMsgToPersonalResultBo sendMsgToPersonal(ImConversationBeanBo imConversationBeanBo) {
        SendMsgPersonalBo sendMsgPersonalBo = new SendMsgPersonalBo();
        sendMsgPersonalBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        sendMsgPersonalBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        sendMsgPersonalBo.setFromAccountId(imConversationBeanBo.getFromAccount());
        sendMsgPersonalBo.setToAccountId(imConversationBeanBo.getToAccount());

        List<SendMsgPersonalBo.MsgContentItem> msgContentItemList = new ArrayList<>();
        for (ImConversationBeanBo.MsgContentItem msgContentItem : imConversationBeanBo.getMsgContentItemList()) {
            SendMsgPersonalBo.MsgContentItem msgContentItem1 = new SendMsgPersonalBo.MsgContentItem();
            msgContentItem1.setMsgType(msgContentItem.getMsgType());
            msgContentItem1.setContent(msgContentItem.getMsgContent());
            msgContentItemList.add(msgContentItem1);
        }

        sendMsgPersonalBo.setMsgContentItemList(msgContentItemList);
        sendMsgPersonalBo.setIsNeedReadReceipt(CommonConstant.ONE);
        return tencentManager.sendMsgToPersonal(sendMsgPersonalBo);

    }

    /**
     * 发送自定义消息给个人
     */
    public SendMsgToPersonalResultBo sendCustomerMsgToPersonal(ImConversationBeanBo imConversationBeanBo) {
        SendCustomerMsgPersonalBo sendMsgPersonalBo = new SendCustomerMsgPersonalBo();
        sendMsgPersonalBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        sendMsgPersonalBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        sendMsgPersonalBo.setFromAccountId(imConversationBeanBo.getFromAccount());
        sendMsgPersonalBo.setToAccountId(imConversationBeanBo.getToAccount());

        List<SendCustomerMsgPersonalBo.MsgContentItem> msgContentItemList = new ArrayList<>();
        for (ImConversationBeanBo.MsgContentItem msgContentItem : imConversationBeanBo.getMsgContentItemList()) {
            SendCustomerMsgPersonalBo.MsgContentItem msgContentItem1 = new SendCustomerMsgPersonalBo.MsgContentItem();
            msgContentItem1.setMsgType(msgContentItem.getMsgType());
            msgContentItem1.setContent(msgContentItem.getMsgContent());
            msgContentItemList.add(msgContentItem1);
        }
        sendMsgPersonalBo.setMsgContentItemList(msgContentItemList);

        //FormCustomerMsgContentBo formCustomerMsgContentBo = JSONObject.parseObject(imConversationBeanBo.getMsgContent(), FormCustomerMsgContentBo.class);
       // sendMsgPersonalBo.setContent(formCustomerMsgContentBo.getData());
        sendMsgPersonalBo.setIsNeedReadReceipt(CommonConstant.ONE);
        return tencentManager.sendCustomerMsgToPersonal(sendMsgPersonalBo);

    }


    /**
     * 获取节点类型
     * 需要判断路由配置了哪些节点，返回机器人->seat->system
     * */


    /**
     * 获取账号信息
     *
     * @param accountIdList
     * @param accountType
     * @return
     */
    public List<GetAccountInfoRespBo> batchGetAccountInfo(List<Long> accountIdList, String accountType) {
        List<GetAccountInfoRespBo> getAccountInfoRespBoList = new ArrayList<>();

        if (CollectionUtils.isEmpty(accountIdList)) {
            return getAccountInfoRespBoList;
        }

        // 用户信息
        if (accountType.equals(UserClassEnum.USER.type)) {
            List<UserInfo> userInfos = this.batchGetUserInfo(accountIdList);

            if (CollectionUtils.isNotEmpty(userInfos)) {
                userInfos.forEach(userInfo -> {
                    GetAccountInfoRespBo getAccountInfoRespBo = new GetAccountInfoRespBo();
                    getAccountInfoRespBo.setAccountType(accountType);
                    getAccountInfoRespBo.setAccountId(userInfo.getUserId());
                    getAccountInfoRespBo.setAccountPhone(userInfo.getPhone());
                    getAccountInfoRespBo.setAccountName(userInfo.getAccount());

                    // 客户端家庭投诉直接展示account，不需要替换为手机号
//                    if (!StringUtils.isEmpty(userInfo.getUserType())) {
//                        // 用户类型为家庭用户，账号名为手机号
//                        getAccountInfoRespBo.setAccountName(
//                                userInfo.getUserType().equals(CommonConstant.USER_TYPE_HOME) ? userInfo.getPhone() : userInfo.getAccount());
//                    }

                    getAccountInfoRespBoList.add(getAccountInfoRespBo);
                });

            }
        }

        // 师傅信息
        if (accountType.equals(AccountTypeEnum.MASTER.type)) {
            List<BatchGetMasterBaseInfoResp> batchGetMasterBaseInfoResps = this.batchGetMasterInfo(accountIdList);

            if (CollectionUtils.isNotEmpty(batchGetMasterBaseInfoResps)) {
                batchGetMasterBaseInfoResps.forEach(masterInfo -> {
                    GetAccountInfoRespBo getAccountInfoRespBo = new GetAccountInfoRespBo();
                    getAccountInfoRespBo.setAccountType(accountType);
                    getAccountInfoRespBo.setAccountId(masterInfo.getMasterId());
                    getAccountInfoRespBo.setAccountPhone(masterInfo.getPhone());
                    getAccountInfoRespBo.setAccountName(masterInfo.getContact());
                    getAccountInfoRespBoList.add(getAccountInfoRespBo);
                });
            }
        }

        // 总包信息
        if (accountType.equals(AccountTypeEnum.ENTERPRISE.type)) {
            List<EnterpriseBaseInfoResp> enterpriseInfoResps = this.batchGetEnterpriseInfo(accountIdList);

            if (CollectionUtils.isNotEmpty(enterpriseInfoResps)) {
                enterpriseInfoResps.forEach(enterpriseInfo -> {
                    GetAccountInfoRespBo getAccountInfoRespBo = new GetAccountInfoRespBo();
                    getAccountInfoRespBo.setAccountType(accountType);
                    getAccountInfoRespBo.setAccountId(enterpriseInfo.getEnterpriseId());
                    // 总包这个接口拿不到手机号
                    getAccountInfoRespBo.setAccountName(enterpriseInfo.getEnterpriseName());
                    getAccountInfoRespBoList.add(getAccountInfoRespBo);
                });
            }
        }

        return getAccountInfoRespBoList;
    }

    /**
     * 获取师傅信息
     *
     * @param masterIdList
     * @return
     */
    public List<BatchGetMasterBaseInfoResp> batchGetMasterInfo(List<Long> masterIdList) {
        if (CollectionUtils.isEmpty(masterIdList)) {
            return new ArrayList<>();
        }

        try {
            BatchGetMasterBaseInfoRqt getMasterInfoRqt = new BatchGetMasterBaseInfoRqt();
            getMasterInfoRqt.setMasterIds(masterIdList);
            getMasterInfoRqt.setPageSize(20);
            return commonQueryServiceApi.batchGetMasterBaseInfo(getMasterInfoRqt);
        } catch (ApiAccessException e) {
            log.info("批量获取师傅信息异常 masterIdList:{} e", masterIdList, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取用户信息
     *
     * @param userIdList
     * @return
     */
    public List<UserInfo> batchGetUserInfo(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }

        try {
            List<UserInfo> userInfoList = userInfoApi.getList(userIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            return userInfoList;
        } catch (ApiAccessException e) {
            log.error("批量获取用户信息异常 userIdList:{} e", userIdList, e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量获取总包信息
     *
     * @param enterpriseIdList
     * @return
     */
    public List<EnterpriseBaseInfoResp> batchGetEnterpriseInfo(List<Long> enterpriseIdList) {
        if (CollectionUtils.isEmpty(enterpriseIdList)) {
            return new ArrayList<>();
        }

        try {
            EnterpriseByIdsRqt seoEnterpriseDetailRqt = new EnterpriseByIdsRqt();
            seoEnterpriseDetailRqt.setEnterpriseIds(enterpriseIdList);
            return merchantEnterpriseApi.batchQueryEnterpriseBaseInfo(seoEnterpriseDetailRqt);
        } catch (ApiAccessException e) {
            log.error("批量获取总包信息失败 enterpriseIdList:{} e", enterpriseIdList, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取会话消息内容
     *
     * @param msgContent
     * @return
     */
    public List<MsgBodyItemBo> getMsgContent(String msgContent) {
        if (StringUtils.isBlank(msgContent)) {
            return new ArrayList<>();
        }
        List<MsgBodyItemBo> msgBodyItemBoList = JSONArray.parseArray(msgContent, MsgBodyItemBo.class);
        return msgBodyItemBoList;
    }

    /**
     * 发送消息mq
     * */
    public void sendArtificialTypeMsg(Long imConversationId, String triggerMode, String seatOuterUserId, String preToOuterUserType){
        ToArtificialTypesBo toArtificialTypesBo = new ToArtificialTypesBo();
        toArtificialTypesBo.setImConversationId(imConversationId);
        toArtificialTypesBo.setTriggerMode(triggerMode);
        toArtificialTypesBo.setSeatOuterUserId(seatOuterUserId);
        toArtificialTypesBo.setPreToOuterUserType(preToOuterUserType);
        toArtificialWelcomeMsgProducer.sendMessage(toArtificialTypesBo);
    }

    /**
     * 如果有渠道配置就拿渠道配置，否则返回全局配置
     * */
    public List<ConversationAutoReplyDetailConfig> getRealAutoReplyDetailConfigList(List<Long> conversationConfigIds,
                                                                                    List<Long> globalAutoReplyConfigIds,
                                                                                    List<ConversationAutoReplyDetailConfig> listByAutoReplyConfigIdList){
        if(CollectionUtils.isEmpty(listByAutoReplyConfigIdList)){
            return new ArrayList<>();
        }

        List<ConversationAutoReplyDetailConfig> resultList = new ArrayList<>();

        //获取所有配置的并集
        List<String> allMsgSubTypeList = listByAutoReplyConfigIdList.stream().map(ConversationAutoReplyDetailConfig::getMsgSubType).distinct().collect(Collectors.toList());

        for (String msgSubType : allMsgSubTypeList) {
            List<ConversationAutoReplyDetailConfig> collector = listByAutoReplyConfigIdList.stream().filter(f->f.getMsgSubType().equals(msgSubType)).collect(Collectors.toList());
            Optional<ConversationAutoReplyDetailConfig> first = collector.stream().filter(f -> conversationConfigIds.contains(f.getConversationAutoReplyConfigId())).findFirst();
            if(first.isPresent()){
                resultList.add(first.get());
                continue;
            }
            Optional<ConversationAutoReplyDetailConfig> firstGlobalOptional = collector.stream().filter(f -> globalAutoReplyConfigIds.contains(f.getConversationAutoReplyConfigId())).findFirst();
            if(firstGlobalOptional.isPresent()){
                resultList.add(firstGlobalOptional.get());
            }
        }
        return resultList;
    }

    /**
     * 根据坐席id或外部用户id获取坐席信息
     * @param seatId
     * @param seatOuterUserId
     * @return
     */
    public SeatInfo getSeatInfoBySeatIdOrOutUserId(Long seatId, String seatOuterUserId) {

        if ( Objects.isNull(seatId) && StringUtils.isBlank(seatOuterUserId) ){
            log.error("坐席id和坐席外部id必传一个，seatId:{}, toOuterUserId:{}", seatId, seatOuterUserId);
            return null;
        }

        SeatInfo seatInfo = null;
        if ( Objects.nonNull(seatId) ){
            seatInfo = seatInfoRepository.selectByPrimaryKey(seatId);
        }else{
            GetSeatRegisterInfoRespBo seatInfoByOutUserId = this.getSeatInfoByOutUserId(seatOuterUserId);
            if ( Objects.nonNull(seatInfoByOutUserId) ){
                seatInfo = seatInfoByOutUserId.getSeatInfo();
            }
        }

        if ( Objects.isNull(seatInfo) ){
            log.error("会话接线坐席信息不存在，seatId:{}, toOuterUserId:{}", seatId, seatOuterUserId);
            return null;
        }

        return seatInfo;
    }

    /**
     * 根据外部用户id获取坐席信息
     * @param outerUserId
     * @return
     */
    public GetSeatRegisterInfoRespBo getSeatInfoByOutUserId(String outerUserId) {
        // 获取会话对应的客服id
        UserRegisterInfo userRegisterInfo = userRegisterInfoRepository.selectByOuterUserIdAndUserClass(outerUserId, UserClassEnum.SEAT.type);
        if ( Objects.isNull(userRegisterInfo) ){
            log.error("会话接线坐席注册信息不存在, outerUserId:{}", outerUserId);
            return null;
        }

        // 查询坐席信息
        SeatInfo seatInfo = seatInfoRepository.selectByPrimaryKey(userRegisterInfo.getUserId());
        if ( Objects.isNull(seatInfo) ){
            log.error("会话接线坐席信息不存在, outerUserId:{}", outerUserId);
            return null;
        }

        return new GetSeatRegisterInfoRespBo(seatInfo, userRegisterInfo);
    }

    /**
     * 坐席状态切换
     * @param seatInfo
     * @param switchStatus
     * @return
     */
    public Integer seatStatusSwitch(SeatInfo seatInfo, String switchStatus) {
        if ( seatInfo.getCurrentSeatStatusEn().equals(switchStatus) ){
            log.error("坐席状态切换失败，当前状态和切换状态一致，seatId:{}, currentSeatStatusEn:{}, switchStatus:{}", seatInfo.getSeatId(), seatInfo.getCurrentSeatStatusEn(), switchStatus);
            return 0;
        }

        // 开启事务
        //手动事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            // 更新坐席表
            SeatInfo update = new SeatInfo();
            update.setSeatId(seatInfo.getSeatId());
            update.setCurrentSeatStatusEn(switchStatus);
            seatInfoRepository.updateByPrimaryKeySelective(update);

            // 更新日志表
            SeatStatusSwitchLog seatStatusSwitchLog = new SeatStatusSwitchLog();
            seatStatusSwitchLog.setSeatStatusEnBefore(seatInfo.getCurrentSeatStatusEn());
            seatStatusSwitchLog.setSeatStatusEnAfter(switchStatus);
            // 不是满负荷的情况下切换成在线需要记录首次在线时间
            if ( !CommonConstant.SEAT_STATUS_FULL_LOAD.equals(seatStatusSwitchLog.getSeatStatusEnBefore()) && CommonConstant.SEAT_STATUS_ONLINE.equals(switchStatus) ){
                seatStatusSwitchLog.setFirstOnlineTime(new Date());
            }else if (
                    CommonConstant.SEAT_STATUS_FULL_LOAD.equals(seatStatusSwitchLog.getSeatStatusEnBefore()) && CommonConstant.SEAT_STATUS_ONLINE.equals(switchStatus)
                    || CommonConstant.SEAT_STATUS_ONLINE.equals(seatStatusSwitchLog.getSeatStatusEnBefore()) && CommonConstant.SEAT_STATUS_FULL_LOAD.equals(switchStatus)
            ){
                // 如果是从在线切换成满负荷或者从满负荷切换成在线，需要取上一条记录的首次在线时间
                SeatStatusSwitchLog lastSeatStatusSwitchLog = seatStatusSwitchLogRepository.selectLastBySeatId(seatInfo.getSeatId());
                if ( Objects.nonNull(lastSeatStatusSwitchLog) && Objects.nonNull(lastSeatStatusSwitchLog.getFirstOnlineTime()) ){
                    seatStatusSwitchLog.setFirstOnlineTime(lastSeatStatusSwitchLog.getFirstOnlineTime());
                }else{
                    log.error("获取上次坐席状态切换记录的首次在线时间为null，seatId:{}", seatInfo.getSeatId());
                    seatStatusSwitchLog.setFirstOnlineTime(new Date());
                }
            }
            seatStatusSwitchLog.setSeatId(seatInfo.getSeatId());
            seatStatusSwitchLogRepository.insertSelective(seatStatusSwitchLog);

            platformTransactionManager.commit(status);
        }catch (Exception e){
            platformTransactionManager.rollback(status);
            throw new BusinessException(e.getMessage());
        }

        // 更新redis
        String getTokenKey = String.format(CommonRedisConstant.IOP_ACCOUNT_MAPPING_TOKEN_KEY, seatInfo.getAccountId());
        String imToken = redisHelper.get(getTokenKey);
        if( StringUtils.isNotEmpty(imToken) ){
            String cacheKey = String.format(CommonRedisConstant.IOP_IM_TOKEN_KEY, imToken);
            String seatInfoJson = redisHelper.get(cacheKey);
            if ( StringUtils.isNotEmpty(seatInfoJson) ){
                SetLoginAuthResp setLoginAuthResp = JSONObject.parseObject(seatInfoJson, SetLoginAuthResp.class);
                if ( Objects.nonNull(setLoginAuthResp.getSeatInfo()) ){
                    setLoginAuthResp.getSeatInfo().setCurrentSeatStatusEn(switchStatus);
                    redisHelper.set(cacheKey, JSONObject.toJSONString(setLoginAuthResp), this.getSecondsToMidnight().intValue());
                }
            }
        }

        // 非离线的情况下，更新坐席在线信息
        if (!CommonConstant.DEFAULT_OFFLINE_SEAT_STATUS.equals(switchStatus)){
            SendImWebSocketMessageBo sendImWebSocketMessageBo = new SendImWebSocketMessageBo();
            sendImWebSocketMessageBo.setSeatId(seatInfo.getSeatId());
            sendImWebSocketMessageBo.setBusinessType(SocketBaseManagerImpl.UPDATE_SEAT_INFO);
            sendImWebSocketMessageProducer.sendImWebSocketMessage(sendImWebSocketMessageBo);
        }

        return 1;
    }

    /**
     * 获取校验坐席信息
     * @param req
     * @return
     */
    public GetSeatRegisterInfoRespBo getSeatRegisterInfo(BaseOnlineSeatReq req){
        SeatInfo verifySeatInfo = this.getVerifySeatInfo(req);

        // 获取坐席的外部账号id
        UserRegisterInfo seatRegisterInfo = userRegisterInfoRepository.selectOuterUserIdByUser(req.getSeatId(), UserClassEnum.SEAT.type);
        if ( Objects.isNull(seatRegisterInfo) ){
            log.error("获取坐席的外部账号id失败, seatId: {}", req.getSeatId());
            throw new BusinessException("坐席关联的外部账号不存在");
        }

        return new GetSeatRegisterInfoRespBo(verifySeatInfo, seatRegisterInfo);
    }

    /**
     * 获取校验坐席信息
     * @param req
     * @return
     */
    public SeatInfo getVerifySeatInfo(BaseOnlineSeatReq req){
        SeatInfo seatInfo = seatInfoRepository.selectByPrimaryKey(req.getSeatId());
        if ( Objects.isNull(seatInfo) ){
            log.error("获取坐席信息失败, seatId: {}", req.getSeatId());
            throw new BusinessException("获取坐席信息失败");
        }

        if ( CommonConstant.DEFAULT_OFFLINE_SEAT_STATUS.equals(seatInfo.getCurrentSeatStatusEn()) ){
            log.error("不合法操作, 坐席已离线 req:{}", JSONObject.toJSONString(req));
            throw new BusinessException("不合法操作, 坐席已离线");
        }

        if ( !req.getTenantId().equals(seatInfo.getTenantId()) ){
            log.error("不合法操作, 不支持跨租户切换坐席状态 req:{}", JSONObject.toJSONString(req));
            throw new BusinessException("不合法操作，请勿跨租户切换坐席状态");
        }

        return seatInfo;
    }

    /**
     * 向群聊中发送消息
     * */
    public Integer sendMsgToGroup(Long conversationId, String fromAccount,  String content){

        ImGroup imGroup = imGroupRepository.selectImGroupByConversationId(conversationId);
        if(imGroup==null){
            log.error("群组不存在，conversationId={}", conversationId);
            return 0;
        }
        SendMsgGroupBo sendMsgGroupBo = new SendMsgGroupBo();
        sendMsgGroupBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        sendMsgGroupBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        sendMsgGroupBo.setGroupId(imGroup.getOuterGroupId());
        sendMsgGroupBo.setFromAccountId(fromAccount);
        List<SendMsgGroupBo.MsgListItem> msgList = new ArrayList<>();
        SendMsgGroupBo.MsgListItem msgListItem = new SendMsgGroupBo.MsgListItem();
        msgListItem.setMsgType(MsgTypeEnum.TEXT.type);
        CallbackSingleSendMsgAfterReq.MsgContent msgContent = new CallbackSingleSendMsgAfterReq.MsgContent();
        msgContent.setText(content);
        msgListItem.setContent(msgContent);
        msgList.add(msgListItem);

        sendMsgGroupBo.setMsgList(msgList);
        Integer i = tencentManager.sendMsgToGroup(sendMsgGroupBo);
        if(i==null){
            log.error("群组发送消息失败，conversationId={}", conversationId);
            return 0;
        }
        return 1;

    }

    /**
     * 获取会话访客注册信息
     */
    public GetConversationVisitorRegisterInfoRespBo getVerityConversationVisitorRegisterInfo(BaseOnlineConversationReq req){
        // 获取会话信息
        ImConversation imConversation = imConversationRepository.selectByPrimaryKey(req.getConversationId());
        if ( Objects.isNull(imConversation) ){
            log.error("会话不存在, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话不存在");
        }

        // 获取访客信息
        UserRegisterInfo userRegisterInfo = userRegisterInfoRepository.selectByOuterUserIdAndUserClass(imConversation.getFromOuterUserId(), imConversation.getFromOuterUserType());
        if ( Objects.isNull(userRegisterInfo) ){
            log.error("会话访客注册信息不存在, conversationId:{}", req.getConversationId());
            throw new BusinessException("会话访客注册信息不存在");
        }

        if ( !userRegisterInfo.getImId().equals(req.getTenantId()) ){
            log.error("不合法操作, 会话非当前租户应用会话 req:{}", JSONObject.toJSONString(req));
            throw new BusinessException("不合法操作，会话非当前租户应用会话");
        }

        return new GetConversationVisitorRegisterInfoRespBo(imConversation, userRegisterInfo);
    }


}
