package com.wanshifu.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.leftSidebar.*;
import com.wanshifu.iop.im.api.resp.leftSidebar.*;
import com.wanshifu.iop.im.domain.bo.ConversationAssignBo;
import com.wanshifu.iop.im.domain.bo.CoreActiveConversationRespBo;
import com.wanshifu.iop.im.domain.bo.HistoryConversationItemSenderInfoRespBo;
import com.wanshifu.iop.im.domain.bo.leftSidebar.VisitorInnerInfoBo;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;

import java.util.List;

/**
 * 左侧边栏服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:06
 */
public interface LeftSidebarService {

    LeftSidebarEnumsResp enums();

    OnlineSeatInfoResp onlineSeatInfo(OnlineSeatInfoReq req);

    Integer onlineSeatStatusSwitch(OnlineSeatStatusSwitchReq req);

    List<GetWaitProcessConversationListResp> waitProcessConversationList(GetWaitProcessConversationListReq req);

    SimplePageInfo<GetLeaveConversationListResp> leaveConversationList(GetLeaveConversationListReq req);

    Integer seatMarkForVisitor(SeatMarkForVisitorReq req);

    Integer seatUnmarkForVisitor(SeatUnmarkForVisitorReq req);

    SimplePageInfo<GetHistoryConversationListResp> historyConversationList(GetHistoryConversationListReq req);

    Integer historyConversationActive(HistoryConversationActiveReq req);

    Integer claimLeaveConversation(ClaimLeaveConversationReq req);

    List<HistoryConversationItemSenderInfoRespBo> addHistorySenderMemberToGroup(CoreActiveConversationRespBo coreActiveConversationRespBo);

    SimplePageInfo<GetHistoryConversationItemResp> getHistoryConversationItemLogs(GetHistoryConversationItemListReq req);

    Integer importHistoryConversationByVisitor(CoreActiveConversationRespBo coreActiveConversationRespBo);

    void closeConversation(SendImWebSocketMessageBo req);

    void historyToLeaveConversation(SendImWebSocketMessageBo req);

    void conversationAssign(ConversationAssignBo req);

    Integer updateConversationRead(SendImWebSocketMessageBo req);
}
