package com.wanshifu.service;

import com.wanshifu.iop.im.api.req.BatchGetEnumsReq;
import com.wanshifu.iop.im.api.req.ConversationThemeConfigRqt;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.incomingConfig.SearchDomainListReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.CommonEnumsResp;
import com.wanshifu.iop.im.api.resp.ConversationThemeConfigResp;
import com.wanshifu.iop.im.api.resp.VerifyRepeatNameResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchDomainListResp;

import java.util.List;
import java.util.Map;

/**
 * 通用服务
 * <AUTHOR>
 * @date： 2025-06-04 15:27:06
 */
public interface CommonService {

    VerifyRepeatNameResp verifyRepeatName(VerifyRepeatNameReq req);

    Integer switchStatus(SwitchStatusReq req);

    Map<String, List<CommonEnumsResp>> getEnums(BatchGetEnumsReq req);

    Map<String,List<ConversationThemeConfigResp>> getThemeConfigList(ConversationThemeConfigRqt req);
}
