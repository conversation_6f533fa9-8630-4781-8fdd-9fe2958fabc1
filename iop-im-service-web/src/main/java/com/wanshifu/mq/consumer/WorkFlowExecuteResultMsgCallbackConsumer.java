package com.wanshifu.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.base.ai.domain.message.WorkflowExecuteResultMessage;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.domain.bo.AIWorkflowExecuteResultMsgBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgTagBo;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


@Slf4j
@Topic("${wanshifu.ai.topic}")
@Component
public class WorkFlowExecuteResultMsgCallbackConsumer {

    @Resource
    private ImInterService imInterService;

    @Tag(value =  MqConfig.AI_MSG_CALLBACK_TAG, desc = "ai消息回调")
    public Action aiMsgCallbackAction(@Validated @MessageBody AIWorkflowExecuteResultMsgBo req) {

        log.info("AI回复消息：{}", JSON.toJSONString(req));

//        try {
            Integer result = imInterService.sendAIMsgToPersonal(req);

            if (result == 0) {
                return Action.ReconsumeLater;
            }

            return Action.CommitMessage;

//        } catch (Exception e) {
//            log.warn("AI消息消费失败,{}", e.getMessage());
//            return Action.ReconsumeLater;
//        }
    }
}
