package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.domain.bo.CoreActiveConversationRespBo;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.LeftSidebarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
@Component
public class ImportHistoryConversationConsumer {

    @Resource
    private LeftSidebarService leftSidebarService;

    @Tag(value = MqConfig.IMPORT_HISTORY_CONVERSATION_MESSAGE, desc = "历史消息异步导入")
    public Action action(@Validated @MessageBody CoreActiveConversationRespBo req) {
        try {
            leftSidebarService.importHistoryConversationByVisitor(req);
            return Action.CommitMessage;
        } catch (Exception e) {
            log.warn("历史消息异步导入,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
