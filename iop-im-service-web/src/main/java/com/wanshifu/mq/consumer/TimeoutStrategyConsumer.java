package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.factory.timeout.TimeoutFactory;
import com.wanshifu.factory.timeout.TimeoutHandleI;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.domain.bo.ImportMsgTagBo;
import com.wanshifu.iop.im.domain.bo.channel.reply.TimeoutStrategyReqBo;
import com.wanshifu.iop.im.domain.enums.channel.AutoReplyMsgSubTypeEnum;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.general.topic}")
@Component
public class TimeoutStrategyConsumer {

    @Tag(value =  MqConfig.TIMEOUT_STRATEGY_TAG, desc = "超时策略执行")
    public Action importMsgAction(@Validated @MessageBody TimeoutStrategyReqBo timeoutStrategyReqBo) {

        try {

            AutoReplyMsgSubTypeEnum enumByType = AutoReplyMsgSubTypeEnum.getEnumByType(timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getMsgSubType());
            if(enumByType==null){
                log.error("超时策略执行异常,msgSubType:{}", timeoutStrategyReqBo.getConversationAutoReplyDetailConfig().getMsgSubType());
                return Action.CommitMessage;
            }
            TimeoutHandleI timeoutHandleI = TimeoutFactory.setFactoryType(enumByType.type);
            Integer i = timeoutHandleI.timeoutAction(timeoutStrategyReqBo);

            if (i == 0) {
                return Action.ReconsumeLater;
            }

            return Action.CommitMessage;

        } catch (Exception e) {
            log.warn("导入消息失败,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
