package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.domain.bo.ImportMsgTagBo;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.general.topic}")
@Component
public class ImportMsgConsumer {

    @Resource
    private ImInterService imInterService;

    @Tag(value =  MqConfig.IMPORT_MSG_TAG, desc = "历史消息导入单聊+创建群+导入群消息")
    public Action importMsgAction(@Validated @MessageBody ImportMsgTagBo importMsgTagBo) {

        try {
            Integer integer = imInterService.importMsgAction(importMsgTagBo);

            if (integer == 0) {
                return Action.ReconsumeLater;
            }

            return Action.CommitMessage;

        } catch (Exception e) {
            log.warn("导入消息失败,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
