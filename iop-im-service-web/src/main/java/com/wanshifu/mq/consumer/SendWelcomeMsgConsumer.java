package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.ConversationConstant;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.api.req.tencent.SendMsgPersonalReq;
import com.wanshifu.iop.im.domain.bo.ImportMsgTagBo;
import com.wanshifu.iop.im.domain.bo.SendMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgToPersonalResultBo;
import com.wanshifu.iop.im.domain.bo.SendWelcomeMsgBo;
import com.wanshifu.iop.im.domain.enums.MsgTypeEnum;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.general.topic}")
@Component
public class SendWelcomeMsgConsumer {

    @Resource
    private TencentManager tencentManager;

    @Tag(value = MqConfig.SEND_WELCOME_MSG_TAG, desc = "发送欢迎语")
    public Action importMsgAction(@Validated @MessageBody SendWelcomeMsgBo req) {
        try {

            SendMsgPersonalBo sendMsgPersonalBo = new SendMsgPersonalBo();
            sendMsgPersonalBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
            sendMsgPersonalBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
            sendMsgPersonalBo.setFromAccountId(req.getFromAccountId());
            sendMsgPersonalBo.setToAccountId(req.getToAccountId());
            List<SendMsgPersonalBo.MsgContentItem> msgContentItemList = new ArrayList<>();
            SendMsgPersonalBo.MsgContentItem msgContentItem = new SendMsgPersonalBo.MsgContentItem();
            msgContentItem.setMsgType(MsgTypeEnum.TEXT.type);

            SendMsgPersonalReq.MsgBodyContent msgBodyContent = new SendMsgPersonalReq.MsgBodyContent();
            msgBodyContent.setText(ConversationConstant.USER_OPEN_CHAT_GREETING);
            msgContentItem.setContent(msgBodyContent);
            msgContentItemList.add(msgContentItem);
            sendMsgPersonalBo.setMsgContentItemList(msgContentItemList);
            sendMsgPersonalBo.setIsNeedReadReceipt(CommonConstant.ONE);


            SendMsgToPersonalResultBo sendMsgToPersonalResultBo = tencentManager.sendMsgToPersonal(sendMsgPersonalBo);

            if (sendMsgToPersonalResultBo != null) {
                return Action.CommitMessage;
            }

            return Action.ReconsumeLater;

        } catch (Exception e) {
            log.warn("导入消息失败,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
