package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.constant.ConversationConstant;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.api.req.tencent.SendMsgPersonalReq;
import com.wanshifu.iop.im.domain.bo.SendMsgPersonalBo;
import com.wanshifu.iop.im.domain.bo.SendMsgToPersonalResultBo;
import com.wanshifu.iop.im.domain.bo.SendWelcomeMsgBo;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.iop.im.domain.enums.MsgTypeEnum;
import com.wanshifu.manager.socketBase.SocketBaseManager;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.mq.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
@Component
public class SendImWebSocketMessageConsumer {

    @Resource
    private SocketBaseManager socketBaseManager;

    @Tag(value = MqConfig.IM_WEBSOCKET_PUSH_MSG, desc = "在线工作台socket消息异步发送")
    public Action action(@Validated @MessageBody SendImWebSocketMessageBo req) {
        try {
            socketBaseManager.consumerMessage(req);
            return Action.CommitMessage;
        } catch (Exception e) {
            log.warn("在线工作台socket消息异步发送失败,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
