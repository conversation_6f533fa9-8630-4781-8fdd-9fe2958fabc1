package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.domain.bo.ImportMsgTagBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToSingleBo;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
@Component
public class ImportMsgSingleConsumer {

    @Resource
    private ImInterService imInterService;

    @Tag(value =  MqConfig.IMPORT_MSG_SINGLE_DELAY_TAG, desc = "历史消息导入单聊")
    public Action importMsgSingleAction(@Validated @MessageBody ImportMsgToSingleBo importMsgToSingleBo) {

        try {
            Integer integer = imInterService.importMsgSingleAction(importMsgToSingleBo);

            if (integer == 0) {
                return Action.ReconsumeLater;
            }
            return Action.CommitMessage;
        } catch (Exception e) {
            log.warn("导入单聊消息失败,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
