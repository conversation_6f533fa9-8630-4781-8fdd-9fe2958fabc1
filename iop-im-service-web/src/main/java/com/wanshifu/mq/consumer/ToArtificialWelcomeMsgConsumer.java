package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.domain.bo.channel.msg.ToArtificialTypesBo;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
@Component
public class ToArtificialWelcomeMsgConsumer {

    @Resource
    private ImInterService imInterService;

    @Tag(value =  MqConfig.TO_ARTIFICIAL_TYPE_TAG, desc = "转人工提交表单后发送欢迎语")
    public Action sendWelcomeAction(@Validated @MessageBody ToArtificialTypesBo req) {

        try {
            Integer integer = imInterService.toArtificialWelcomeMsg(req);

            if (integer == 1) {
                return Action.CommitMessage;
            }

            return Action.ReconsumeLater;

        } catch (Exception e) {
            log.warn("发送欢迎语失败,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
