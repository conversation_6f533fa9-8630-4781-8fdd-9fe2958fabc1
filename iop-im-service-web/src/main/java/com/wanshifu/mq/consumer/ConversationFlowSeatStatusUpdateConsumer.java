package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.api.req.seat.ConversationFlowSeatStatusUpdateReq;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.manager.socketBase.SocketBaseManager;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.SeatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
@Component
public class ConversationFlowSeatStatusUpdateConsumer {

    @Resource
    private SeatService seatService;

    @Tag(value = MqConfig.CONVERSATION_FLOW_SEAT_STATUS_UPDATE, desc = "会话流转更新坐席状态")
    public Action action(@Validated @MessageBody ConversationFlowSeatStatusUpdateReq req) {
        try {
            seatService.conversationFlowSeatStatusUpdate(req);
            return Action.CommitMessage;
        } catch (Exception e) {
            log.warn("会话流转更新坐席状态消费失败,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
