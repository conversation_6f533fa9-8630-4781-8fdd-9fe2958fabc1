package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.domain.bo.ToArtificialMsgBo;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.general.topic}")
@Component
public class ToArtificialMsgConsumer {

    @Resource
    private ImInterService imInterService;

    @Tag(value =  MqConfig.TO_ARTIFICIAL_TAG, desc = "发送询前表单")
    public Action sendPreFormAction(@Validated @MessageBody ToArtificialMsgBo req) {

        try {
            Integer integer = imInterService.sendPreForm(req);

            if (integer == 1) {
                return Action.CommitMessage;
            }

            return Action.ReconsumeLater;

        } catch (Exception e) {
            log.warn("发送询前消息失败,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
