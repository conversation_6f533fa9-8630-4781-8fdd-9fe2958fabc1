package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.domain.bo.ImportMsgTagBo;
import com.wanshifu.iop.im.domain.bo.queue.XxlDistributeMqReqBo;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.DistributeService;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
@Component
public class XxlDistributeConsumer {

    @Resource
    private DistributeService distributeService;

    @Tag(value =  MqConfig.DELAY_DISTRIBUTE_TAG, desc = "xxl脚本触发分配mq消费")
    public Action distributeAction(@Validated @MessageBody XxlDistributeMqReqBo req) {

        try {
            Integer integer = distributeService.distributeXxl(req);

            if (integer == 1) {
                return Action.CommitMessage;
            }

            return Action.ReconsumeLater;

        } catch (Exception e) {
            log.warn("xxl脚本触发分配消息消费失败,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
