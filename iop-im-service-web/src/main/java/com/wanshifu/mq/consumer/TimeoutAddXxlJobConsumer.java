package com.wanshifu.mq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.im.domain.bo.ImportMsgTagBo;
import com.wanshifu.iop.im.domain.bo.conversation.XxlTimeoutReqBo;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.service.ImInterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


@Slf4j
@Topic("${wanshifu.rocketMQ.iop.im.service.general.topic}")
@Component
public class TimeoutAddXxlJobConsumer {

    @Resource
    private ImInterService imInterService;

    @Tag(value =  MqConfig.TIMEOUT_TASK_ADD_TAG, desc = "添加超时任务并启动任务")
    public Action addXxlJobAction(@Validated @MessageBody XxlTimeoutReqBo req) {

        try {
            Integer integer = imInterService.addTimeoutTask(req);

            if (integer == 1) {
                return Action.CommitMessage;
            }

            return Action.ReconsumeLater;

        } catch (Exception e) {
            log.warn("添加超时任务并启动任务失败,{}", e.getMessage());
            return Action.ReconsumeLater;
        }
    }
}
