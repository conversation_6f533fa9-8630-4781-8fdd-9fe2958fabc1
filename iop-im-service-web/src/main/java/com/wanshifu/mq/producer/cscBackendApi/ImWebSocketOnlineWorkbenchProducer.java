package com.wanshifu.mq.producer.cscBackendApi;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.iop.im.domain.bo.ToArtificialMsgBo;
import com.wanshifu.mq.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 在线工作台socket消息发送
 * <AUTHOR>
 * @date： 2025-07-24 09:43:27
 */

@Component
@Slf4j
public class ImWebSocketOnlineWorkbenchProducer {

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.csc.backend.api.general.topic}")
    private String generalTopic;


    public void sendImWebSocketOnlineWorkbenchMessage(Object request) {
        try {
            rocketMqSendService.sendSyncMessage(generalTopic, MqConfig.IM_WEBSOCKET_ONLINE_WORKBENCH, JSON.toJSONString(request));
            log.info("MQ生产成功 sendImWebSocketOnlineWorkbenchMessage success, messageBody:{}", JSON.toJSONString(request));
        } catch (BusException e) {
            log.error("MQ生产异常 sendImWebSocketOnlineWorkbenchMessage fail,  messageBody:{}", JSON.toJSONString(request));
        }
    }
}
