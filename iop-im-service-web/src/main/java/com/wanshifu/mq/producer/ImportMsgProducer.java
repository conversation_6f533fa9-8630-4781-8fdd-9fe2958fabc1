package com.wanshifu.mq.producer;

import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.mq.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 导入消息生产者
 * */

@Component
@Slf4j
public class ImportMsgProducer {

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.iop.im.service.general.topic}")
    private String generalTopic;


    public void sendMessage( String messageBody) {
        try {
            rocketMqSendService.sendSyncMessage(generalTopic, MqConfig.IMPORT_MSG_TAG, messageBody);
            log.info("MQ生产成功 sendMessage success, messageBody:{}", messageBody);
        } catch (BusException e) {
            log.error("MQ生产异常 sendMessage fail,  messageBody:{}", messageBody);
        }
    }
}
