package com.wanshifu.mq.producer;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.mq.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 在线工作台socket消息异步发送
 * <AUTHOR>
 * @date： 2025-07-24 09:43:27
 */

@Component
@Slf4j
public class SendImWebSocketMessageProducer {

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
    private String generalTopic;


    public void sendImWebSocketMessage(SendImWebSocketMessageBo request) {
        try {
            rocketMqSendService.sendDelayMessage(generalTopic, MqConfig.IM_WEBSOCKET_PUSH_MSG, JSON.toJSONString(request), MqConfig.IM_WEBSOCKET_PUSH_MSG_DELAY_TIME);
            log.info("MQ生产成功 sendImWebSocketMessage success, messageBody:{}", JSON.toJSONString(request));
        } catch (BusException e) {
            log.error("MQ生产异常 sendImWebSocketMessage fail,  messageBody:{}", JSON.toJSONString(request));
        }
    }
}
