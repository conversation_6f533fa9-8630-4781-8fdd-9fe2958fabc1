package com.wanshifu.mq.producer;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.iop.im.domain.bo.CoreActiveConversationRespBo;
import com.wanshifu.mq.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 在线工作台socket消息异步发送
 * <AUTHOR>
 * @date： 2025-07-24 09:43:27
 */

@Component
@Slf4j
public class ImportHistoryConversationProducer {

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
    private String generalTopic;


    public void sendMessage(CoreActiveConversationRespBo request) {
        try {
            rocketMqSendService.sendDelayMessage(generalTopic, MqConfig.IMPORT_HISTORY_CONVERSATION_MESSAGE, JSON.toJSONString(request), MqConfig.IMPORT_HISTORY_CONVERSATION_MESSAGE_DELAY_TIME);
            log.info("MQ生产成功 importHistoryConversationByVisitor success, messageBody:{}", JSON.toJSONString(request));
        } catch (BusException e) {
            log.error("MQ生产异常 importHistoryConversationByVisitor fail,  messageBody:{}", JSON.toJSONString(request));
        }
    }
}
