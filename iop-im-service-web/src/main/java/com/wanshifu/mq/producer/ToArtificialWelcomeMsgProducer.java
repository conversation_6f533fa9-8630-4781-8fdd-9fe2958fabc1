package com.wanshifu.mq.producer;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.iop.im.domain.bo.channel.msg.ToArtificialTypesBo;
import com.wanshifu.mq.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 发送欢迎语消息
 * */

@Component
@Slf4j
public class ToArtificialWelcomeMsgProducer {

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
    private String topic;


    public void sendMessage(ToArtificialTypesBo req) {
        try {
            rocketMqSendService.sendDelayMessage(topic, MqConfig.TO_ARTIFICIAL_TYPE_TAG, JSON.toJSONString(req), MqConfig.TO_ARTIFICIAL_TYPE_TAG_TIME);
        } catch (BusException e) {
            log.error("MQ生产异常 sendMessage fail,  参数:{}", JSON.toJSONString(req));
        }
    }
}
