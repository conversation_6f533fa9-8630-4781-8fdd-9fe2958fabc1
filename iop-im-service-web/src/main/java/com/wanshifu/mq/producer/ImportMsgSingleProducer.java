package com.wanshifu.mq.producer;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.iop.im.domain.bo.ImportMsgToSingleBo;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.mq.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 导入消息生产者
 * */

@Component
@Slf4j
public class ImportMsgSingleProducer {

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
    private String delayTopic;


    public void sendMessage(ImConversationItem imConversationItem, Long delayTime) {
        ImportMsgToSingleBo importMsgToSingleBo = returnImportMsgToSingleBean(imConversationItem);
        try {
            rocketMqSendService.sendDelayMessage(delayTopic, MqConfig.IMPORT_MSG_SINGLE_DELAY_TAG, JSON.toJSONString(importMsgToSingleBo), delayTime);
            log.info("MQ生产成功 sendMessage success, messageBody:{}", JSON.toJSONString(importMsgToSingleBo));
        } catch (BusException e) {
            log.error("MQ生产异常 sendMessage fail,  messageBody:{}", JSON.toJSONString(importMsgToSingleBo));
        }
    }

    public void sendMessageByImportMsgToSingleBo(ImportMsgToSingleBo importMsgToSingleBo, Long delayTime) {
        try {
            rocketMqSendService.sendDelayMessage(delayTopic, MqConfig.IMPORT_MSG_SINGLE_DELAY_TAG, JSON.toJSONString(importMsgToSingleBo), delayTime);
            log.info("MQ生产成功 sendMessage success, messageBody:{}", JSON.toJSONString(importMsgToSingleBo));
        } catch (BusException e) {
            log.error("MQ生产异常 sendMessage fail,  messageBody:{}", JSON.toJSONString(importMsgToSingleBo));
        }
    }

    /**
     * 组装bean
     * */
    public ImportMsgToSingleBo returnImportMsgToSingleBean(ImConversationItem imConversationItem){
        ImportMsgToSingleBo importMsgToSingleBo = new ImportMsgToSingleBo();
        importMsgToSingleBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        importMsgToSingleBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        importMsgToSingleBo.setSyncFromOldSystem(5);
        importMsgToSingleBo.setFromAccount(imConversationItem.getFromOuterUserId());
        importMsgToSingleBo.setToAccount(imConversationItem.getToOuterUserId());
        importMsgToSingleBo.setMsgSeq(imConversationItem.getMsgSeq());
        importMsgToSingleBo.setMsgRandom(imConversationItem.getMsgSeq());
        importMsgToSingleBo.setMsgTimeStamp(imConversationItem.getMsgSendTime().getTime()/1000L);

        List<ImportMsgToSingleBo.MsgBodyItem> msgBodyItems = JSONArray.parseArray(imConversationItem.getMsgContent(), ImportMsgToSingleBo.MsgBodyItem.class);

        importMsgToSingleBo.setMsgBody(msgBodyItems);

        return importMsgToSingleBo;
    }


}
