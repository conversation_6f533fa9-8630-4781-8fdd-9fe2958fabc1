package com.wanshifu.mq.producer;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.iop.im.api.req.seat.ConversationFlowSeatStatusUpdateReq;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.mq.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 会话流转更新坐席状态生产者
 * <AUTHOR>
 * @date： 2025-07-24 09:43:27
 */

@Component
@Slf4j
public class ConversationFlowSeatStatusUpdateProducer {

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
    private String generalTopic;


    public void sendMessage(ConversationFlowSeatStatusUpdateReq request) {
        try {
            rocketMqSendService.sendDelayMessage(generalTopic, MqConfig.CONVERSATION_FLOW_SEAT_STATUS_UPDATE, JSON.toJSONString(request), MqConfig.CONVERSATION_FLOW_SEAT_STATUS_UPDATE_DELAY_TIME);
            log.info("MQ生产成功 ConversationFlowSeatStatusUpdateProducer success, messageBody:{}", JSON.toJSONString(request));
        } catch (BusException e) {
            log.error("MQ生产异常 ConversationFlowSeatStatusUpdateProducer fail,  messageBody:{}", JSON.toJSONString(request));
        }
    }
}
