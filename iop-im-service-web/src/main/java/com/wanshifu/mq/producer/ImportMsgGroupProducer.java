package com.wanshifu.mq.producer;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.bo.ImportMsgGroupReqBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToGroupBo;
import com.wanshifu.iop.im.domain.bo.ImportMsgToSingleBo;
import com.wanshifu.iop.im.domain.bo.SendMsgGroupBo;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.mq.config.MqConfig;
import com.wanshifu.utils.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 导入消息生产者
 * */

@Component
@Slf4j
public class ImportMsgGroupProducer {

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
    private String delayTopic;


    public void sendMessage(ImportMsgGroupReqBo ImportMsgGroupReqBo, Long delayTime) {
        ImportMsgToGroupBo importMsgToSingleBo = returnImportMsgToGroupBean(ImportMsgGroupReqBo);
        try {
            rocketMqSendService.sendDelayMessage(delayTopic, MqConfig.IMPORT_MSG_GROUP_DELAY_TAG, JSON.toJSONString(importMsgToSingleBo), delayTime);
            log.info("MQ生产成功 sendMessage success, messageBody:{}", JSON.toJSONString(importMsgToSingleBo));
        } catch (BusException e) {
            log.error("MQ生产异常 sendMessage fail,  messageBody:{}", JSON.toJSONString(importMsgToSingleBo));
        }
    }

    public void sendMessageByImportMsgToGroupBo(ImportMsgToGroupBo importMsgToSingleBo, Long delayTime) {
        try {
            rocketMqSendService.sendDelayMessage(delayTopic, MqConfig.IMPORT_MSG_GROUP_DELAY_TAG, JSON.toJSONString(importMsgToSingleBo), delayTime);
            log.info("MQ生产成功 sendMessage success, messageBody:{}", JSON.toJSONString(importMsgToSingleBo));
        } catch (BusException e) {
            log.error("MQ生产异常 sendMessage fail,  messageBody:{}", JSON.toJSONString(importMsgToSingleBo));
        }
    }

    /**
     * 组装bean
     * */
    public ImportMsgToGroupBo returnImportMsgToGroupBean(ImportMsgGroupReqBo ImportMsgGroupReqBo){
        ImportMsgToGroupBo importMsgToGroupBo = new ImportMsgToGroupBo();
        importMsgToGroupBo.setTenantId(CommonConstant.DEFAULT_TENANT_ID);
        importMsgToGroupBo.setImType(CommonConstant.DEFAULT_IM_TYPE);
        importMsgToGroupBo.setGroupId(ImportMsgGroupReqBo.getGroupId());
        importMsgToGroupBo.setRecentContactFlag(1);
        List<ImportMsgToGroupBo.MsgListItem> MsgList = new ArrayList<>();
        for (ImConversationItem imConversationItem : ImportMsgGroupReqBo.getImConversationItems()) {
            ImportMsgToGroupBo.MsgListItem MsgListItem = new ImportMsgToGroupBo.MsgListItem();
            MsgListItem.setFrom_Account(imConversationItem.getFromOuterUserId());
            if(StringUtils.isNotEmpty(imConversationItem.getResponseOuterUserId())){
                MsgListItem.setFrom_Account(imConversationItem.getResponseOuterUserId());
            }
            MsgListItem.setSendTime(imConversationItem.getMsgSendTime().getTime()/1000L);
            MsgListItem.setRandom(MathUtil.generateCustomerLength(32));
            MsgListItem.setMsgSeq(imConversationItem.getMsgSeq());
            List<ImportMsgToGroupBo.MsgBodyItem> MsgBody = JSONArray.parseArray(imConversationItem.getMsgContent(), ImportMsgToGroupBo.MsgBodyItem.class);
            MsgListItem.setMsgBody(MsgBody);
        }

        importMsgToGroupBo.setMsgList(MsgList);

        return importMsgToGroupBo;
    }


}
