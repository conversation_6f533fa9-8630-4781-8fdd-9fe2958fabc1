package com.wanshifu.mq.producer;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.iop.im.domain.bo.queue.XxlDistributeMqReqBo;
import com.wanshifu.mq.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * xxl脚本分配消息发送
 * */

@Component
@Slf4j
public class XxlDistributeProducer {

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.iop.im.service.delay.topic}")
    private String generalTopic;

    public void sendMessage( XxlDistributeMqReqBo req, Long delayTime) {
        try {
            rocketMqSendService.sendDelayMessage(generalTopic, MqConfig.DELAY_DISTRIBUTE_TAG, JSON.toJSONString(req), delayTime);
            log.info("MQ生产成功 sendMessage success, messageBody:{}", JSON.toJSONString(req));
        } catch (BusException e) {
            log.error("MQ生产异常 sendMessage fail,  messageBody:{}", JSON.toJSONString(req));
        }
    }
}
