package com.wanshifu.mq.producer;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.iop.im.domain.bo.ToArtificialMsgBo;
import com.wanshifu.mq.config.MqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 转人工发送表单消息
 * */

@Component
@Slf4j
public class ToArtificialMsgProducer {

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.iop.im.service.general.topic}")
    private String generalTopic;


    public void sendMessage(ToArtificialMsgBo req) {
        try {
            rocketMqSendService.sendSyncMessage(generalTopic, MqConfig.TO_ARTIFICIAL_TAG, JSON.toJSONString(req));
            log.info("MQ生产成功 sendMessage success, messageBody:{}", JSON.toJSONString(req));
        } catch (BusException e) {
            log.error("MQ生产异常 sendMessage fail,  messageBody:{}", JSON.toJSONString(req));
        }
    }
}
