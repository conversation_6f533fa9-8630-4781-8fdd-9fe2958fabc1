package com.wanshifu.mq.config;


public class MqConfig {
    /**
     * 导入单聊消息+群聊消息
     * */
    public static final String IMPORT_MSG_TAG = "import_msg_tag";


    /**
     * 导入单聊消息mq
     * */
    public static final String IMPORT_MSG_SINGLE_DELAY_TAG = "import_msg_single_delay_tag";

    /**
     * 延时消息时间
     * */
    public static final Long IMPORT_MSG_SINGLE_DELAY_TIME = 200L;


    /**
     * 导入群聊消息mq
     * */
    public static final String IMPORT_MSG_GROUP_DELAY_TAG = "import_msg_group_delay_tag";

    /**
     * 延时消息时间
     * */
    public static final Long IMPORT_MSG_GROUP_DELAY_TIME = 200L;

    /**
     * 发送欢迎语消息
     * */
    public static final String SEND_WELCOME_MSG_TAG = "send_welcome_msg_tag";

    /**
     * AI机器人，消息回调tag
     * */
    public static final String AI_MSG_CALLBACK_TAG = "workflow_execute_result_iop";


    /**
     * 转人工消息tag
     * */
    public static final String TO_ARTIFICIAL_TAG = "to_artificial_tag";

    /**
     * 转人工发送消息，包含 欢迎语、介绍语等
     * */
    public static final String TO_ARTIFICIAL_TYPE_TAG = "to_artificial_type_tag";
    /**
     * 转人工发送消息
     * */
    public static final Long TO_ARTIFICIAL_TYPE_TAG_TIME = 1000L;

    /**
     * im websocket在线工作台 消息推送key
     */
    public static final String IM_WEBSOCKET_ONLINE_WORKBENCH = "im_websocket_online_workbench";

    /**
     * 超时策略执行
     * */
    public static final String TIMEOUT_STRATEGY_TAG = "timeout_strategy_tag";

    /**
     * 创建完会话后异步添加超时执行任务
     * */
    public static final String TIMEOUT_TASK_ADD_TAG = "timeout_task_add";

    /**
     * im websocket异步推送消息
     */
    public static final String IM_WEBSOCKET_PUSH_MSG = "im_websocket_push_msg";

    /**
     * im websocket异步推送消息 200ms
     */
    public static final Long IM_WEBSOCKET_PUSH_MSG_DELAY_TIME = 200L;

    /**
     * 延时分配tag
     * */
    public static final String DELAY_DISTRIBUTE_TAG = "delay_distribute_tag";

    /**
     * 延时分配时间
     * */
    public static final Long DELAY_DISTRIBUTE_TIME = 500L;

    /**
     * 群消息导入mq import_history_conversation_message
     */
    public static final String IMPORT_HISTORY_CONVERSATION_MESSAGE = "import_history_conversation_message";

    /**
     * 群消息导入mq 延迟时间 1000L
     */
    public static final Long IMPORT_HISTORY_CONVERSATION_MESSAGE_DELAY_TIME = 1000L;

    /**
     * 会话流转更新坐席状态 ConversationFlowSeatStatusUpdate
     */
    public static final String CONVERSATION_FLOW_SEAT_STATUS_UPDATE = "conversation_flow_seat_status_update";

    /**
     * 会话流转更新坐席状态 延迟时间 200L
     */
    public static final Long CONVERSATION_FLOW_SEAT_STATUS_UPDATE_DELAY_TIME = 200L;
}
