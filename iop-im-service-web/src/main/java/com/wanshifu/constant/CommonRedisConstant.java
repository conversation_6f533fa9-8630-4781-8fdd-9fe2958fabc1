package com.wanshifu.constant;

/**
 * 通用redis
 * <AUTHOR>
 * @date： 2025-06-03 19:36:45
 */
public class CommonRedisConstant {

    /**
     * 坐席创建账号缓存
     */
    public static final String SEAT_ACCOUNT_CREATE_KEY = "iopImService:seatCreate:accountId:%s";
    public static final Integer SEAT_ACCOUNT_CREATE_TIME = 60;

    /**
     * 坐席账号枚举缓存
     * */
    public static final String SEAT_ACCOUNT_ENUM_KEY = "iopImService:seatAccountEnum";
    public static final Integer SEAT_ACCOUNT_ENUM_TIME = 60 * 5;

    /**
     * 坐席最新工号缓存
     */
    public static final String SEAT_LAST_SEAT_NO_KEY = "iopImService:seatLastSeatNo:%s";
    public static final Integer SEAT_LAST_SEAT_NO_TIME = 60 * 30;

    /**
     * 坐席登录token缓存
     * */
    public static final String IOP_IM_TOKEN_KEY = "iopImService:imToken:%s";
    /**
     * 销毁坐席登录token缓存
     */
    public static final String DELETE_IOP_IM_TOKEN_KEY = "iopImService:imToken";

    /**
     * 账号登录缓存token
     * */
    public static final String IOP_ACCOUNT_MAPPING_TOKEN_KEY = "iopImService:accountId:imToken:%s";
    /**
     * 销毁账号登录缓存token
     */
    public static final String DELETE_IOP_ACCOUNT_MAPPING_TOKEN_KEY = "iopImService:accountId:imToken";

    public static final Integer IOP_IM_TOKEN_TIME = 86400;

    /**
     * 添加防重复超时时间 毫秒
     */
    public static final Integer REPEAT_OPERATE_TIME = 2;

    /**
     * 创建端侧前缀
     */
    public static final String CREATE_CLIENT_CATEGORY_PREFIX = "iopImService:create:category%s";
    /**
     * 创建渠道前缀
     */
    public static final String CREATE_CHANNEL_PREFIX = "iopImService:create:channel:%s";

    /**
     * redis图片缓存key
     */
    public static String PICTURE_URL_KEY = "iopImService:pictureUrl:fileId:%s";

    public static final Integer PICTURE_URL_KEY_CACHE_TIME = 60 * 60 * 2;

    /**
     * 分组队列key
     */
    public static final String TENANT_GROUP_QUEUE_KEY = "iopImService:tenantId_%s:group_%s_Queue";

    /**
     * 批量查询脚本
     * */
    // 修改后的Lua脚本（使用KEYS参数动态传入）
    public static final String LUA_SCRIPT =
            "local result = {} " +
                    "for i, key in ipairs(KEYS) do " +
                    "    local elem = redis.call('ZRANGE', key, 0, 0, 'WITHSCORES') " +
                    "    result[i] = {key, elem} " +
                    "end " +
                    "return result";
}
