package com.wanshifu.constant;

import com.alibaba.druid.sql.ast.SQLDataTypeImpl;

public class CommonConstant {

    /**
     * 1
     */
    public static final Integer ONE = 1;
    /**
     * 0
     */
    public static final Integer ZERO = 0;
    /**
     * 2
     */
    public static final Integer TWO = 2;
    /**
     * 3
     */
    public static final Integer THREE = 3;
    /**
     * 4
     */
    public static final Integer FOUR = 4 ;
    /**
     * 5
     */
    public static final Integer FIVE = 5 ;


    public static final Integer SEVEN = 7 ;

    /**
     * 坐席状态在线切换类型
     */
    public static final String SEAT_STATUS_ONLINE_SWITCH_TYPE = "online_switch";

    /**
     * 坐席状态 认领留言 功能 claim_leave
     */
    public static final String SEAT_STATUS_FUNCTION_CLAIM_LEAVE = "claim_leave";

    /**
     * 坐席状态 主动发起会话 功能 initiate_conversation
     */
    public static final String SEAT_STATUS_FUNCTION_INITIATE_CONVERSATION = "initiate_conversation";

    /**
     * 默认坐席离线状态
     */
    public static final String DEFAULT_OFFLINE_SEAT_STATUS = "offline";

    /**
     * 默认坐席在线状态
     */
    public static final String DEFAULT_ONLINE_SEAT_STATUS = "wait_connect_line";

    /**
     * 坐席在线状态
     */
    public static final String SEAT_STATUS_ONLINE = "online";

    /**
     * 坐席满负荷状态 full_load
     */
    public static final String SEAT_STATUS_FULL_LOAD = "full_load";

    /**
     * 进线会话坐席状态功能类型 auto_receive_queue_conversation
     */
    public static final String SEAT_STATUS_FUNCTION_AUTO_RECEIVE_QUEUE_CONVERSATION = "auto_receive_queue_conversation";

    /**
     * 坐席工号开始工号
     */
    public static final Integer SEAT_SEAT_NO_START = 1000;


    /**
     * 腾讯回调状态码，标识成功
     * */
    public static final String ACTION_STATUS_OK = "OK";

    /**
     * 腾讯回调状态码，标识失败
     * */
    public static final String ACTION_STATUS_NO = "FAIL";

    /**
     * 回调地址
     * */
    public static final String TENCENT_CALL_BACK_URL = "/open/tencent/callback";

    /**
     * 0L
     */
    public static final Long LONG_ZERO = 0L;
    /**
     * 0L
     */
    public static final Long LONG_ONE = 1L;


    /**
     * 默认租户id
     * */
    public static final Long DEFAULT_TENANT_ID = 1L;

    /**
     * 默认IM类型
     * */
    public static final String DEFAULT_IM_TYPE = "chat";

    /**
     * 用户侧看到的聊天对象，虚拟账号昵称；
     * */
    public static final String VIRTUAL_USER_NICKNAME = "客服-小万";

    /**
     *  系统用户ID
     */
    public static final Long SYS_USER_ID = 9999L;

    /**
     *  其他业务写日志默认表id 999999
     */
    public static final Integer OTHER_BUSINESS_TABLE_ID = 999999;

    /**
     * 群名称统一 跟客服相关的群聊
     * */
    public static final String GROUP_NAME = "在线咨询";

    /**
     * 非客服相关的群聊
     * */
    public static final String GROUP_NOT_SEAT_NAME = "在线对话";

    /**
     * "null" 字符串
     */
    public static final String NULL_STRING = "null";

    /**
     * 发送对象 sendObjectFrom 访客-visitor 坐席-seat
     */
    public static final String SEND_OBJECT_VISITOR = "visitor";

    /**
     * 默认群组前缀 GROUP 这个字段给前端用户打开会话窗口的
     * */
    public static final String DEFAULT_IM_GROUP_PREFIX = "GROUP";

    /**
     * 分配规则ka
     * */
    public static final String RULE_KA = "KA";
    public static final String RULE_NOT_KA = "非KA";
}
