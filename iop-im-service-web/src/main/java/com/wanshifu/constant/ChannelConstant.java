package com.wanshifu.constant;

/**
 * 端侧渠道初始化json
 */
public class ChannelConstant {

    /**
     * 系统自动回复消息配置初始化JSON字符串
     */
    public static String AUTO_REPLY_DETAIL_CONFIG_INIT_JSON = "[{\n" +
            "        \"conversationAutoReplyDetailConfig\": 0,\n" +
            "        \"conversationAutoReplyConfigId\": 0,\n" +
            "        \"msgType\": \"timeout_auto_reply\",\n" +
            "        \"msgSubType\": \"seat_seat_timeout_tip\",\n" +
            "        \"subTypeShowSeq\": 1,\n" +
            "        \"autoReplyReceiveObject\": \"seat\",\n" +
            "        \"autoReplyContent\": \"您还未回复访客消息，请尽快回复！\",\n" +
            "        \"triggerMode\": \"seat_seat_timeout\",\n" +
            "        \"placeholderJson\": null,\n" +
            "        \"status\": 1,\n" +
            "        \"isDelete\": 0,\n" +
            "        \"operatorId\": 0,\n" +
            "        \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "        \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    },"+
            "      {\n" +
            "        \"conversationAutoReplyDetailConfig\": 0,\n" +
            "        \"conversationAutoReplyConfigId\": 0,\n" +
            "        \"msgType\": \"timeout_auto_reply\",\n" +
            "        \"msgSubType\": \"visitor_seat_timeout_tip\",\n" +
            "        \"subTypeShowSeq\": 2,\n" +
            "        \"autoReplyReceiveObject\": \"visitor\",\n" +
            "        \"autoReplyContent\": \"您还未回复访客消息，请尽快回复！\",\n" +
            "        \"triggerMode\": \"visitor_seat_timeout\",\n" +
            "        \"timeoutCostMinutes\": 10,\n" +
            "        \"supportPlaceholder\": 0,\n" +
            "        \"placeholderJson\": null,\n" +
            "        \"status\": 1,\n" +
            "        \"isDelete\": 0,\n" +
            "        \"operatorId\": 0,\n" +
            "        \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "        \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    }," +
            "    {\n" +
            "      \"conversationAutoReplyDetailConfig\": 0,\n" +
            "      \"conversationAutoReplyConfigId\": 0,\n" +
            "      \"msgType\": \"timeout_auto_reply\",\n" +
            "      \"subTypeShowSeq\": 3,\n" +
            "      \"msgSubType\": \"visitor_timeout_tip\",\n" +
            "      \"autoReplyReceiveObject\": \"visitor\",\n" +
            "      \"autoReplyContent\": \"您好，还有新的问题吗？由于长时间未接收到消息，抱歉会话已关闭！\",\n" +
            "      \"triggerMode\": \"visitor_timeout\",\n" +
            "      \"timeoutCostMinutes\": 10,\n" +
            "      \"supportPlaceholder\": 0,\n" +
            "      \"placeholderJson\": null,\n" +
            "      \"status\": 1,\n" +
            "      \"isDelete\": 0,\n" +
            "      \"operatorId\": 0,\n" +
            "      \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "      \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"conversationAutoReplyDetailConfig\": 0,\n" +
            "      \"conversationAutoReplyConfigId\": 0,\n" +
            "      \"msgType\": \"timeout_auto_reply\",\n" +
            "      \"msgSubType\": \"seat_timeout_tip\",\n" +
            "      \"subTypeShowSeq\": 4,\n" +
            "      \"autoReplyReceiveObject\": \"seat\",\n" +
            "      \"autoReplyContent\": \"您还未回复访客消息，请尽快回复！\",\n" +
            "      \"triggerMode\": \"seat_timeout\",\n" +
            "      \"timeoutCostMinutes\": 5,\n" +
            "      \"supportPlaceholder\": 0,\n" +
            "      \"placeholderJson\": null,\n" +
            "      \"status\": 1,\n" +
            "      \"isDelete\": 0,\n" +
            "      \"operatorId\": 0,\n" +
            "      \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "      \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"conversationAutoReplyDetailConfig\": 0,\n" +
            "      \"conversationAutoReplyConfigId\": 0,\n" +
            "      \"msgType\": \"wait_auto_reply\",\n" +
            "      \"msgSubType\": \"queue_tip\",\n" +
            "      \"subTypeShowSeq\": 1,\n" +
            "      \"autoReplyReceiveObject\": \"visitor\",\n" +
            "      \"autoReplyContent\": \"排队中,您当前排在第{num}位,排队将自动接入\",\n" +
            "      \"triggerMode\": \"system_active\",\n" +
            "      \"timeoutCostMinutes\": 0,\n" +
            "      \"supportPlaceholder\": 1,\n" +
            "      \"placeholderJson\": \"{\\\"num\\\": \\\"排队位置\\\"}\",\n" +
            "      \"status\": 1,\n" +
            "      \"isDelete\": 0,\n" +
            "      \"operatorId\": 0,\n" +
            "      \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "      \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"conversationAutoReplyDetailConfig\": 0,\n" +
            "      \"conversationAutoReplyConfigId\": 0,\n" +
            "      \"msgType\": \"wait_auto_reply\",\n" +
            "      \"msgSubType\": \"queue_comfort_tip\",\n" +
            "      \"subTypeShowSeq\": 2,\n" +
            "      \"autoReplyReceiveObject\": \"visitor\",\n" +
            "      \"autoReplyContent\": \"当前咨询人数较多，请您耐心等待，客服正在加急处理中，请耐心等待客服为您服务。\",\n" +
            "      \"triggerMode\": \"system_active\",\n" +
            "      \"timeoutCostMinutes\": 0,\n" +
            "      \"supportPlaceholder\": 0,\n" +
            "      \"placeholderJson\": null,\n" +
            "      \"status\": 1,\n" +
            "      \"isDelete\": 0,\n" +
            "      \"operatorId\": 0,\n" +
            "      \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "      \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"conversationAutoReplyDetailConfig\": 0,\n" +
            "      \"conversationAutoReplyConfigId\": 0,\n" +
            "      \"msgType\": \"welcome_auto_reply\",\n" +
            "      \"msgSubType\": \"welcome_tip\",\n" +
            "      \"subTypeShowSeq\": 1,\n" +
            "      \"autoReplyReceiveObject\": \"visitor\",\n" +
            "      \"autoReplyContent\": \"欢迎咨询在线客服，请问有什么可以帮助您？\",\n" +
            "      \"triggerMode\": \"visitor_transfer_to_agent\",\n" +
            "      \"timeoutCostMinutes\": 0,\n" +
            "      \"supportPlaceholder\": 0,\n" +
            "      \"placeholderJson\": null,\n" +
            "      \"status\": 1,\n" +
            "      \"isDelete\": 0,\n" +
            "      \"operatorId\": 0,\n" +
            "      \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "      \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"conversationAutoReplyDetailConfig\": 0,\n" +
            "      \"conversationAutoReplyConfigId\": 0,\n" +
            "      \"msgType\": \"welcome_auto_reply\",\n" +
            "      \"msgSubType\": \"introduction_tip\",\n" +
            "      \"subTypeShowSeq\": 2,\n" +
            "      \"autoReplyReceiveObject\": \"visitor\",\n" +
            "      \"autoReplyContent\": \"您好，客服(nickname)很高兴为您服务，请问有什么可以帮您？\",\n" +
            "      \"triggerMode\": \"visitor_enter_agent\",\n" +
            "      \"timeoutCostMinutes\": 0,\n" +
            "      \"supportPlaceholder\": 1,\n" +
            "      \"placeholderJson\": \"{\\\"nickname\\\": \\\"坐席昵称\\\"}\",\n" +
            "      \"status\": 1,\n" +
            "      \"isDelete\": 0,\n" +
            "      \"operatorId\": 0,\n" +
            "      \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "      \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"conversationAutoReplyDetailConfig\": 0,\n" +
            "      \"conversationAutoReplyConfigId\": 0,\n" +
            "      \"msgType\": \"end_auto_reply\",\n" +
            "      \"msgSubType\": \"visitor_end_tip\",\n" +
            "      \"subTypeShowSeq\": 1,\n" +
            "      \"autoReplyReceiveObject\": \"visitor\",\n" +
            "      \"autoReplyContent\": \"感谢您的咨询，祝您生活愉快！\",\n" +
            "      \"triggerMode\": \"visitor_end\",\n" +
            "      \"timeoutCostMinutes\": 0,\n" +
            "      \"supportPlaceholder\": 0,\n" +
            "      \"placeholderJson\": null,\n" +
            "      \"status\": 1,\n" +
            "      \"isDelete\": 0,\n" +
            "      \"operatorId\": 0,\n" +
            "      \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "      \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"conversationAutoReplyDetailConfig\": 0,\n" +
            "      \"conversationAutoReplyConfigId\": 0,\n" +
            "      \"msgType\": \"end_auto_reply\",\n" +
            "      \"msgSubType\": \"agent_end_tip\",\n" +
            "      \"subTypeShowSeq\": 2,\n" +
            "      \"autoReplyReceiveObject\": \"visitor\",\n" +
            "      \"autoReplyContent\": \"如有问题欢迎再次咨询，您觉得此次我的服务您还满意的话，请您给我一个非常满意哦，十分感谢您！\",\n" +
            "      \"triggerMode\": \"agent_end\",\n" +
            "      \"timeoutCostMinutes\": 0,\n" +
            "      \"supportPlaceholder\": 0,\n" +
            "      \"placeholderJson\": null,\n" +
            "      \"status\": 1,\n" +
            "      \"isDelete\": 0,\n" +
            "      \"operatorId\": 0,\n" +
            "      \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "      \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"conversationAutoReplyDetailConfig\": 0,\n" +
            "      \"conversationAutoReplyConfigId\": 0,\n" +
            "      \"msgType\": \"end_auto_reply\",\n" +
            "      \"msgSubType\": \"system_end_tip\",\n" +
            "      \"subTypeShowSeq\": 3,\n" +
            "      \"autoReplyReceiveObject\": \"visitor\",\n" +
            "      \"autoReplyContent\": \"系统自动结束会话，感谢您的咨询！\",\n" +
            "      \"triggerMode\": \"system_end\",\n" +
            "      \"timeoutCostMinutes\": 0,\n" +
            "      \"supportPlaceholder\": 0,\n" +
            "      \"placeholderJson\": null,\n" +
            "      \"status\": 1,\n" +
            "      \"isDelete\": 0,\n" +
            "      \"operatorId\": 0,\n" +
            "      \"createTime\": \"2025-06-11 21:13:38\",\n" +
            "      \"updateTime\": \"2025-06-11 21:13:38\"\n" +
            "    }]\n";


    /**
     * 满意度初始化JSON字符串
     */
    public static String SATISFACTION_LEVEL_CONFIG_INIT_JSON =  "{\n" +
            "  \"satisfactionLevelConfigId\": 0,\n" +
            "  \"channelId\": 0,\n" +
            "  \"resultCallbackFormJsonBo\": {\n" +
            "    \"filedBoList\": [\n" +
            "      {\n" +
            "        \"satisfactionLevelConfigId\": 0,\n" +
            "        \"filedId\": 0,\n" +
            "        \"filedName\": \"sendCondition\",\n" +
            "        \"filedDesc\": \"满意度发送条件\",\n" +
            "        \"filedType\": \"checkbox\",\n" +
            "        \"isRequired\": 0,\n" +
            "        \"optionBoList\": [\n" +
            "          { \"isRequired\": 0, \"isSelected\": 0, \"label\": \"seat_send\", \"value\": \"坐席主动发送\" },\n" +
            "          { \"isRequired\": 0, \"isSelected\": 0, \"label\": \"seat_close\", \"value\": \"坐席关闭会话\" },\n" +
            "          { \"isRequired\": 0, \"isSelected\": 0, \"label\": \"visitor_end\", \"value\": \"访客结束会话（超时未回复+超时离开）\" }\n" +
            "        ]\n" +
            "      },\n" +

            "      {\n" +
            "        \"satisfactionLevelConfigId\": 0,\n" +
            "        \"filedId\": 1,\n" +
            "        \"filedName\": \"satisfactionTimeOut\",\n" +
            "        \"filedDesc\": \"满意度时效设置\",\n" +
            "        \"filedType\": \"radio\",\n" +
            "        \"isRequired\": 0,\n" +
            "        \"optionBoList\": [\n" +
            "          { \"isRequired\": 0, \"isSelected\": 0, \"label\": \"today\", \"value\": \"当天失效\", \"timeOutEndTime\": null }\n" +
            "        ]\n" +
            "      },\n" +

            "      {\n" +
            "        \"satisfactionLevelConfigId\": 0,\n" +
            "        \"filedId\": 2,\n" +
            "        \"filedName\": \"evaluationPrompt\",\n" +
            "        \"filedDesc\": \"评价引导语\",\n" +
            "        \"filedType\": \"text\",\n" +
            "        \"isRequired\": 0,\n" +
            "        \"optionBoList\": []\n" +
            "      },\n" +
            "      {\n" +
            "        \"satisfactionLevelConfigId\": 0,\n" +
            "        \"filedId\": 3,\n" +
            "        \"filedName\": \"resultCallbackButton\",\n" +
            "        \"filedDesc\": \"结果回收按钮\",\n" +
            "        \"filedType\": \"button\",\n" +
            "        \"isRequired\": 1,\n" +
            "        \"optionBoList\": [\n" +
            "          {\n" +
            "            \"isRequired\": 0,\n" +
            "            \"isSelected\": 0,\n" +
            "            \"buttonId\": \"0\",\n" +
            "            \"buttonIcon\": \"aid1\",\n" +
            "            \"label\": \"has_solved_problem\",\n" +
            "            \"value\": \"已解决\"\n" +
            "          },\n" +
            "          {\n" +
            "            \"isRequired\": 0,\n" +
            "            \"isSelected\": 0,\n" +
            "            \"buttonId\": \"1\",\n" +
            "            \"buttonIcon\": \"aid2\",\n" +
            "            \"label\": \"no_solved_problem\",\n" +
            "            \"value\": \"未解决\",\n" +
            "            \"buttonReasonItemList\": [\n" +
            "              { \"buttonId\": 1, \"buttonReason\": \"客服态度不好\", \"checkStatus\": 0 },\n" +
            "              { \"buttonId\": 1, \"buttonReason\": \"客服不专业\", \"checkStatus\": 0 }\n" +
            "            ]\n" +
            "          }\n" +
            "        ]\n" +
            "      },\n" +
            "      {\n" +
            "        \"satisfactionLevelConfigId\": 0,\n" +
            "        \"filedId\": 4,\n" +
            "        \"filedName\": \"evaluationStarLevel\",\n" +
            "        \"filedDesc\": \"评价星级\",\n" +
            "        \"filedType\": \"composite\",\n" +
            "        \"isRequired\": 1,\n" +
            "        \"optionBoList\": [\n" +
            "          { \"isRequired\": 0, \"isSelected\": 0, \"label\": \"5\", \"value\": \"非常满意\" },\n" +
            "          { \"isRequired\": 0, \"isSelected\": 0, \"label\": \"4\", \"value\": \"满意\" },\n" +
            "          { \"isRequired\": 0, \"isSelected\": 0, \"label\": \"3\", \"value\": \"一般\" },\n" +
            "          { \"isRequired\": 0, \"isSelected\": 0, \"label\": \"2\", \"value\": \"不满意\" },\n" +
            "          { \"isRequired\": 0, \"isSelected\": 0, \"label\": \"1\", \"value\": \"非常不满意\" }\n" +
            "        ]\n" +
            "      },\n" +
            "      {\n" +
            "        \"satisfactionLevelConfigId\": 0,\n" +
            "        \"filedId\": 5,\n" +
            "        \"filedName\": \"otherQuestion\",\n" +
            "        \"filedDesc\": \"其他问题\",\n" +
            "        \"filedType\": \"text\",\n" +
            "        \"isRequired\": 1,\n" +
            "        \"optionBoList\": []\n" +
            "      }\n" +
            "    ]\n" +
            "  },\n" +
            "  \"status\": 0,\n" +
            "  \"isDelete\": 0,\n" +
            "  \"operatorId\": 0,\n" +
            "  \"createTime\": \"2025-06-10 15:07:42\",\n" +
            "  \"updateTime\": \"2025-06-10 15:07:42\"\n" +
            "}\n";

    /**
     *  满意度、系统自动回复、会话样式侧边栏初始化 全部初始化json
     */
    public static String DEFAULT_ALL_CONFIG_INIT_JSON = "";

    /**
     * 会话管理全局配置标识，channel_en
     * */
    public static String CONVERSATION_GLOBAL_CHANNEL_EN = "conversationGlobalConfigAll";

    /**
     * 客服用户名 占位符
     * */
    public static String KF_NICK_NAME = "{seatName}";
    public static String KF_SEAT_ID = "{seatId}";

    /**
     * 会话全局配置 key
     */
    public static String CONVERSATION_GLOBAL_CONFIG = "conversation_global_config";
    public static String CONVERSATION_GLOBAL_CONFIG_SUB = "conversation_style_config_tip";

    /**
     * 响应超时提醒
     */
    public static String RESPONSE_TIMEOUT_REMINDER = "response_timeout_reminder";

    /**
     * 响应即将超时提醒
     */
    public static String IMMEDIATELY_RESPONSE_TIMEOUT_REMINDER = "immediately_response_timeout_reminder";

}
