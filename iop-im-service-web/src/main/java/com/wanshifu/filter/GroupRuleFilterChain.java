package com.wanshifu.filter;

import com.wanshifu.iop.im.domain.bo.CommonFilterResultBo;
import com.wanshifu.iop.im.domain.bo.queue.ConversationDistributeEnGroupFilterBo;
import com.wanshifu.iop.im.domain.bo.queue.GroupConfigRuleAttributeReqBo;

import java.util.List;

/**
 * 分组规则过来器
 */
public class GroupRuleFilterChain {

    /**
     * 分配过滤器
     */
    private final List<GroupRuleFilter> filtersOptionAction;
    /**
     * 会话分配属性
     */
    private final ConversationDistributeEnGroupFilterBo conversationDistributeEnGroupFilterBo;

    /**
     * 分组规则配置属性
     */
    private final GroupConfigRuleAttributeReqBo groupConfigRuleAttributeReqBo;

    public GroupRuleFilterChain(List<GroupRuleFilter> workOrderFilters, ConversationDistributeEnGroupFilterBo conversationDistributeEnGroupFilterBo, GroupConfigRuleAttributeReqBo groupConfigRuleAttributeReqBo) {
        this.filtersOptionAction = workOrderFilters;
        this.conversationDistributeEnGroupFilterBo = conversationDistributeEnGroupFilterBo;
        this.groupConfigRuleAttributeReqBo = groupConfigRuleAttributeReqBo;
    }

    public CommonFilterResultBo doFilter() {
        for (GroupRuleFilter filterOption : this.filtersOptionAction) {
            CommonFilterResultBo handle = filterOption.handle(conversationDistributeEnGroupFilterBo, groupConfigRuleAttributeReqBo);
            if (!handle.isResult()) {
                return handle;
            }
        }

        return new CommonFilterResultBo(true, "go running");
    }
}
