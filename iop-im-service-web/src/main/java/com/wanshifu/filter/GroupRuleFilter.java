package com.wanshifu.filter;

import com.wanshifu.iop.im.domain.bo.CommonFilterResultBo;
import com.wanshifu.iop.im.domain.bo.queue.ConversationDistributeEnGroupFilterBo;
import com.wanshifu.iop.im.domain.bo.queue.GroupConfigRuleAttributeReqBo;

public interface GroupRuleFilter {

    CommonFilterResultBo handle(ConversationDistributeEnGroupFilterBo conversationDistributeEnGroupFilterBo, GroupConfigRuleAttributeReqBo groupConfigRuleAttributeReqBo);
}
