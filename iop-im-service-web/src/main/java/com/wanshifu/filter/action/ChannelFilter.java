package com.wanshifu.filter.action;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.filter.GroupRuleFilter;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.domain.bo.CommonFilterResultBo;
import com.wanshifu.iop.im.domain.bo.groupManage.GroupRuleConfigValueBo;
import com.wanshifu.iop.im.domain.bo.queue.ConversationDistributeEnGroupFilterBo;
import com.wanshifu.iop.im.domain.bo.queue.GroupConfigRuleAttributeReqBo;
import com.wanshifu.iop.im.domain.enums.RuleConfigValueSelectiveTypeEnum;
import com.wanshifu.iop.im.domain.enums.RuleMetricConfigEnum;
import com.wanshifu.iop.im.domain.po.GroupRule;
import com.wanshifu.iop.im.domain.po.RuleMetricConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 会话入口过滤器
 */
@Component
@Slf4j
public class ChannelFilter implements GroupRuleFilter {
    @Override
    public CommonFilterResultBo handle(ConversationDistributeEnGroupFilterBo conversationDistributeEnGroupFilterBo, GroupConfigRuleAttributeReqBo groupConfigRuleAttributeReqBo) {
        CommonFilterResultBo resultBo = new CommonFilterResultBo();

        String filterType = RuleMetricConfigEnum.CONVERSATION_ENTRY.type;

        // 获取当前分组的会话入口
        RuleMetricConfig ruleMetricConfig = groupConfigRuleAttributeReqBo.getRuleMetricConfigs().stream()
                .filter(f -> filterType.equals(f.getRuleMetricEn())).findFirst().orElse(null);
        if (Objects.isNull(ruleMetricConfig)){
            log.error("分组规则指标不存在 groupId:{} ruleMetricEn:{}", groupConfigRuleAttributeReqBo.getGroupId(), filterType);
            resultBo.setResult(false);
            resultBo.setMsg("会话入口配置不存在");
            return resultBo;
        }

        // 获取分组的会话入口配置
        GroupRule groupRule = groupConfigRuleAttributeReqBo.getGroupRuleList().stream().filter(f -> ruleMetricConfig.getRuleMetricId().equals(f.getRuleMetricId())).findFirst().orElse(null);
        if (Objects.isNull(groupRule)){
            log.error("分组规则没有会话入口的配置 groupId:{} ruleMetricEn:{}", groupConfigRuleAttributeReqBo.getGroupId(), filterType);
            resultBo.setResult(false);
            resultBo.setMsg("分组规则没有会话入口的配置");
            return resultBo;
        }

        if ( StringUtils.isBlank(groupRule.getRuleConfigValue()) ){
            log.error("分组规则会话入口配置为空 groupId:{} groupRule:{}", groupConfigRuleAttributeReqBo.getGroupId(), JSONObject.toJSONString(groupRule));
            resultBo.setResult(false);
            resultBo.setMsg("分组规则会话入口配置为空");
            return resultBo;
        }

        GroupRuleConfigValueBo groupRuleConfigValueBo = JSONObject.parseObject(groupRule.getRuleConfigValue(), GroupRuleConfigValueBo.class);
        if ( Objects.isNull(groupRuleConfigValueBo) || StringUtils.isBlank(groupRuleConfigValueBo.getSelectiveType()) ){
            log.error("分组规则会话入口配置解析失败 groupId:{} groupRule:{}", groupConfigRuleAttributeReqBo.getGroupId(), JSONObject.toJSONString(groupRule));
            resultBo.setResult(false);
            resultBo.setMsg("分组规则会话入口配置解析失败");
            return resultBo;
        }

        List<String> groupRuleConfigValueList = new ArrayList<>();
        if (RuleConfigValueSelectiveTypeEnum.SINGLE.type.equals(groupRuleConfigValueBo.getSelectiveType()) ){
            groupRuleConfigValueList.add(groupRuleConfigValueBo.getSingleValue());
        }else if (RuleConfigValueSelectiveTypeEnum.MULTIPLE.type.equals(groupRuleConfigValueBo.getSelectiveType()) ){
            groupRuleConfigValueList.addAll(groupRuleConfigValueBo.getMultipleValue());
        }

        if ( CollectionUtils.isEmpty(groupRuleConfigValueList) ){
            log.error("分组规则会话入口配置解析失败（groupRuleConfigValueList为null） groupId:{} groupRule:{}", groupConfigRuleAttributeReqBo.getGroupId(), JSONObject.toJSONString(groupRule));
            resultBo.setResult(false);
            resultBo.setMsg("分组规则会话入口配置解析失败");
            return resultBo;
        }

        if ( groupRuleConfigValueList.stream().anyMatch(item -> conversationDistributeEnGroupFilterBo.getChannelId().toString().equals(item)) ){
            resultBo.setResult(true);
            resultBo.setMsg("会话入口匹配成功");
            return resultBo;
        }else{
            log.info("会话入口匹配失败 conversationDistributeEnGroupFilterBo:{} groupConfigRuleAttributeReqBo:{}"
                    ,JSONObject.toJSONString(conversationDistributeEnGroupFilterBo), JSONObject.toJSONString(groupConfigRuleAttributeReqBo));
            resultBo.setResult(false);
            resultBo.setMsg("会话入口匹配失败");
            return resultBo;
        }
    }
}
