package com.wanshifu.mapper;

import com.wanshifu.iop.im.domain.po.SeatStatusSwitchLog;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SeatStatusSwitchLogMapper extends IBaseCommMapper<SeatStatusSwitchLog> {

    List<SeatStatusSwitchLog> selectLastSwitchStatusBySeatIdList(@Param("seatIdList") List<Long> seatIdList);
}