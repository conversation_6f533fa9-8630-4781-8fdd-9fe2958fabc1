package com.wanshifu.mapper;

import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.domain.po.ImConversationItem;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ImConversationItemMapper extends IBaseCommMapper<ImConversationItem> {

    List<ImConversationItem> selectLastMessageByConversationIdList(@Param("conversationIdList") List<Long> conversationIdList);

    List<CommonLabelValueResp> selectUnreadCountByConversationIdList(@Param("conversationIdList") List<Long> conversationIdList);

    List<ImConversationItem> selectLastMessageByConversationIdListAndVisitorAndAgent(@Param("conversationIdList") List<Long> conversationIdList);

    void updateMsgContentByKey(@Param("key") Long key, @Param("content") String content);

    Long countByConversationId(@Param("conversationIdList") List<Long> conversationIdList);

    List<ImConversationItem> selectLastListByConversationIdList(@Param("conversationId") Long conversationId);

    Integer updateConversationRead(@Param("conversationId") Long conversationId);
}