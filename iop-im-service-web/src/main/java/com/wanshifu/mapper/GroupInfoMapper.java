package com.wanshifu.mapper;

import com.wanshifu.iop.im.domain.po.GroupInfo;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GroupInfoMapper extends IBaseCommMapper<GroupInfo> {

    Integer selectEnableCountByGroupIdList(@Param("groupIdList") List<Integer> groupIdList);

    List<GroupInfo> getEnums(@Param("includeGroupIdList") List<Long> includeGroupIdList, @Param("tenantId") Long tenantId);

    Integer selectMaxSortByTenantId(@Param("tenantId") Long tenantId);

    Integer updateNowTime(@Param("groupId") Integer groupId, @Param("operatorId") Long operatorId);

    List<GroupInfo> selectEnableBySeatId(@Param("seatId") Long seatId);
}