package com.wanshifu.mapper;

import com.wanshifu.iop.im.domain.bo.seat.SeatOutUserIdMappingRespBo;
import com.wanshifu.iop.im.domain.po.UserRegisterInfo;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserRegisterInfoMapper extends IBaseCommMapper<UserRegisterInfo> {

    List<SeatOutUserIdMappingRespBo> selectOuterUserIdListByGroupIdList(@Param("groupIdList") List<Integer> enGroupIdList);
}