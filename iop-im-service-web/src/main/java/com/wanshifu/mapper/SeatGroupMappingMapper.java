package com.wanshifu.mapper;

import com.wanshifu.iop.im.api.req.group.RemoveGroupSeatReq;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.domain.bo.seat.SeatGroupMappingBo;
import com.wanshifu.iop.im.domain.po.SeatGroupMapping;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SeatGroupMappingMapper extends IBaseCommMapper<SeatGroupMapping> {

    Integer deleteMappingBySeatIdList(@Param("seatIdList") List<Long> seatIdList);

    List<CommonLabelValueResp> selectCountByGroupIdList(@Param("groupIdList") List<Integer> groupIdList);

    Integer batchUpdateStatusBySeatId(@Param("seatId") Long seatId, @Param("status") Integer status);

    Integer deleteByGroupIdAndSeatIdList(RemoveGroupSeatReq req);

    List<Integer> selectGroupIdBySeatId(@Param("seatId") Long seatId);

    List<SeatGroupMappingBo> selectSeatMappingByGroupId(@Param("groupId") Integer groupId);

    List<SeatGroupMappingBo> selectSeatMappingBySeatId(@Param("seatId") Long seatId);

    Integer updateDisableStatusByGroupId(@Param("groupId") Integer groupId);

    Integer updateEnableStatusByGroupIdAndSeatIdList(@Param("groupId") Integer groupId, @Param("seatIds") List<Long> seatIds, @Param("operatorId") Long operatorId);

    Integer updateEnableStatusBySeatIdAndGroupIdList(@Param("seatId") Long seatId, @Param("groupIdList") List<Integer> groupIdList, @Param("operatorId") Long operatorId);
}