package com.wanshifu.mapper;

import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.domain.bo.groupManage.GroupSeatListRespBo;
import com.wanshifu.iop.im.domain.bo.seat.MaxOnlineCountByGroupIdListRespBo;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SeatInfoMapper extends IBaseCommMapper<SeatInfo> {

    List<Long> getAccountIdByAllSeatInfo();

    Integer getLastSeatNo(@Param("tenantId") Long tenantId);

    Integer updateSeatOnlineStatus(@Param("seatId") Long seatId);

    List<Long> selectAccountIdListEnableSeatInfo();

    Integer updateSeatOfflineStatus(@Param("seatStatusEn") String seatStatusEn, @Param("seatIdList") List<Long> seatIdList);

    List<SeatInfo> getEnums(GetEnumsReq req);

    List<MaxOnlineCountByGroupIdListRespBo> selectMaxOnlineCountByGroupIdList(@Param("enGroupIdList") List<Integer> enGroupIdList);

    List<GroupSeatListRespBo> selectSeatInfoByGroupId(Integer groupId);
}