package com.wanshifu.mapper;

import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.domain.po.UserProblemClass;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;

import java.util.List;

public interface UserProblemClassMapper extends IBaseCommMapper<UserProblemClass> {

    List<UserProblemClass> getEnums(GetEnumsReq req);

    List<Long> checkNonexistent(CheckNonexistentReq req);
}