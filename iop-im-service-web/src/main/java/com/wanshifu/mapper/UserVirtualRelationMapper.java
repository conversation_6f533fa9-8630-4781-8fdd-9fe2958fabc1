package com.wanshifu.mapper;

import com.wanshifu.iop.im.domain.po.UserVirtualRelation;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface UserVirtualRelationMapper extends IBaseCommMapper<UserVirtualRelation> {

    void closeConversationUpdateBind(@Param("outerUserId") String outerUserId,
                                     @Param("virtualUserId") String virtualUserId,
                                     @Param("updateTime") Date updateTime);
}