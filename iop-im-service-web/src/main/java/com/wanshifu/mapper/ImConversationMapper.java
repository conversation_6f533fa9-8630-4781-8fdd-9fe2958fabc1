package com.wanshifu.mapper;

import com.wanshifu.iop.im.api.req.leftSidebar.FormatSearchGetLeaveConversationListReq;
import com.wanshifu.iop.im.api.resp.CommonLabelValueResp;
import com.wanshifu.iop.im.domain.bo.leftSidebar.FormatSearchConversationListBo;
import com.wanshifu.iop.im.domain.dto.distribute.SelectOnlineNumDto;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ImConversationMapper extends IBaseCommMapper<ImConversation> {


    List<ImConversation> getInfoByFromIdAndToIdAndStatus(@Param("fromId") String fromId, @Param("toId") String toId, @Param("status") String status);


    List<ImConversation> getInfoByFromIdAndToId(@Param("fromId") String fromId);

    List<CommonLabelValueResp> selectCountByToOuterUserId(@Param("toOutUserIdList") List<String> toOutUserIdList, @Param("toOuterUserType") String toOuterUserType);

    List<ImConversation> selectWaitProcessList(FormatSearchConversationListBo searchBo);

    List<ImConversation> selectLeaveList(FormatSearchGetLeaveConversationListReq searchBo);

    List<ImConversation> selectHistoryList(FormatSearchConversationListBo searchBo);

    List<SelectOnlineNumDto> selectOnlineNum(@Param("toOutUserIdList") List<String> toOuterUserIdList);

    Integer selectWaitingCountByToOuterUserId(@Param("toOutUserId") String toOutUserId);
}