package com.wanshifu.mapper.channel;

import com.wanshifu.iop.im.domain.bo.ConversationStyleConfigV2Bo;
import com.wanshifu.iop.im.domain.bo.channel.ConversationStyleConfigBo;
import com.wanshifu.iop.im.domain.po.channel.ConversationStyleConfig;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;

import java.util.List;

public interface ConversationStyleConfigMapper extends IBaseCommMapper<ConversationStyleConfig> {

    Integer batchInsertOrUpdateConversationStyleConfig(List<ConversationStyleConfigBo> conversationStyleConfigBoList);

    Integer insertOrUpdateConversationStyleConfig(ConversationStyleConfigV2Bo conversationStyleConfigBo);

    Integer batchInsertOrUpdateConversationStyleConfigV2(List<ConversationStyleConfigV2Bo> conversationStyleConfigBoList);
}