package com.wanshifu.mapper.channel;

import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.api.resp.channel.GetChannelConfigDraftListResp;
import com.wanshifu.iop.im.domain.bo.channel.ChannelBo;
import com.wanshifu.iop.im.domain.po.channel.ChannelConfig;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;

import java.util.List;

public interface ChannelConfigMapper extends IBaseCommMapper<ChannelConfig> {

    Integer insertOrUpdateChannelConfig(ChannelConfig channelConfig);

    Integer batchInsertOrUpdateChannelConfig(List<ChannelConfig> channelConfigList);

    Integer batchInsertOrUpdateChannelBo(List<ChannelBo> channelBoList);

    List<ChannelConfig> getEnums(GetEnumsReq req);

    List<Long> checkNonexistent(CheckNonexistentReq req);

    List<GetChannelConfigDraftListResp> getChannelConfigDraftList();
}