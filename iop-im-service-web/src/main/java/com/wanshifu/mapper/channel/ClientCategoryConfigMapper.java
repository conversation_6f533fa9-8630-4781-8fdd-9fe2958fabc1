package com.wanshifu.mapper.channel;

import com.wanshifu.iop.im.api.req.CheckNonexistentReq;
import com.wanshifu.iop.im.api.req.GetEnumsReq;
import com.wanshifu.iop.im.domain.bo.channel.ClientCategoryBo;
import com.wanshifu.iop.im.domain.po.channel.ClientCategoryConfig;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;

import java.util.List;

public interface ClientCategoryConfigMapper extends IBaseCommMapper<ClientCategoryConfig> {

    Integer insertOrUpdateClientCategoryConfig(ClientCategoryConfig clientCategoryConfig);

    Integer insertOrUpdateClientCategoryConfigBo(ClientCategoryBo clientCategoryBo);

    List<ClientCategoryConfig> getEnums(GetEnumsReq req);

    List<Long> checkNonexistent(CheckNonexistentReq req);
}