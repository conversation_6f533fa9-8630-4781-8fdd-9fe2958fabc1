package com.wanshifu.mapper.channel;

import com.wanshifu.iop.im.domain.po.channel.ClientChannelConfigDraft;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;

import java.util.List;

public interface ClientChannelConfigDraftMapper extends IBaseCommMapper<ClientChannelConfigDraft> {

    Integer insertOrUpdateClientCategoryDraft(ClientChannelConfigDraft clientChannelConfigDraft);

    Integer batchUpdateClientChannelConfigDraft(List<ClientChannelConfigDraft> clientChannelConfigDraftList);
}