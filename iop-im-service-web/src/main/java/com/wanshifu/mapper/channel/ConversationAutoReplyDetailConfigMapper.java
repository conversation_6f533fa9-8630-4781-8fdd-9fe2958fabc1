package com.wanshifu.mapper.channel;

import com.wanshifu.iop.im.domain.bo.SelectConversationAutoReplyDetailConfigReqBo;
import com.wanshifu.iop.im.domain.bo.SelectConversationAutoReplyDetailConfigRespBo;
import com.wanshifu.iop.im.domain.bo.channel.ConversationAutoReplyDetailConfigBo;
import com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyDetailConfig;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;

import java.util.List;

public interface ConversationAutoReplyDetailConfigMapper extends IBaseCommMapper<ConversationAutoReplyDetailConfig> {
    Integer insertOrUpdateConversationAutoReplyDetailConfig(ConversationAutoReplyDetailConfig conversationAutoReplyDetailConfig);

    List<SelectConversationAutoReplyDetailConfigRespBo> batchSelectConfig(SelectConversationAutoReplyDetailConfigReqBo autoReplyDetailConfigReqBo);
}