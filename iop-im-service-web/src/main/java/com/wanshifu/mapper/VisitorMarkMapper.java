package com.wanshifu.mapper;

import com.wanshifu.iop.im.domain.po.VisitorMark;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

public interface VisitorMarkMapper extends IBaseCommMapper<VisitorMark> {

    Integer deleteByOuterUserIdAndAccount(@Param("outerUserId") String outerUserId, @Param("accountId") String accountId, @Param("accountClass") String accountClass);

    Integer deleteSingleByOuterUserIdAndAccount(@Param("outerUserId") String outerUserId, @Param("accountId") String accountId, @Param("accountClass") String accountClass, @Param("removeLabel") String removeLabel);
}