package com.wanshifu.mapper;

import com.wanshifu.iop.im.domain.bo.seat.SeatStatusFunctionMappingRespBo;
import com.wanshifu.iop.im.domain.po.SeatStatusFunctionMapping;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SeatStatusFunctionMappingMapper extends IBaseCommMapper<SeatStatusFunctionMapping> {

    Integer deleteAllBySeatStatusId(@Param("seatStatusId") Integer seatStatusId, @Param("seatFunctionIdList") List<Integer> seatFunctionIdList, @Param("operatorId") Long operatorId);

    List<SeatStatusFunctionMappingRespBo> getSeatStatusByFunctionEnAndFunctionType(@Param("seatFunctionEn") String seatFunctionEn, @Param("seatFunctionType") String seatFunctionType);
}