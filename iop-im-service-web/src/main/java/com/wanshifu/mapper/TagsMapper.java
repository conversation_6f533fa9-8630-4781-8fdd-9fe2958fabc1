package com.wanshifu.mapper;

import com.wanshifu.iop.im.domain.po.Tags;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TagsMapper extends IBaseCommMapper<Tags> {

    Integer selectExistById(@Param("tagId") Integer tagId);

    List<Tags> getEnums(@Param("scene") String scene, @Param("includeEnumsValueIdList") List<Long> includeEnumsValueIdList);
}