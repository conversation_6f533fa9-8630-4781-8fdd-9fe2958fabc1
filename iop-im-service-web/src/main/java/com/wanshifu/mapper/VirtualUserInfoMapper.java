package com.wanshifu.mapper;

import com.wanshifu.iop.im.domain.po.VirtualUserInfo;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VirtualUserInfoMapper extends IBaseCommMapper<VirtualUserInfo> {

    Integer batchUpdateVirtualStatus(@Param("virtualInfoIdList") List<Long> virtualInfoIdList, @Param("status") String status);
}