package com.wanshifu.sdk;

import com.wanshifu.iop.im.domain.bo.innerAccountService.GetThirdPartyIpLocationRespBo;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 内部账户服务api
 * <AUTHOR>
 * @date： 2025-07-25 17:15:26
 */
@FeignClient(
        value = "account-service",
        path = "/", configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.account-service.url}"
)
public interface InnerAccountServiceApi {

    @GetMapping ("/ip-info/getThirdPartyIpLocation")
    GetThirdPartyIpLocationRespBo getThirdPartyIpLocation(@RequestParam("ip") String ip);
}
