package com.wanshifu.sdk.config;

import feign.Response;
import feign.Util;
import feign.codec.DecodeException;
import feign.codec.Decoder;

import java.io.IOException;
import java.lang.reflect.Type;

import static java.lang.String.format;


public class LongDecoder implements Decoder {

    @Override
    public Object decode(Response response, Type type) throws IOException {
        Response.Body body = response.body();
        if (body == null) {
            return null;
        }
        if (Long.class.equals(type)) {
            return Long.parseLong(Util.toString(body.asReader()));
        }
        throw new DecodeException(format("%s is not a type supported by this decoder.", type));
    }
}