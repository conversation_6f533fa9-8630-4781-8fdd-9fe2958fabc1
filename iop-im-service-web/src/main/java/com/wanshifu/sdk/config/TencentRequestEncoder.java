package com.wanshifu.sdk.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import feign.RequestTemplate;
import feign.codec.EncodeException;
import feign.codec.Encoder;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.util.Date;

public class TencentRequestEncoder implements Encoder {


    private static final String CONTENT_TYPE = "Content-Type";

    @Override
    public void encode(Object object, Type bodyType, RequestTemplate template) {
        if (object == null) return;
        // 强制设置Content-Type为application/json
        template.header(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        // 使用FastJSON序列化
        String body = JSON.toJSONString(object,
            SerializerFeature.WriteEnumUsingToString,
            SerializerFeature.DisableCircularReferenceDetect);
        template.body(body);
    }

    private String doCreateFormString(Object object) {
        StringBuilder stringBuilder = new StringBuilder();
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(object.getClass());
        boolean isFirst = true;
        try {
            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                String propertyName = propertyDescriptor.getName();
                if ("class".equals(propertyName)) continue;
                Method readMethod = propertyDescriptor.getReadMethod();
                if (readMethod != null) {
                    Object value = readMethod.invoke(object);
                    if (value == null) {
                        continue;
                    }
                    if (!isFirst) {
                        stringBuilder.append("&");
                    } else {
                        isFirst = false;
                    }
                    stringBuilder.append(propertyName);
                    stringBuilder.append("=");
                    if (propertyDescriptor.getPropertyType().isAssignableFrom(Date.class)) {
                        stringBuilder.append(((Date) value).getTime());
                    } else {
                        stringBuilder.append(URLEncoder.encode(String.valueOf(value), "utf-8"));
                    }
                }
            }
        } catch (Exception e) {
            throw new EncodeException("form data type serialize fail!", e);
        }
        return stringBuilder.toString();
    }

}
