package com.wanshifu.sdk.config;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.iop.im.api.resp.tencent.TencentResultResp;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import feign.Response;
import feign.codec.Decoder;
import lombok.Data;
import org.apache.commons.io.IOUtils;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class TencentResponseDecoder implements Decoder {

    private final static Map<Type, ParameterizedTypeImpl> TYPE_PARAMETERIZED_CACHE = new ConcurrentHashMap<>();

    private final static Object LOCK = new Object();

    @Override
    public Object decode(Response response, Type type) {
        if (response.body() == null) return null;
        TencentResultResp tencentResultResp;
        ParameterizedTypeImpl parameterizedType = this.getParameterizedType(type);
        String content = null;
        try {
            InputStream inputStream = response.body().asInputStream();
            content = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            tencentResultResp = doDecode(content, parameterizedType);

        } catch (Exception e) {
            throw new ApiAccessException(response.request().url(), content, e);
        }
        if (tencentResultResp == null) {
            throw new ApiAccessException(response.request().url(), "");
        }
        return tencentResultResp;
    }

    private ParameterizedTypeImpl getParameterizedType(Type type) {
        ParameterizedTypeImpl parameterizedType = TYPE_PARAMETERIZED_CACHE.get(type);
        if (parameterizedType == null) {
            synchronized (LOCK) {
                parameterizedType = TYPE_PARAMETERIZED_CACHE.get(type); //double check here
                if (parameterizedType == null) {
                    parameterizedType = ParameterizedTypeImpl.make(ResponseEntity.class, new Type[]{type}, null);
                    TYPE_PARAMETERIZED_CACHE.put(type, parameterizedType);
                }
            }
        }
        return parameterizedType;
    }

    private TencentResultResp doDecode(String content, ParameterizedTypeImpl parameterizedType) throws Exception {
        return JSONObject.parseObject(content, TencentResultResp.class);
    }


    @Data
    static class ResponseEntity<T> {

        static final ResponseEntity EMPTY = new ResponseEntity();

        private T data;
    }

}
