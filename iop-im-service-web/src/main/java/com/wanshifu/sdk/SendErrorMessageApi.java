package com.wanshifu.sdk;

import com.wanshifu.iop.im.domain.bo.ErrorInfoBo;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @create 2022/8/16 18:43
 * @Desc 推送
 */
@FeignClient(
        value = "feishu",
        url = "https://open.feishu.cn",
        path = "/open-apis/bot/v2/hook",
        configuration = {DefaultDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class}
)
public interface SendErrorMessageApi {

    /**
     * 智能运营告警群机器人发送告警信息
     */
    @PostMapping("/ad85e3d9-68be-4cb2-af6e-53ebafdd8f62")
    void sendError(@Valid @RequestBody ErrorInfoBo errorInfoBo);
}
