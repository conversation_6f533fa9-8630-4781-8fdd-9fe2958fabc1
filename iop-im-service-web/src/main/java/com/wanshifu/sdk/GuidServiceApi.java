package com.wanshifu.sdk;

import com.wanshifu.sdk.config.LongDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(value = "guid-service", path = "", configuration = {LongDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class}, url = "${wanshifu.guid.url}")
public interface GuidServiceApi {

    /**
     * 获取guid
     * */
    @GetMapping("/")
    Long getGuid();
}
