package com.wanshifu.sdk;

import com.wanshifu.iop.im.api.req.AccountImportReq;
import com.wanshifu.iop.im.api.req.tencent.AddMemberToGroupTencentReq;
import com.wanshifu.iop.im.api.req.tencent.CreateGroupReq;
import com.wanshifu.iop.im.api.req.tencent.DelMemberFromGroupTencentReq;
import com.wanshifu.iop.im.api.req.tencent.DeleteFriendShipReq;
import com.wanshifu.iop.im.api.req.tencent.DestroyGroupReq;
import com.wanshifu.iop.im.api.req.tencent.GetOnlineStatusReq;
import com.wanshifu.iop.im.api.req.tencent.ImportMsgToGroupReq;
import com.wanshifu.iop.im.api.req.tencent.ImportMsgToSingleReq;
import com.wanshifu.iop.im.api.req.tencent.ModifyMsgSingleTencentReq;
import com.wanshifu.iop.im.api.req.tencent.SendMsgGroupReq;
import com.wanshifu.iop.im.api.req.tencent.SendMsgPersonalReq;
import com.wanshifu.iop.im.api.resp.tencent.TencentResultResp;
import com.wanshifu.sdk.config.TencentRequestEncoder;
import com.wanshifu.sdk.config.TencentResponseDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

@FeignClient(value = "tencent-service",
        path = "",
        configuration = {TencentRequestEncoder.class, TencentResponseDecoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.tencent.url}")
public interface TencentServiceApi {

    /**
     * 导入单个账号
     * sdkappid: 创建应用时即时通信 IM 控制台分配的 SDKAppID
     * identifier: 必须为 App 管理员账号
     * usersig: App 管理员账号生成的签名
     * random: 请输入随机的32位无符号整数，取值范围0 - **********
     * contenttype: 请求格式固定值为json
     */
    @PostMapping("/v4/im_open_login_svc/account_import")
    TencentResultResp accountImport(
            @Valid @RequestBody AccountImportReq accountImportReq,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );

    /**
     * 创建群组
     * */
    @PostMapping("/v4/group_open_http_svc/create_group")
    TencentResultResp createGroup(
            @RequestBody CreateGroupReq createGroupReq,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );

    /**
     * 发送消息 单聊
     * 管理员指定某一账号向其他账号发送消息
     * 适用用户单聊对象给用户发送消息
     * */
    @PostMapping("/v4/openim/sendmsg")
    TencentResultResp sendMsgPersonal(
            @RequestBody Object sendMsgPersonalReq,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );

    /**
     * 发送消息 群聊
     * 管理员指定某一账号向指定群中发送消息
     * */
    @PostMapping("/v4/group_open_http_svc/send_group_msg")
    TencentResultResp sendMsgGroup(
            @RequestBody SendMsgGroupReq sendMsgGroupReq,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );

    /**
     * 批量向群内导入消息
     * */
    @PostMapping("/v4/group_open_http_svc/import_group_msg")
    TencentResultResp importMsgToGroup(
            @RequestBody ImportMsgToGroupReq importMsgToGroupReq,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );


    /**
     * 批量向单聊内导入消息
     * */
    @PostMapping("/v4/openim/importmsg")
    TencentResultResp importMsgToSingle(
            @RequestBody ImportMsgToSingleReq importMsgToSingleReq,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );


    /**
     * 解散群聊
     * */
    @PostMapping("/v4/group_open_http_svc/destroy_group")
    TencentResultResp destroyGroup(
            @RequestBody DestroyGroupReq DestroyGroupReq,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );

    /**
     * 查询用户在线状态
     * */
    @PostMapping("/v4/openim/query_online_status")
    TencentResultResp getOnlineStatus(
            @RequestBody GetOnlineStatusReq req,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );

    /**
     * 向群中添加用户
     * */
    @PostMapping("/v4/group_open_http_svc/add_group_member")
    TencentResultResp addGroupMember(
            @RequestBody AddMemberToGroupTencentReq req,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );

    /**
     * 从群中移除用户
     * */
    @PostMapping("/v4/group_open_http_svc/delete_group_member")
    TencentResultResp deleteGroupMember(
            @RequestBody DelMemberFromGroupTencentReq req,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );


    /**
     * 修改历史单聊消息
     * */
    @PostMapping("/v4/openim/modify_c2c_msg")
    TencentResultResp modifyMsgSingle(
            @RequestBody ModifyMsgSingleTencentReq req,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );

    /**
     * 删除好友关系
     * */
    @PostMapping("/v4/sns/friend_delete")
    TencentResultResp deleteFriendShip(
            @RequestBody DeleteFriendShipReq req,
            @RequestParam("sdkappid") String sdkappid,
            @RequestParam("identifier") String identifier,
            @RequestParam("usersig") String usersig,
            @RequestParam("random") String random,
            @RequestParam("contenttype") String contenttype
    );

}
