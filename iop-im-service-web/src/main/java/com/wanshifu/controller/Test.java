package com.wanshifu.controller;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.List;

public class Test {

    // Lua 脚本：批量获取多个zset的第一个元素（带分数）
    private static final String LUA_SCRIPT =
            "local keys = {'zset1', 'zset2', 'zset3'} " +
                    "local result = {} " +
                    "for i, key in ipairs(keys) do " +
                    "    local elem = redis.call('ZRANGE', key, 0, 0, 'WITHSCORES') " +
                    "    result[i] = {key, elem} " +
                    "end " +
                    "return result";

    public static void main(String[] args) {
        // 1. 创建Redis连接池
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(10);
        poolConfig.setMaxIdle(5);

        try (JedisPool jedisPool = new JedisPool(  poolConfig, "iop-test-redis.wanshifu.com", 6379, 2000,"tRedisW135",0);
             Jedis jedis = jedisPool.getResource()) {

            // 2. 准备测试数据（仅用于演示）
            prepareTestData(jedis);

            // 3. 方法一：直接使用Lua脚本
            System.out.println("=== 方法一：使用Lua脚本 ===");
            queryWithLuaScript(jedis);

        }
    }

    /**
     * 准备测试数据
     */
    private static void prepareTestData(Jedis jedis) {
        // 清空可能存在的旧数据
        jedis.del("zset1", "zset2", "zset3", "zset4");

        // 添加测试数据
        jedis.zadd("zset1", 1.0, "123456");
        jedis.zadd("zset1", 2.0, "member1-2");

        jedis.zadd("zset2", 5.0, "member2-1");
        jedis.zadd("zset2", 3.0, "member2-2");
        // 这个分数更低，应该是第一个

        jedis.zadd("zset3", 10.0, "member3-1");
    }

    /**
     * 方法一：使用Lua脚本查询多个ZSET的第一个元素
     */
    private static void queryWithLuaScript(Jedis jedis) {
        // 执行Lua脚本
        Object result = jedis.eval(LUA_SCRIPT);

        // 解析结果（Lua脚本返回的是嵌套列表结构）
        @SuppressWarnings("unchecked")
        List<List<Object>> rawResults = (List<List<Object>>) result;

        for (List<Object> item : rawResults) {
            String key = (String) item.get(0);
            @SuppressWarnings("unchecked")
            List<String> elem = (List<String>) item.get(1);

            if (elem.isEmpty()) {
                System.out.printf("ZSET %s 为空%n", key);
            } else {
                // 偶数索引是成员，奇数索引是分数（因为WITHSCORES返回的是交替的成员和分数）
                String member = elem.get(0);
                String score = elem.get(1);
                System.out.printf("ZSET %s 的第一个元素: member=%s, score=%s%n",
                        key, member, score);
            }
        }
    }


}