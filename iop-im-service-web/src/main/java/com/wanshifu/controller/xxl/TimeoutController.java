package com.wanshifu.controller.xxl;


import com.wanshifu.service.DistributeService;
import com.wanshifu.service.XxlService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/23 11:02
 * @description TODO
 */
@RestController
@RequestMapping("/xxl")
public class TimeoutController {

    @Resource
    private XxlService xxlService;

    @Resource
    private DistributeService distributeService;

    /**
     * 定时任务超时
     * */

    @GetMapping("/timeout")
    public Integer timeout(@RequestParam(value = "id", required = true) Long id) {
        return xxlService.channelAndConversationTimeout(id);
    }

    /**
     * 队列分配
     * */
    @GetMapping("/distribute")
    public Integer distribute() {
        return distributeService.distribute();
    }
}
