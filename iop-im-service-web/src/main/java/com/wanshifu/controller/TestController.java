package com.wanshifu.controller;

import com.alibaba.fastjson.JSON;
import com.wanshifu.constant.TencentConstant;
import com.wanshifu.iop.im.api.req.group.DistributeEnGroupReq;
import com.wanshifu.iop.im.api.req.group.DistributeGroupSeatReq;
import com.wanshifu.iop.im.api.req.group.QueueGroupEnQueueReq;
import com.wanshifu.iop.im.api.req.rightSidebar.BaseOnlineConversationReq;
import com.wanshifu.iop.im.api.req.seat.ConversationFlowSeatStatusUpdateReq;
import com.wanshifu.iop.im.domain.bo.*;
import com.wanshifu.iop.im.domain.bo.channel.msg.ToArtificialTypesBo;
import com.wanshifu.iop.im.domain.bo.conversation.XxlTimeoutReqBo;
import com.wanshifu.iop.im.domain.bo.innerAccountService.GetThirdPartyIpLocationRespBo;
import com.wanshifu.iop.im.domain.bo.socketBase.SendImWebSocketMessageBo;
import com.wanshifu.iop.im.domain.bo.tencentBase.UpdateMsgSingleBo;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.manager.queueManager.QueueManager;
import com.wanshifu.manager.socketBase.SocketBaseManager;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.mq.producer.SendImWebSocketMessageProducer;
import com.wanshifu.repository.ImConversationRepository;
import com.wanshifu.repository.SeatInfoRepository;
import com.wanshifu.repository.VirtualUserInfoRepository;
import com.wanshifu.sdk.InnerAccountServiceApi;
import com.wanshifu.service.DistributeService;
import com.wanshifu.service.ImInterService;
import com.wanshifu.service.LeftSidebarService;
import com.wanshifu.service.SeatService;
import com.wanshifu.utils.TencentUtil;
import org.slf4j.MDC;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
public class TestController {

    @Resource
    private TencentManager tencentManager;

    @Resource
    private ImInterService imInterService;

    @PostMapping("/test")
    public String test11(){
        System.out.println("traceId: "+MDC.get("trace"));
//        Integer result = imInterService.sendAIMsgToPersonal(req);

        return "1111111";
    }

    @PostMapping("/createGroup")
    public String test(@Valid @RequestBody CreateGroupReqBo req){

        tencentManager.createGroup(req);

        return "createGroup";
    }

    @GetMapping("/gensign")
    public String gensign(){
        TencentUtil tencentUtil = new TencentUtil(TencentConstant.IM_SDKAPPID, TencentConstant.IM_SECRET);
        String s = tencentUtil.getUserSig("administrator", 86400 * 60L);
        return s;
    }

    @Resource
    private VirtualUserInfoRepository virtualUserInfoRepository;

    @GetMapping("/test2")
    public String test(){
        return JSON.toJSONString(virtualUserInfoRepository.getNotUsedVirtualInfo());
    }

    @PostMapping("/test3")
    public String test3(@Valid @RequestBody DestroyGroupBo req){

        tencentManager.destroyGroup(req);

        return "1111111";
    }

    @PostMapping("/test4")
    public String test4(@Valid @RequestBody UpdateMsgSingleBo req){
        tencentManager.updateMsgSingle(req);
        return "1";
    }

    @Resource
    private QueueManager queueManager;

    /**
     * 入队
     * @param req
     */
    @PostMapping("/enqueue")
    public void enqueue(@Valid @RequestBody QueueGroupEnQueueReq req){
        queueManager.groupEnQueue(req);
    }

    @Resource
    private DistributeService distributeService;

    /**
     * 进组
     * @param req
     */
    @PostMapping("/engroup")
    public Integer engroup(@Valid @RequestBody DistributeEnGroupReq req){
        return distributeService.distributeEnGroup(req);
    }

    /**
     * 发送询前表单
     * */
    @PostMapping("/sendform")
    public Integer sendPreForm(@Valid @RequestBody ToArtificialMsgBo req){
        return imInterService.sendPreForm(req);
    }

    @PostMapping("/test5")
    public Integer toArtificialWelcomeMsg( @Valid @RequestBody ToArtificialTypesBo req){
        return imInterService.toArtificialWelcomeMsg(req);
    }

    @Resource
    private SocketBaseManager socketBaseManager;

    @Resource
    private SendImWebSocketMessageProducer sendImWebSocketMessageProducer;

    /**
     * mq消费器
     */
    @PostMapping("/consumerMessage")
    public void consumerMessage(@Valid @RequestBody SendImWebSocketMessageBo req){
//        sendImWebSocketMessageProducer.sendImWebSocketMessage(req);
        socketBaseManager.consumerMessage(req);
    }

    /**
     * 待处理会话更新
     */
    @PostMapping("/sendWaitConversationUpdate")
    public void sendWaitConversationUpdate(@Valid @RequestParam(value = "conversationId", required = false) Long conversationId, @RequestParam(value = "seatOuterUserId", required = false) String seatOuterUserId){
        socketBaseManager.sendWaitConversationUpdate(conversationId);
    }

    /**
     * 坐席新增会话
     */
    @PostMapping("/sendSeatAddConversation")
    public void sendSeatAddConversation(@Valid @RequestParam(value = "seatId", required = false) Long seatId, @RequestParam(value = "seatOuterUserId", required = false) String seatOuterUserId){
        socketBaseManager.sendSeatAddConversation(seatId, seatOuterUserId);
    }

    /**
     * 会话关闭
     */
    @PostMapping("/sendCloseConversation")
    public void sendCloseConversation(@Valid @RequestParam(value = "conversationId", required = false) Long conversationId){
        socketBaseManager.sendCloseConversation(conversationId);
    }

    /**
     * 临近超时未回复
     */
    @PostMapping("/sendNearTimeoutMessage")
    public void sendNearTimeoutMessage(@Valid @RequestParam(value = "conversationId", required = false) Long conversationId){
        socketBaseManager.sendNearTimeoutMessage(conversationId);
    }

    /**
     * 超时未回复
     */
    @PostMapping("/sendTimeoutMessage")
    public void sendTimeoutMessage(@Valid @RequestParam(value = "conversationId", required = false) Long conversationId){
        socketBaseManager.sendTimeoutMessage(conversationId);
    }

    @Resource
    private SeatInfoRepository seatInfoRepository;

    /**
     * 坐席状态切换
     */
    @PostMapping("/seatStatusSwitch")
    public void seatStatusSwitch(@Valid @RequestParam(value = "seatId", required = true) Long seatId, @RequestParam(value = "switchStatus", required = true) String switchStatus){
        SeatInfo seatInfo = seatInfoRepository.selectByPrimaryKey(seatId);
        distributeService.seatStatusSwitch(seatInfo, switchStatus);
    }
    @Resource
    private InnerAccountServiceApi innerAccountServiceApi;

    @GetMapping("testip")
    public void testip(@Valid @RequestParam(value = "ip", required = true) String ip){

        GetThirdPartyIpLocationRespBo thirdPartyIpLocation = innerAccountServiceApi.getThirdPartyIpLocation(ip);
        System.out.println(JSON.toJSON(thirdPartyIpLocation));
    }

    @PostMapping("/test6")
    public void test6(@Valid @RequestBody XxlTimeoutReqBo req){
        Integer integer = imInterService.addTimeoutTask(req);
    }

    @Resource
    private LeftSidebarService leftSidebarService;

    /**
     * 会话分配
     */
    @PostMapping("/conversationAssign")
    public void conversationAssign(@Valid @RequestBody ConversationAssignBo req){
        leftSidebarService.conversationAssign(req);
    }

    /**
     * 测试关闭会话
     * @param req
     */
    @PostMapping("/closeConversation")
    public void closeConversation(@Valid @RequestBody SendImWebSocketMessageBo req){
        leftSidebarService.closeConversation(req);
    }

    /**
     * 历史会话变成留言会话
     * @param req
     */
    @PostMapping("/historyToLeaveConversation")
    public void historyToLeaveConversation(@Valid @RequestBody SendImWebSocketMessageBo req){
        leftSidebarService.historyToLeaveConversation(req);
    }

    /**
     * 更新会话已读状态
     */
    @PostMapping("/updateConversationRead")
    public Integer updateConversationRead(@Valid @RequestBody SendImWebSocketMessageBo req){
        return leftSidebarService.updateConversationRead(req);
    }

    /**
     * 更新会话已读状态
     */
    @PostMapping("/test7")
    public void updateConversationRead(@Valid @RequestBody DistributeGroupSeatReq req){
        distributeService.distributeGroupSeat(req);
    }

    /**
     * 更新会话已读状态
     */
    @PostMapping("/test8")
    public void sendAIMsgToPersonal(@Valid @RequestBody AIWorkflowExecuteResultMsgBo req){
        imInterService.sendAIMsgToPersonal(req);
    }

    @Resource
    private SeatService seatService;

    /**
     * 会话流转更新坐席状态
     */
    @PostMapping("/conversationFlowSeatStatusUpdate")
    public Integer conversationFlowSeatStatusUpdate(@Valid @RequestBody ConversationFlowSeatStatusUpdateReq req){
        return seatService.conversationFlowSeatStatusUpdate(req);
    }

    /**
     * 更新会话已读状态
     */
    @PostMapping("/test9")
    public void test9(@Valid @RequestBody ToArtificialTypesBo req){
        imInterService.toArtificialWelcomeMsg(req);
    }
}
