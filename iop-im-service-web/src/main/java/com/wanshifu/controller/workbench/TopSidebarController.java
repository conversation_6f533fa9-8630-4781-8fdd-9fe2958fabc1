package com.wanshifu.controller.workbench;

import com.wanshifu.iop.im.api.resp.topSidebar.*;
import com.wanshifu.iop.im.api.req.topSidebar.*;
import com.wanshifu.iop.im.service.api.workbench.TopSidebarServiceApi;
import com.wanshifu.service.TopSidebarService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 顶边栏
 * <AUTHOR>
 * @date： 2025-07-24 11:55:32
 */
@RestController
@RequestMapping("/topSidebar/")
public class TopSidebarController implements TopSidebarServiceApi {

    @Resource
    private TopSidebarService topSidebarService;

    /**
     * 在线坐席接线情况列表
     */
    @Override
    @PostMapping("seatConnectionInfoList")
    public List<SeatConnectionInfoListResp> seatConnectionInfoList(@Valid @RequestBody SeatConnectionInfoListReq req) {
        return topSidebarService.seatConnectionInfoList(req);
    }
}
