package com.wanshifu.controller.workbench;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.leftSidebar.*;
import com.wanshifu.iop.im.api.resp.leftSidebar.*;
import com.wanshifu.iop.im.domain.bo.CoreActiveConversationRespBo;
import com.wanshifu.iop.im.domain.bo.HistoryConversationItemSenderInfoRespBo;
import com.wanshifu.iop.im.service.api.workbench.LeftSidebarServiceApi;
import com.wanshifu.service.LeftSidebarService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 左侧边栏
 * <AUTHOR>
 * @date： 2025-06-04 15:26:32
 */
@RestController
@RequestMapping("/leftSidebar/")
public class LeftSidebarController implements LeftSidebarServiceApi {

    @Resource
    private LeftSidebarService leftSidebarService;

    /**
     * 公共枚举接口
     */
    @Override
    @PostMapping("/enums")
    public LeftSidebarEnumsResp enums(){
        return leftSidebarService.enums();
    }

    /**
     * 坐席基本信息
     */
    @Override
    @PostMapping("/onlineSeatInfo")
    public OnlineSeatInfoResp onlineSeatInfo(@Valid @RequestBody OnlineSeatInfoReq req){
        return leftSidebarService.onlineSeatInfo(req);
    }

    /**
     * 坐席状态切换
     */
    @Override
    @PostMapping("/onlineSeatStatusSwitch")
    public Integer onlineSeatStatusSwitch(@Valid @RequestBody OnlineSeatStatusSwitchReq req){
        return leftSidebarService.onlineSeatStatusSwitch(req);
    }

    /**
     * 待处理列表
     */
    @Override
    @PostMapping("/waitProcessConversationList")
    public List<GetWaitProcessConversationListResp> waitProcessConversationList(@Valid @RequestBody GetWaitProcessConversationListReq req){
        return leftSidebarService.waitProcessConversationList(req);
    }

    /**
     * 访客人工打标
     */
    @Override
    @PostMapping("/seatMarkForVisitor")
    public Integer seatMarkForVisitor(@Valid @RequestBody SeatMarkForVisitorReq req){
        return leftSidebarService.seatMarkForVisitor(req);
    }

    /**
     * 访客标签移除
     */
    @Override
    @PostMapping("/seatUnmarkForVisitor")
    public Integer seatUnmarkForVisitor(@Valid @RequestBody SeatUnmarkForVisitorReq req){
        return leftSidebarService.seatUnmarkForVisitor(req);
    }

    /**
     * 留言会话列表
     */
    @Override
    @PostMapping("/leaveConversationList")
    public SimplePageInfo<GetLeaveConversationListResp> leaveConversationList(@Valid @RequestBody GetLeaveConversationListReq req){
        return leftSidebarService.leaveConversationList(req);
    }

    /**
     * 认领留言
     */
    @Override
    @PostMapping("/claimLeaveConversation")
    public Integer claimLeaveConversation(@Valid @RequestBody ClaimLeaveConversationReq req){
        return leftSidebarService.claimLeaveConversation(req);
    }

    /**
     * 历史会话列表
     */
    @Override
    @PostMapping("/historyConversationList")
    public SimplePageInfo<GetHistoryConversationListResp> historyConversationList(@Valid @RequestBody GetHistoryConversationListReq req){
        return leftSidebarService.historyConversationList(req);
    }

    /**
     * 历史会话激活
     */
    @Override
    @PostMapping("/historyConversationActive")
    public Integer historyConversationActive(@Valid @RequestBody HistoryConversationActiveReq req){
        return leftSidebarService.historyConversationActive(req);
    }

    /**
     * 历史会话消息记录
     */
    @Override
    @PostMapping("/getHistoryConversationItemLogs")
    public SimplePageInfo<GetHistoryConversationItemResp> getHistoryConversationItemLogs(@Valid @RequestBody GetHistoryConversationItemListReq req){
        return leftSidebarService.getHistoryConversationItemLogs(req);
    }

    /**
     * 群组新增客消息历史发送过的消息对象
     */
    @PostMapping("/addHistorySenderMemberToGroup")
    public List<HistoryConversationItemSenderInfoRespBo> addHistorySenderMemberToGroup(@Valid @RequestBody CoreActiveConversationRespBo coreActiveConversationRespBo){
        return leftSidebarService.addHistorySenderMemberToGroup(coreActiveConversationRespBo);
    }

    /**
     * 导入历史会话
     */
    @PostMapping("/importHistoryConversationByVisitor")
    public void importHistoryConversationByVisitor(@Valid @RequestBody CoreActiveConversationRespBo coreActiveConversationRespBo){
        leftSidebarService.importHistoryConversationByVisitor(coreActiveConversationRespBo);
    }
}
