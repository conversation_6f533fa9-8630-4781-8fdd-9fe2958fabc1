package com.wanshifu.controller.workbench;

import com.wanshifu.iop.im.api.req.knowledgeBase.*;
import com.wanshifu.iop.im.api.req.rightSidebar.*;
import com.wanshifu.iop.im.api.resp.CommonLabelValueLevelResp;
import com.wanshifu.iop.im.api.resp.knowledgeBase.KnowledgeBaseDataListResp;
import com.wanshifu.iop.im.api.resp.rightSidebar.*;
import com.wanshifu.iop.im.service.api.workbench.RightSidebarServiceApi;
import com.wanshifu.service.KnowledgeBaseManageService;
import com.wanshifu.service.RightSidebarService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 右侧边栏
 * <AUTHOR>
 * @date： 2025-06-04 15:26:32
 */
@RestController
@RequestMapping("/rightSidebar/")
public class RightSidebarController implements RightSidebarServiceApi {

    @Resource
    private RightSidebarService rightSidebarService;

    @Resource
    private KnowledgeBaseManageService knowledgeBaseManageService;

    /**
     * 访客信息
     */
//    @Override
    @PostMapping("visitorInfo")
    public List<GetVisitorInfoResp> visitorInfo(@Valid @RequestBody GetVisitorInfoReq req) {
        return rightSidebarService.visitorInfo(req);
    }

    /**
     * 表单枚举
     */
    //    @Override
    @PostMapping("fromEnums")
    public GetFromEnumsResp fromEnums(@Valid @RequestBody BaseOnlineConversationReq req) {
        return rightSidebarService.fromEnums(req);
    }

    /**
     * 通过业务小结模板id获取问题类型枚举
     */
    //    @Override
    @PostMapping("getProblemTypeEnums")
    public List<CommonLabelValueLevelResp> getProblemTypeEnums(@Valid @RequestBody GetProblemTypeEnumsReq req) {
        return rightSidebarService.getProblemTypeEnums(req);
    }

    /**
     * 业务小结提交表单
     */
    //    @Override
    @PostMapping("businessSummarySubmitForm")
    public Integer businessSummarySubmitForm(@Valid @RequestBody BusinessSummarySubmitFormReq req) {
        return rightSidebarService.businessSummarySubmitForm(req);
    }

    /**
     * 创建工单提交表单
     */
    //    @Override
    @PostMapping("createOrderSubmitForm")
    public Integer createOrderSubmitForm(@Valid @RequestBody CreateOrderSubmitFormReq req) {
        return rightSidebarService.createOrderSubmitForm(req);
    }

    /**
     * 联络动态 会话访客历程
     */
    //    @Override
    @PostMapping("conversationVisitorHistoryLogs")
    public List<ConversationVisitorHistoryLogsResp> conversationVisitorHistoryLogs(@Valid @RequestBody ConversationVisitorHistoryLogsReq req) {
        return rightSidebarService.conversationVisitorHistoryLogs(req);
    }

    /**
     * 历史会话明细
     */
    //    @Override
    @PostMapping("conversationHistoryMessageItemLogs")
    public ConversationHistoryMessageItemLogsResp conversationHistoryMessageItemLogs(@Valid @RequestBody ConversationHistoryMessageItemLogsReq req) {
        return rightSidebarService.conversationHistoryMessageItemLogs(req);
    }

    /**
     * 对话信息
     */
    //    @Override
    @PostMapping("conversationBaseInfo")
    public ConversationBaseInfoResp conversationBaseInfo(@Valid @RequestBody ConversationBaseInfoReq req) {
        return rightSidebarService.conversationBaseInfo(req);
    }

    /**
     * 对话访问信息
     */
    //    @Override
    @PostMapping("conversationVisitInfo")
    public ConversationVisitInfoResp conversationVisitInfo(@Valid @RequestBody ConversationVisitInfoReq req) {
        return rightSidebarService.conversationVisitInfo(req);
    }

    /**
     * 会话工单信息 todo
     */
    //    @Override
    @PostMapping("conversationWorkOrderInfo")
    public ConversationWorkOrderInfoResp conversationWorkOrderInfo(@Valid @RequestBody ConversationWorkOrderInfoReq req) {
        return rightSidebarService.conversationWorkOrderInfo(req);
    }

    /**
     * 知识库数据列表
     */
    //    @Override
    @PostMapping("knowledgeBaseDataList")
    public RightSidebarKnowledgeBaseDataListResp knowledgeBaseDataList(@Valid @RequestBody RightSidebarKnowledgeBaseDataListReq req) {
        KnowledgeBaseDataListResp knowledgeBaseDataListResp = knowledgeBaseManageService.knowledgeBaseDataList(req);

        RightSidebarKnowledgeBaseDataListResp resp = new RightSidebarKnowledgeBaseDataListResp();
        BeanUtils.copyProperties(resp, knowledgeBaseDataListResp);
        return resp;
    }

    /**
     * 添加知识库分类
     */
    //    @Override
    @PostMapping("addKnowledgeBaseCategory")
    public Integer addKnowledgeBaseCategory(@Valid @RequestBody RightSidebarAddKnowledgeBaseCategoryReq req) {
        return knowledgeBaseManageService.addKnowledgeBaseCategory(req);
    }

    /**
     * 添加知识库内容
     */
    //    @Override
    @PostMapping("addKnowledgeBaseContent")
    public Integer addKnowledgeBaseContent(@Valid @RequestBody RightSidebarAddKnowledgeBaseContentReq req) {
        return knowledgeBaseManageService.addKnowledgeBaseContent(req);
    }

    /**
     * 编辑知识库分类
     */
    //    @Override
    @PostMapping("editKnowledgeBaseCategory")
    public Integer editKnowledgeBaseCategory(@Valid @RequestBody RightSidebarEditKnowledgeBaseCategoryReq req) {
        return knowledgeBaseManageService.editKnowledgeBaseCategory(req);
    }

    /**
     * 编辑知识库内容
     */
    //    @Override
    @PostMapping("editKnowledgeBaseContent")
    public Integer editKnowledgeBaseContent(@Valid @RequestBody RightSidebarEditKnowledgeBaseContentReq req) {
        return knowledgeBaseManageService.editKnowledgeBaseContent(req);
    }

    /**
     * 删除知识库数据
     */
    //    @Override
    @PostMapping("deleteKnowledgeBaseData")
    public Integer deleteKnowledgeBaseData(@Valid @RequestBody RightSidebarDeleteKnowledgeBaseDataReq req) {
        return knowledgeBaseManageService.deleteKnowledgeBaseData(req);
    }

    /**
     * 订单/工单信息便携查询 todo
     */
    //    @Override
    @PostMapping("queryOrderAndWorkOrderInfo")
    public QueryOrderAndWorkOrderInfoResp queryOrderAndWorkOrderInfo(@Valid @RequestBody QueryOrderAndWorkOrderInfoReq req) {
        return rightSidebarService.queryOrderAndWorkOrderInfo(req);
    }
}
