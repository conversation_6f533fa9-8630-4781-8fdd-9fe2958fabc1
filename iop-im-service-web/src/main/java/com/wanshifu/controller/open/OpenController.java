package com.wanshifu.controller.open;

import com.wanshifu.iop.im.api.resp.tencent.TencentCallBackResp;
import com.wanshifu.service.OpenService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/open")
public class OpenController {

    @Resource
    private OpenService openService;

    /**
     * 腾讯事件回调，需要记录回调消息
     * 存在并发的场景
     * */
    @RequestMapping("/tencent/callback")
    public TencentCallBackResp tencentCallback(
            @RequestBody Object obj,
            @RequestParam("SdkAppid") Integer SdkAppid,
            @RequestParam("CallbackCommand") String CallbackCommand
            ){
        return openService.tencentCallBack(obj, CallbackCommand, SdkAppid);
    }

    /**
     * 腾讯事件回调，需要记录回调消息
     * 存在并发的场景
     * */
    @RequestMapping("/tencent/test")
    public Integer test(){
        return 1;
    }
    /**
     * 腾讯事件回调，需要记录回调消息
     * 存在并发的场景
     * */
    @RequestMapping("/tencent/test1")
    public Integer test1(){
        return 1;
    }

}
