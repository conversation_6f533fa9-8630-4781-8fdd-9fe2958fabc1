package com.wanshifu.controller;

import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.im.api.req.seat.BatchLogoutSeatReq;
import com.wanshifu.service.AuthService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 定时任务控制器
 * <AUTHOR>
 * @date： 2025-06-04 15:26:32
 */
@RestController
@RequestMapping("/timed/")
public class TimedTaskController {

    @Resource
    private AuthService authService;

    /**
     * 批量登出坐席
     */
    @GetMapping("/anyLogoutSeat")
    public Integer batchLogoutSeat(@RequestParam(value = "ids", required = false) String ids){
        BatchLogoutSeatReq req = new BatchLogoutSeatReq();
        if (StringUtils.isNotEmpty(ids)){
            req.setAccountIdList(Arrays.stream(StringUtils.split(ids, ",")).filter(StringUtils::isNotEmpty).map(Long::parseLong).distinct().collect(Collectors.toList()));
        }
        return authService.batchLogoutSeat(req);
    }
}
