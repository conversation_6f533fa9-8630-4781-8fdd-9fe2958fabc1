package com.wanshifu.controller.backend;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.flow.FlowAddReq;
import com.wanshifu.iop.im.api.req.flow.FlowConfigReq;
import com.wanshifu.iop.im.api.req.flow.FlowDelReq;
import com.wanshifu.iop.im.api.req.flow.FlowDetailReq;
import com.wanshifu.iop.im.api.req.flow.FlowListReq;
import com.wanshifu.iop.im.api.req.flow.FlowModifyReq;
import com.wanshifu.iop.im.api.resp.flow.FlowAllListResp;
import com.wanshifu.iop.im.api.resp.flow.FlowDetailResp;
import com.wanshifu.iop.im.api.resp.flow.FlowListResp;
import com.wanshifu.iop.im.service.api.backend.FlowServiceApi;
import com.wanshifu.service.FlowService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/flow/")
public class FlowController implements FlowServiceApi {

    @Resource
    private FlowService flowService;

    /**
     * 路由导航列表
     *
     * @param req
     */
    @Override
    @PostMapping("/flowList")
    public SimplePageInfo<FlowListResp> flowList(@Valid @RequestBody FlowListReq req) {
        return flowService.flowList(req);
    }

    /**
     * 路由导航删除
     *
     * @param req
     */
    @Override
    @PostMapping("/flowDel")
    public Integer flowDel(@Valid @RequestBody FlowDelReq req) {
        return flowService.flowDel(req);
    }

    /**
     * 路由导航详情
     *
     * @param req
     */
    @Override
    @PostMapping("/flowDetail")
    public FlowDetailResp flowDetail(@Valid @RequestBody FlowDetailReq req) {
        return flowService.flowDetail(req);
    }

    /**
     * 路由导航编辑
     *
     * @param req
     */
    @Override
    @PostMapping("/flowModify")
    public Integer flowModify(@Valid @RequestBody FlowModifyReq req) {
        return flowService.flowModify(req);
    }

    /**
     * 路由导航新增
     *
     * @param req
     */
    @Override
    @PostMapping("/flowAdd")
    public Long flowAdd(@Valid @RequestBody FlowAddReq req) {
        return flowService.flowAdd(req);
    }

    /**
     * 路由导航新增
     *
     * @param req
     */
    @Override
    @PostMapping("/flowConfig")
    public Integer flowConfig(@Valid @RequestBody FlowConfigReq req) {
        return flowService.flowConfig(req);
    }

    /**
     * 路由导航all列表
     */
    @Override
    @PostMapping("/flowAllList")
    public List<FlowAllListResp> flowAllList() {
        return flowService.flowAllList();
    }
}
