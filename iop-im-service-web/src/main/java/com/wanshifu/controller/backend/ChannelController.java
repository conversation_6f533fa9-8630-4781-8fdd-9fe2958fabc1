package com.wanshifu.controller.backend;

import com.wanshifu.aop.annotation.LogOperate;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.channel.BatchEditChannelReq;
import com.wanshifu.iop.im.api.req.channel.ChannelEnumsReq;
import com.wanshifu.iop.im.api.req.channel.ChannelSearchListReq;
import com.wanshifu.iop.im.api.req.channel.ChannelUserProblemAddReq;
import com.wanshifu.iop.im.api.req.channel.ChannelUserProblemChangeStatusReq;
import com.wanshifu.iop.im.api.req.channel.ChannelUserProblemClassReq;
import com.wanshifu.iop.im.api.req.channel.ChannelUserProblemDelReq;
import com.wanshifu.iop.im.api.req.channel.ClientCategoryCreateReq;
import com.wanshifu.iop.im.api.req.channel.ClientCategoryUpdateReq;
import com.wanshifu.iop.im.api.req.channel.CreateChannelReq;
import com.wanshifu.iop.im.api.req.channel.CreateConversationStyleConfigRqt;
import com.wanshifu.iop.im.api.req.channel.CreateDraftReq;
import com.wanshifu.iop.im.api.req.channel.CreateOrEditAutoReplyConfigReq;
import com.wanshifu.iop.im.api.req.channel.CreateOrEditSatisfactionLevelConfigReq;
import com.wanshifu.iop.im.api.req.channel.CreateStyleAndSideBarReq;
import com.wanshifu.iop.im.api.req.channel.DeleteChannelReq;
import com.wanshifu.iop.im.api.req.channel.DeleteClientCategoryReq;
import com.wanshifu.iop.im.api.req.channel.DeployClientCategoryAndChannelReq;
import com.wanshifu.iop.im.api.req.channel.EditStyleAndSideBarReq;
import com.wanshifu.iop.im.api.req.channel.GetAutoReplyConfigReq;
import com.wanshifu.iop.im.api.req.channel.GetChannelConfigDraftListRqt;
import com.wanshifu.iop.im.api.req.channel.GetSatisfactionLevelConfigReq;
import com.wanshifu.iop.im.api.req.channel.GetStyleAndSideBarReq;
import com.wanshifu.iop.im.api.req.channel.IconUploadReq;
import com.wanshifu.iop.im.api.req.channel.PreviewClientCategoryAndChannelReq;
import com.wanshifu.iop.im.api.req.channel.SwitchChannelStatusReq;
import com.wanshifu.iop.im.api.req.channel.UpdateChannelReq;
import com.wanshifu.iop.im.api.req.channel.UpdateConversationStyleConfigRqt;
import com.wanshifu.iop.im.api.resp.ImgUploadResp;
import com.wanshifu.iop.im.api.resp.channel.ChannelEnumsResp;
import com.wanshifu.iop.im.api.resp.channel.ChannelSearchListResp;
import com.wanshifu.iop.im.api.resp.channel.ChannelUserProblemResp;
import com.wanshifu.iop.im.api.resp.channel.GetAutoReplyConfigResp;
import com.wanshifu.iop.im.api.resp.channel.GetChannelConfigDraftListResp;
import com.wanshifu.iop.im.api.resp.channel.GetSatisfactionLevelConfigResp;
import com.wanshifu.iop.im.api.resp.channel.GetStyleAndSideBarResp;
import com.wanshifu.iop.im.api.resp.channel.PreviewClientCategoryAndChannelResp;
import com.wanshifu.iop.im.api.resp.channel.SwitchChannelStatusResp;
import com.wanshifu.iop.im.domain.bo.channel.ChannelBo;
import com.wanshifu.iop.im.domain.po.channel.ClientCategoryConfig;
import com.wanshifu.iop.im.domain.po.channel.ClientChannelConfigDraft;
import com.wanshifu.iop.im.domain.po.channel.SatisfactionLevelConfig;
import com.wanshifu.iop.im.service.api.backend.ChannelServiceBackendApi;
import com.wanshifu.service.ChannelService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/channel/")
public class ChannelController implements ChannelServiceBackendApi {

    @Resource
    private ChannelService channelService;


    /**
     * 端侧+渠道枚举
     *
     */
    @Override
    @PostMapping("/enums")
    public ChannelEnumsResp enums(@Valid @RequestBody ChannelEnumsReq req) {
        return channelService.enums(req);
    }

    /**
     * 端侧+渠道列表
     *
     */
    @Override
    @PostMapping("/searchList")
    public ChannelSearchListResp searchList(@Valid @RequestBody ChannelSearchListReq req) {
        return channelService.searchList(req);
    }

    /**
     * 新增端侧
     *
     */
    @Override
    @PostMapping("/createClientCategory")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "add", idField = "clientCategoryConfigId")
    public Long createClientCategory(@Valid @RequestBody ClientCategoryCreateReq req) {
        return channelService.createClientCategory(req);
    }

    /**
     * 编辑端侧
     *
     */
    @Override
    @PostMapping("/editClientCategory")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "modify", idField = "clientCategoryId")
    public Long editClientCategory(@Valid @RequestBody ClientCategoryUpdateReq req) {
        return channelService.editClientCategory(req);
    }

    /**
     * 通过端侧ID查询渠道列表
     *
     */
    @Override
    @PostMapping("/channelList")
    public List<ChannelBo> channelList(@Valid @RequestBody ChannelSearchListReq req) {
        return channelService.channelList(req);
    }

    /**
     * 删除渠道
     */
    @Override
    @PostMapping("/deleteClientCategory")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "modify", idField = "clientCategoryId")
    public Long deleteClientCategory(@Valid @RequestBody DeleteClientCategoryReq req) {
        return channelService.deleteClientCategory(req);
    }

    /**
     * 新增渠道
     *
     */
    @Override
    @PostMapping("/createChannel")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "add", idField = "clientCategoryId")
    public Long createChannel(@Valid @RequestBody CreateChannelReq req) {
        return channelService.createChannel(req);
    }

    /**
     * 编辑渠道
     *
     */
    @Override
    @PostMapping("/editChannel")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "modify", idField = "clientCategoryId")
    public Long editChannel(@Valid @RequestBody UpdateChannelReq req) {
        return channelService.editChannel(req);
    }

    /**
     * 删除渠道
     *
     */
    @Override
    @PostMapping("/deleteChannel")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "modify", idField = "clientCategoryId")
    public Long deleteChannel(@Valid @RequestBody DeleteChannelReq req) {
        return channelService.deleteChannel(req);
    }

    /**
     * 修改渠道状态
     *
     */
    @Override
    @PostMapping("/switchChannelStatus")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "modify", idField = "clientCategoryId")
    public SwitchChannelStatusResp switchChannelStatus(@Valid @RequestBody SwitchChannelStatusReq req) {
        return channelService.switchChannelStatus(req);
    }

    /**
     * 批量编辑渠道
     *
     */
    @Override
    @PostMapping("/batchEditChannel")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "modify", idField = "clientCategoryId")
    public Integer batchEditChannel(@Valid @RequestBody BatchEditChannelReq req) {
        return channelService.batchEditChannel(req);
    }

    /**
     * 预览端侧+渠道
     *
     */
    @Override
    @PostMapping("/previewDraft")
    public PreviewClientCategoryAndChannelResp previewDraft(@Valid @RequestBody PreviewClientCategoryAndChannelReq req) {
        return channelService.previewDraft(req);
    }

    /**
     * 发布端侧+渠道  即从草稿表发布到正式表(正式表由多张表构成)
     *
     */
    @Override
    @PostMapping("/deployConfig")
    @LogOperate(businessClass = ClientCategoryConfig.class, businessType = "add", idField = "clientCategoryId")
    public Long deployConfig(@Valid @RequestBody DeployClientCategoryAndChannelReq req) {
        return channelService.deployConfig(req);
    }

    /**
     * 保存端侧+渠道草稿
     *
     */
    @Override
    @PostMapping("/saveConfigDraft")
    @LogOperate(businessClass = ClientCategoryConfig.class, businessType = "add", idField = "clientCategoryConfigId")
    public Long saveConfigDraft(@Valid @RequestBody CreateDraftReq req) {
        return channelService.saveConfigDraft(req);
    }

    /**
     * 会话主题样式和侧边栏查询
     *
     */
    @Override
    @PostMapping("/getStyleAndSideBar")
    public GetStyleAndSideBarResp getStyleAndSideBar(@Valid @RequestBody GetStyleAndSideBarReq req) {
        return channelService.getStyleAndSideBar(req);
    }

    /**
     * 编辑会话主题样式和侧边栏
     *
     */
    @Override
    @PostMapping("/editStyleAndSideBar")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "modify", idField = "clientCategoryId")
    public Long editStyleAndSideBar(@Valid @RequestBody UpdateConversationStyleConfigRqt req) {
        return channelService.editStyleAndSideBar(req);
    }

    /**
     * 新增会话主题样式和侧边栏
     *
     */
    @Override
    @PostMapping("/createStyleAndSideBar")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "add", idField = "clientCategoryId")
    public Long createStyleAndSideBar(@Valid @RequestBody CreateConversationStyleConfigRqt req) {
        return channelService.createStyleAndSideBar(req);
    }

    /**
     * 会话满意度配置查询
     *
     */
    @Override
    @PostMapping("/getSatisfactionLevelConfig")
    public GetSatisfactionLevelConfigResp getSatisfactionLevelConfig(@Valid @RequestBody GetSatisfactionLevelConfigReq req) {
        return channelService.getSatisfactionLevelConfig(req);
    }

    /**
     * 新增或编辑会话满意度配置
     *
     */
    @Override
    @PostMapping("/createOrEditSatisfactionLevelConfig")
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "add", idField = "clientCategoryId")
    public Long createOrEditSatisfactionLevelConfig(@Valid @RequestBody CreateOrEditSatisfactionLevelConfigReq req) {
        return channelService.createOrEditSatisfactionLevelConfig(req);
    }

    /**
     * 会话系统自动回复消息配置查询
     *
     */
    @Override
    @PostMapping("/getAutoReplyConfig")
    public GetAutoReplyConfigResp getAutoReplyConfig(@Valid @RequestBody GetAutoReplyConfigReq req) {
        return channelService.getAutoReplyConfig(req);
    }

    /**
     * 编辑会话系统自动回复消息配置
     *
     */
    @Override
    @LogOperate(businessClass = ClientChannelConfigDraft.class, businessType = "add", idField = "clientCategoryId")
    @PostMapping("/createOrEditAutoReplyConfig")
    public Long createOrEditAutoReplyConfig(@Valid @RequestBody CreateOrEditAutoReplyConfigReq req) {
        return channelService.createOrEditAutoReplyConfig(req);
    }

    /**
     * 按钮图标上传
     *
     * @return aid
     */
    @Override
    @LogOperate(businessClass = SatisfactionLevelConfig.class, businessType = "add", idField = "satisfactionLevelConfigId")
    @PostMapping("/iconUpload")
    public ImgUploadResp iconUpload(@RequestParam("satisfactionLevelConfigId") Long satisfactionLevelConfigId, @RequestParam("buttonName") String buttonName, @RequestParam("file") MultipartFile file) {
        IconUploadReq req = new IconUploadReq();
        req.setSatisfactionLevelConfigId(satisfactionLevelConfigId);
        req.setButtonName(buttonName);
        return channelService.iconUpload(req, file);
    }


    /**
     * 用户咨询问题列表
     * */
    @Override
    @PostMapping("/userProblemClassList")
    public SimplePageInfo<ChannelUserProblemResp> userProblemClassList(@Valid @RequestBody ChannelUserProblemClassReq req){
        return channelService.userProblemClassList(req);
    }

    /**
     * 用户咨询问题分类-删除
     *
     * @param req
     */
    @Override
    @PostMapping("/userProblemClassDel")
    public Integer userProblemClassDel(@Valid @RequestBody ChannelUserProblemDelReq req) {
        return channelService.userProblemClassDel(req);
    }

    /**
     * 用户咨询问题分类-修改状态
     *
     * @param req
     */
    @Override
    @PostMapping("/userProblemClassChangeStatus")
    public Integer userProblemClassChangeStatus(@Valid @RequestBody ChannelUserProblemChangeStatusReq req) {
        return channelService.userProblemClassChangeStatus(req);
    }

    /**
     * 用户咨询问题分类-新增
     *
     * @param req
     */
    @Override
    @PostMapping("/userProblemClassAdd")
    public Long userProblemClassAdd(@Valid @RequestBody ChannelUserProblemAddReq req) {
        return channelService.userProblemClassAdd(req);
    }

    /**
     * 获取渠道id和端侧id信息
     * @param req
     * @return
     */
    @Override
    @PostMapping("/getChannelConfigDraftList")
    public List<GetChannelConfigDraftListResp> getChannelConfigDraftList(@Valid @RequestBody GetChannelConfigDraftListRqt req) {
        return channelService.getChannelConfigDraftList(req);
    }
}
