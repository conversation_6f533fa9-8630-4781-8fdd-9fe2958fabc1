package com.wanshifu.controller.backend;

import com.wanshifu.aop.annotation.LogOperate;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.seat.*;
import com.wanshifu.iop.im.api.req.tags.CreateTagsReq;
import com.wanshifu.iop.im.api.req.tags.GetTagsListReq;
import com.wanshifu.iop.im.api.req.tags.SwitchTagsStatusReq;
import com.wanshifu.iop.im.api.resp.seat.ConversationTransferServiceResp;
import com.wanshifu.iop.im.api.resp.seat.GetSeatStatusListResp;
import com.wanshifu.iop.im.api.resp.seat.SeatFunctionResp;
import com.wanshifu.iop.im.api.resp.seat.SeatEnumsResp;
import com.wanshifu.iop.im.api.resp.seat.SeatSearchListResp;
import com.wanshifu.iop.im.api.resp.tags.GetTagsListResp;
import com.wanshifu.iop.im.domain.enums.TagsSceneEnum;
import com.wanshifu.iop.im.domain.po.ImConversation;
import com.wanshifu.iop.im.domain.po.SeatInfo;
import com.wanshifu.iop.im.domain.po.SeatStatus;
import com.wanshifu.iop.im.domain.po.Tags;
import com.wanshifu.iop.im.service.api.backend.SeatServiceApi;
import com.wanshifu.manager.tencentBase.TencentManager;
import com.wanshifu.service.SeatService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/seat/")
public class SeatController implements SeatServiceApi {

    @Resource
    private TencentManager tencentManager;

    @Resource
    private SeatService seatService;

    /**
     * 坐席列表枚举
     * @return
     */
    @Override
    @PostMapping("/enums")
    public SeatEnumsResp enums(@Valid @RequestBody SeatEnumsReq req){
        return seatService.enums(req);
    }

    /**
     * 坐席列表
     * @return
     */
    @Override
    @PostMapping("/searchList")
    public SimplePageInfo<SeatSearchListResp> searchList(@Valid @RequestBody SeatSearchListReq req){
        return seatService.searchList(req);
    }

    /**
     * 添加坐席
     * @param req
     * @return
     */
    @Override
    @PostMapping("/create")
    @LogOperate(businessClass = SeatInfo.class, businessType = "add", idField = "seatId")
    public Integer create(@Valid @RequestBody SeatCreateReq req){
        return seatService.create(req);
    }

    /**
     * 编辑坐席
     * @param req
     * @return
     */
    @Override
    @PostMapping("/edit")
    @LogOperate(businessClass = SeatInfo.class, businessType = "modify", idField = "seatId")
    public Integer edit(@Valid @RequestBody SeatEditReq req){
        return seatService.edit(req);
    }

    /**
     * 坐席标签列表
     * @return
     */
    @Override
    @PostMapping("/tagsList")
    public List<GetTagsListResp> tagsList(){
        GetTagsListReq req = new GetTagsListReq();
        req.setScene(TagsSceneEnum.SEAT.type);
        return seatService.tagsList(req);
    }

    /**
     * 添加标签
     * @param req
     * @return
     */
    @Override
    @PostMapping("/createTags")
    @LogOperate(businessClass = Tags.class, businessType = "add", idField = "tagId")
    public Integer createTags(@Valid @RequestBody CreateTagsReq req){
        req.setScene(TagsSceneEnum.SEAT.type);
        return seatService.createTags(req);
    }

    /**
     * 编辑标签
     * @param req
     * @return
     */
    @Override
    @PostMapping("/editTags")
    @LogOperate(businessClass = Tags.class, businessType = "modify", idField = "tagId")
    public Integer editTags(@Valid @RequestBody CreateTagsReq req){
        return seatService.editTags(req);
    }

    /**
     * 切换标签状态
     * @param req
     * @return
     */
    @Override
    @PostMapping("/switchTagsStatus")
    public Integer switchTagsStatus(@Valid @RequestBody SwitchTagsStatusReq req){
        return seatService.switchTagsStatus(req);
    }

    /**
     * 获取坐席功能枚举
     * @return
     */
    @Override
    @PostMapping("/seatFunctionEnums")
    public List<SeatFunctionResp> seatFunctionEnums(){
        return seatService.seatFunctionEnums();
    }

    /**
     * 坐席状态列表
     * @return
     */
    @Override
    @PostMapping("/seatStatusList")
    public List<GetSeatStatusListResp> seatStatusList(){
        return seatService.seatStatusList();
    }

    /**
     * 修改坐席状态配置
     * @param req
     * @return
     */
    @Override
    @PostMapping("/editSeatStatus")
    @LogOperate(businessClass = SeatStatus.class, businessType = "modify", idField = "seatStatusId")
    public Integer editSeatStatus(@Valid @RequestBody EditSeatStatusReq req){
        return seatService.editSeatStatus(req);
    }

    /**
     * 关闭会话
     *
     * @param req
     */
    @Override
    @PostMapping("/closeConversation")
    @LogOperate(businessClass = ImConversation.class, businessType = "modify", idField = "conversationId")
    public Integer closeConversation(@Valid @RequestBody CloseConversationReq req) {
        return seatService.closeConversation(req);
    }

    /**
     * 坐席列表-转接坐席列表
     * */
    @Override
    @PostMapping("/transferConversation")
    public List<ConversationTransferServiceResp> transferConversation(){
        return seatService.transferConversation();
    }

    /**
     * 会话转接
     * */
    @Override
    @PostMapping("/transfer")
    public Integer transfer(@Valid @RequestBody TransferServiceReq req){
        return seatService.transfer(req);
    }

}
