package com.wanshifu.controller.backend;

import com.wanshifu.aop.annotation.LogOperate;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.group.*;
import com.wanshifu.iop.im.api.resp.group.*;
import com.wanshifu.iop.im.domain.po.GroupInfo;
import com.wanshifu.iop.im.service.api.backend.GroupManageServiceApi;
import com.wanshifu.service.GroupManageService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 进线配置控制器
 * <AUTHOR>
 * @date： 2025-06-04 15:26:32
 */
@RestController
@RequestMapping("/groupManage/")
public class GroupManageController implements GroupManageServiceApi {

    @Resource
    private GroupManageService groupManageService;

    /**
     * 枚举接口
     */
    @Override
    @PostMapping("/enums")
    public GroupConfigEnumsResp enums(){
        return groupManageService.enums();
    }

    /**
     * 分组列表
     */
    @Override
    @PostMapping("/list")
    public SimplePageInfo<GroupListResp> list(@Valid @RequestBody GroupListReq req){
        return groupManageService.list(req);
    }

    /**
     * 新增分组
     */
    @Override
    @PostMapping("/add")
    @LogOperate(businessClass = GroupInfo.class, businessType = "add", idField = "groupId")
    public Long add(@Valid @RequestBody AddGroupReq req){
        return groupManageService.add(req);
    }

    /**
     * 分组排序
     */
    @Override
    @PostMapping("/rearrangement")
    @LogOperate(businessClass = GroupInfo.class, businessType = "rearrangement", idField = "groupId")
    public Integer rearrangement(@Valid @RequestBody GroupRearrangementReq req){
        return groupManageService.rearrangement(req);
    }

    /**
     * 分组坐席列表
     */
    @Override
    @PostMapping("/seatList")
    public SimplePageInfo<GroupSeatListResp> seatList(@Valid @RequestBody GroupSeatListReq req){
        return groupManageService.seatList(req);
    }

    /**
     * 新增分组坐席
     */
    @Override
    @PostMapping("/addSeat")
    public Long addSeat(@Valid @RequestBody AddGroupSeatReq req){
        return groupManageService.addSeat(req);
    }

    /**
     * 移除分组坐席
     */
    @Override
    @PostMapping("/removeSeat")
    public Integer removeSeat(@Valid @RequestBody RemoveGroupSeatReq req){
        return groupManageService.removeSeat(req);
    }

    /**
     * 分组接入规则详情
     */
    @Override
    @PostMapping("/ruleDetail")
    public GroupAccessRuleDetailResp ruleDetail(@Valid @RequestBody GroupAccessRuleDetailReq req){
        return groupManageService.ruleDetail(req);
    }

    /**
     * 编辑分组接入规则
     */
    @Override
    @PostMapping("/editRule")
    public Integer editRule(@Valid @RequestBody EditGroupAccessRuleReq req){
        return groupManageService.editRule(req);
    }

    /**
     * 分组坐席优先级分配规则枚举
     */
    @Override
    @PostMapping("/seatPriorityDistributeRuleEnums")
    public SeatPriorityDistributeRuleEnumsResp seatPriorityDistributeRuleEnums(@Valid @RequestBody GroupAccessRuleDetailReq req){
        return groupManageService.seatPriorityDistributeRuleEnums(req);
    }

    /**
     * 分组坐席优先级分配规则列表
     */
    @Override
    @PostMapping("/seatPriorityDistributeRuleList")
    public SeatPriorityDistributeRuleDetailResp seatPriorityDistributeRuleList(@Valid @RequestBody SeatPriorityDistributeRuleDetailReq req){
        return groupManageService.seatPriorityDistributeRuleList(req);
    }

    /**
     * 编辑分组分配策略
     */
    @Override
    @PostMapping("/editDistributeStrategy")
    public Integer editDistributeStrategy(@Valid @RequestBody EditDistributeStrategyReq req){
        return groupManageService.editDistributeStrategy(req);
    }

    /**
     * 编辑分组坐席分配规则
     */
    @Override
    @PostMapping("/editSeatDistributeRule")
    public Integer editSeatDistributeRule(@Valid @RequestBody EditGroupSeatDistributeRuleReq req){
        return groupManageService.editSeatDistributeRule(req);
    }
}
