package com.wanshifu.controller.backend;

import com.wanshifu.aop.annotation.LogOperate;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.incomingConfig.*;
import com.wanshifu.iop.im.api.req.knowledgeBase.*;
import com.wanshifu.iop.im.api.resp.incomingConfig.GetRegistrationFrequencyConfigResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.IncomingConfigEnumsResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchDomainListResp;
import com.wanshifu.iop.im.api.resp.incomingConfig.SearchWhiteAndBlackListResp;
import com.wanshifu.iop.im.api.resp.knowledgeBase.KnowledgeBaseDataListResp;
import com.wanshifu.iop.im.domain.po.IncomingPropertyConfig;
import com.wanshifu.iop.im.domain.po.IncomingSafetyConfig;
import com.wanshifu.iop.im.service.api.backend.IncomingConfigServiceApi;
import com.wanshifu.iop.im.service.api.backend.KnowledgeBaseManageServiceApi;
import com.wanshifu.service.IncomingConfigService;
import com.wanshifu.service.KnowledgeBaseManageService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 知识库管理配置控制器
 * <AUTHOR>
 * @date： 2025-06-04 15:26:32
 */
@RestController
@RequestMapping("/knowledgeBaseManage/")
public class KnowledgeBaseManageController implements KnowledgeBaseManageServiceApi {

    @Resource
    private KnowledgeBaseManageService knowledgeBaseManageService;

    /**
     * 知识库数据列表
     */
    //    @Override
    @PostMapping("knowledgeBaseDataList")
    public KnowledgeBaseDataListResp knowledgeBaseDataList(@Valid @RequestBody KnowledgeBaseDataListReq req) {
        return knowledgeBaseManageService.knowledgeBaseDataList(req);
    }

    /**
     * 添加知识库分类
     */
    //    @Override
    @PostMapping("addKnowledgeBaseCategory")
    public Integer addKnowledgeBaseCategory(@Valid @RequestBody AddKnowledgeBaseCategoryReq req) {
        return knowledgeBaseManageService.addKnowledgeBaseCategory(req);
    }

    /**
     * 添加知识库内容
     */
    //    @Override
    @PostMapping("addKnowledgeBaseContent")
    public Integer addKnowledgeBaseContent(@Valid @RequestBody AddKnowledgeBaseContentReq req) {
        return knowledgeBaseManageService.addKnowledgeBaseContent(req);
    }

    /**
     * 编辑知识库分类
     */
    //    @Override
    @PostMapping("editKnowledgeBaseCategory")
    public Integer editKnowledgeBaseCategory(@Valid @RequestBody EditKnowledgeBaseCategoryReq req) {
        return knowledgeBaseManageService.editKnowledgeBaseCategory(req);
    }

    /**
     * 编辑知识库内容
     */
    //    @Override
    @PostMapping("editKnowledgeBaseContent")
    public Integer editKnowledgeBaseContent(@Valid @RequestBody EditKnowledgeBaseContentReq req) {
        return knowledgeBaseManageService.editKnowledgeBaseContent(req);
    }

    /**
     * 删除知识库数据
     */
    //    @Override
    @PostMapping("deleteKnowledgeBaseData")
    public Integer deleteKnowledgeBaseData(@Valid @RequestBody DeleteKnowledgeBaseDataReq req) {
        return knowledgeBaseManageService.deleteKnowledgeBaseData(req);
    }
}
