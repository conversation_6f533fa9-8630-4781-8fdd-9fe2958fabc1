package com.wanshifu.controller.backend;

import com.wanshifu.aop.annotation.LogOperate;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.im.api.req.incomingConfig.*;
import com.wanshifu.iop.im.api.resp.incomingConfig.*;
import com.wanshifu.iop.im.domain.po.IncomingPropertyConfig;
import com.wanshifu.iop.im.domain.po.IncomingSafetyConfig;
import com.wanshifu.iop.im.service.api.backend.IncomingConfigServiceApi;
import com.wanshifu.service.IncomingConfigService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 进线配置控制器
 * <AUTHOR>
 * @date： 2025-06-04 15:26:32
 */
@RestController
@RequestMapping("/incomingConfig/")
public class IncomingConfigController implements IncomingConfigServiceApi {

    @Resource
    private IncomingConfigService incomingConfigService;

    /**
     * 枚举接口
     */
    @Override
    @PostMapping("/enums")
    public IncomingConfigEnumsResp enums(){
        return incomingConfigService.enums();
    }

    /**
     * 域名配置列表
     */
    @Override
    @PostMapping("/domainList")
    public List<SearchDomainListResp> domainList(@Valid @RequestBody SearchDomainListReq req){
        return incomingConfigService.domainList(req);
    }

    /**
     * 添加域名接口
     */
    @Override
    @PostMapping("/addDomain")
    @LogOperate(businessClass = IncomingSafetyConfig.class, businessType = "add", idField = "safetyId")
    public Integer addDomain(@Valid @RequestBody AddDomainReq req){
        return incomingConfigService.addDomain(req);
    }

    /**
     * 删除域名接口
     */
    @Override
    @PostMapping("/deleteDomain")
    @LogOperate(businessClass = IncomingSafetyConfig.class, businessType = "delete", idField = "safetyId")
    public Integer deleteDomain(@Valid @RequestBody DeleteDomainReq req){
        return incomingConfigService.deleteDomain(req);
    }

    /**
     * 获取访客黑白名单列表
     * @param req 请求参数，包含租户ID
     * @return 访客黑白名单列表
     */
    @Override
    @PostMapping("/whiteAndBlackList")
    public SimplePageInfo<SearchWhiteAndBlackListResp> whiteAndBlackList(@Valid @RequestBody SearchWhiteAndBlackListReq req) {
        return incomingConfigService.whiteAndBlackList(req);
    }

    /**
     * 添加访客黑白名单
     * @param req 请求参数，包含租户ID、访客ID、类型(黑/白名单)和备注
     * @return 操作结果
     */
    @Override
    @PostMapping("/addWhiteAndBlack")
    @LogOperate(businessClass = IncomingPropertyConfig.class, businessType = "add", idField = "propertyId")
    public Integer addWhiteAndBlack(@Valid @RequestBody AddWhiteAndBlackReq req) {
        return incomingConfigService.addWhiteAndBlack(req);
    }

    /**
     * 更新访客黑白名单
     * @param req 请求参数，包含租户ID、原访客ID、新访客ID、类型和备注
     * @return 操作结果
     */
    @Override
    @PostMapping("/updateWhiteAndBlack")
    @LogOperate(businessClass = IncomingPropertyConfig.class, businessType = "modify", idField = "propertyId")
    public Integer updateWhiteAndBlack(@Valid @RequestBody UpdateWhiteAndBlackReq req) {
        return incomingConfigService.updateWhiteAndBlack(req);
    }

    /**
     * 获取注册频率配置
     * @param req 请求参数，包含租户ID
     * @return 注册频率配置信息(限制次数和时间间隔)
     */
    @Override
    @PostMapping("/getRegistrationFrequencyConfig")
    public GetRegistrationFrequencyConfigResp getRegistrationFrequencyConfig(@Valid @RequestBody GetRegistrationFrequencyConfigReq req) {
        return incomingConfigService.getRegistrationFrequencyConfig(req);
    }

    /**
     * 更新注册频率配置
     * @param req 请求参数，包含租户ID、限制次数和时间间隔
     * @return 操作结果
     */
    @Override
    @PostMapping("/updateRegistrationFrequency")
    @LogOperate(businessClass = IncomingSafetyConfig.class, businessType = "modify", idField = "safetyId")
    public Integer updateRegistrationFrequency(@Valid @RequestBody UpdateRegistrationFrequencyReq req) {
        return incomingConfigService.updateRegistrationFrequency(req);
    }
}
