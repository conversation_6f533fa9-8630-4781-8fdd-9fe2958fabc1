package com.wanshifu.controller.backend;

import com.wanshifu.iop.im.api.req.BatchGetEnumsReq;
import com.wanshifu.iop.im.api.req.ConversationThemeConfigRqt;
import com.wanshifu.iop.im.api.req.SwitchStatusReq;
import com.wanshifu.iop.im.api.req.seat.VerifyRepeatNameReq;
import com.wanshifu.iop.im.api.resp.CommonEnumsResp;
import com.wanshifu.iop.im.api.resp.ConversationThemeConfigResp;
import com.wanshifu.iop.im.api.resp.VerifyRepeatNameResp;
import com.wanshifu.iop.im.service.api.backend.CommonServiceApi;
import com.wanshifu.service.CommonService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 通用控制器
 * <AUTHOR>
 * @date： 2025-06-04 15:26:32
 */
@RestController
@RequestMapping("/common/")
public class CommonController implements CommonServiceApi {

    @Resource
    private CommonService commonService;

    /**
     * 校验重复名称接口
     */
    @Override
    @PostMapping("/verifyRepeatName")
    public VerifyRepeatNameResp verifyRepeatName(@Valid @RequestBody VerifyRepeatNameReq req){
        return commonService.verifyRepeatName(req);
    }

    /**
     * 启用禁用状态接口
     */
    @Override
    @PostMapping("/switchStatus")
    public Integer switchStatus(@Valid @RequestBody SwitchStatusReq req){
        return commonService.switchStatus(req);
    }

    /**
     * 获取标签枚举接口
     */
    @Override
    @PostMapping("/getEnums")
    public Map<String, List<CommonEnumsResp>> getEnums(@Valid @RequestBody BatchGetEnumsReq req){
        return commonService.getEnums(req);
    }

    /**
     * 获取主题列表接口
     */
    @Override
    @PostMapping("/getThemeConfigList")
    public Map<String,List<ConversationThemeConfigResp>> getThemeConfigList(@Valid @RequestBody ConversationThemeConfigRqt req) {
        return commonService.getThemeConfigList(req);
    }
}
