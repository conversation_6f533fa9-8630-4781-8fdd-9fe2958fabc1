package com.wanshifu.controller.backend;

import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.im.api.req.seat.BatchLogoutSeatReq;
import com.wanshifu.iop.im.api.req.seat.DisableSeatStatusReq;
import com.wanshifu.iop.im.api.req.seat.SetLoginAuthReq;
import com.wanshifu.iop.im.api.resp.SetLoginAuthResp;
import com.wanshifu.iop.im.service.api.backend.AuthServiceApi;
import com.wanshifu.service.AuthService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 鉴权控制器
 * <AUTHOR>
 * @date： 2025-06-04 15:26:32
 */
@RestController
@RequestMapping("/auth/")
public class AuthController implements AuthServiceApi {

    @Resource
    private AuthService authService;

    /**
     * 登录坐席
     */
    @Override
    @PostMapping("/loginSeat")
    public SetLoginAuthResp loginSeat(@Valid @RequestBody SetLoginAuthReq req){
        return authService.loginSeat(req);
    }

    /**
     * 批量登出坐席
     */
    @Override
    @PostMapping("/batchLogoutSeat")
    public Integer batchLogoutSeat(@Valid @RequestBody BatchLogoutSeatReq req){
        if (CollectionUtils.isEmpty(req.getAccountIdList())){
            throw new BusinessException("登出坐席账号id不能为空");
        }
        return authService.batchLogoutSeat(req);
    }

    /**
     * 禁用坐席状态
     * @param req
     * @return
     */
    @Override
    @PostMapping("/disableSeatStatus")
    public Integer disableSeatStatus(@Valid @RequestBody DisableSeatStatusReq req){
        if ( req.getDisableAccountId() == null || req.getDisableAccountId() == 0){
            throw new BusinessException("坐席账号id不能为空");
        }
        return authService.disableSeatStatus(req);
    }
}
