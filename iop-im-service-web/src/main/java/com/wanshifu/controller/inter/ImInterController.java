package com.wanshifu.controller.inter;

import com.wanshifu.iop.im.api.req.inter.GetGroupListReq;
import com.wanshifu.iop.im.api.req.inter.GetSeatImInfoReq;
import com.wanshifu.iop.im.api.req.inter.GetVirtualUserInfoReq;
import com.wanshifu.iop.im.api.req.inter.RegisterUserReq;
import com.wanshifu.iop.im.api.req.inter.ToArtificialServiceReq;
import com.wanshifu.iop.im.api.resp.inter.GetGroupListResp;
import com.wanshifu.iop.im.api.resp.inter.GetVirtualUserInfoResp;
import com.wanshifu.iop.im.api.resp.inter.RegisterUserResp;
import com.wanshifu.iop.im.service.api.inter.ImInterServiceApi;
import com.wanshifu.service.ImInterService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/inter/im")
public class ImInterController implements ImInterServiceApi {

    @Resource
    private ImInterService imInterService;

    /**
     * 用户注册
     *
     * @param req
     */
    @Override
    @PostMapping("/registerUser")
    public RegisterUserResp registerUser(@Valid @RequestBody RegisterUserReq req) {
        return imInterService.registerUser(req);
    }

    /**
     * 批量注册虚拟用户
     * */
    @PostMapping("/batch")
    public Integer batchRegisterUser(@RequestParam("num") Integer num) {
        return imInterService.batchRegisterUser(num);
    }

    /**
     * 作用：获取虚拟用户信息，创建会话、绑定关系，系统自动发送欢迎语消息
     * */
    @Override
    @PostMapping("/getVirtualInfo")
    public GetVirtualUserInfoResp getVirtualInfo(@Valid @RequestBody GetVirtualUserInfoReq req){
        return imInterService.getVirtualInfo(req);
    }

    /**
     * 客服初始化 坐席信息
     * */
    @Override
    @PostMapping("/seatImInfo")
    public RegisterUserResp getSeatImInfo(@Valid @RequestBody GetSeatImInfoReq req){
        return imInterService.getSeatImInfo(req);
    }


    /**
     * 坐席端-获取群列表
     * */
    @Override
    @PostMapping("/getGroupList")
    public List<GetGroupListResp> getGroupList(@Valid @RequestBody GetGroupListReq req){
        return imInterService.getGroupList(req);
    }

    /**
     * 转人工，发送表单信息，等提交表单数据后 实际是进入排队
     *
     * @param req
     */
    @Override
    @PostMapping("/toArtificial")
    public Integer toArtificial(@Valid @RequestBody ToArtificialServiceReq req) {
        return imInterService.toArtificial(req);
    }


}
