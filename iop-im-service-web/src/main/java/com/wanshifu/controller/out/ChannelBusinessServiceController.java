package com.wanshifu.controller.out;

import com.wanshifu.iop.im.api.req.channel.GetConversationThemeInfoRqt;
import com.wanshifu.iop.im.api.req.channel.PreviewClientCategoryAndChannelReq;
import com.wanshifu.iop.im.api.req.channel.SaveSatisfactionDataRqt;
import com.wanshifu.iop.im.api.resp.channel.GetConversationThemeInfoResp;
import com.wanshifu.iop.im.api.resp.channel.PreviewClientCategoryAndChannelResp;
import com.wanshifu.iop.im.service.api.outer.ChannelServiceApi;
import com.wanshifu.service.ChannelBusinessService;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author:<PERSON><PERSON><PERSON>@wanshifu.com
 * @create:2025-07-24 11:32:37
 * @Description ：
 **/
@RestController
@RequestMapping("/outer/channel")
public class ChannelBusinessServiceController implements ChannelServiceApi {
    @Resource
    private ChannelBusinessService channelBusinessService;
    /**
     * 预览渠道
     * @param req
     * @return
     */
    @Override
    @PostMapping("/previewDraft")
    public PreviewClientCategoryAndChannelResp previewDraft(@Valid @RequestBody PreviewClientCategoryAndChannelReq req) {
        return channelBusinessService.previewDraft(req);
    }

    /**
     * 获取会话主题样式配置
     * @param req
     * @return
     */
    @Override
    @PostMapping("/getConversationThemeInfo")
    public GetConversationThemeInfoResp getConversationThemeInfo(@Valid @RequestBody GetConversationThemeInfoRqt req) {
        return channelBusinessService.getConversationThemeInfo(req);
    }

    /**
     * 保存满意度数据
     * @param req
     * @return
     */
    @Override
    @PostMapping("/saveSatisfactionData")
    public Integer saveSatisfactionData(@Valid @RequestBody SaveSatisfactionDataRqt req) {
        return channelBusinessService.saveSatisfactionData(req);
    }
}