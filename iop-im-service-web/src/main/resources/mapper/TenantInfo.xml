<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.TenantInfoMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.TenantInfo">
        <id column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="tenant_name" jdbcType="VARCHAR" property="tenantName"/>
        <result column="tencent_desc" jdbcType="VARCHAR" property="tencentDesc"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">tenantId, tenantName, tencentDesc, isDelete, updateTime, createTime </sql>
</mapper>