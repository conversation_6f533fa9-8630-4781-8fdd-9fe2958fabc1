<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImConversationDistributeMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImConversationDistribute">
        <id column="conversation_distribute_id" jdbcType="BIGINT" property="conversationDistributeId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="group_id" jdbcType="INTEGER" property="groupId"/>
        <result column="seat_id" jdbcType="BIGINT" property="seatId"/>
        <result column="outer_user_id" jdbcType="VARCHAR" property="outerUserId"/>
        <result column="group_queue_id" jdbcType="BIGINT" property="groupQueueId"/>
        <result column="distribute_rule_json" jdbcType="LONGVARCHAR" property="distributeRuleJson"/>
        <result column="distribute_status" jdbcType="VARCHAR" property="distributeStatus"/>
        <result column="distribute_group_time" jdbcType="TIMESTAMP" property="distributeGroupTime"/>
        <result column="distribute_seat_time" jdbcType="TIMESTAMP" property="distributeSeatTime"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">conversationDistributeId, conversationId, groupId, seatId, outerUserId, groupQueueId, distributeRuleJson, distributeStatus, distributeGroupTime, distributeSeatTime, cancelTime, isDelete, createTime, updateTime </sql>
</mapper>