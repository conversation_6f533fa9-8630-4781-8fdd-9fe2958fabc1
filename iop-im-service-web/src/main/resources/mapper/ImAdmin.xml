<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImAdminMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImAdmin">
        <id column="im_admin_id" jdbcType="BIGINT" property="imAdminId"/>
        <result column="im_id" jdbcType="BIGINT" property="imId"/>
        <result column="admin_account_id" jdbcType="VARCHAR" property="adminAccountId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">imAdminId, imId, adminAccountId, isDelete, updateTime, createTime </sql>
</mapper>