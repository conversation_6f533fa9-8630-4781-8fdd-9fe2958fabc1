<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.VisitorInfoTemplateMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.VisitorInfoTemplate">
        <id column="visitor_info_template_id" jdbcType="BIGINT" property="visitorInfoTemplateId"/>
        <result column="visitor_class_en" jdbcType="VARCHAR" property="visitorClassEn"/>
        <result column="visitor_class_cn" jdbcType="VARCHAR" property="visitorClassCn"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">visitorInfoTemplateId, visitorClassEn, visitorClassCn, tenantId, isDelete, createTime, updateTime </sql>
</mapper>