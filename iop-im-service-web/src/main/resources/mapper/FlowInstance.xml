<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.FlowInstanceMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.FlowInstance">
        <id column="instance_id" jdbcType="BIGINT" property="instanceId"/>
        <result column="flow_define_id" jdbcType="BIGINT" property="flowDefineId"/>
        <result column="outer_user_id" jdbcType="VARCHAR" property="outerUserId"/>
        <result column="current_node_id" jdbcType="BIGINT" property="currentNodeId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">instanceId, flowDefineId, outerUserId, currentNodeId, status, isDelete, updateTime, createTime,conversationId </sql>
</mapper>