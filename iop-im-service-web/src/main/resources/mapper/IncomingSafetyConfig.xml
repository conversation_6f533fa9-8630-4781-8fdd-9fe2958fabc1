<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.IncomingSafetyConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.IncomingSafetyConfig">
        <id column="safety_id" jdbcType="BIGINT" property="safetyId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="url_froms" jdbcType="LONGVARCHAR" property="urlFroms"/>
        <result column="dimension_ip_times" jdbcType="INTEGER" property="dimensionIpTimes"/>
        <result column="dimension_type" jdbcType="VARCHAR" property="dimensionType"/>
        <result column="dimension_value" jdbcType="INTEGER" property="dimensionValue"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">safetyId, tenantId, urlFroms, dimensionIpTimes, dimensionType, dimensionValue, operatorId, status, isDelete, updateTime, createTime </sql>
</mapper>