<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.GroupRuleMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.GroupRule">
        <id column="group_rule_id" jdbcType="BIGINT" property="groupRuleId"/>
        <result column="group_id" jdbcType="INTEGER" property="groupId"/>
        <result column="rule_metric_id" jdbcType="INTEGER" property="ruleMetricId"/>
        <result column="comparison_operator" jdbcType="VARCHAR" property="comparisonOperator"/>
        <result column="rule_config_value" jdbcType="VARCHAR" property="ruleConfigValue"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">groupRuleId, groupId, ruleMetricId, comparisonOperator, ruleConfigValue, operatorId, status, isDelete, createTime, updateTime </sql>
</mapper>