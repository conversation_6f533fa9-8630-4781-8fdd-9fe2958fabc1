<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.SeatStatusFunctionMappingMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.SeatStatusFunctionMapping">
        <id column="seat_status_function_mapping_id" jdbcType="INTEGER" property="seatStatusFunctionMappingId"/>
        <result column="seat_status_id" jdbcType="INTEGER" property="seatStatusId"/>
        <result column="seat_status_en" jdbcType="VARCHAR" property="seatStatusEn"/>
        <result column="seat_function_id" jdbcType="INTEGER" property="seatFunctionId"/>
        <result column="seat_function_en" jdbcType="VARCHAR" property="seatFunctionEn"/>
        <result column="seat_function_type" jdbcType="VARCHAR" property="seatFunctionType"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">seatStatusFunctionMappingId, seatStatusId, seatStatusEn, seatFunctionId, seatFunctionEn, seatFunctionType, isDelete, operatorId, createTime, updateTime </sql>
    <update id="deleteAllBySeatStatusId">
        update
            seat_status_function_mapping
        set
            is_delete = 1
        , operator_id = #{operatorId}
        where
            seat_status_id = #{seatStatusId}
        AND  seat_function_id IN
        <foreach collection="seatFunctionIdList" item="seatFunction" index="index" separator="," open="(" close=")">
            #{seatFunction}
        </foreach>
    </update>

    <select id="getSeatStatusByFunctionEnAndFunctionType"
            resultType="com.wanshifu.iop.im.domain.bo.seat.SeatStatusFunctionMappingRespBo">
        SELECT
            ss.seat_status_id AS seatStatusId,
            ss.seat_status_en AS seatStatusEn,
            ss.seat_status_cn AS seatStatusCn,
            ssfm.seat_function_id AS seatFunctionId,
            ssfm.seat_function_en AS seatFunctionEn,
            ssfm.seat_function_type AS seatFunctionType
        FROM
            seat_status_function_mapping ssfm
                LEFT JOIN
            seat_status ss ON ss.seat_status_id = ssfm.seat_status_id
        WHERE
            ssfm.seat_function_en = #{seatFunctionEn}
            AND ssfm.seat_function_type = #{seatFunctionType}
            AND ssfm.is_delete = 0
    </select>
</mapper>