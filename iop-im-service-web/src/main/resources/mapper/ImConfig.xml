<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImConfig">
        <id column="im_id" jdbcType="BIGINT" property="imId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="im_name" jdbcType="VARCHAR" property="imName"/>
        <result column="im_third_id" jdbcType="VARCHAR" property="imThirdId"/>
        <result column="im_third_secret" jdbcType="VARCHAR" property="imThirdSecret"/>
        <result column="im_desc" jdbcType="VARCHAR" property="imDesc"/>
        <result column="im_type" jdbcType="VARCHAR" property="imType"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">imId, tenantId, imName, imThirdId, imType, imThirdSecret, imDesc, isDelete, updateTime, createTime </sql>
</mapper>