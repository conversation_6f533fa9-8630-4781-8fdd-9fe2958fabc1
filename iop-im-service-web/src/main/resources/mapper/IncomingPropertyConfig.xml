<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.IncomingPropertyConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.IncomingPropertyConfig">
        <id column="property_id" jdbcType="BIGINT" property="propertyId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="property_value" jdbcType="VARCHAR" property="propertyValue"/>
        <result column="property_type" jdbcType="VARCHAR" property="propertyType"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">propertyId, tenantId, propertyValue, propertyType, type, operatorId, status, isDelete, updateTime, createTime </sql>
</mapper>