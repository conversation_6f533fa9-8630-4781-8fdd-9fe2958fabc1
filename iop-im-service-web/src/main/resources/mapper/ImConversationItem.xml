<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImConversationItemMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImConversationItem">
        <id column="conversation_item_id" jdbcType="BIGINT" property="conversationItemId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="from_outer_user_id" jdbcType="VARCHAR" property="fromOuterUserId"/>
        <result column="from_outer_user_type" jdbcType="VARCHAR" property="fromOuterUserType"/>
        <result column="response_outer_user_type" jdbcType="VARCHAR" property="responseOuterUserType"/>
        <result column="response_outer_user_id" jdbcType="VARCHAR" property="responseOuterUserId"/>
        <result column="to_outer_user_id" jdbcType="VARCHAR" property="toOuterUserId"/>
        <result column="to_outer_user_type" jdbcType="VARCHAR" property="toOuterUserType"/>
        <result column="msg_send_time" jdbcType="TIMESTAMP" property="msgSendTime"/>
        <result column="has_read" jdbcType="TINYINT" property="hasRead"/>
        <result column="msg_read_time" jdbcType="TIMESTAMP" property="msgReadTime"/>
        <result column="msg_type" jdbcType="VARCHAR" property="msgType"/>
        <result column="msg_content" jdbcType="LONGVARCHAR" property="msgContent"/>
        <result column="msg_seq" jdbcType="BIGINT" property="msgSeq"/>
        <result column="msg_key" jdbcType="VARCHAR" property="msgKey"/>
        <result column="msg_id" jdbcType="VARCHAR" property="msgId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="msg_label" jdbcType="VARCHAR" property="msgLabel"/>
    </resultMap>
    <sql id="Base_Column_List">conversationItemId, conversationId, fromOuterUserId, fromOuterUserType, toOuterUserId, toOuterUserType, msgSendTime, hasRead,responseOuterUserType,responseOuterUserId, msgReadTime, msgType, msgContent, msgSeq, msgKey, msgId, isDelete, updateTime, createTime, msg_label </sql>
    <update id="updateMsgContentByKey">
        update im_conversation_item
        set msg_content = #{content}
        where conversation_item_id = #{key}
    </update>
    <select id="selectLastMessageByConversationIdList"
            resultMap="BaseResultMap">
        select
            t1.*
        from
            im_conversation_item t1
            RIGHT JOIN (
            SELECT MAX(conversation_item_id) AS conversation_item_id FROM im_conversation_item
            where
            conversation_id in
            <foreach collection="conversationIdList" item="conversationId" index="index" open="(" separator="," close=")">
                #{conversationId}
            </foreach>
            and is_delete = 0
            GROUP BY conversation_id
            ) t2 on t1.conversation_item_id = t2.conversation_item_id

    </select>

    <select id="selectUnreadCountByConversationIdList"
            resultType="com.wanshifu.iop.im.api.resp.CommonLabelValueResp">
        select
            count(1) as value,
            conversation_id as label
        from
            im_conversation_item
        where
            conversation_id in
            <foreach collection="conversationIdList" item="conversationId" index="index" open="(" separator="," close=")">
                #{conversationId}
            </foreach>
            and from_outer_user_type IN ('tourist', 'merchant', 'master', 'enterprise', 'client', 'robot')
            and has_read = 0
            and is_delete = 0
            GROUP BY conversation_id
    </select>

    <select id="selectLastMessageByConversationIdListAndVisitorAndAgent"
            resultMap="BaseResultMap">
        select
        t1.*
        from
        im_conversation_item t1
        RIGHT JOIN (
        SELECT MAX(conversation_item_id) AS conversation_item_id FROM im_conversation_item
        where
        conversation_id in
        <foreach collection="conversationIdList" item="conversationId" index="index" open="(" separator="," close=")">
            #{conversationId}
        </foreach>
        and (
            from_outer_user_type IN ('tourist', 'merchant', 'master', 'enterprise', 'client')
            or ( from_outer_user_type = 'virtual' and response_outer_user_type = 'seat' )
        )
        and is_delete = 0
        GROUP BY conversation_id
        ) t2 on t1.conversation_item_id = t2.conversation_item_id
    </select>

    <select id="countByConversationId" resultType="java.lang.Long">
        select
            count(1)
        from
            im_conversation_item
        where
            conversation_id IN
            <foreach collection="conversationIdList" item="conversationId" index="index" open="(" separator="," close=")">
                #{conversationId}
            </foreach>
            and is_delete = 0
    </select>
    <select id="selectLastListByConversationIdList"
            resultMap="BaseResultMap">

        (SELECT *
         FROM im_conversation_item
         WHERE conversation_id = #{conversationId}
           AND is_delete = 0
           AND response_outer_user_id != ''
         ORDER BY conversation_item_id DESC
             LIMIT 1)
         UNION ALL
         (SELECT *
         FROM im_conversation_item
         WHERE conversation_id = #{conversationId}
           AND is_delete = 0
           AND response_outer_user_id = ''
         ORDER BY conversation_item_id DESC
             LIMIT 1)
        UNION ALL
        (SELECT *
         FROM im_conversation_item
         WHERE conversation_id = #{conversationId}
           AND from_outer_user_type IN ('robot', 'system')
           AND is_delete = 0
           AND response_outer_user_id = ''
         ORDER BY conversation_item_id DESC
             LIMIT 1)

    </select>

    <update id="updateConversationRead">
        update
            im_conversation_item
        set
            has_read = 1,
            msg_read_time = now()
        where
            conversation_id = #{conversationId}
            and has_read = 0
    </update>
</mapper>