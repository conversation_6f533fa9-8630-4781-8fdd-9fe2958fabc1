<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.TagsMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.Tags">
        <id column="tag_id" jdbcType="INTEGER" property="tagId"/>
        <result column="tag_name" jdbcType="VARCHAR" property="tagName"/>
        <result column="tag_desc" jdbcType="VARCHAR" property="tagDesc"/>
        <result column="scene" jdbcType="VARCHAR" property="scene"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">tagId, tagName, tagDesc, scene, isDelete, operatorId, createTime, updateTime </sql>
    <select id="selectExistById" resultType="java.lang.Integer">
        select
            count(tag_id)
        from
            tags
        where
            tag_id = #{tagId}
          and is_delete = 0
          and status = 0
    </select>

    <select id="getEnums" resultType="com.wanshifu.iop.im.domain.po.Tags">
        SELECT
            tag_id AS tagId,
            tag_name AS tagName,
            status AS status
        FROM
            tags
        WHERE
            scene = #{scene}
            AND (
                    ( status = 0
                    AND is_delete = 0 )
                    <if test="includeEnumsValueIdList != null and includeEnumsValueIdList.size()>0">
                        OR tag_id IN
                        <foreach item="item" index="index" collection="includeEnumsValueIdList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
            )
    </select>
</mapper>