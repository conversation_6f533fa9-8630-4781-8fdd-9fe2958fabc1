<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ConversationVisitorDetailMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ConversationVisitorDetail">
        <id column="conversation_visitor_detail_id" jdbcType="BIGINT" property="conversationVisitorDetailId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="device_Id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="device_type" jdbcType="VARCHAR" property="deviceType"/>
        <result column="device_version" jdbcType="VARCHAR" property="deviceVersion"/>
        <result column="phone_model" jdbcType="VARCHAR" property="phoneModel"/>
        <result column="conversation_page_url" jdbcType="VARCHAR" property="conversationPageUrl"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">conversationVisitorDetailId, conversationId, ip, deviceId, deviceType, deviceVersion, phoneModel, conversationPageUrl, isDelete, createTime, updateTime </sql>
</mapper>