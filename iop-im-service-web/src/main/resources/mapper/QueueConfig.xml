<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.QueueConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.QueueConfig">
        <id column="queue_config_id" jdbcType="INTEGER" property="queueConfigId"/>
        <result column="from_id" jdbcType="BIGINT" property="fromId"/>
        <result column="from_type" jdbcType="VARCHAR" property="fromType"/>
        <result column="max_length" jdbcType="BIGINT" property="maxLength"/>
        <result column="member_interval_minute" jdbcType="BIGINT" property="memberIntervalMinute"/>
        <result column="msg_ttl" jdbcType="BIGINT" property="msgTtl"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">queueConfigId, fromId, fromType, maxLength, memberIntervalMinute, msgTtl, operatorId, isDelete, createTime, updateTime </sql>
</mapper>