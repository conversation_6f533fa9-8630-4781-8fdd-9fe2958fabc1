<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.channel.ClientChannelConfigDraftMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.channel.ClientChannelConfigDraft">
        <id column="client_channel_config_draft_id" jdbcType="BIGINT" property="clientChannelConfigDraftId"/>
        <result column="client_category_name" jdbcType="VARCHAR" property="clientCategoryName"/>
        <result column="client_category_type" jdbcType="VARCHAR" property="clientCategoryType"/>
        <result column="client_category_en" jdbcType="VARCHAR" property="clientCategoryEn"/>
        <result column="rule_indicators_config_en" jdbcType="VARCHAR" property="ruleIndicatorsConfigEn"/>
        <result column="um_id" jdbcType="BIGINT" property="umId"/>
        <result column="all_config_json" jdbcType="LONGVARCHAR" property="allConfigJson"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">clientChannelConfigDraftId, clientCategoryName, clientCategoryType, ruleIndicatorsConfigEn, clientCategoryEn, umId, allConfigJson, status, isDelete, operatorId, createTime, updateTime </sql>

    <insert id="insertOrUpdateClientCategoryDraft">
        INSERT INTO client_channel_config_draft (
            client_channel_config_draft_id,
            client_category_name,
            client_category_type,
            client_category_en,
            rule_indicators_config_en,
            um_id,
            all_config_json,
            status,
            is_delete,
            operator_id,
            create_time,
            update_time
        ) VALUES (
                     #{clientChannelConfigDraftId},
                     #{clientCategoryName},
                     #{clientCategoryType},
                     #{clientCategoryEn},
                     #{ruleIndicatorsConfigEn},
                     #{umId},
                     #{allConfigJson},
                     #{status},
                     #{isDelete},
                     #{operatorId},
                     #{createTime},
                     #{updateTime}
                 )
        ON DUPLICATE KEY UPDATE
                             client_category_name = VALUES(client_category_name),
                             client_category_type = VALUES(client_category_type),
                             client_category_en = VALUES(client_category_en),
                             rule_indicators_config_en = VALUES(rule_indicators_config_en),
                             um_id = VALUES(um_id),
                             all_config_json = VALUES(all_config_json),
                             status = VALUES(status),
                             is_delete = VALUES(is_delete),
                             update_time = VALUES(update_time)
    </insert>

    <update id="batchUpdateClientChannelConfigDraft">
        <foreach item="item" collection="list" separator=";">
            UPDATE client_channel_config_draft
            SET
                client_category_name = #{item.clientCategoryName},
                client_category_type = #{item.clientCategoryType},
                client_category_en = #{item.ruleIndicatorsConfigEn},
                all_config_json = #{item.allConfigJson},
                status = #{item.status},
                update_time = #{item.updateTime}
            WHERE client_channel_config_draft_id = #{item.clientChannelConfigDraftId}
        </foreach>
    </update>
</mapper>