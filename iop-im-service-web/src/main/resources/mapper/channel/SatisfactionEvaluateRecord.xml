<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.channel.SatisfactionEvaluateRecordMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.channel.SatisfactionEvaluateRecord">
        <id column="satisfaction_evaluate_record_id" jdbcType="BIGINT" property="satisfactionEvaluateRecordId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="satisfaction_level_config_id" jdbcType="BIGINT" property="satisfactionLevelConfigId"/>
        <result column="guideline_desc" jdbcType="BIGINT" property="guidelineDesc"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="has_solve_problem" jdbcType="TINYINT" property="hasSolveProblem"/>
        <result column="result_callback_form_json" jdbcType="LONGVARCHAR" property="resultCallbackFormJson"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">satisfactionEvaluateRecordId, channelId, satisfactionLevelConfigId,guidelineDesc, title, hasSolveProblem, resultCallbackFormJson, configJson, status, isDelete, operatorId, createTime, updateTime </sql>
</mapper>