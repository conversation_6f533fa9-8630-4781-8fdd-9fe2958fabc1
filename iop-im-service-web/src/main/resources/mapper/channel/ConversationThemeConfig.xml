<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.channel.ConversationThemeConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.channel.ConversationThemeConfig">
        <id column="conversation_theme_config_id" jdbcType="BIGINT" property="conversationThemeConfigId"/>
        <result column="client_user_type" jdbcType="VARCHAR" property="clientUserType"/>
        <result column="client_port_type" jdbcType="VARCHAR" property="clientPortType"/>
        <result column="theme_type" jdbcType="VARCHAR" property="themeType"/>
        <result column="config_json" jdbcType="VARCHAR" property="configJson"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">conversationThemeConfigId, clientPortType, clientUserType, themeType,configJson, isDelete, operatorId, createTime, updateTime </sql>
</mapper>