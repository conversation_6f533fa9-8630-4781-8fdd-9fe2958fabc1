<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.channel.ConversationStyleConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.channel.ConversationStyleConfig">
        <id column="conversation_style_config_id" jdbcType="BIGINT" property="conversationStyleConfigId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="client_port_type" jdbcType="VARCHAR" property="clientPortType"/>
        <result column="other_json" jdbcType="VARCHAR" property="otherJson"/>
        <result column="conversation_theme_id" jdbcType="BIGINT" property="conversationThemeId"/>
        <result column="sidebar_json" jdbcType="LONGVARCHAR" property="sidebarJson"/>
        <result column="client_category_id" jdbcType="BIGINT" property="clientCategoryId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">conversationStyleConfigId, channelId, clientPortType, otherJson, conversationThemeId, sidebarJson, clientCategoryId, status, isDelete, operatorId, createTime, updateTime </sql>

    <insert id="batchInsertOrUpdateConversationStyleConfig">
        <foreach item="item" collection="list" separator=";">
            INSERT INTO conversation_style_config (
                conversation_style_config_id,
                channel_id,
                client_port_type,
                other_json,
                conversation_theme_id,
                sidebar_json,
                client_category_id,
                status,
                is_delete,
                operator_id,
                create_time,
                update_time
            ) VALUES (
                #{item.conversationStyleConfigId},
                #{item.channelId},
                #{item.clientPortType},
                #{item.otherJson},
                #{item.conversationThemeId},
                #{item.sidebarJson},
                #{item.clientCategoryId},
                #{item.status},
                #{item.isDelete},
                #{item.operatorId},
                #{item.createTime},
                #{item.updateTime}
            )
            ON DUPLICATE KEY UPDATE
                    channel_id = VALUES(channel_id),
                    client_port_type = VALUES(client_port_type),
                    other_json = VALUES(other_json),
                    conversation_theme_id = VALUES(conversation_theme_id),
                    sidebar_json = VALUES(sidebar_json),
                    client_category_id = VALUES(client_category_id),
                    status = VALUES(status),
                    is_delete = VALUES(is_delete),
                    operator_id = VALUES(operator_id),
                    update_time = VALUES(update_time)
        </foreach>
    </insert>
    <insert id="insertOrUpdateConversationStyleConfig">
            INSERT INTO conversation_style_config (
            conversation_style_config_id,
            channel_id,
            client_port_type,
            other_json,
            conversation_theme_id,
            sidebar_json,
            client_category_id,
            status,
            is_delete,
            operator_id,
            create_time,
            update_time
            ) VALUES (
            #{item.conversationStyleConfigId},
            #{item.channelId},
            #{item.clientPortType},
            #{item.otherJson},
            #{item.conversationThemeId},
            #{item.sidebarJson},
            #{item.clientCategoryId},
            #{item.status},
            #{item.isDelete},
            #{item.operatorId},
            #{item.createTime},
            #{item.updateTime}
            )
            ON DUPLICATE KEY UPDATE
            channel_id = VALUES(channel_id),
            client_port_type = VALUES(client_port_type),
            other_json = VALUES(other_json),
            conversation_theme_id = VALUES(conversation_theme_id),
            sidebar_json = VALUES(sidebar_json),
            client_category_id = VALUES(client_category_id),
            status = VALUES(status),
            is_delete = VALUES(is_delete),
            operator_id = VALUES(operator_id),
            update_time = VALUES(update_time)
    </insert>
  <insert id="batchInsertOrUpdateConversationStyleConfigV2">
    <foreach item="item" collection="list" separator=";">
      INSERT INTO conversation_style_config (
      conversation_style_config_id,
      channel_id,
      client_port_type,
      other_json,
      conversation_theme_id,
      sidebar_json,
      client_category_id,
      status,
      is_delete,
      operator_id,
      create_time,
      update_time
      ) VALUES (
      #{item.conversationStyleConfigId},
      #{item.channelId},
      #{item.clientPortType},
      #{item.otherJson},
      #{item.conversationThemeId},
      #{item.sidebarJson},
      #{item.clientCategoryId},
      #{item.status},
      #{item.isDelete},
      #{item.operatorId},
      #{item.createTime},
      #{item.updateTime}
      )
      ON DUPLICATE KEY UPDATE
      channel_id = VALUES(channel_id),
      client_port_type = VALUES(client_port_type),
      other_json = VALUES(other_json),
      conversation_theme_id = VALUES(conversation_theme_id),
      sidebar_json = VALUES(sidebar_json),
      client_category_id = VALUES(client_category_id),
      status = VALUES(status),
      is_delete = VALUES(is_delete),
      operator_id = VALUES(operator_id),
      update_time = VALUES(update_time)
    </foreach>
  </insert>
</mapper>