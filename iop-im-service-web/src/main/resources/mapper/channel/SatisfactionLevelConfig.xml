<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.channel.SatisfactionLevelConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.channel.SatisfactionLevelConfig">
        <id column="satisfaction_level_config_id" jdbcType="BIGINT" property="satisfactionLevelConfigId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="result_callback_form_json" jdbcType="LONGVARCHAR" property="resultCallbackFormJson"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">satisfactionLevelConfigId, channelId, resultCallbackFormJson, configJson, status, isDelete, operatorId, createTime, updateTime </sql>

    <insert id="insertOrUpdateSatisfactionLevelConfigBo">
            INSERT INTO satisfaction_level_config (
                satisfaction_level_config_id,
                channel_id,
                result_callback_form_json,
                status,
                is_delete,
                operator_id,
                create_time,
                update_time
            ) VALUES (
                #{satisfactionLevelConfigId},
                #{channelId},
                #{resultCallbackFormJson},
                #{status},
                #{isDelete},
                #{operatorId},
                #{createTime},
                #{updateTime}
            )
            ON DUPLICATE KEY UPDATE
                channel_id = VALUES(channel_id),
                result_callback_form_json = VALUES(result_callback_form_json),
                status = VALUES(status),
                is_delete = VALUES(is_delete),
                operator_id = VALUES(operator_id),
                update_time = VALUES(update_time)
    </insert>

</mapper>