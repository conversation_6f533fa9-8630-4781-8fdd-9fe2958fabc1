<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.channel.ConversationAutoReplyConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyConfig">
        <id column="conversation_auto_reply_config_id" jdbcType="BIGINT" property="conversationAutoReplyConfigId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="msg_type" jdbcType="VARCHAR" property="msgType"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">conversationAutoReplyConfigId, channelId, msgType, status, isDelete, operatorId, createTime, updateTime </sql>

    <insert id="insertOrUpdateConversationAutoReplyConfig">
        INSERT INTO conversation_auto_reply_config (
            conversation_auto_reply_config_id,
            channel_id,
            msg_type,
            status,
            is_delete,
            operator_id,
            create_time,
            update_time
        )
        VALUES(
            #{conversationAutoReplyConfigId, jdbcType=BIGINT},
            #{channelId, jdbcType=BIGINT},
            #{msgType, jdbcType=VARCHAR},
            #{status, jdbcType=TINYINT},
            #{isDelete, jdbcType=TINYINT},
            #{operatorId, jdbcType=BIGINT},
            #{createTime, jdbcType=TIMESTAMP},
            #{updateTime, jdbcType=TIMESTAMP}
         )
        ON DUPLICATE KEY UPDATE
        channel_id = VALUES(channel_id),
        msg_type = VALUES(msg_type),
        status = VALUES(status),
        is_delete = VALUES(is_delete),
        operator_id = VALUES(operator_id),
        create_time = VALUES(create_time),
        update_time = VALUES(update_time)
    </insert>
</mapper>