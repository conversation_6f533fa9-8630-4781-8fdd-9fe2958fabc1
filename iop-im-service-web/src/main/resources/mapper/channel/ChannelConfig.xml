<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.channel.ChannelConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.channel.ChannelConfig">
        <id column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="channel_name" jdbcType="VARCHAR" property="channelName"/>
        <result column="channel_en" jdbcType="VARCHAR" property="channelEn"/>
        <result column="channel_desc" jdbcType="VARCHAR" property="channelDesc"/>
        <result column="channel_type" jdbcType="VARCHAR" property="channelType"/>
        <result column="flow_define_id" jdbcType="BIGINT" property="flowDefineId"/>
        <result column="client_category_id" jdbcType="BIGINT" property="clientCategoryId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="preview_url_template" jdbcType="VARCHAR" property="previewUrlTemplate"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">channelId, channelName, channelEn, channelDesc, channelType, flowDefineId, clientCategoryId, status, previewUrlTemplate, isDelete, operatorId, createTime, updateTime </sql>

    <insert id="insertOrUpdateChannelConfig">
        INSERT INTO channel_config (
            channel_id,
            channel_name,
            channel_en,
            channel_desc,
            channel_type,
            flow_define_id,
            client_category_id,
            status,
            preview_url_template,
            is_delete,
            operator_id,
            create_time,
            update_time
        ) VALUES (
                     #{channelId},
                     #{channelName},
                     #{channelEn},
                     #{channelDesc},
                     #{channelType},
                     #{flowDefineId},
                     #{clientCategoryId},
                     #{status},
                     #{previewUrlTemplate},
                     #{isDelete},
                     #{operatorId},
                     #{createTime},
                     #{updateTime}
                 )
        ON DUPLICATE KEY UPDATE
                             channel_name = VALUES(channel_name),
                             channel_en = VALUES(channel_en),
                             channel_desc = VALUES(channel_desc),
                             channel_type = VALUES(channel_type),
                             flow_define_id = VALUES(flow_define_id),
                             client_category_id = VALUES(client_category_id),
                             status = VALUES(status),
                             preview_url_template = VALUES(preview_url_template),
                             is_delete = VALUES(is_delete),
                             operator_id = VALUES(operator_id),
                             update_time = VALUES(update_time)
    </insert>

    <insert id="batchInsertOrUpdateChannelConfig">
        <foreach item="item" collection="list" separator=";">
            INSERT INTO channel_config (
                channel_id,
                channel_name,
                channel_en,
                channel_desc,
                channel_type,
                flow_define_id,
                client_category_id,
                status,
                preview_url_template,
                is_delete,
                operator_id,
                create_time,
                update_time
            ) VALUES (
                #{item.channelId},
                #{item.channelName},
                #{item.channelEn},
                #{item.channelDesc},
                #{item.channelType},
                #{item.flowDefineId},
                #{item.clientCategoryId},
                #{item.status},
                #{item.previewUrlTemplate},
                #{item.isDelete},
                #{item.operatorId},
                #{item.createTime},
                #{item.updateTime}
            )
            ON DUPLICATE KEY UPDATE
                        channel_name = VALUES(channel_name),
                        channel_en = VALUES(channel_en),
                        channel_desc = VALUES(channel_desc),
                        channel_type = VALUES(channel_type),
                        flow_define_id = VALUES(flow_define_id),
                        client_category_id = VALUES(client_category_id),
                        status = VALUES(status),
                        preview_url_template = VALUES(preview_url_template),
                        is_delete = VALUES(is_delete),
                        operator_id = VALUES(operator_id),
                        update_time = VALUES(update_time)
        </foreach>
    </insert>

    <insert id="batchInsertOrUpdateChannelBo">
        <foreach item="item" collection="list" separator=";">
            INSERT INTO channel_config (
                channel_id,
                channel_name,
                channel_en,
                channel_desc,
                channel_type,
                flow_define_id,
                client_category_id,
                status,
                preview_url_template,
                is_delete,
                operator_id,
                create_time,
                update_time
            ) VALUES (
                #{item.channelId},
                #{item.channelName},
                #{item.channelEn},
                #{item.channelDesc},
                #{item.channelType},
                #{item.flowDefineId},
                #{item.clientCategoryId},
                #{item.status},
                #{item.previewUrlTemplate},
                #{item.isDelete},
                #{item.operatorId},
                #{item.createTime},
                #{item.updateTime}
            )
            ON DUPLICATE KEY UPDATE
                channel_name = VALUES(channel_name),
                channel_en = VALUES(channel_en),
                channel_desc = VALUES(channel_desc),
                channel_type = VALUES(channel_type),
                flow_define_id = VALUES(flow_define_id),
                client_category_id = VALUES(client_category_id),
                status = VALUES(status),
                preview_url_template = VALUES(preview_url_template),
                is_delete = VALUES(is_delete),
                operator_id = VALUES(operator_id),
                update_time = VALUES(update_time)
        </foreach>
    </insert>

    <select id="getEnums" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            channel_config
        WHERE
            <if test="enumsParentIdList != null and enumsParentIdList.size()>0">
                client_category_id IN
                <foreach item="enumsParentId" index="index" collection="enumsParentIdList" open="(" separator="," close=")">
                    #{enumsParentId}
                </foreach>
            </if>
            AND (
                ( status = 0
                AND is_delete = 0 )
                <if test="includeEnumsValueIdList != null and includeEnumsValueIdList.size()>0">
                    OR channel_id IN
                    <foreach item="item" index="index" collection="includeEnumsValueIdList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            )

    </select>

    <select id="checkNonexistent" resultType="java.lang.Long">
        SELECT
            channel_id
        FROM
            channel_config
        WHERE
            channel_id IN
            <foreach item="item" index="index" collection="existIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND client_category_id IN
            <foreach item="parentId" index="index" collection="parentIdList" open="(" separator="," close=")">
                #{parentId}
            </foreach>
            AND status = 0
            AND is_delete = 0
    </select>
    <select id="getChannelConfigDraftList" resultType="com.wanshifu.iop.im.api.resp.channel.GetChannelConfigDraftListResp">
        select channel_id as channelId,
               channel_name as channelName,
               client_category_id as clientCategoryId,
               client_category_name as clientCategoryName
        from channel_config  as  channelConfig left join client_category_config cateConfig
        on channelConfig.client_category_id = cateConfig.client_category_config_id
    </select>
</mapper>