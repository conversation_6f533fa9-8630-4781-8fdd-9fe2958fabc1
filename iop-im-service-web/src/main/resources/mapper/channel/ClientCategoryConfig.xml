<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.channel.ClientCategoryConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.channel.ClientCategoryConfig">
        <id column="client_category_config_id" jdbcType="BIGINT" property="clientCategoryConfigId"/>
        <result column="client_category_name" jdbcType="VARCHAR" property="clientCategoryName"/>
        <result column="client_category_type" jdbcType="VARCHAR" property="clientCategoryType"/>
        <result column="client_category_en" jdbcType="VARCHAR" property="clientCategoryEn"/>
        <result column="um_id" jdbcType="BIGINT" property="umId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">clientCategoryConfigId, clientCategoryName, clientCategoryType, clientCategoryEn, umId, isDelete, operatorId, createTime, updateTime </sql>

    <insert id="insertOrUpdateClientCategoryConfig">
        INSERT INTO client_category_config (
            client_category_config_id,
            client_category_name,
            client_category_type,
            client_category_en,
            um_id,
            is_delete,
            operator_id,
            create_time,
            update_time
        ) VALUES (
                     #{clientCategoryConfigId},
                     #{clientCategoryName},
                     #{clientCategoryType},
                     #{clientCategoryEn},
                     #{umId},
                     #{isDelete},
                     #{operatorId},
                     #{createTime},
                     #{updateTime}
                 )
        ON DUPLICATE KEY UPDATE
                             client_category_name = VALUES(client_category_name),
                             client_category_type = VALUES(client_category_type),
                             client_category_en = VALUES(client_category_en),
                             um_id = VALUES(um_id),
                             is_delete = VALUES(is_delete),
                             operator_id = VALUES(operator_id),
                             update_time = VALUES(update_time)
    </insert>

    <insert id="insertOrUpdateClientCategoryConfigBo">
        INSERT INTO client_category_config (
            client_category_config_id,
            client_category_name,
            client_category_type,
            client_category_en,
            um_id,
            is_delete,
            operator_id,
            create_time,
            update_time
        ) VALUES (
                     #{clientCategoryConfigId},
                     #{clientCategoryName},
                     #{clientCategoryType},
                     #{clientCategoryEn},
                     #{umId},
                     #{isDelete},
                     #{operatorId},
                     #{createTime},
                     #{updateTime}
                 )
        ON DUPLICATE KEY UPDATE
                             client_category_name = VALUES(client_category_name),
                             client_category_type = VALUES(client_category_type),
                             client_category_en = VALUES(client_category_en),
                             um_id = VALUES(um_id),
                             is_delete = VALUES(is_delete),
                             operator_id = VALUES(operator_id),
                             update_time = VALUES(update_time)
    </insert>

    <select id="getEnums" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            client_category_config
        WHERE (
        ( is_delete = 0 )
        <if test="includeEnumsValueIdList != null and includeEnumsValueIdList.size()>0">
            OR client_category_config_id IN
            <foreach item="item" index="index" collection="includeEnumsValueIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )
    </select>

    <select id="checkNonexistent" resultType="java.lang.Long">
        SELECT
            client_category_config_id
        FROM
            client_category_config
        WHERE
            client_category_config_id IN
            <foreach item="item" index="index" collection="existIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND is_delete = 0
    </select>
</mapper>