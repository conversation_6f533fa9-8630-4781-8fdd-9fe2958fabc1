<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.channel.ConversationAutoReplyDetailConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.channel.ConversationAutoReplyDetailConfig">
        <id column="conversation_auto_reply_detail_config_id" jdbcType="BIGINT" property="conversationAutoReplyDetailConfig"/>
        <result column="conversation_auto_reply_config_id" jdbcType="BIGINT" property="conversationAutoReplyConfigId"/>
        <result column="msg_type" jdbcType="VARCHAR" property="msgType"/>
        <result column="msg_sub_type" jdbcType="VARCHAR" property="msgSubType"/>
        <result column="sub_type_show_seq" jdbcType="TINYINT" property="subTypeShowSeq"/>
        <result column="autoReply_receive_object" jdbcType="VARCHAR" property="autoReplyReceiveObject"/>
        <result column="autoReply_content" jdbcType="VARCHAR" property="autoReplyContent"/>
        <result column="trigger_mode" jdbcType="VARCHAR" property="triggerMode"/>
        <result column="timeout_cost_second" jdbcType="VARCHAR" property="timeoutCostSecond"/>
        <result column="support_placeholder" jdbcType="TINYINT" property="supportPlaceholder"/>
        <result column="extra_json" jdbcType="VARCHAR" property="extraJson"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">conversationAutoReplyDetailConfig, conversationAutoReplyConfigId, msgType, msgSubType, subTypeShowSeq, autoReplyReceiveObject, autoReplyContent, triggerMode, timeoutCostSecond, supportPlaceholder, extraJson, status, isDelete, operatorId, createTime, updateTime </sql>

    <insert id="insertOrUpdateConversationAutoReplyDetailConfig">
        INSERT INTO conversation_auto_reply_detail_config (
            conversation_auto_reply_detail_config_id,
            conversation_auto_reply_config_id,
            msg_type,
            msg_sub_type,
            sub_type_show_seq,
            autoReply_receive_object,
            autoreply_content,
            trigger_mode,
            timeout_cost_second,
            support_placeholder,
            extra_json,
            status,
            is_delete,
            operator_id,
            create_time,
            update_time
        ) VALUES (
                     #{conversationAutoReplyDetailConfig},
                     #{conversationAutoReplyConfigId},
                     #{msgType},
                     #{msgSubType},
                     #{subTypeShowSeq},
                     #{autoReplyReceiveObject},
                     #{autoReplyContent},
                     #{triggerMode},
                     #{timeoutCostSecond},
                     #{supportPlaceholder},
                     #{extraJson},
                     #{status},
                     #{isDelete},
                     #{operatorId},
                     #{createTime},
                     #{updateTime}
                 )
        ON DUPLICATE KEY UPDATE
                             conversation_auto_reply_config_id = VALUES(conversation_auto_reply_config_id),
                             msg_type = VALUES(msg_type),
                             msg_sub_type = VALUES(msg_sub_type),
                             sub_type_show_seq = VALUES(sub_type_show_seq),
                             autoReply_receive_object = VALUES(autoReply_receive_object),
                             autoreply_content = VALUES(autoreply_content),
                             trigger_mode = VALUES(trigger_mode),
            timeout_cost_second = VALUES(timeout_cost_second),
                             support_placeholder = VALUES(support_placeholder),
                             extra_json = VALUES(extra_json),
                             status = VALUES(status),
                             is_delete = VALUES(is_delete),
                             operator_id = VALUES(operator_id),
                             update_time = VALUES(update_time)
    </insert>

    <select id="batchSelectConfig"
            resultType="com.wanshifu.iop.im.domain.bo.SelectConversationAutoReplyDetailConfigRespBo">
        select
        r.channel_id as channelId,
        r.msg_type as msgType,
        d.msg_sub_type as msgSubType,
        d.timeout_cost_second as timeoutCostSecond,
        d.extra_json as extraJson
        from conversation_auto_reply_config r
        LEFT JOIN conversation_auto_reply_detail_config d ON r.conversation_auto_reply_config_id = d.conversation_auto_reply_config_id
        where
        r.channel_id in
        <foreach collection="channelIdList" item="channelId" index="index" open="(" separator="," close=")">
            #{channelId}
        </foreach>

          <if test="msgTypeList != null and msgTypeList.size() > 0">
            and r.msg_type in
            <foreach collection="msgTypeList" item="msgType" index="index" open="(" separator="," close=")">
                #{msgType}
            </foreach>
          </if>

          <if test="msgSubTypeList != null and msgSubTypeList.size() > 0">
            and d.msg_sub_type in
            <foreach collection="msgSubTypeList" item="msgSubType" index="index" open="(" separator="," close=")">
                #{msgSubType}
            </foreach>
          </if>

        and r.status = 1
        and r.is_delete = 0
        and d.status = 1
        and d.is_delete = 0
    </select>
</mapper>