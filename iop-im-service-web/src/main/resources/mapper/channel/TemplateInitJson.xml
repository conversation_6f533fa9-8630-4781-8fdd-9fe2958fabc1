<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.channel.TemplateInitJsonMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.channel.TemplateInitJson">
        <id column="template_init_json_id" jdbcType="BIGINT" property="templateInitJsonId"/>
        <result column="table_type_name" jdbcType="VARCHAR" property="tableTypeName"/>
        <result column="column_type_name" jdbcType="VARCHAR" property="columnTypeName"/>
        <result column="init_json_type" jdbcType="VARCHAR" property="initJsonType"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">templateInitJsonId, tableTypeName, columnTypeName, initJsonType, content, isDelete, operatorId, createTime, updateTime </sql>
</mapper>