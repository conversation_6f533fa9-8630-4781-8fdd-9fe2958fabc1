<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.BusinessUserAccountMappingMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.BusinessUserAccountMapping">
        <id column="business_user_account_mapping_id" jdbcType="BIGINT" property="businessUserAccountMappingId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="account_id" jdbcType="BIGINT" property="accountId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">businessUserAccountMappingId, userId, userType, accountId, status, isDelete, operatorId, createTime, updateTime </sql>
</mapper>