<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImConversationActiveLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImConversationActiveLog">
        <id column="conversation_active_log_id" jdbcType="BIGINT" property="conversationActiveLogId"/>
        <result column="from_conversation_id" jdbcType="BIGINT" property="fromConversationId"/>
        <result column="to_conversation_id" jdbcType="BIGINT" property="toConversationId"/>
        <result column="activate_scene" jdbcType="VARCHAR" property="activateScene"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">conversationActiveLogId, fromConversationId, toConversationId, activateScene, isDelete, createTime, updateTime </sql>
    <update id="deleteLogByConversationId">
        update
            im_conversation_active_log
        set is_delete = 1
        where
            to_conversation_id = #{conversationId}
        and is_delete = 0
    </update>
</mapper>