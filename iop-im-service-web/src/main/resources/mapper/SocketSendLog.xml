<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.SocketSendLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.SocketSendLog">
        <id column="socket_send_log_id" jdbcType="BIGINT" property="socketSendLogId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="subscriber_class" jdbcType="VARCHAR" property="subscriberClass"/>
        <result column="subscriber_id" jdbcType="VARCHAR" property="subscriberId"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="message_content" jdbcType="LONGVARCHAR" property="messageContent"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">socketSendLogId, conversationId, subscriberClass, subscriberId, businessType, messageContent, isDelete, createTime, updateTime </sql>
</mapper>