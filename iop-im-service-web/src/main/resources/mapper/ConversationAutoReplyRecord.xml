<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ConversationAutoReplyRecordMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ConversationAutoReplyRecord">
        <id column="record_id" jdbcType="BIGINT" property="recordId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="conversation_item_id" jdbcType="BIGINT" property="conversationItemId"/>
        <result column="conversation_auto_reply_detail_config_id" jdbcType="BIGINT" property="conversationAutoReplyDetailConfigId"/>
        <result column="trigger_mode" jdbcType="VARCHAR" property="triggerMode"/>
        <result column="record_status" jdbcType="VARCHAR" property="recordStatus"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">recordId, conversationId, conversationItemId, conversationAutoReplyDetailConfigId, triggerMode, recordStatus, content, isDelete, updateTime, createTime </sql>
</mapper>