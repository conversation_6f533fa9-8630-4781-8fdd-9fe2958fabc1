<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.VisitorMarkMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.VisitorMark">
        <id column="visitor_mark_id" jdbcType="BIGINT" property="visitorMarkId"/>
        <result column="account_id" jdbcType="BIGINT" property="accountId"/>
        <result column="account_class" jdbcType="VARCHAR" property="accountClass"/>
        <result column="outer_user_id" jdbcType="VARCHAR" property="outerUserId"/>
        <result column="label_value" jdbcType="VARCHAR" property="labelValue"/>
        <result column="label_name" jdbcType="VARCHAR" property="labelName"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">visitorMarkId, accountId, accountClass, outerUserId, labelValue, labelName, isDelete, createTime, updateTime </sql>

    <update id="deleteByOuterUserIdAndAccount">
        update
            visitor_mark
        set
            is_delete = 1
        where
            outer_user_id = #{outerUserId}
          and account_id = #{accountId}
          and account_class = #{accountClass}
    </update>

    <update id="deleteSingleByOuterUserIdAndAccount">
        update
            visitor_mark
        set
            is_delete = 1
        where
            outer_user_id = #{outerUserId}
          and account_id = #{accountId}
          and account_class = #{accountClass}
          and label_value = #{removeLabel}
          and is_delete = 0
    </update>
</mapper>