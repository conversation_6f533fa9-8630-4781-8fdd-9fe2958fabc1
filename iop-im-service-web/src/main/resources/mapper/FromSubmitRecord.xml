<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.FromSubmitRecordMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.FromSubmitRecord">
        <id column="from_submit_record_id" jdbcType="BIGINT" property="fromSubmitRecordId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="seat_id" jdbcType="BIGINT" property="seatId"/>
        <result column="business_from_template_id" jdbcType="BIGINT" property="businessFromTemplateId"/>
        <result column="submit_content" jdbcType="LONGVARCHAR" property="submitContent"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">fromSubmitRecordId, conversationId, seatId, businessFromTemplateId, submitContent, isDelete, createTime, updateTime </sql>
</mapper>