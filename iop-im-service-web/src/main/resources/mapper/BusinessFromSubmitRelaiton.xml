<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.BusinessFromSubmitRelaitonMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.BusinessFromSubmitRelaiton">
        <id column="business_from_submit_relaiton_id" jdbcType="BIGINT" property="businessFromSubmitRelaitonId"/>
        <result column="from_submit_record_id" jdbcType="BIGINT" property="fromSubmitRecordId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="business_from_id" jdbcType="BIGINT" property="businessFromId"/>
        <result column="business_from_type" jdbcType="VARCHAR" property="businessFromType"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">businessFromSubmitRelaitonId, fromSubmitRecordId, conversationId, businessFromId, businessFromType, isDelete, createTime, updateTime </sql>
</mapper>