<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.SeatStatusMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.SeatStatus">
        <id column="seat_status_id" jdbcType="INTEGER" property="seatStatusId"/>
        <result column="seat_status_cn" jdbcType="VARCHAR" property="seatStatusCn"/>
        <result column="seat_status_en" jdbcType="VARCHAR" property="seatStatusEn"/>
        <result column="seat_status_desc" jdbcType="VARCHAR" property="seatStatusDesc"/>
        <result column="default_type" jdbcType="TINYINT" property="defaultType"/>
        <result column="is_reckon_in_man_hour" jdbcType="TINYINT" property="isReckonInManHour"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">seatStatusId, seatStatusCn, seatStatusEn, seatStatusDesc, defaultType, isReckonInManHour, isDelete, operatorId, createTime, updateTime </sql>

    <select id="selectStatusByFunctionEn" resultMap="BaseResultMap">
        SELECT
            s.*
        FROM
            seat_status s
        LEFT JOIN seat_status_function_mapping m ON s.seat_status_en = m.seat_status_en AND m.is_delete = 0
        WHERE
            m.seat_function_en = #{functionEn}
            AND s.is_delete = 0
    </select>
</mapper>