<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.SatisfactionEvaluateResultRecordMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.SatisfactionEvaluateResultRecord">
        <id column="satisfaction_evaluate_result_record_id" jdbcType="BIGINT" property="satisfactionEvaluateResultRecordId"/>
        <result column="satisfaction_evaluate_record_id" jdbcType="BIGINT" property="satisfactionEvaluateRecordId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="seat_id" jdbcType="BIGINT" property="seatId"/>
        <result column="out_user_id" jdbcType="BIGINT" property="outUserId"/>
        <result column="guideline_desc" jdbcType="VARCHAR" property="guidelineDesc"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="has_solve_problem" jdbcType="TINYINT" property="hasSolveProblem"/>
        <result column="result_callback_form_json" jdbcType="LONGVARCHAR" property="resultCallbackFormJson"/>
        <result column="result_json" jdbcType="LONGVARCHAR" property="resultJson"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">satisfactionEvaluateResultRecordId, satisfactionEvaluateRecordId, channelId, seatId, outUserId, guidelineDesc, title, hasSolveProblem, resultCallbackFormJson, resultJson, isDelete, createTime, updateTime </sql>
</mapper>