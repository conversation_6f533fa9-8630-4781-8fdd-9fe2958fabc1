<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.OperateLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.OperateLog">
        <id column="operate_log_id" jdbcType="BIGINT" property="operateLogId"/>
        <result column="from_table_id" jdbcType="BIGINT" property="fromTableId"/>
        <result column="from_table_name" jdbcType="VARCHAR" property="fromTableName"/>
        <result column="from_business" jdbcType="VARCHAR" property="fromBusiness"/>
        <result column="operate_content" jdbcType="LONGVARCHAR" property="operateContent"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">operateLogId, fromTableId, fromTableName, fromBusiness, operateContent, isDelete, operatorId, createTime, updateTime </sql>
</mapper>