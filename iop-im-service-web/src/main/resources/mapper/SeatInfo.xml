<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.SeatInfoMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.SeatInfo">
        <id column="seat_id" jdbcType="BIGINT" property="seatId"/>
        <result column="seat_name" jdbcType="VARCHAR" property="seatName"/>
        <result column="face_url" jdbcType="VARCHAR" property="faceUrl"/>
        <result column="account_id" jdbcType="BIGINT" property="accountId"/>
        <result column="account_type" jdbcType="VARCHAR" property="accountType"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="seat_no" jdbcType="BIGINT" property="seatNo"/>
        <result column="seat_type" jdbcType="TINYINT" property="seatType"/>
        <result column="tag_id" jdbcType="INTEGER" property="tagId"/>
        <result column="current_seat_status_en" jdbcType="VARCHAR" property="currentSeatStatusEn"/>
        <result column="login_seat_status_en" jdbcType="VARCHAR" property="loginSeatStatusEn"/>
        <result column="max_wiring_quantity" jdbcType="INTEGER" property="maxWiringQuantity"/>
        <result column="seat_extra_json" jdbcType="LONGVARCHAR" property="seatExtraJson"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">seatId, seatName, faceUrl, accountId, accountType, tenantId, seatNo, seatType, tagId, currentSeatStatusEn, loginSeatStatusEn, maxWiringQuantity, seatExtraJson, status, isDelete, operatorId, createTime, updateTime </sql>

    <select id="getAccountIdByAllSeatInfo" resultType="java.lang.Long">
        SELECT
            account_id
        FROM
            seat_info
        WHERE
            is_delete = 0
    </select>

    <select id="getLastSeatNo" resultType="java.lang.Integer">
        SELECT
            seat_no
        FROM
            seat_info
        WHERE
            tenant_id = #{tenantId}
        ORDER BY
            seat_no DESC
        LIMIT 1
    </select>

    <update id="updateSeatOnlineStatus">
        UPDATE
            seat_info
        SET
            current_seat_status_en = login_seat_status_en,
            update_time = now()
        WHERE
            seat_id = #{seatId}
            AND status = 0
            AND is_delete = 0
            AND current_seat_status_en = 'offline'
    </update>

    <select id="selectAccountIdListEnableSeatInfo" resultType="java.lang.Long">
        SELECT
            account_id
        FROM
            seat_info
    </select>

    <update id="updateSeatOfflineStatus">
        UPDATE
            seat_info
        SET
            current_seat_status_en = #{seatStatusEn}
        WHERE
            current_seat_status_en != #{seatStatusEn}
            AND seat_id IN
            <foreach item="item" index="index" collection="seatIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
    </update>

    <select id="getEnums" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        seat_info
        WHERE (
            ( status = 0
                AND is_delete = 0 )
            <if test="includeEnumsValueIdList != null and includeEnumsValueIdList.size()>0">
                OR seat_id IN
                <foreach item="item" index="index" collection="includeEnumsValueIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        )
        AND tenant_id = #{tenantId}
    </select>

    <select id="selectMaxOnlineCountByGroupIdList"
            resultType="com.wanshifu.iop.im.domain.bo.seat.MaxOnlineCountByGroupIdListRespBo">
        SELECT
            SUM(s.max_wiring_quantity) AS maxOnlineCount,
            m.group_id AS groupId
        FROM
            seat_info s
        LEFT JOIN seat_group_mapping m ON s.seat_id = m.seat_id AND m.status = 0 AND m.is_delete = 0
        WHERE
            s.status = 0
            AND s.is_delete = 0
            <if test="enGroupIdList != null and enGroupIdList.size()>0">
                AND m.group_id IN
                <foreach item="item" index="index" collection="enGroupIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        GROUP BY
            m.group_id
    </select>

    <select id="selectSeatInfoByGroupId" resultType="com.wanshifu.iop.im.domain.bo.groupManage.GroupSeatListRespBo">
        SELECT
            s.seat_id as seatId,
            s.account_id as accountId,
            s.seat_name as seatName,
            s.seat_no as seatNo,
            s.max_wiring_quantity as maxWiringQuantity,
            m.group_id as groupId
        FROM
            seat_info s
        LEFT JOIN seat_group_mapping m ON s.seat_id = m.seat_id AND m.status = 0 AND m.is_delete = 0
        WHERE
            s.status = 0
            AND s.is_delete = 0
            AND m.group_id = #{groupId}
        GROUP BY
            s.seat_id
    </select>

</mapper>