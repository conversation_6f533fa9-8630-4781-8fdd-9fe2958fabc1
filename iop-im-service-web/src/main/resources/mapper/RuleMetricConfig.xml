<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.RuleMetricConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.RuleMetricConfig">
        <id column="rule_metric_id" jdbcType="INTEGER" property="ruleMetricId"/>
        <result column="rule_metric_cn" jdbcType="VARCHAR" property="ruleMetricCn"/>
        <result column="rule_metric_en" jdbcType="VARCHAR" property="ruleMetricEn"/>
        <result column="rule_metric_object" jdbcType="VARCHAR" property="ruleMetricObject"/>
        <result column="rule_metric_desc" jdbcType="VARCHAR" property="ruleMetricDesc"/>
        <result column="rule_metric_extra_json" jdbcType="LONGVARCHAR" property="ruleMetricExtraJson"/>
        <result column="from_type" jdbcType="VARCHAR" property="fromType"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">ruleMetricId, ruleMetricCn, ruleMetricEn, ruleMetricObject, ruleMetricDesc, ruleMetricExtraJson, fromType, operatorId, isDelete, createTime, updateTime </sql>
</mapper>