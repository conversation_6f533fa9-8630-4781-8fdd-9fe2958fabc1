<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.GroupRuleJoinConditionMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.GroupRuleJoinCondition">
        <id column="join_condition_id" jdbcType="INTEGER" property="joinConditionId"/>
        <result column="group_id" jdbcType="INTEGER" property="groupId"/>
        <result column="logical_operator" jdbcType="VARCHAR" property="logicalOperator"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">joinConditionId, groupId, logicalOperator, operatorId, isDelete, createTime, updateTime </sql>
</mapper>