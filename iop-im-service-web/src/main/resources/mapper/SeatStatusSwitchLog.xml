<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.SeatStatusSwitchLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.SeatStatusSwitchLog">
        <id column="seat_status_switch_log_id" jdbcType="BIGINT" property="seatStatusSwitchLogId"/>
        <result column="seat_id" jdbcType="BIGINT" property="seatId"/>
        <result column="seat_status_en_before" jdbcType="VARCHAR" property="seatStatusEnBefore"/>
        <result column="seat_status_en_after" jdbcType="VARCHAR" property="seatStatusEnAfter"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="first_online_time" jdbcType="TIMESTAMP" property="firstOnlineTime"/>
        <result column="full_load_seconds" jdbcType="BIGINT" property="fullLoadSeconds"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">seatStatusSwitchLogId, seatId, seatStatusEnBefore, seatStatusEnAfter, isDelete, firstOnlineTime, fullLoadSeconds, createTime, updateTime</sql>

    <select id="selectLastSwitchStatusBySeatIdList"
            resultMap="BaseResultMap">
        SELECT
            ssls.*
        FROM
            seat_status_switch_log ssls
                JOIN
            (
                SELECT
                    seat_id,
                    MAX(seat_status_switch_log_id) AS seat_status_switch_log_id
                FROM
                    seat_status_switch_log
                WHERE
                    is_delete = 0
                  AND seat_id IN
                    <foreach collection="seatIdList" item="seatId" index="index" open="(" separator="," close=")">
                        #{seatId}
                    </foreach>
                GROUP BY
                    seat_id
            ) latest
            ON ssls.seat_id = latest.seat_id
            AND ssls.seat_status_switch_log_id = latest.seat_status_switch_log_id
        WHERE
            ssls.is_delete = 0;
    </select>
</mapper>