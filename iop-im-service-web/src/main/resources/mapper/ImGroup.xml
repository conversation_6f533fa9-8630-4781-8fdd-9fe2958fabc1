<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImGroupMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImGroup">
        <id column="im_group_id" jdbcType="BIGINT" property="imGroupId"/>
        <result column="outer_group_id" jdbcType="VARCHAR" property="outerGroupId"/>
        <result column="im_id" jdbcType="BIGINT" property="imId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="group_status" jdbcType="VARCHAR" property="groupStatus"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">imGroupId, imId, outerGroupId, conversationId, groupStatus, isDelete, updateTime, createTime </sql>
</mapper>