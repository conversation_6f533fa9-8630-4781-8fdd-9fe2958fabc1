<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.FlowTransitionMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.FlowTransition">
        <id column="transition_id" jdbcType="BIGINT" property="transitionId"/>
        <result column="flow_define_id" jdbcType="BIGINT" property="flowDefineId"/>
        <result column="from_node_id" jdbcType="BIGINT" property="fromNodeId"/>
        <result column="to_node_id" jdbcType="BIGINT" property="toNodeId"/>
        <result column="condition_expression" jdbcType="LONGVARCHAR" property="conditionExpression"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">transitionId, flowDefineId, fromNodeId, toNodeId, conditionExpression, isDelete, updateTime, createTime </sql>
    <update id="updateSetIsDeleteDefineTransition">
        update flow_transition
        set is_delete = 1
        where flow_define_id = #{flowDefineId}
          and is_delete = 0
    </update>
</mapper>