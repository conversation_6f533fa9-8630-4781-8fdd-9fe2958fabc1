<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.UserRegisterInfoMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.UserRegisterInfo">
        <id column="register_info_id" jdbcType="BIGINT" property="registerInfoId"/>
        <result column="im_id" jdbcType="BIGINT" property="imId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_class" jdbcType="VARCHAR" property="userClass"/>
        <result column="outer_user_id" jdbcType="VARCHAR" property="outerUserId"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="register_status" jdbcType="VARCHAR" property="registerStatus"/>
        <result column="register_time" jdbcType="TIMESTAMP" property="registerTime"/>
        <result column="user_sign" jdbcType="VARCHAR" property="userSign"/>
        <result column="user_sign_expire_time" jdbcType="TIMESTAMP" property="userSignExpireTime"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="online_state" jdbcType="VARCHAR" property="onlineState"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="online_state_change_time" jdbcType="TIMESTAMP" property="onlineStateChangeTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">registerInfoId, imId, userId, userClass, deviceId, outerUserId, registerStatus, registerTime, userSign, userSignExpireTime, status, isDelete, updateTime, createTime,
              online,onlineStateChangeTime </sql>

    <select id="selectOuterUserIdListByGroupIdList"
            resultType="com.wanshifu.iop.im.domain.bo.seat.SeatOutUserIdMappingRespBo">
        SELECT
            r.user_id AS seatId,
            r.outer_user_id AS outerUserId
        FROM
            user_register_info r
        LEFT JOIN seat_group_mapping m ON r.user_id = m.seat_id and m.status = 0 and m.is_delete = 0
        WHERE
            r.user_class = 'seat'
            AND r.status = 0
            AND r.is_delete = 0
            AND r.register_status = 'success'
            AND m.group_id IN
            <foreach collection="groupIdList" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        GROUP BY
            r.user_id
    </select>
</mapper>