<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.UserProblemClassMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.UserProblemClass">
        <id column="problem_id" jdbcType="BIGINT" property="problemId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="problem_name" jdbcType="VARCHAR" property="problemName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">problemId, channelId, problemName, status, updateAccountId, createAccountId, isDelete, updateTime, createTime </sql>
    <select id="getEnums" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            user_problem_class
        WHERE
            <if test="enumsParentIdList != null and enumsParentIdList.size()>0">
                channel_id IN
                <foreach item="enumsParentId" index="index" collection="enumsParentIdList" open="(" separator="," close=")">
                    #{enumsParentId}
                </foreach>
            </if>
        AND (
            ( status = 'enable'
            AND is_delete = 0 )
            <if test="includeEnumsValueIdList != null and includeEnumsValueIdList.size()>0">
                OR problem_id IN
                <foreach item="item" index="index" collection="includeEnumsValueIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        )
    </select>

    <select id="checkNonexistent" resultType="java.lang.Long">
        SELECT
            problem_id
        FROM
            user_problem_class
        WHERE
            problem_id IN
            <foreach item="item" index="index" collection="existIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND channel_id IN
            <foreach item="parentId" index="index" collection="parentIdList" open="(" separator="," close=")">
                #{parentId}
            </foreach>
            AND status = 'enable'
            AND is_delete = 0
    </select>
</mapper>