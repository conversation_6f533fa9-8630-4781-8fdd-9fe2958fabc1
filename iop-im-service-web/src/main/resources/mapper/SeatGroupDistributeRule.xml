<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.SeatGroupDistributeRuleMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.SeatGroupDistributeRule">
        <id column="seat_group_distribute_rule_id" jdbcType="INTEGER" property="seatGroupDistributeRuleId"/>
        <result column="group_id" jdbcType="INTEGER" property="groupId"/>
        <result column="seat_id" jdbcType="BIGINT" property="seatId"/>
        <result column="rule_metric_id" jdbcType="INTEGER" property="ruleMetricId"/>
        <result column="rule_config_value" jdbcType="LONGVARCHAR" property="ruleConfigValue"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">seatGroupDistributeRuleId, groupId, seatId, ruleMetricId, ruleConfigValue, operatorId, isDelete, createTime, updateTime </sql>
</mapper>