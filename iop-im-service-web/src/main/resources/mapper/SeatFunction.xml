<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.SeatFunctionMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.SeatFunction">
        <id column="seat_function_id" jdbcType="INTEGER" property="seatFunctionId"/>
        <result column="seat_function_cn" jdbcType="VARCHAR" property="seatFunctionCn"/>
        <result column="seat_function_en" jdbcType="VARCHAR" property="seatFunctionEn"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">seatFunctionId, seatFunctionCn, seatFunctionEn, isDelete, operatorId, createTime, updateTime </sql>
</mapper>