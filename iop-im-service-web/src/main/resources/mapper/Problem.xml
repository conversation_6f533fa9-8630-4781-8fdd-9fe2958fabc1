<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ProblemMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.Problem">
        <id column="problem_id" jdbcType="BIGINT" property="problemId"/>
        <result column="problem_name" jdbcType="VARCHAR" property="problemName"/>
        <result column="problem_desc" jdbcType="VARCHAR" property="problemDesc"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="level" jdbcType="TINYINT" property="level"/>
        <result column="problem_extra" jdbcType="LONGVARCHAR" property="problemExtra"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">problemId, problemName, problemDesc, parentId, level, problemExtra, tenantId, status, isDelete, createTime, updateTime </sql>
</mapper>