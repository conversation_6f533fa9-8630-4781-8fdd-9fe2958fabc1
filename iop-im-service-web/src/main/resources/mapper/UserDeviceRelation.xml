<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.UserDeviceRelationMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.UserDeviceRelation">
        <id column="user_device_id" jdbcType="BIGINT" property="userDeviceId"/>
        <result column="outer_user_id" jdbcType="VARCHAR" property="outerUserId"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">userDeviceId, outerUserId, deviceId, isDelete, updateTime, createTime </sql>
</mapper>