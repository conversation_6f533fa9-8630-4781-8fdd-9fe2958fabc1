<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.VirtualUserInfoMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.VirtualUserInfo">
        <id column="virtual_info_id" jdbcType="BIGINT" property="virtualInfoId"/>
        <result column="im_id" jdbcType="BIGINT" property="imId"/>
        <result column="outer_user_id" jdbcType="VARCHAR" property="outerUserId"/>
        <result column="register_time" jdbcType="TIMESTAMP" property="registerTime"/>
        <result column="user_sign" jdbcType="VARCHAR" property="userSign"/>
        <result column="user_sign_expire_time" jdbcType="TIMESTAMP" property="userSignExpireTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">virtualInfoId, imId, outerUserId, registerTime, userSign, userSignExpireTime, status, isDelete, updateTime, createTime </sql>

    <update id="batchUpdateVirtualStatus">
        update
            virtual_user_info
        set
            status = #{status}
        where
            virtual_info_id in
            <foreach collection="virtualInfoIdList" item="virtualInfoId" index="index" open="(" separator="," close=")">
                #{virtualInfoId}
            </foreach>
            and is_delete = 0
    </update>
</mapper>