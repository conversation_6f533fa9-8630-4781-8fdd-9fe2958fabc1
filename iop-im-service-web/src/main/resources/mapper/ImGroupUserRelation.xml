<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImGroupUserRelationMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImGroupUserRelation">
        <id column="im_user_relation_id" jdbcType="BIGINT" property="imUserRelationId"/>
        <result column="im_group_id" jdbcType="BIGINT" property="imGroupId"/>
        <result column="outer_user_id" jdbcType="VARCHAR" property="outerUserId"/>
        <result column="outer_class_type" jdbcType="VARCHAR" property="outerClassType"/>
        <result column="member_status" jdbcType="VARCHAR" property="memberStatus"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">imUserRelationId, imGroupId, outerUserId, outerClassType,memberStatus, isDelete, updateTime, createTime </sql>
</mapper>