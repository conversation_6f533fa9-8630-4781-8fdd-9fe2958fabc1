<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.SeatVirtualRelationMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.SeatVirtualRelation">
        <id column="seat_virtual_relation_id" jdbcType="BIGINT" property="seatVirtualRelationId"/>
        <result column="seat_id" jdbcType="BIGINT" property="seatId"/>
        <result column="outer_user_id" jdbcType="VARCHAR" property="outerUserId"/>
        <result column="virtual_user_id" jdbcType="VARCHAR" property="virtualUserId"/>
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime"/>
        <result column="over_bind_time" jdbcType="TIMESTAMP" property="overBindTime"/>
        <result column="bind_status" jdbcType="VARCHAR" property="bindStatus"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">seatVirtualRelationId, seatId, outerUserId, virtualUserId, bindTime, overBindTime, bindStatus, isDelete, updateTime, createTime </sql>
</mapper>