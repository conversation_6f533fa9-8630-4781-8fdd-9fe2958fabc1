<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.GroupInfoMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.GroupInfo">
        <id column="group_id" jdbcType="INTEGER" property="groupId"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="distribute_strategy" jdbcType="VARCHAR" property="distributeStrategy"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">groupId, groupName, isDelete, distributeStrategy, operatorId, createTime, updateTime </sql>
    <select id="selectEnableCountByGroupIdList" resultType="java.lang.Integer">
        select count(group_id) from group_info where group_id in
        <foreach collection="groupIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and status = 0
        and is_delete = 0
    </select>

    <select id="getEnums" resultType="com.wanshifu.iop.im.domain.po.GroupInfo">
        SELECT
            group_id AS groupId,
            group_name AS groupName,
            status AS status
        FROM
            group_info
        WHERE
            ( status = 0
                AND is_delete = 0 )
            <if test="includeGroupIdList != null and includeGroupIdList.size()>0">
                OR group_id IN
                <foreach item="item" index="index" collection="includeGroupIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND tenant_id = #{tenantId}
    </select>

    <select id="selectMaxSortByTenantId" resultType="java.lang.Integer">
        select max(sort) from group_info where tenant_id = #{tenantId}
    </select>

    <select id="selectEnableBySeatId" resultMap="BaseResultMap">
        select
            g.*
        from
            seat_group_mapping m
                LEFT JOIN group_info g ON m.group_id = g.group_id
        where
            m.seat_id = #{seatId}
            AND m.is_delete = 0
            AND m.status = 0
            AND g.is_delete = 0
            AND g.status = 0
        GROUP BY g.group_id
        ORDER BY g.sort ASC
    </select>

    <update id="updateNowTime">
        update group_info set update_time = now(), operator_id = #{operatorId} where group_id = #{groupId}
    </update>
</mapper>