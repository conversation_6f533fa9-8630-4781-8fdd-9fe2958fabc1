<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.BusinessFromTemplateMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.BusinessFromTemplate">
        <id column="business_from_template_id" jdbcType="BIGINT" property="businessFromTemplateId"/>
        <result column="business_template_en" jdbcType="VARCHAR" property="businessTemplateEn"/>
        <result column="business_template_cn" jdbcType="VARCHAR" property="businessTemplateCn"/>
        <result column="user_class" jdbcType="VARCHAR" property="userClass"/>
        <result column="problem_id" jdbcType="INTEGER" property="problemId"/>
        <result column="business_template_desc" jdbcType="VARCHAR" property="businessTemplateDesc"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">businessFromTemplateId, businessTemplateEn, businessTemplateCn, userClass, problemId, businessTemplateDesc, tenantId, status, isDelete, createTime, updateTime</sql>
</mapper>