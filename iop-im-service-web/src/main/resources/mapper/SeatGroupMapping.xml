<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.SeatGroupMappingMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.SeatGroupMapping">
        <id column="seat_group_mapping_id" jdbcType="INTEGER" property="seatGroupMappingId"/>
        <result column="group_id" jdbcType="INTEGER" property="groupId"/>
        <result column="seat_id" jdbcType="BIGINT" property="seatId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">seatGroupMappingId, groupId, seatId, status, isDelete, operatorId, createTime, updateTime </sql>

    <update id="deleteMappingBySeatIdList">
        update
            seat_group_mapping
        set is_delete = 1
        where
            seat_id in
            <foreach collection="seatIdList" item="seatId" open="(" separator="," close=")">
                #{seatId}
            </foreach>
    </update>

    <select id="selectCountByGroupIdList" resultType="com.wanshifu.iop.im.api.resp.CommonLabelValueResp">
        SELECT
            m.group_id AS label,
            COUNT(s.seat_id) AS value
        FROM
            seat_group_mapping m
            LEFT JOIN seat_info s ON m.seat_id = s.seat_id
        WHERE
            m.group_id IN
            <foreach item="groupId" collection="groupIdList" separator="," open="(" close=")" index="">
                #{groupId}
            </foreach>
            AND m.status = 0
            AND m.is_delete = 0
            AND s.seat_id IS NOT NULL
            AND s.status = 0
            AND s.is_delete = 0
        GROUP BY m.group_id
    </select>

    <select id="selectGroupIdBySeatId" resultType="java.lang.Integer">
        select
            group_id
        from
            seat_group_mapping
        where
            seat_id = #{seatId}
        AND is_delete = 0
        AND status = 0
    </select>

    <select id="selectSeatMappingByGroupId" resultType="com.wanshifu.iop.im.domain.bo.seat.SeatGroupMappingBo">
        select
            m.seat_group_mapping_id as seatGroupMappingId,
            m.status as seatGroupMappingStatus,
            s.seat_id as seatId,
            s.status as seatStatus
        from
            seat_group_mapping m
        LEFT JOIN seat_info s ON m.seat_id = s.seat_id
        where
            m.group_id = #{groupId}
        AND m.is_delete = 0
    </select>

    <select id="selectSeatMappingBySeatId" resultType="com.wanshifu.iop.im.domain.bo.seat.SeatGroupMappingBo">
        select
            m.seat_group_mapping_id as seatGroupMappingId,
            m.status as seatGroupMappingStatus,
            g.group_id as groupId,
            g.status as groupStatus
        from
            seat_group_mapping m
        LEFT JOIN group_info g ON m.group_id = g.group_id
        where
            m.seat_id = #{seatId}
        AND m.is_delete = 0
    </select>

    <update id="batchUpdateStatusBySeatId">
        update
            seat_group_mapping
        set
            status = #{status}
        where
            seat_id = #{seatId}
        AND is_delete = 0
    </update>

    <update id="updateDisableStatusByGroupId">
        update
            seat_group_mapping
        set
            status = 1
        where
            group_id = #{groupId}
        AND is_delete = 0
    </update>

    <update id="updateEnableStatusByGroupIdAndSeatIdList">
        update
            seat_group_mapping
        set
            status = 0,
            operator_id = #{operatorId}
        where
            group_id = #{groupId}
            AND seat_id in
            <foreach item="seatId" collection="seatIds" separator="," open="(" close=")" index="">
                #{seatId}
            </foreach>
            AND is_delete = 0
    </update>

    <update id="updateEnableStatusBySeatIdAndGroupIdList">
        update
            seat_group_mapping
        set
            status = 0,
            operator_id = #{operatorId}
        where
            seat_id = #{seatId}
            AND group_id in
            <foreach item="groupId" collection="groupIdList" separator="," open="(" close=")" index="">
                #{groupId}
            </foreach>
            AND is_delete = 0
    </update>

    <delete id="deleteByGroupIdAndSeatIdList">
        update
            seat_group_mapping
        set
            status = 1
            , is_delete = 1
        where
            group_id = #{groupId}
            AND
                seat_id in
            <foreach item="seatId" collection="seatIdList" separator="," open="(" close=")" index="">
                #{seatId}
            </foreach>
            AND is_delete = 0
    </delete>

</mapper>