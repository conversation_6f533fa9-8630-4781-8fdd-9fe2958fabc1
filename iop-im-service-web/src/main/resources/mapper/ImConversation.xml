<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImConversationMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImConversation">
        <id column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="from_outer_user_id" jdbcType="VARCHAR" property="fromOuterUserId"/>
        <result column="from_outer_user_type" jdbcType="VARCHAR" property="fromOuterUserType"/>
        <result column="conversation_status" jdbcType="VARCHAR" property="conversationStatus"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="to_outer_user_id" jdbcType="VARCHAR" property="toOuterUserId"/>
        <result column="to_outer_user_type" jdbcType="VARCHAR" property="toOuterUserType"/>
        <result column="from_type" jdbcType="VARCHAR" property="fromType"/>
        <result column="from_id" jdbcType="VARCHAR" property="fromId"/>
        <result column="from_scene" jdbcType="VARCHAR" property="fromScene"/>
        <result column="scene_value" jdbcType="VARCHAR" property="sceneValue"/>
        <result column="conversation_tag_ids" jdbcType="VARCHAR" property="conversationTagIds"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="agent_outer_user_id" jdbcType="VARCHAR" property="agentOuterUserId"/>
        <result column="channel_config_id" jdbcType="BIGINT" property="channelConfigId"/>
        <result column="conversation_type" jdbcType="VARCHAR" property="conversationType"/>
    </resultMap>
    <sql id="Base_Column_List">
        conversation_id as conversationId,
        from_outer_user_id as fromOuterUserId,
        from_outer_user_type as fromOuterUserType,
        conversation_status as conversationStatus,
        group_id as groupId,
        to_outer_user_id as toOuterUserId,
        to_outer_user_type as toOuterUserType,
        from_type as fromType,
        from_id as fromId,
        from_scene as fromScene,
        scene_value as sceneValue,
        conversation_tag_ids as conversationTagIds,
        complete_time as completeTime,
        is_delete as isDelete,
        update_time as updateTime,
        create_time as createTime,
        agent_outer_user_id as agentOuterUserId,
        channel_config_id as channelConfigId,
        conversation_type as conversationType
    </sql>
    <select id="getInfoByFromIdAndToIdAndStatus" resultType="com.wanshifu.iop.im.domain.po.ImConversation">
        select
            <include refid="Base_Column_List"/>
        from im_conversation
                 where
                     (
                         (
                             from_outer_user_id = #{fromId}
                            and agent_outer_user_id=#{toId}
                             )
                          or
                         (
                            from_outer_user_id = #{toId}
                            and agent_outer_user_id=#{fromId}
                              )
                        )
                   and conversation_status=#{status}

    </select>
    <select id="getInfoByFromIdAndToId" resultType="com.wanshifu.iop.im.domain.po.ImConversation">
        select
            <include refid="Base_Column_List"/>
        from im_conversation
        where
            (

                    from_outer_user_id = #{fromId}

            or
                    to_outer_user_id=#{fromId}

            )
    </select>

    <select id="selectCountByToOuterUserId"
            resultType="com.wanshifu.iop.im.api.resp.CommonLabelValueResp">
        SELECT
            COUNT(1) AS value,
            to_outer_user_id AS label
        FROM im_conversation
        WHERE
            to_outer_user_id IN
            <foreach collection="toOutUserIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and to_outer_user_type = #{toOuterUserType}
            and conversation_status = 'processing'
        GROUP BY
            to_outer_user_id
    </select>

    <select id="selectWaitProcessList" resultMap="BaseResultMap">
        select
            *
        from
            im_conversation
        where
            conversation_status = 'processing'
            and conversation_type = 'online'
            and is_delete = 0
            and to_outer_user_id = #{toOuterUserId}
            and to_outer_user_type = #{toOuterUserType}

        <if test=" ( searchMarkOuterUserIdList != null and searchMarkOuterUserIdList.size() > 0 ) || (allMarkOuterUserIdList != null and allMarkOuterUserIdList.size() > 0) ">
            and (
                <if test="searchMarkOuterUserIdList != null and searchMarkOuterUserIdList.size() > 0">
                    ( from_outer_user_id in
                        <foreach collection="searchMarkOuterUserIdList" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    )
                </if>

                <if test=" searchMarkOuterUserIdList != null and searchMarkOuterUserIdList.size() > 0 and allMarkOuterUserIdList != null and allMarkOuterUserIdList.size() > 0 ">
                    or
                </if>

                <if test="allMarkOuterUserIdList != null and allMarkOuterUserIdList.size() > 0">
                    ( from_outer_user_id not in
                    <foreach collection="allMarkOuterUserIdList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )
        </if>
    </select>

    <select id="selectLeaveList" resultMap="BaseResultMap">
        select
        *
        from
        im_conversation
        where
        conversation_status = 'complete'
        and conversation_type = 'leave'
        and is_delete = 0

        <if test="channelIdList != null and channelIdList.size() > 0">
            and channel_config_id in
            <foreach collection="channelIdList" item="channelId" open="(" separator="," close=")">
                #{channelId}
            </foreach>
        </if>

        order by complete_time desc
    </select>
    <select id="selectHistoryList" resultMap="BaseResultMap">
        select
        i.*
        from
        im_conversation i
        LEFT JOIN im_conversation_active_log l on i.conversation_id = l.from_conversation_id AND l.is_delete = 0 AND l.activate_scene = 'leave'
        where
        i.conversation_type = 'history'
        and i.conversation_status = 'complete'
        and i.is_delete = 0
        and i.to_outer_user_id = #{toOuterUserId}
        and i.to_outer_user_type = #{toOuterUserType}
        and l.conversation_active_log_id is null

        <if test="conversationId!=null and conversationId>0 ">
            and i.conversation_id = #{conversationId}
        </if>

        <if test="searchStartTime!=null and searchStartTime!='' ">
            AND i.create_time <![CDATA[ >= ]]> #{searchStartTime}
        </if>
        <if test="searchEndTime!=null and searchEndTime!='' ">
            AND i.create_time <![CDATA[ <= ]]> #{searchEndTime}
        </if>

        <if test=" ( searchMarkOuterUserIdList != null and searchMarkOuterUserIdList.size() > 0 ) || (allMarkOuterUserIdList != null and allMarkOuterUserIdList.size() > 0) ">
            and (
            <if test="searchMarkOuterUserIdList != null and searchMarkOuterUserIdList.size() > 0">
                ( i.from_outer_user_id in
                <foreach collection="searchMarkOuterUserIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test=" searchMarkOuterUserIdList != null and searchMarkOuterUserIdList.size() > 0 and allMarkOuterUserIdList != null and allMarkOuterUserIdList.size() > 0 ">
                or
            </if>

            <if test="allMarkOuterUserIdList != null and allMarkOuterUserIdList.size() > 0">
                ( i.from_outer_user_id not in
                <foreach collection="allMarkOuterUserIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            )
        </if>

        order by i.complete_time desc
    </select>
    <select id="selectOnlineNum" resultType="com.wanshifu.iop.im.domain.dto.distribute.SelectOnlineNumDto">

SELECT
    to_outer_user_id AS toOuterUserId,
    COUNT(1) AS num
FROM
    im_conversation
WHERE
    conversation_status = 'processing'
    AND is_delete = 0
    AND to_outer_user_id in (
        <foreach collection="toOutUserIdList" item="toOuterUserId" separator=",">
        #{toOuterUserId}
    </foreach>
        )
GROUP BY
    to_outer_user_id
    </select>

    <select id="selectWaitingCountByToOuterUserId" resultType="java.lang.Integer">
        select
            count(1)
        from
            im_conversation
        WHERE
        conversation_status = 'processing'
        AND is_delete = 0
        AND to_outer_user_id = #{toOutUserId}
        AND conversation_type = 'online'
    </select>
</mapper>