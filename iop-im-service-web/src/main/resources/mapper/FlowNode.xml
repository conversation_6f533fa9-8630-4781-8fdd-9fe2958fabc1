<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.FlowNodeMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.FlowNode">
        <id column="flow_node_id" jdbcType="BIGINT" property="flowNodeId"/>
        <result column="flow_define_id" jdbcType="BIGINT" property="flowDefineId"/>
        <result column="node_name" jdbcType="VARCHAR" property="nodeName"/>
        <result column="node_type" jdbcType="VARCHAR" property="nodeType"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="config_json" jdbcType="LONGVARCHAR" property="configJson"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">flowNodeId, flowDefineId, nodeName, nodeType, sort, configJson, isDelete, updateTime, createTime </sql>
    <update id="deleteOldFlowNodeByFlowDefineId">
        update flow_node set is_delete=1 where flow_define_id=#{flowDefineId} and is_delete=0
    </update>

</mapper>