<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.FlowDefineMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.FlowDefine">
        <id column="flow_define_id" jdbcType="BIGINT" property="flowDefineId"/>
        <result column="flow_name" jdbcType="VARCHAR" property="flowName"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">flowDefineId, flowName, updateAccountId, createAccountId, status, isDelete, updateTime, createTime </sql>
</mapper>