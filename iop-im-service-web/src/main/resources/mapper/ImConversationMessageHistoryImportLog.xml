<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImConversationMessageHistoryImportLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImConversationMessageHistoryImportLog">
        <id column="conversation_message_history_import_log_id" jdbcType="BIGINT" property="conversationMessageHistoryImportLogId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="import_status" jdbcType="VARCHAR" property="importStatus"/>
        <result column="import_time" jdbcType="TIMESTAMP" property="importTime"/>
        <result column="import_remark" jdbcType="LONGVARCHAR" property="importRemark"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">conversationMessageHistoryImportLogId, conversationId, importStatus, importTime, importRemark, isDelete, createTime, updateTime </sql>
</mapper>