<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.FieldAttributeMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.FieldAttribute">
        <id column="field_attribute_id" jdbcType="BIGINT" property="fieldAttributeId"/>
        <result column="from_template_id" jdbcType="BIGINT" property="fromTemplateId"/>
        <result column="from_template_type" jdbcType="VARCHAR" property="fromTemplateType"/>
        <result column="field_en" jdbcType="VARCHAR" property="fieldEn"/>
        <result column="field_cn" jdbcType="VARCHAR" property="fieldCn"/>
        <result column="field_scene" jdbcType="VARCHAR" property="fieldScene"/>
        <result column="field_type" jdbcType="VARCHAR" property="fieldType"/>
        <result column="field_desc" jdbcType="VARCHAR" property="fieldDesc"/>
        <result column="field_extra" jdbcType="LONGVARCHAR" property="fieldExtra"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">fieldAttributeId, fromTemplateId, fromTemplateType, fieldEn, fieldCn, fieldScene, fieldType, fieldDesc, fieldExtra, status, isDelete, createTime, updateTime </sql>
</mapper>