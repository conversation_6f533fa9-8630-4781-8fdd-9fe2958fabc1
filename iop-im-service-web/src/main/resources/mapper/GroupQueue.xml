<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.GroupQueueMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.GroupQueue">
        <id column="group_queue_id" jdbcType="BIGINT" property="groupQueueId"/>
        <result column="conversation_distribute_id" jdbcType="INTEGER" property="conversationDistributeId"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="queue_config_id" jdbcType="INTEGER" property="queueConfigId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="outer_user_id" jdbcType="VARCHAR" property="outerUserId"/>
        <result column="queue_status" jdbcType="VARCHAR" property="queueStatus"/>
        <result column="release_time" jdbcType="TIMESTAMP" property="releaseTime"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
        <result column="sort" jdbcType="BIGINT" property="sort"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">groupQueueId, conversationDistributeId, groupId, queueConfigId, conversationId, outerUserId, queueStatus, releaseTime, cancelTime, sort, isDelete, createTime, updateTime </sql>
</mapper>