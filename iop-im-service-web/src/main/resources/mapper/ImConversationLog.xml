<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImConversationLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImConversationLog">
        <id column="conversation_log_id" jdbcType="BIGINT" property="conversationLogId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="from_outer_user_id" jdbcType="VARCHAR" property="fromOuterUserId"/>
        <result column="from_outer_user_type" jdbcType="VARCHAR" property="fromOuterUserType"/>
        <result column="conversation_status" jdbcType="VARCHAR" property="conversationStatus"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="to_outer_user_id" jdbcType="VARCHAR" property="toOuterUserId"/>
        <result column="to_outer_user_type" jdbcType="VARCHAR" property="toOuterUserType"/>
        <result column="from_type" jdbcType="VARCHAR" property="fromType"/>
        <result column="from_id" jdbcType="VARCHAR" property="fromId"/>
        <result column="from_scene" jdbcType="VARCHAR" property="fromScene"/>
        <result column="scene_value" jdbcType="VARCHAR" property="sceneValue"/>
        <result column="conversation_tag_ids" jdbcType="VARCHAR" property="conversationTagIds"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="agent_outer_user_id" jdbcType="VARCHAR" property="agentOuterUserId"/>
        <result column="channel_config_id" jdbcType="BIGINT" property="channelConfigId"/>
        <result column="conversation_type" jdbcType="VARCHAR" property="conversationType"/>
        <result column="timeout_xxl_id" jdbcType="BIGINT" property="timeoutXxlId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">conversationLogId, conversationId, fromOuterUserId, fromOuterUserType, conversationStatus, groupId, toOuterUserId, toOuterUserType, fromType, fromId, fromScene, sceneValue, conversationTagIds, completeTime, agentOuterUserId, channelConfigId, conversationType, timeoutXxlId, isDelete, updateTime, createTime </sql>
</mapper>