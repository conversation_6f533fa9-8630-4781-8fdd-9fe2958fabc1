<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.UserVirtualRelationMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.UserVirtualRelation">
        <id column="user_virtual_relation_id" jdbcType="BIGINT" property="userVirtualRelationId"/>
        <result column="outer_user_id" jdbcType="VARCHAR" property="outerUserId"/>
        <result column="virtual_user_id" jdbcType="VARCHAR" property="virtualUserId"/>
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime"/>
        <result column="over_bind_time" jdbcType="TIMESTAMP" property="overBindTime"/>
        <result column="bind_status" jdbcType="VARCHAR" property="bindStatus"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">userVirtualRelationId, outerUserId, virtualUserId, bindTime, overBindTime, bindStatus, isDelete, updateTime, createTime </sql>
    <update id="closeConversationUpdateBind">
        update user_virtual_relation
        set over_bind_time = #{updateTime},
            bind_status = 'over'
        where outer_user_id = #{outerUserId}
        and virtual_user_id = #{virtualUserId}
        and bind_status = 'binding'
        and is_delete = 0
    </update>
</mapper>