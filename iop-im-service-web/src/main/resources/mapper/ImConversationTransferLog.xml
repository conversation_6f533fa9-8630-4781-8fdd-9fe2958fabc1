<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.mapper.ImConversationTransferLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.iop.im.domain.po.ImConversationTransferLog">
        <id column="transfer_log_id" jdbcType="BIGINT" property="transferLogId"/>
        <result column="conversation_id" jdbcType="BIGINT" property="conversationId"/>
        <result column="origin_group_info_id" jdbcType="BIGINT" property="originGroupInfoId"/>
        <result column="origin_outer_user_id" jdbcType="VARCHAR" property="originOuterUserId"/>
        <result column="new_group_info_id" jdbcType="BIGINT" property="newGroupInfoId"/>
        <result column="new_outer_user_id" jdbcType="VARCHAR" property="newOuterUserId"/>
        <result column="transfer_time" jdbcType="TIMESTAMP" property="transferTime"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/></resultMap>
    <sql id="Base_Column_List">transferLogId, conversationId, originGroupInfoId, originOuterUserId, newGroupInfoId, newOuterUserId, transferTime, note, isDelete, updateTime, createTime </sql>
</mapper>