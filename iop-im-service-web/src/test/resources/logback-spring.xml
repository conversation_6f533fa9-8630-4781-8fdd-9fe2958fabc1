<?xml version="1.0" encoding="UTF-8"?>
<!-- 属性描述 scan：性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true
scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。
当scan为true时，此属性生效。默认的时间间隔为1分钟。
debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <property resource="config/application.properties" />
    <contextName>${spring.application.name}</contextName>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoder默认配置为PartternLayoutEncoder    -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[${spring.application.name}][%5p] %d{yyyy-MM-dd HH:mm:ss SSS} [%X{RequestID}]- %c >>> %m %n</pattern>
        </encoder>
    </appender>

    <logger name="com.wanshifu.mapper" level="DEBUG"/>

    <logger name="com.wanshifu.common.log.ControllerLogAspect" level="DEBUG"/>

    <logger name="com.wanshifu.common.log.ControllerLogAspect" level="DEBUG"/>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>