DROP TABLE IF EXISTS business_from_submit_relaiton; 

CREATE TABLE `business_from_submit_relaiton` (
  `business_from_submit_relaiton_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `from_submit_record_id` bigint(20) unsigned NOT NULL COMMENT '表单信息提交记录id',
  `conversation_id` bigint(20) unsigned NOT NULL COMMENT '会话id',
  `business_from_id` bigint(20) unsigned NOT NULL COMMENT '业务来源id customerOrder-售后工单id secondline-二线工单id',
  `business_from_type` varchar(50) NOT NULL COMMENT '业务来源类型 customerOrder-售后工单 secondline-二线工单',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`business_from_submit_relaiton_id`)   
) COMMENT='业务表单提交关联表';


DROP TABLE IF EXISTS business_from_template; 

CREATE TABLE `business_from_template` (
  `business_from_template_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `business_template_en` varchar(50) NOT NULL COMMENT '业务模板英文名称',
  `business_template_cn` varchar(50) NOT NULL COMMENT '业务模板中文名称',
  `user_class` varchar(50) NOT NULL COMMENT '访客类型 user-用户 master-师傅 visitor-访客',
  `problem_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '模板问题类型一级id',
  `business_template_desc` varchar(500) NOT NULL DEFAULT '' COMMENT '业务模板描述',
  `tenant_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户id',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态: 1-禁用, 0-启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`business_from_template_id`)  
) COMMENT='业务表单模板表';


DROP TABLE IF EXISTS business_user_account_mapping; 

CREATE TABLE `business_user_account_mapping` (
  `business_user_account_mapping_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '业务用户账号映射id',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '业务用户id(外部用户id，如外部总包id)',
  `user_type` varchar(50) NOT NULL COMMENT '用户类型：outer_enterprise:外部总包',
  `account_id` bigint(20) unsigned NOT NULL COMMENT '账号中台id',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态：1：禁用，0：启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`business_user_account_mapping_id`) USING BTREE 
) COMMENT='业务用户账号映射表';


DROP TABLE IF EXISTS channel_config; 

CREATE TABLE `channel_config` (
  `channel_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '渠道ID',
  `channel_seq` varchar(30) DEFAULT NULL COMMENT '端侧内的渠道序号',
  `channel_name` varchar(30) NOT NULL COMMENT '渠道中文名',
  `channel_en` varchar(30) NOT NULL COMMENT '渠道英文名',
  `channel_desc` varchar(255) DEFAULT NULL COMMENT '渠道描述',
  `channel_type` varchar(30) NOT NULL COMMENT '渠道类型: 用户渠道-user_channel、订单渠道-order_channel',
  `flow_define_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '路由导航配置id',
  `client_category_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '端侧ID',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态  1：禁用，0：启用',
  `preview_url_template` varchar(255) DEFAULT NULL COMMENT '预览url模版  https://www.im.wnshifu.com/client?conversationId=xxx',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`channel_id`) USING BTREE 
 COMMENT '端侧ID'  
COMMENT '路由导航配置id' 
) COMMENT='渠道配置表';


DROP TABLE IF EXISTS client_category_config; 

CREATE TABLE `client_category_config` (
  `client_category_config_id` bigint(20) unsigned NOT NULL COMMENT '端侧ID 取值: client_channel_config_draft_id',
  `client_category_name` varchar(30) NOT NULL COMMENT '端侧名',
  `client_category_type` varchar(30) NOT NULL COMMENT '端侧类型: 内部-inner、外部-outer、游客-visitor',
  `client_category_en` varchar(50) DEFAULT NULL COMMENT '端侧标识',
  `rule_indicators_config_en` varchar(50) DEFAULT NULL COMMENT '分配规则英文名称',
  `um_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '腾讯应用配置ID',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`client_category_config_id`) USING BTREE
) COMMENT='端侧配置表';


DROP TABLE IF EXISTS client_channel_config_draft; 

CREATE TABLE `client_channel_config_draft` (
  `client_channel_config_draft_id` bigint(20) unsigned NOT NULL COMMENT '端侧ID',
  `client_category_name` varchar(30) NOT NULL COMMENT '端侧名',
  `client_category_type` varchar(30) NOT NULL COMMENT '端侧类型: 内部-inner、外部-outer、游客-visitor',
  `client_category_en` varchar(50) NOT NULL COMMENT '端侧标识',
  `rule_indicators_config_en` varchar(50) DEFAULT NULL COMMENT '分配规则英文名称',
  `um_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '腾讯应用配置ID',
  `all_config_json` longtext COMMENT '端侧+渠道 全部配置的json字符串',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '端侧上线状态  0-未上线  1-已上线',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`client_channel_config_draft_id`) USING BTREE
) COMMENT='端侧渠道草稿记录表';


DROP TABLE IF EXISTS conversation_autoReply_config; 

CREATE TABLE `conversation_autoReply_config` (
  `conversation_autoReply_config_id` bigint(20) unsigned NOT NULL COMMENT '系统自动回复配置ID',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道id',
  `msg_type` varchar(30) NOT NULL COMMENT '系统自动回复消息类型：欢迎语 - welcome_auto_reply 排队提示语 - wait_auto_reply 超时回复提醒语 timeout_auto_reply 会话结束语 end_auto_reply',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态  1：启用，0：未启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`conversation_autoReply_config_id`) USING BTREE COMMENT '渠道id'
) COMMENT='系统自动回复消息配置表';


DROP TABLE IF EXISTS conversation_autoReply_detail_config; 

CREATE TABLE `conversation_autoReply_detail_config` (
  `conversation_autoReply_detail_config_id` bigint(20) unsigned NOT NULL COMMENT '系统自动回复明细配置ID',
  `conversation_autoReply_config_id` bigint(20) NOT NULL COMMENT '系统自动回复配置ID',
  `msg_type` varchar(30) NOT NULL COMMENT '系统自动回复消息类型：欢迎语 - welcome_auto_reply 排队提示语 - wait_auto_reply 超时回复提醒语 timeout_auto_reply 会话结束语 end_auto_reply',
  `msg_sub_type` varchar(30) NOT NULL COMMENT '系统自动回复消息类型：欢迎语 - welcome_auto_reply、排队提示语、坐席超时回复提醒语 seat_timeout_auto_reply、访客超时回复提醒语 visitor_timeout_auto_reply',
  `sub_type_show_seq` tinyint(3) NOT NULL DEFAULT '0' COMMENT '页面展示顺序(子类型内)',
  `autoReply_receive_object` varchar(30) NOT NULL COMMENT '系统自动消息接收对象：坐席-seat、访客-visitor',
  `autoReply_content` varchar(255) NOT NULL COMMENT '自动回复内容（字符串模板）',
  `default_content` varchar(255) NOT NULL COMMENT '自动回复内容（字符串模板）默认值',
  `trigger_mode` varchar(30) DEFAULT NULL COMMENT '提示语触发事件类型：系统主动 - system_active、 访客回复超时 - visitor_timeout、坐席回复超时 - agent_timeout、访客主动关闭 - visitor_close、客服主动关闭 - agent-close',
  `timeout_cost_minutes` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '回复超时时长（分钟）',
  `support_placeholder` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否插入占位符',
  `placeholder_json` varchar(255) DEFAULT NULL COMMENT '占位符键值对json 格式',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态  1：启用，0：未启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`conversation_autoReply_detail_config_id`) USING BTREE COMMENT '系统自动回复配置ID'
) COMMENT='系统自动回复消息明细配置表';


DROP TABLE IF EXISTS conversation_auto_reply_config; 

CREATE TABLE `conversation_auto_reply_config` (
  `conversation_auto_reply_config_id` bigint(20) unsigned NOT NULL COMMENT '系统自动回复配置ID',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道id',
  `msg_type` varchar(30) NOT NULL COMMENT '系统自动回复消息类型：欢迎语 - welcome_auto_reply 排队提示语 - wait_auto_reply 超时回复提醒语 timeout_auto_reply 会话结束语 end_auto_reply',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态  1：启用，0：未启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`conversation_auto_reply_config_id`) COMMENT '自动回复配置id'  
) COMMENT='系统自动回复消息配置表';


DROP TABLE IF EXISTS conversation_auto_reply_detail_config; 

CREATE TABLE `conversation_auto_reply_detail_config` (
  `conversation_auto_reply_detail_config_id` bigint(20) unsigned NOT NULL COMMENT '系统自动回复明细配置ID',
  `conversation_auto_reply_config_id` bigint(20) NOT NULL COMMENT '系统自动回复配置ID',
  `msg_type` varchar(30) NOT NULL COMMENT '系统自动回复消息类型：欢迎语 - welcome_auto_reply 排队提示语 - wait_auto_reply 超时回复提醒语 timeout_auto_reply 会话结束语 end_auto_reply',
  `msg_sub_type` varchar(50) NOT NULL COMMENT '系统自动回复消息子类型 超时 seat_timeout_visitor_comfort- 坐席超时访客安抚语 seat_timeout_seat_tip - 坐席超时坐席提示语 visitor_timeout_visitor_tip - 访客超时访客提示语   visitor_timeout_seat_tip - 访客超时坐席提示语  visitor_timeout_close_visitor_tip - 访客超时关闭访客提示语\r\n        排队 queue_up_tip 排队提示语 queue_up_comfort 排队安抚语 结束  visitor_leave_timeout_tip 访客离开超时自动关闭 seat_end_tip - 坐席主动关闭提示语  欢迎 welcome_tip -欢迎语配置,  welcome_introduce 欢迎介绍语',
  `sub_type_show_seq` tinyint(3) NOT NULL DEFAULT '0' COMMENT '页面展示顺序(子类型内)',
  `autoReply_receive_object` varchar(30) NOT NULL COMMENT '系统自动消息接收对象：坐席-seat、访客-visitor',
  `autoReply_content` varchar(255) DEFAULT NULL COMMENT '自动回复内容（字符串模板）',
  `default_content` varchar(255) DEFAULT NULL COMMENT '自动回复内容（字符串模板）默认值',
  `trigger_mode` varchar(30) DEFAULT NULL COMMENT '提示语触发事件类型：系统主动 - system_active、 访客回复超时 - visitor_timeout、坐席回复超时 - agent_timeout、访客主动关闭 - visitor_close、客服主动关闭 - agent-close',
  `timeout_cost_second` varchar(3) DEFAULT '' COMMENT '回复超时时长（秒）',
  `support_placeholder` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否插入占位符 0-否 1-是',
  `extra_json` varchar(255) DEFAULT NULL COMMENT '扩展字符串',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态  1：启用，0：未启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`conversation_auto_reply_detail_config_id`) USING BTREE COMMENT '系统自动回复配置ID'
) COMMENT='系统自动回复消息明细配置表';


DROP TABLE IF EXISTS conversation_auto_reply_detail_config_back; 

CREATE TABLE `conversation_auto_reply_detail_config_back` (
  `conversation_auto_reply_detail_config_id` bigint(20) unsigned NOT NULL COMMENT '系统自动回复明细配置ID',
  `conversation_auto_reply_config_id` bigint(20) NOT NULL COMMENT '系统自动回复配置ID',
  `msg_type` varchar(30) NOT NULL COMMENT '系统自动回复消息类型：欢迎语 - welcome_auto_reply 排队提示语 - wait_auto_reply 超时回复提醒语 timeout_auto_reply 会话结束语 end_auto_reply',
  `msg_sub_type` varchar(50) NOT NULL COMMENT '系统自动回复消息子类型 超时 seat_timeout_visitor_comfort- 坐席超时访客安抚语 seat_timeout_seat_tip - 坐席超时坐席提示语 visitor_timeout_visitor_tip - 访客超时访客提示语   visitor_timeout_seat_tip - 访客超时坐席提示语  visitor_timeout_close_visitor_tip - 访客超时关闭访客提示语\r\n        排队 queue_up_tip 排队提示语 queue_up_comfort 排队安抚语 结束  visitor_leave_timeout_tip 访客离开超时自动关闭 seat_end_tip - 坐席主动关闭提示语  欢迎 welcome_tip -欢迎语配置,  welcome_introduce 欢迎介绍语',
  `sub_type_show_seq` tinyint(3) NOT NULL DEFAULT '0' COMMENT '页面展示顺序(子类型内)',
  `autoReply_receive_object` varchar(30) NOT NULL COMMENT '系统自动消息接收对象：坐席-seat、访客-visitor',
  `autoReply_content` varchar(255) DEFAULT NULL COMMENT '自动回复内容（字符串模板）',
  `default_content` varchar(255) DEFAULT NULL COMMENT '自动回复内容（字符串模板）默认值',
  `trigger_mode` varchar(30) DEFAULT NULL COMMENT '提示语触发事件类型：系统主动 - system_active、 访客回复超时 - visitor_timeout、坐席回复超时 - agent_timeout、访客主动关闭 - visitor_close、客服主动关闭 - agent-close',
  `timeout_cost_minutes` varchar(3) DEFAULT '' COMMENT '回复超时时长（分钟）',
  `support_placeholder` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否插入占位符 0-否 1-是',
  `placeholder_json` varchar(255) DEFAULT NULL COMMENT '占位符键值对json 格式',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态  1：启用，0：未启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间'
) ;


DROP TABLE IF EXISTS conversation_auto_reply_record; 

CREATE TABLE `conversation_auto_reply_record` (
  `record_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `conversation_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '会话id',
  `conversation_item_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '会话记录id',
  `conversation_auto_reply_detail_config_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '自动回复配置明细id',
  `msg_sub_type` varchar(50) NOT NULL DEFAULT '' COMMENT '超时类型',
  `record_status` varchar(20) NOT NULL DEFAULT '' COMMENT '发送状态：success:执行成功，fail:执行失败',
  `content` varchar(200) NOT NULL DEFAULT '' COMMENT '发送内容',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，1:删除，0:未删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`record_id`)    
) COMMENT='定时任务执行记录表';


DROP TABLE IF EXISTS conversation_style_config; 

CREATE TABLE `conversation_style_config` (
  `conversation_style_config_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '会话样式配置ID',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道id',
  `client_port_type` varchar(20) NOT NULL COMMENT '面向端口类型:  web-web端 app-app/小程序',
  `conversation_theme_id` bigint(20) NOT NULL COMMENT '当前选择主题ID',
  `sidebar_json` text COMMENT '侧边栏配置json',
  `other_json` text COMMENT '其他配置json',
  `client_category_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '端侧ID',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态  1：禁用，0：启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`conversation_style_config_id`) USING BTREE,
  UNIQUE KEY (`channel_id`,`client_port_type`)  
) COMMENT='渠道会话样式配置表';


DROP TABLE IF EXISTS conversation_theme_config; 

CREATE TABLE `conversation_theme_config` (
  `conversation_theme_config_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主题配置ID',
  `client_user_type` varchar(30) NOT NULL COMMENT '端侧对象类型:  seat-坐席 visitor-访客',
  `client_port_type` varchar(20) NOT NULL COMMENT '面向端口类型:  web-web端 app-app/小程序',
  `theme_type` varchar(20) NOT NULL COMMENT '主题类型:  theme 主题， orderCard 订单主题样式，wordOrderCard 工单主题样式',
  `config_json` text COMMENT '配置json',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`conversation_theme_config_id`) USING BTREE 
) COMMENT='渠道会话主题配置表';


DROP TABLE IF EXISTS conversation_visitor_detail; 

CREATE TABLE `conversation_visitor_detail` (
  `conversation_visitor_detail_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `conversation_id` bigint(20) unsigned NOT NULL COMMENT '会话id',
  `ip` varchar(50) DEFAULT NULL COMMENT 'ip地址',
  `device_Id` varchar(50) DEFAULT NULL COMMENT '设备id',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备型号 web-网页 android-安卓 ios-苹果',
  `device_version` varchar(50) DEFAULT NULL COMMENT '设备版本',
  `phone_model` varchar(50) DEFAULT NULL COMMENT '手机型号',
  `conversation_page_url` varchar(500) DEFAULT NULL COMMENT '会话页地址url',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`conversation_visitor_detail_id`)  
) COMMENT='会话访客明细表';


DROP TABLE IF EXISTS field_attribute; 

CREATE TABLE `field_attribute` (
  `field_attribute_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `from_template_id` bigint(20) unsigned NOT NULL COMMENT '来源模板id',
  `from_template_type` varchar(50) NOT NULL COMMENT '来源模板类型 visitor_info-访客信息  business_from-业务表单',
  `field_en` varchar(50) NOT NULL COMMENT '字段英文名称',
  `field_cn` varchar(50) NOT NULL COMMENT '字段中文名称',
  `field_scene` varchar(50) NOT NULL COMMENT '字段场景 info-信息 from-表单',
  `field_type` varchar(50) NOT NULL COMMENT '字段录入类型:text-文本,input-单行文本,textara-文本框,select-下拉列表,compositeSelect-多级复合下拉列表,picture-图片,video-视频,audio-音频',
  `field_desc` varchar(500) NOT NULL DEFAULT '' COMMENT '字段说明',
  `field_extra` text COMMENT '字段扩展信息',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态: 1-禁用, 0-启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`field_attribute_id`)  
) COMMENT='字段属性表';


DROP TABLE IF EXISTS flow_context; 

CREATE TABLE `flow_context` (
  `context_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `instance_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '流程实例id',
  `outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '外部用户id',
  `context_value` text COMMENT '上下文内容',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，1：删除，0：正常',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`context_id`)   
) COMMENT='流程上下文表';


DROP TABLE IF EXISTS flow_define; 

CREATE TABLE `flow_define` (
  `flow_define_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `flow_name` varchar(50) NOT NULL DEFAULT '' COMMENT '路由导航名称',
  `update_account_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人id',
  `create_account_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人id',
  `status` varchar(30) NOT NULL DEFAULT 'enable' COMMENT '状态：enable：启用， disable：禁用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，1：删除，0：正常',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`flow_define_id`) 
) COMMENT='流程定义表';


DROP TABLE IF EXISTS flow_instance; 

CREATE TABLE `flow_instance` (
  `instance_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `flow_define_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '流程表主键id',
  `outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '外部用户id',
  `current_node_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '当前节点id',
  `status` varchar(30) NOT NULL DEFAULT 'prepare' COMMENT 'running:进行中，completed:已完成，fail:失败',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，1：删除，0：正常',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `conversation_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '会话id',
  PRIMARY KEY (`instance_id`)    
) COMMENT='流程实例表';


DROP TABLE IF EXISTS flow_node; 

CREATE TABLE `flow_node` (
  `flow_node_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `flow_define_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '流程表主键id',
  `node_name` varchar(50) NOT NULL DEFAULT '' COMMENT '节点名',
  `node_type` varchar(30) NOT NULL DEFAULT '' COMMENT '节点类型：robot:机器人，time:时间，system_msg:系统消息，artificial：转人工，system_reply:系统自动回复，leave_word:留言',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '顺序，越小越靠前',
  `config_json` text COMMENT '节点配置json',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，1：删除，0：正常',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`flow_node_id`)  
) COMMENT='流程节点表';


DROP TABLE IF EXISTS flow_transition; 

CREATE TABLE `flow_transition` (
  `transition_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `flow_define_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '流程表主键id',
  `from_node_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '来源节点id',
  `to_node_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '下一个节点id',
  `condition_expression` text COMMENT '条件表达式，决定是否执行to_node_id节点',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，1：删除，0：正常',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`transition_id`)    
) COMMENT='流程节点关系表';


DROP TABLE IF EXISTS from_submit_record; 

CREATE TABLE `from_submit_record` (
  `from_submit_record_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `conversation_id` bigint(20) unsigned NOT NULL COMMENT '会话id',
  `seat_id` bigint(20) unsigned NOT NULL COMMENT '坐席id',
  `business_from_template_id` bigint(20) unsigned NOT NULL COMMENT '业务模板id',
  `submit_content` text COMMENT '提交内容json',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`from_submit_record_id`)  
) COMMENT='表单信息提交记录表';


DROP TABLE IF EXISTS group_info; 

CREATE TABLE `group_info` (
  `group_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分组id',
  `group_name` varchar(50) NOT NULL COMMENT '分组名称',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态：1：禁用，0：启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `distribute_strategy` varchar(20) NOT NULL DEFAULT 'balanced_saturation' COMMENT '分配策略 balanced_saturation-饱和度均衡',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `tenant_id` bigint(20) unsigned NOT NULL COMMENT '租户id',
  PRIMARY KEY (`group_id`) USING BTREE
) COMMENT='分组表';


DROP TABLE IF EXISTS group_queue; 

CREATE TABLE `group_queue` (
  `group_queue_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `conversation_distribute_id` bigint(20) unsigned NOT NULL COMMENT '会话分配id',
  `group_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分组id',
  `conversation_id` bigint(20) NOT NULL COMMENT '会话id',
  `queue_config_id` int(11) NOT NULL COMMENT '队列配置id',
  `outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '外部用户id',
  `queue_status` varchar(50) NOT NULL DEFAULT 'wait' COMMENT '队列状态 wait-排队中 completed-分配完成 cancel-取消排队  expired-过期 ',
  `release_time` datetime DEFAULT NULL COMMENT '出队时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `sort` bigint(20) NOT NULL COMMENT '排序',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`group_queue_id`),
  UNIQUE KEY (`conversation_distribute_id`)     
) COMMENT='分组队列表';


DROP TABLE IF EXISTS group_rule; 

CREATE TABLE `group_rule` (
  `group_rule_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `group_id` int(11) unsigned NOT NULL COMMENT '分组id',
  `rule_metric_id` int(11) unsigned NOT NULL COMMENT '规则指标id',
  `comparison_operator` varchar(20) NOT NULL DEFAULT 'eq' COMMENT '比较运算符: eq-属于, not_eq-不属于',
  `rule_config_value` text COMMENT '规则配置值',
  `operator_id` bigint(20) unsigned NOT NULL COMMENT '操作人账号id',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态: 1-禁用, 0-启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`group_rule_id`)   
) COMMENT='分组规则表';


DROP TABLE IF EXISTS group_rule_join_condition; 

CREATE TABLE `group_rule_join_condition` (
  `join_condition_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `group_id` int(11) unsigned NOT NULL COMMENT '分组id',
  `logical_operator` varchar(20) NOT NULL DEFAULT 'and' COMMENT '逻辑运算符 and-且 or-或',
  `operator_id` bigint(20) unsigned NOT NULL COMMENT '操作人账号id',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`join_condition_id`)  
) COMMENT='分组规则连接条件表';


DROP TABLE IF EXISTS im_admin; 

CREATE TABLE `im_admin` (
  `im_admin_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `im_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '应用id',
  `admin_account_id` varchar(50) NOT NULL DEFAULT '' COMMENT '管理员账号',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '状态：1：删除，0：正常',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`im_admin_id`)  
) COMMENT='应用管理员账号信息';


DROP TABLE IF EXISTS im_config; 

CREATE TABLE `im_config` (
  `im_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户id',
  `im_name` varchar(30) NOT NULL DEFAULT '' COMMENT '应用名',
  `im_third_id` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方应用id',
  `im_third_secret` varchar(255) NOT NULL DEFAULT '' COMMENT '第三方应用密钥',
  `im_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '业务说明',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '状态：1：删除，0：正常',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `im_type` varchar(30) NOT NULL DEFAULT '' COMMENT 'im使用场景',
  PRIMARY KEY (`im_id`)  
) COMMENT='im信息配置表';


DROP TABLE IF EXISTS im_conversation; 

CREATE TABLE `im_conversation` (
  `conversation_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `from_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '会话发起人外部用户id',
  `from_outer_user_type` varchar(20) NOT NULL DEFAULT '' COMMENT 'merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,robot:机器人,seat:坐席',
  `conversation_status` varchar(20) NOT NULL DEFAULT 'processing' COMMENT '会话状态：processing:进行中，complete:已完成',
  `group_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '分组id',
  `to_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '消息接收者id指的是单聊对象虚拟用户id',
  `to_outer_user_type` varchar(20) NOT NULL DEFAULT '' COMMENT 'merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,robot:机器人,seat:坐席',
  `from_type` varchar(30) NOT NULL DEFAULT '' COMMENT '来源类型：sensitive:敏感词，normal:正常进入（通用入口）',
  `from_id` varchar(30) NOT NULL DEFAULT '' COMMENT '来源id，敏感词才有值',
  `from_scene` varchar(50) NOT NULL DEFAULT '' COMMENT '来源场景，order_no:订单',
  `scene_value` varchar(30) NOT NULL DEFAULT '' COMMENT '场景值，订单编号',
  `conversation_tag_ids` varchar(50) DEFAULT NULL COMMENT '会话标签，多个id组成的json',
  `complete_time` datetime DEFAULT NULL COMMENT '会话完成时间',
  `is_delete` tinyint(3) DEFAULT '0' COMMENT '删除状态，0:正常，1:删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `agent_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '代理账号id',
  `channel_config_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'channel_config主键id',
  `conversation_type` varchar(50) NOT NULL DEFAULT '' COMMENT '会话类型 online-在线会话 leave-留言 history-历史会话',
  `timeout_xxl_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'xxljob任务id',
  PRIMARY KEY (`conversation_id`)    
) COMMENT='会话表';


DROP TABLE IF EXISTS im_conversation_active_log; 

CREATE TABLE `im_conversation_active_log` (
  `conversation_active_log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `from_conversation_id` bigint(20) unsigned NOT NULL COMMENT '激活来源会话id',
  `to_conversation_id` bigint(20) unsigned NOT NULL COMMENT '被激活的会话id',
  `activate_scene` varchar(50) NOT NULL COMMENT '激活场景 history-历史会话  leave-留言',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`conversation_active_log_id`)  
) COMMENT='会话激活日志表';


DROP TABLE IF EXISTS im_conversation_distribute; 

CREATE TABLE `im_conversation_distribute` (
  `conversation_distribute_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `conversation_id` bigint(20) NOT NULL COMMENT '会话id',
  `group_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分组id',
  `seat_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '坐席id',
  `outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '外部用户id',
  `group_queue_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '分组队列id',
  `distribute_rule_json` text COMMENT '分配规则json',
  `distribute_status` varchar(50) NOT NULL DEFAULT '' COMMENT '分配状态 engroup-进组 completed-分配完成 cancel-取消分配',
  `distribute_group_time` datetime DEFAULT NULL COMMENT '进组时间',
  `distribute_seat_time` datetime DEFAULT NULL COMMENT '分配坐席时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`conversation_distribute_id`)     
) COMMENT='会话分配表';


DROP TABLE IF EXISTS im_conversation_item; 

CREATE TABLE `im_conversation_item` (
  `conversation_item_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `conversation_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '会话id',
  `from_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '消息发送方外部用户id',
  `from_outer_user_type` varchar(30) NOT NULL DEFAULT '' COMMENT 'merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,virtual:虚拟账号',
  `to_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '接收方外部用户id',
  `to_outer_user_type` varchar(30) NOT NULL DEFAULT '' COMMENT 'merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,virtual:虚拟账号',
  `response_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '群聊接待人外部用户id',
  `response_outer_user_type` varchar(50) NOT NULL DEFAULT '' COMMENT '群聊接待人类型robot:机器人,seat:坐席',
  `msg_send_time` datetime DEFAULT NULL COMMENT '消息发送时间',
  `has_read` tinyint(3) NOT NULL DEFAULT '0' COMMENT '接收方是否已读消息',
  `msg_read_time` datetime DEFAULT NULL COMMENT '消息读取时间',
  `msg_type` varchar(100) NOT NULL DEFAULT 'TIMTextElem' COMMENT '消息类型，TIMTextElem（文本消息）,TIMLocationElem（位置消息）,TIMFaceElem（表情消息）,TIMCustomElem（自定义消息）,TIMSoundElem（语音消息）,TIMImageElem（图像消息）,TIMFileElem（文件消息）,TIMVideoFileElem（视频消息）',
  `msg_content` text COMMENT '消息内容',
  `msg_seq` bigint(20) NOT NULL DEFAULT '0' COMMENT '消息序列号',
  `msg_key` varchar(200) NOT NULL DEFAULT '' COMMENT '消息唯一标识，可用于撤回',
  `msg_id` varchar(200) NOT NULL DEFAULT '' COMMENT '消息在客户端上的唯一标识',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，0:正常，1:删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `msg_label` varchar(50) NOT NULL DEFAULT '' COMMENT '会话类型 online-在线会话 leave-留言 history-历史会话',
  PRIMARY KEY (`conversation_item_id`)         
) COMMENT='会话消息明细表';


DROP TABLE IF EXISTS im_conversation_log; 

CREATE TABLE `im_conversation_log` (
  `conversation_log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `conversation_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '会话id',
  `from_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '会话发起人外部用户id',
  `from_outer_user_type` varchar(20) NOT NULL DEFAULT '' COMMENT 'merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,robot:机器人,seat:坐席',
  `conversation_status` varchar(20) NOT NULL DEFAULT 'processing' COMMENT '会话状态：processing:进行中，complete:已完成',
  `group_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '分组id',
  `to_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '消息接收者id指的是单聊对象虚拟用户id',
  `to_outer_user_type` varchar(20) NOT NULL DEFAULT '' COMMENT 'merchant:企业用户,master:师傅,enterprise:总包,client:家庭用户,robot:机器人,seat:坐席',
  `from_type` varchar(30) NOT NULL DEFAULT '' COMMENT '来源类型：sensitive:敏感词，normal:正常进入（通用入口）',
  `from_id` varchar(30) NOT NULL DEFAULT '' COMMENT '来源id，敏感词才有值',
  `from_scene` varchar(50) NOT NULL DEFAULT '' COMMENT '来源场景，order_no:订单',
  `scene_value` varchar(30) NOT NULL DEFAULT '' COMMENT '场景值，订单编号',
  `conversation_tag_ids` varchar(50) DEFAULT NULL COMMENT '会话标签，多个id组成的json',
  `complete_time` datetime DEFAULT NULL COMMENT '会话完成时间',
  `agent_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '代理账号id',
  `channel_config_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'channel_config主键id',
  `conversation_type` varchar(50) NOT NULL DEFAULT '' COMMENT '会话类型 online-在线会话 leave-留言 history-历史会话',
  `timeout_xxl_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'xxljob任务id',
  `is_delete` tinyint(3) DEFAULT '0' COMMENT '删除状态，0:正常，1:删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`conversation_log_id`)     
) COMMENT='会话日志表';


DROP TABLE IF EXISTS im_conversation_message_history_import_log; 

CREATE TABLE `im_conversation_message_history_import_log` (
  `conversation_message_history_import_log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `conversation_id` bigint(20) unsigned NOT NULL COMMENT '会话id',
  `import_status` varchar(20) NOT NULL DEFAULT '0' COMMENT '导入状态: processing-导入中, complete-导入完成, fail-导入失败',
  `import_time` datetime DEFAULT NULL COMMENT '导入时间',
  `import_remark` text COMMENT '导入备注',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`conversation_message_history_import_log_id`)  
) COMMENT='会话历史消息导入日志表';


DROP TABLE IF EXISTS im_conversation_transfer_log; 

CREATE TABLE `im_conversation_transfer_log` (
  `transfer_log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `conversation_id` bigint(20) NOT NULL COMMENT '会话id',
  `origin_group_info_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '原来的坐席分组id',
  `origin_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '原来的坐席外部账号id',
  `new_group_info_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '转接的分组分组id',
  `new_outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '转接的坐席外部用户id',
  `transfer_time` datetime DEFAULT NULL COMMENT '转接时间',
  `note` varchar(255) NOT NULL DEFAULT '' COMMENT '转接备注',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`transfer_log_id`)     
) COMMENT='会话转接记录表';


DROP TABLE IF EXISTS im_group; 

CREATE TABLE `im_group` (
  `im_group_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `im_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '应用id',
  `conversation_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '会话id',
  `outer_group_id` varchar(50) NOT NULL DEFAULT '' COMMENT '外部群组id',
  `group_status` varchar(30) NOT NULL DEFAULT 'used' COMMENT '群组状态，used:使用中，no_used:已解散',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，0:正常，1:删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`im_group_id`)    
) COMMENT='群组表';


DROP TABLE IF EXISTS im_group_user_relation; 

CREATE TABLE `im_group_user_relation` (
  `im_user_relation_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `im_group_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '内部群组id',
  `outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '外部用户id',
  `outer_class_type` varchar(30) NOT NULL DEFAULT '' COMMENT '用户类型，virtual:虚拟账号，seat:坐席对应的外部账号',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，0:正常，1:删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `member_status` varchar(30) NOT NULL DEFAULT 'in' COMMENT '成员状态，in:是群成员，out:已退出',
  PRIMARY KEY (`im_user_relation_id`)   
) COMMENT='群聊成员关系';


DROP TABLE IF EXISTS incoming_property_config; 

CREATE TABLE `incoming_property_config` (
  `property_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户id',
  `property_value` varchar(250) NOT NULL DEFAULT '' COMMENT '特征值，ip/用户id/师傅id/总包id/游客设备id',
  `property_type` varchar(50) NOT NULL DEFAULT '' COMMENT '特征类型，user/master/enterprise/tourist/ip',
  `control_type` varchar(50) NOT NULL DEFAULT '' COMMENT '控制类型，white:白名单，black:黑名单',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态：1：禁用，0：启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态：0:正常，1:删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`property_id`)  
) COMMENT='进线属性配置';


DROP TABLE IF EXISTS incoming_safety_config; 

CREATE TABLE `incoming_safety_config` (
  `safety_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户id',
  `url_froms` text NOT NULL COMMENT '进线来源url',
  `dimension_ip_times` int(11) NOT NULL COMMENT 'ip注册上限，0:不限制',
  `dimension_type` varchar(30) NOT NULL DEFAULT '' COMMENT '维度类型：daily日期维度、minute分钟维度',
  `dimension_value` int(11) NOT NULL DEFAULT '0' COMMENT '如果是分钟维度，表示10分钟内的注册次数；如果是日期维度，表示10天内的注册次数',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态：1：禁用，0：启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态：0:正常，1:删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`safety_id`)  
) COMMENT='进线安全配置';


DROP TABLE IF EXISTS operate_log; 

CREATE TABLE `operate_log` (
  `operate_log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志id',
  `from_table_id` bigint(20) unsigned NOT NULL COMMENT '业务表id',
  `from_table_name` varchar(255) NOT NULL COMMENT '业务表名',
  `from_business` varchar(50) NOT NULL COMMENT '业务类型（add,modify,delete,changeStatus）',
  `operate_content` text COMMENT '操作内容',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`operate_log_id`) USING BTREE 
) COMMENT='操作日志表';


DROP TABLE IF EXISTS problem; 

CREATE TABLE `problem` (
  `problem_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `problem_name` varchar(50) NOT NULL COMMENT '问题类型名称',
  `problem_desc` varchar(500) NOT NULL DEFAULT '' COMMENT '问题类型描述',
  `parent_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '父级id',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '等级',
  `problem_extra` text COMMENT '扩展信息',
  `tenant_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户id',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态: 1-禁用, 0-启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`problem_id`)   
) COMMENT='问题类型表';


DROP TABLE IF EXISTS questionnaire_coin_grant; 

CREATE TABLE `questionnaire_coin_grant` (
  `questionnaire_coin_grant_id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `questionnaire_result_record_id` bigint(20) unsigned NOT NULL COMMENT '问卷ID',
  `coin_grant_status` varchar(20) NOT NULL DEFAULT 'un_grant' COMMENT '奖励金币发放状态 un_grant-未发放 has_grant-已发放 needless-无需发放',
  `coin_grant_count` int(11) unsigned DEFAULT '0' COMMENT '奖励金币个数',
  `grant_faild_reason` varchar(20) DEFAULT 'un_grant' COMMENT '奖励金币发放失败原因',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `is_delete` tinyint(3) unsigned DEFAULT '0' COMMENT '是否删除：1 - 是；0 - 否',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`questionnaire_coin_grant_id`),
  UNIQUE KEY (`questionnaire_result_record_id`) 
) COMMENT='奖励金币发放表';


DROP TABLE IF EXISTS questionnaire_result_records; 

CREATE TABLE `questionnaire_result_records` (
  `questionnaire_result_record_id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `questionnaire_id` bigint(20) unsigned NOT NULL COMMENT '问卷ID',
  `title` varchar(50) NOT NULL COMMENT '问卷标题',
  `landing_page_template_id` bigint(20) unsigned NOT NULL COMMENT '落地页ID',
  `result_id` bigint(20) unsigned NOT NULL COMMENT '对应问卷答案结果ID',
  `work_type` varchar(30) NOT NULL DEFAULT '' COMMENT '工单类型 second-二线 customerOrder-平台售后',
  `work_id` bigint(20) unsigned NOT NULL COMMENT '工单ID',
  `object_type` varchar(20) NOT NULL COMMENT '对象-user-用户；master-师傅；',
  `answer_result_json` varchar(255) DEFAULT NULL COMMENT '问卷答案json {''answer_result1'':1,''answer_result2'':2,''answer_result3'':3,''answer_result4'':4}',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `is_delete` tinyint(3) unsigned DEFAULT '0' COMMENT '是否删除：1 - 是；0 - 否',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`questionnaire_result_record_id`),
  UNIQUE KEY (`questionnaire_id`) 
) COMMENT='工单问卷结果标记表';


DROP TABLE IF EXISTS queue_config; 

CREATE TABLE `queue_config` (
  `queue_config_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `from_id` bigint(20) unsigned NOT NULL COMMENT '队列来源id group-group_id',
  `from_type` varchar(50) NOT NULL COMMENT '队列类型 group-分组队列',
  `max_length` bigint(20) unsigned NOT NULL COMMENT '队列最大长度',
  `member_interval_minute` bigint(20) unsigned NOT NULL COMMENT '队列成员间隔分钟',
  `msg_ttl` bigint(20) unsigned NOT NULL COMMENT '队列生命周期（秒）',
  `operator_id` bigint(20) unsigned NOT NULL COMMENT '操作人账号id',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`queue_config_id`)  
) COMMENT='队列配置表';


DROP TABLE IF EXISTS rule_metric_config; 

CREATE TABLE `rule_metric_config` (
  `rule_metric_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `rule_metric_cn` varchar(50) NOT NULL DEFAULT '' COMMENT '规则中文名称',
  `rule_metric_en` varchar(50) NOT NULL DEFAULT '' COMMENT '规则英文名称',
  `rule_metric_object` varchar(50) NOT NULL COMMENT '规则对象 group:分组规则 seat:坐席规则',
  `rule_metric_desc` varchar(500) NOT NULL DEFAULT '' COMMENT '规则描述',
  `rule_metric_extra_json` text COMMENT '规则扩展json（存默认枚举值，规则的来源，渠道，入口，咨询类型，用户ka标签等）',
  `from_type` varchar(20) NOT NULL DEFAULT '' COMMENT '规则来源 kf-客服系统 bigdata-大数据',
  `operator_id` bigint(20) unsigned NOT NULL COMMENT '操作人账号id',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`rule_metric_id`) 
) COMMENT='规则指标表';


DROP TABLE IF EXISTS satisfaction_evaluate_record; 

CREATE TABLE `satisfaction_evaluate_record` (
  `satisfaction_evaluate_record_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道配置ID',
  `satisfaction_level_config_id` bigint(20) NOT NULL COMMENT '会话满意度配置ID',
  `guideline_desc` varchar(2000) DEFAULT '' COMMENT '星级和对应的描述json',
  `title` varchar(255) DEFAULT NULL COMMENT '自动回复内容（字符串模板）',
  `has_solve_problem` tinyint(3) NOT NULL DEFAULT '1' COMMENT '客服是否解决问题 0-否 1-是 ',
  `result_callback_form_json` text COMMENT '结果回收表单配置字段json',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态  1：启用，0：未启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`satisfaction_evaluate_record_id`) USING BTREE 
 COMMENT '渠道id'
) COMMENT='渠道会话满意度评价配置快照表';


DROP TABLE IF EXISTS satisfaction_evaluate_result_record; 

CREATE TABLE `satisfaction_evaluate_result_record` (
  `satisfaction_evaluate_result_record_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '会话满意度评价结果记录表id',
  `satisfaction_evaluate_record_id` bigint(20) NOT NULL COMMENT '记录ID',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道id',
  `seat_id` bigint(20) NOT NULL COMMENT '坐席id',
  `out_user_id` bigint(20) NOT NULL COMMENT '外部用户id',
  `guideline_desc` varchar(2000) DEFAULT '' COMMENT '星级和对应的描述',
  `title` varchar(255) DEFAULT NULL,
  `has_solve_problem` tinyint(3) NOT NULL DEFAULT '1' COMMENT '客服是否解决问题 0-否 1-是 ',
  `result_callback_form_json` text COMMENT '结果回收表单配置字段json',
  `result_json` text COMMENT '结果回收表单json',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`satisfaction_evaluate_result_record_id`) USING BTREE   
) COMMENT='渠道会话满意度评价记录表';


DROP TABLE IF EXISTS satisfaction_level_config; 

CREATE TABLE `satisfaction_level_config` (
  `satisfaction_level_config_id` bigint(20) unsigned NOT NULL COMMENT '系统自动回复明细配置ID',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道id',
  `result_callback_form_json` text COMMENT '结果回收表单配置字段json',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态  1：禁用，0：启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`satisfaction_level_config_id`) USING BTREE 
 COMMENT '渠道id'
) COMMENT='渠道会话满意度配置表';


DROP TABLE IF EXISTS seat_function; 

CREATE TABLE `seat_function` (
  `seat_function_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `seat_function_cn` varchar(50) NOT NULL COMMENT '坐席功能名称',
  `seat_function_en` varchar(50) NOT NULL COMMENT '坐席功能英文名称',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`seat_function_id`) USING BTREE
) COMMENT='坐席功能表';


DROP TABLE IF EXISTS seat_group_distribute_rule; 

CREATE TABLE `seat_group_distribute_rule` (
  `seat_group_distribute_rule_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `group_id` int(11) unsigned NOT NULL COMMENT '分组id',
  `seat_id` bigint(20) unsigned NOT NULL COMMENT '坐席id',
  `rule_metric_id` int(11) unsigned NOT NULL COMMENT '规则指标id',
  `rule_config_value` text COMMENT '规则配置值',
  `operator_id` bigint(20) unsigned NOT NULL COMMENT '操作人账号id',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`seat_group_distribute_rule_id`)  
) COMMENT='分组坐席分配规则表';


DROP TABLE IF EXISTS seat_group_mapping; 

CREATE TABLE `seat_group_mapping` (
  `seat_group_mapping_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `group_id` int(11) unsigned NOT NULL COMMENT '分组id',
  `seat_id` bigint(20) unsigned NOT NULL COMMENT '坐席id',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用（分配）状态：1：禁用，0：启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`seat_group_mapping_id`) USING BTREE 
) COMMENT='坐席分组关联表';


DROP TABLE IF EXISTS seat_info; 

CREATE TABLE `seat_info` (
  `seat_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '坐席id',
  `seat_name` varchar(50) NOT NULL COMMENT '坐席昵称',
  `face_url` varchar(255) NOT NULL COMMENT '头像地址',
  `account_id` bigint(20) unsigned NOT NULL COMMENT '客服账号id',
  `account_type` varchar(50) NOT NULL COMMENT '账号类型：ams:客服账号，包含外包，enterprise:总包账号',
  `tenant_id` bigint(20) unsigned NOT NULL COMMENT '租户id',
  `seat_no` bigint(20) unsigned NOT NULL COMMENT '坐席工号',
  `seat_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '坐席类型 0-普通 1-组长 2-管理员',
  `tag_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联标签id',
  `current_seat_status_en` varchar(50) NOT NULL DEFAULT '' COMMENT '当前坐席状态',
  `login_seat_status_en` varchar(50) NOT NULL DEFAULT '' COMMENT '坐席上线状态',
  `max_wiring_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '坐席最大链接数',
  `seat_extra_json` text COMMENT '坐席扩展字段',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态：1：禁用，0：启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`seat_id`) USING BTREE,
  UNIQUE KEY (`tenant_id`,`seat_no`)   
) COMMENT='在线坐席表';


DROP TABLE IF EXISTS seat_status; 

CREATE TABLE `seat_status` (
  `seat_status_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `seat_status_cn` varchar(50) NOT NULL COMMENT '坐席状态名称',
  `seat_status_en` varchar(50) NOT NULL COMMENT '坐席状态英文名称',
  `seat_status_desc` varchar(500) NOT NULL COMMENT '状态说明',
  `default_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '默认值类型 0-无 1-离线默认值 2-在线默认值',
  `is_reckon_in_man_hour` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否计入工时 0-否 1-是',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`seat_status_id`) USING BTREE 
) COMMENT='坐席状态表';


DROP TABLE IF EXISTS seat_status_function_mapping; 

CREATE TABLE `seat_status_function_mapping` (
  `seat_status_function_mapping_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `seat_status_id` int(11) unsigned NOT NULL COMMENT '坐席状态id',
  `seat_status_en` varchar(50) NOT NULL COMMENT '坐席状态英文名称',
  `seat_function_id` int(11) unsigned NOT NULL COMMENT '坐席功能id',
  `seat_function_en` varchar(50) NOT NULL COMMENT '坐席功能英文名称',
  `seat_function_type` varchar(50) NOT NULL COMMENT '坐席功能类型 support-支持 restrict-限制',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`seat_status_function_mapping_id`) USING BTREE 
) COMMENT='坐席状态功能关联表';


DROP TABLE IF EXISTS seat_status_switch_log; 

CREATE TABLE `seat_status_switch_log` (
  `seat_status_switch_log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `seat_id` bigint(20) unsigned NOT NULL COMMENT '坐席id',
  `seat_status_en_before` varchar(50) NOT NULL COMMENT '切换前状态英文名',
  `seat_status_en_after` varchar(50) NOT NULL COMMENT '切换后状态英文名',
  `first_online_time` datetime DEFAULT NULL COMMENT '切换到在线的首次时间，这个字段用于控制在线和满负荷之间多次切换的时候，最开始的时间',
  `full_load_seconds` bigint(20) DEFAULT NULL COMMENT '满负荷秒数，这个字段用户记录本次满负荷回到在线的时长，单位秒',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`seat_status_switch_log_id`)  
) COMMENT='坐席状态切换日志表';


DROP TABLE IF EXISTS seat_virtual_relation; 

CREATE TABLE `seat_virtual_relation` (
  `seat_virtual_relation_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `seat_id` bigint(20) unsigned NOT NULL COMMENT '坐席id',
  `outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '外部用户id',
  `virtual_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '虚拟账号用户id',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定时间',
  `over_bind_time` datetime DEFAULT NULL COMMENT '解绑时间',
  `bind_status` varchar(20) NOT NULL DEFAULT 'binding' COMMENT '绑定状态，binding:绑定中，over:绑定结束',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态：0:正常，1:删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`seat_virtual_relation_id`)    
) COMMENT='坐席虚拟账号绑定关系';


DROP TABLE IF EXISTS socket_send_log; 

CREATE TABLE `socket_send_log` (
  `socket_send_log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `conversation_id` bigint(20) unsigned NOT NULL COMMENT '会话id',
  `subscriber_class` varchar(50) NOT NULL COMMENT '订阅人类型：seat-坐席 user-用户 master-师傅 enterprise-总包',
  `subscriber_id` varchar(50) NOT NULL COMMENT '订阅人id',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型 msg_unread-消息未读  timeout_without-超时未回复 abount_to_timeout_without-临近超时未回复 conversation_distribute-会话分配 conversation_close-会话关闭',
  `message_content` text COMMENT '消息内容json',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`socket_send_log_id`)   
) COMMENT='订阅消息发送日志表';


DROP TABLE IF EXISTS tags; 

CREATE TABLE `tags` (
  `tag_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '标签id',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `tag_desc` varchar(500) NOT NULL DEFAULT '' COMMENT '标签描述',
  `scene` varchar(50) NOT NULL COMMENT '使用场景，conversation：会话,seat：坐席',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '启用状态：1：禁用，0：启用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`tag_id`) USING BTREE
) COMMENT='标签表';


DROP TABLE IF EXISTS template_init_json; 

CREATE TABLE `template_init_json` (
  `template_init_json_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_type_name` varchar(50) NOT NULL COMMENT '关联表名 如 client_channel_config_draft',
  `column_type_name` varchar(30) NOT NULL COMMENT '关联字段名 如 all_config_json',
  `init_json_type` varchar(30) NOT NULL COMMENT '初始化JSON类型:  如会话满意度：satifation, 会话样式主题：style_theme,系统自动回复- auto_reply',
  `content` text COMMENT '初始化JSON内容',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是,0-否',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`template_init_json_id`) USING BTREE 
) COMMENT='端侧渠道初始化json配置表';


DROP TABLE IF EXISTS tenant_info; 

CREATE TABLE `tenant_info` (
  `tenant_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tenant_name` varchar(30) NOT NULL DEFAULT '' COMMENT '租户名',
  `tencent_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '状态：1：删除，0：正常',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`tenant_id`) 
) COMMENT='租户信息表';


DROP TABLE IF EXISTS user_device_relation; 

CREATE TABLE `user_device_relation` (
  `user_device_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '外部用户id',
  `device_id` varchar(250) NOT NULL DEFAULT '' COMMENT '设备id',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，0:正常，1:删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_device_id`)   
) COMMENT='外部用户ID设备ID映射表';


DROP TABLE IF EXISTS user_problem_class; 

CREATE TABLE `user_problem_class` (
  `problem_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `channel_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '关联渠道id',
  `problem_name` varchar(50) NOT NULL DEFAULT '' COMMENT '问题分类名',
  `status` varchar(20) NOT NULL DEFAULT 'enable' COMMENT '状态：enable：启用， disable：禁用',
  `update_account_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人账号id',
  `create_account_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人账号id',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，1：删除，0：正常',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`problem_id`)  
) COMMENT='用户问题分类';


DROP TABLE IF EXISTS user_register_info; 

CREATE TABLE `user_register_info` (
  `register_info_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `im_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'im应用配置ID',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '内部用户ID',
  `user_class` varchar(40) NOT NULL DEFAULT '' COMMENT '用户类型(merchant/master/seat:客服坐席)',
  `outer_user_id` varchar(40) NOT NULL DEFAULT '' COMMENT '外部用户ID',
  `register_status` varchar(20) NOT NULL DEFAULT '' COMMENT '注册状态：success:注册成功，fail:注册失败',
  `register_time` datetime DEFAULT NULL COMMENT '注册时间',
  `user_sign` varchar(250) NOT NULL DEFAULT '' COMMENT '用户签名',
  `user_sign_expire_time` datetime DEFAULT NULL COMMENT '签名过期时间',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '账号状态，0:启用，1:禁用',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，0:正常状态，1:删除状态',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `device_id` varchar(250) NOT NULL DEFAULT '' COMMENT '设备id',
  `online_state` varchar(30) NOT NULL DEFAULT 'online' COMMENT '用户在线状态，online:在线，offline:离线',
  `online_state_change_time` datetime NOT NULL COMMENT '用户状态变更时间',
  PRIMARY KEY (`register_info_id`)     
) COMMENT='用户注册信息表';


DROP TABLE IF EXISTS user_virtual_relation; 

CREATE TABLE `user_virtual_relation` (
  `user_virtual_relation_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '外部用户id',
  `virtual_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '虚拟账号用户id',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定时间',
  `over_bind_time` datetime DEFAULT NULL COMMENT '解绑时间',
  `bind_status` varchar(20) NOT NULL DEFAULT 'binding' COMMENT '绑定状态，binding:绑定中，over:绑定结束',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态：0:正常，1:删除',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_virtual_relation_id`)   
) COMMENT='用户虚拟账号绑定关系';


DROP TABLE IF EXISTS virtual_user_info; 

CREATE TABLE `virtual_user_info` (
  `virtual_info_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `im_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '应用id',
  `outer_user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '外部用户id',
  `register_time` datetime NOT NULL COMMENT '注册时间',
  `user_sign` varchar(250) NOT NULL DEFAULT '' COMMENT '用户签名',
  `user_sign_expire_time` datetime NOT NULL COMMENT '签名过期时间',
  `status` varchar(30) NOT NULL DEFAULT 'no_used' COMMENT '账号状态，no_used:空闲,used:使用中',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态，1:已删除，0:正常',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`virtual_info_id`)    
) COMMENT='虚拟用户注册信息表';


DROP TABLE IF EXISTS visitor_info_template; 

CREATE TABLE `visitor_info_template` (
  `visitor_info_template_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `visitor_class_en` varchar(50) NOT NULL COMMENT '访客类型英文名称 user-用户 master-师傅',
  `visitor_class_cn` varchar(50) NOT NULL COMMENT '访客类型中文名称 user-用户 master-师傅',
  `tenant_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户id',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`visitor_info_template_id`)  
) COMMENT='访客信息模板表';


DROP TABLE IF EXISTS visitor_mark; 

CREATE TABLE `visitor_mark` (
  `visitor_mark_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `account_id` bigint(20) unsigned NOT NULL COMMENT '打标人id',
  `account_class` varchar(50) NOT NULL COMMENT '打标人类型 seat-坐席打标',
  `outer_user_id` varchar(50) NOT NULL COMMENT '外部用户id',
  `label_value` varchar(50) NOT NULL COMMENT '标签值 alreadyFeedback-已反馈 needFeedback-需反馈  needLeave-需留言',
  `label_name` varchar(50) NOT NULL COMMENT '标签名称 alreadyFeedback-已反馈 needFeedback-需反馈  needLeave-需留言',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '删除状态: 1-删除, 0-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`visitor_mark_id`)   
) COMMENT='访客打标表';


DROP TABLE IF EXISTS work_order_questionnaire_config; 

CREATE TABLE `work_order_questionnaire_config` (
  `questionnaire_id` bigint(20) unsigned NOT NULL COMMENT '主键ID',
  `title` varchar(50) NOT NULL COMMENT '问卷标题',
  `landing_page_template_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '落地页ID',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '2' COMMENT '状态，1未发布；2已发布',
  `object_type` varchar(20) NOT NULL COMMENT '对象-user-用户；master-师傅；home-家庭',
  `match_work_type_json` varchar(100) DEFAULT NULL COMMENT '工单处理诉求类型JSON[{"workType":"secondline","checkStatus":0},{"workType:"customerOrder","checkStatus":0},{"workType":"customerOrder","checkStatus:0}]',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id',
  `is_delete` tinyint(3) unsigned DEFAULT '0' COMMENT '是否删除：1 - 是；0 - 否',
  `create_time` timestamp NULL DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`questionnaire_id`)
) COMMENT='工单问卷配置表';


