//package com.wanshifu.iop.im.domain.bo.channel
//import com.alibaba.fastjson.JSON
//import com.alibaba.fastjson.JSONObject
//import com.alibaba.fastjson.parser.Feature;
//import com.fasterxml.jackson.core.type.TypeReference
//import com.fasterxml.jackson.databind.ObjectMapper
//import spock.lang.Specification
//
//class ChannelBoDeserializationTest extends Specification {
//
//    private static final String JSON_DATA = '''
//        {"channelConfigBoList":[{"allSubItemEmpty":false,"channelId":12948312891,"clientCategoryId":12948312886,"satisfactionLevelConfigBo":{"channelId":12948312891,"createTime":"2025-06-12 17:20:47","isDelete":0,"operatorId":0,"resultCallbackFormJsonBo":{"filedBoList":[{"filedDesc":"满意度发送条件","filedId":0,"filedName":"sendCondition","filedType":"checkbox","isRequired":0,"optionBoList":[{"buttonId":0,"isRequired":0,"isSelected":0,"label":"seat_send","value":"坐席主动发送"},{"buttonId":1,"isRequired":0,"isSelected":0,"label":"seat_close","value":"坐席关闭会话"},{"buttonId":2,"isRequired":0,"isSelected":0,"label":"visitor_end","value":"访客结束会话（超时未回复+超时离开）"}],"satisfactionLevelConfigId":12948312890},{"filedDesc":"满意度时效设置","filedId":1,"filedName":"satisfactionTimeOut","filedType":"radio","isRequired":0,"optionBoList":[{"buttonId":0,"isRequired":0,"isSelected":0,"label":"today","value":"当天失效"}],"satisfactionLevelConfigId":12948312890},{"filedDesc":"评价引导语","filedId":2,"filedName":"evaluationPrompt","filedType":"text","isRequired":0,"optionBoList":[],"satisfactionLevelConfigId":12948312890},{"filedDesc":"结果回收按钮","filedId":3,"filedName":"resultCallbackButton","filedType":"button","isRequired":1,"optionBoList":[{"buttonIcon":"aid1","buttonId":0,"isRequired":0,"isSelected":0,"label":"has_solved_problem","value":"已解决"},{"buttonIcon":"aid2","buttonId":1,"buttonReasonItemList":[{"buttonId":1,"buttonReason":"客服态度不好","checkStatus":0},{"buttonId":1,"buttonReason":"客服不专业","checkStatus":0}],"isRequired":0,"isSelected":0,"label":"no_solved_problem","value":"未解决"}],"satisfactionLevelConfigId":12948312890},{"filedDesc":"评价星级","filedId":4,"filedName":"evaluationStarLevel","filedType":"composite","isRequired":1,"optionBoList":[{"buttonId":0,"isRequired":0,"isSelected":0,"label":"5","value":"非常满意"},{"buttonId":1,"isRequired":0,"isSelected":0,"label":"4","value":"满意"},{"buttonId":2,"isRequired":0,"isSelected":0,"label":"3","value":"一般"},{"buttonId":3,"isRequired":0,"isSelected":0,"label":"2","value":"不满意"},{"buttonId":4,"isRequired":0,"isSelected":0,"label":"1","value":"非常不满意"}],"satisfactionLevelConfigId":12948312890},{"filedDesc":"其他问题","filedId":5,"filedName":"otherQuestion","filedType":"text","isRequired":1,"optionBoList":[],"satisfactionLevelConfigId":12948312890}]},"satisfactionLevelConfigId":12948312890,"status":0,"updateTime":"2025-06-12 17:20:47"}}]}
//    '''
//
//    def "test fastjson deserialization"() {
//        when:
//        JSONObject jsonObject = JSON.parseObject(JSON_DATA);
//
//        List<ChannelBo> channelBoList = jsonObject.getObject("channelConfigBoList", new com.alibaba.fastjson.TypeReference<List<ChannelBo>>() {
//        });
//        AllConfigJsonBo result = new AllConfigJsonBo();
//        result.channelConfigBoList = channelBoList
//
//        then:
//        result != null
//        result.channelConfigBoList != null
//        !result.channelConfigBoList.isEmpty()
//
//        when:
//        ChannelBo channelBo = result.channelConfigBoList.first()
//
//        then:
//        channelBo.channelId == 12948312891L
//        channelBo.channelSeq == 1
//        channelBo.channelName == "测试渠道"
//        channelBo.channelEn == "TestChannel"
//        channelBo.channelDesc == "这是一个测试渠道描述"
//        channelBo.channelType == "user_channel"
//        channelBo.navigationId == 1001L
//        channelBo.clientCategoryId == 2001L
//    }
//
//    def "test jackson deserialization"() {
//        given:
//        ObjectMapper mapper = new ObjectMapper()
//
//        when:
//        AllConfigJsonBo result = mapper.readValue(JSON_DATA, AllConfigJsonBo)
//
//        then:
//        result != null
//        result.channelConfigBoList != null
//        !result.channelConfigBoList.isEmpty()
//
//        when:
//        ChannelBo channelBo = result.channelConfigBoList.first()
//
//        then:
//        channelBo.channelId == 12948312891L
//        channelBo.channelSeq == 1
//        channelBo.channelName == "测试渠道"
//        channelBo.channelEn == "TestChannel"
//        channelBo.channelDesc == "这是一个测试渠道描述"
//        channelBo.channelType == "user_channel"
//        channelBo.navigationId == 1001L
//        channelBo.clientCategoryId == 2001L
//    }
//}
