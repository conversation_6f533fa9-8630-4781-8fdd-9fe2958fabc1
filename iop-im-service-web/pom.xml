<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.wanshifu</groupId>
        <artifactId>iop-im-service</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>

    <artifactId>iop-im-service-web</artifactId>
    <version>1.0.1-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-im-service-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-work-order-service-api</artifactId>
            <version>1.0.35-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>user-order-service-api</artifactId>
                    <groupId>com.wanshifu</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-core-spring-boot-starter</artifactId>
            <version>${wanshfiu-framework-version}</version>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-test</artifactId>
            <version>${wanshfiu-framework-version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-templates</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <groupId>net.bull.javamelody</groupId>
                    <artifactId>javamelody-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.bull.javamelody</groupId>
                    <artifactId>javamelody-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--H2-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-rocketmq-spring-boot-starter</artifactId>
            <version>${wanshfiu-framework-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-tcnative-boringssl-static</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-redis-spring-boot-starter</artifactId>
            <version>${wanshfiu-framework-version}</version>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-rocketmq-consume-dispatcher</artifactId>
            <version>${wanshfiu-framework-version}</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>order-config-service-api</artifactId>
            <version>2.0.116</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wallet-service-api</artifactId>
            <version>1.0.71</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>1.4.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>aopalliance</artifactId>
                    <groupId>aopalliance</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--                 其他业务包                     -->

        <!-- 账号服务api-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-account-service-api</artifactId>
            <version>1.0.19</version>
        </dependency>

        <!-- FastHttp -->
        <dependency>
            <groupId>com.github.icecooly</groupId>
            <artifactId>FastHttpClient</artifactId>
            <version>1.7</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-ai-service-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-information-service-api</artifactId>
            <version>1.1.15-project-public-********-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--用户账号服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>user-service-api</artifactId>
            <version>1.0.184-0717-SNAPSHOT</version>
        </dependency>

        <!-- 总包账户服务 -->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>merchant-service-api</artifactId>
            <version>1.0.28</version>
        </dependency>

        <!-- 基础地址服务 -->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-address-service-api</artifactId>
            <version>1.0.39</version>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.4.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.6.3</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.mongoplus</groupId>-->
<!--            <artifactId>mongo-plus-boot-starter</artifactId>-->
<!--            <version>*******</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>bson</artifactId>-->
<!--                    <groupId>org.mongodb</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>mongodb-driver-core</artifactId>-->
<!--                    <groupId>org.mongodb</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>mongodb-driver-sync</artifactId>-->
<!--                    <groupId>org.mongodb</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.mongodb</groupId>-->
<!--            <artifactId>bson</artifactId>-->
<!--            <version>5.1.2</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.mongodb</groupId>-->
<!--            <artifactId>mongodb-driver-core</artifactId>-->
<!--            <version>5.1.2</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <artifactId>mongodb-driver-sync</artifactId>-->
<!--            <groupId>org.mongodb</groupId>-->
<!--            <version>5.1.2</version>-->
<!--        </dependency>-->

    </dependencies>

    <!--多环境部署配置-->
    <profiles>
        <profile>
            <id>test</id>
            <properties>
                <deploy.env>test</deploy.env>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <deploy.env>prod</deploy.env>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <deploy.env>dev</deploy.env>
            </properties>
        </profile>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <deploy.env>local</deploy.env>
            </properties>
        </profile>
    </profiles>

    <build>
        <filters>
            <filter>src/main/resources/config/application-${deploy.env}.properties</filter>
        </filters>
        <resources>
            <resource>
                <directory>src/main/resources/</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <finalName>iop-im-service</finalName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>