# IOP IM Service 开发指南

## 项目概述

IOP IM Service 是万师傅智能运营平台的核心即时通讯服务，基于腾讯IM SDK构建，提供完整的客服系统解决方案。项目采用微服务架构，支持用户注册、消息处理、智能分配、AI机器人集成等功能。

## 核心业务架构

### 1. 业务流程概览

```
用户进入 → 虚拟用户分配 → AI机器人接待 → 转人工服务 → 坐席分配 → 会话处理 → 会话结束
```

### 2. 核心实体关系

```
用户(User) ←→ 虚拟用户(VirtualUser) ←→ 会话(Conversation) ←→ 坐席(Seat) ←→ 分组(Group)
```

## 核心业务流程详解

### 1. 用户注册流程

**流程说明：**
- 用户首次进入系统时需要注册到腾讯IM
- 支持设备ID和用户ID两种注册方式
- 注册成功后获得外部用户ID和签名

**核心代码位置：**
- `ImInterServiceImpl.registerUser()`
- `TencentManager.registerUser()`

**关键步骤：**
1. 参数校验（设备ID或用户ID必须有一个）
2. 调用腾讯IM注册接口
3. 保存用户注册信息到数据库
4. 返回外部用户ID和签名

### 2. 虚拟用户分配流程

**流程说明：**
- 为每个真实用户分配一个虚拟用户进行IM通讯
- 虚拟用户池管理，支持复用
- 建立用户与虚拟用户的绑定关系

**核心代码位置：**
- `ImInterServiceImpl.getVirtualInfo()`

**关键步骤：**
1. 查找可用的虚拟用户
2. 建立绑定关系
3. 创建会话记录
4. 发送欢迎语消息
5. 添加超时任务

### 3. 智能分配流程

**流程说明：**
- 根据分组规则和坐席状态进行智能分配
- 支持直接分配和排队机制
- 基于坐席饱和度进行负载均衡

**核心代码位置：**
- `DistributeServiceImpl.distributeEnGroup()`
- `DistributeServiceImpl.distributeGroupSeat()`

**关键步骤：**
1. 获取分组信息和规则
2. 检查是否有空闲坐席
3. 计算坐席饱和度
4. 执行分配或进入排队

### 4. AI机器人交互流程

**流程说明：**
- 集成AI工作流引擎
- 支持自动回复和智能对话
- 消息回调处理机制

**核心代码位置：**
- `WorkFlowExecuteResultMsgCallbackConsumer`
- `ImInterServiceImpl.sendAIMsgToPersonal()`

**关键步骤：**
1. 接收AI回调消息
2. 解析消息内容
3. 发送给用户
4. 记录会话明细

## 数据模型设计

### 1. 核心实体

#### 会话表 (im_conversation)
```sql
- conversation_id: 会话ID
- from_outer_user_id: 发起人外部用户ID
- to_outer_user_id: 接收者外部用户ID
- conversation_status: 会话状态(processing/complete)
- group_id: 分组ID
```

#### 用户注册表 (user_register_info)
```sql
- user_id: 内部用户ID
- outer_user_id: 外部用户ID
- user_class: 用户类型
- register_status: 注册状态
- user_sign: 用户签名
```

#### 虚拟用户表 (virtual_user_info)
```sql
- outer_user_id: 外部用户ID
- status: 状态(no_used/used)
- user_sign: 用户签名
```

#### 坐席信息表 (seat_info)
```sql
- seat_id: 坐席ID
- account_id: 账号ID
- max_wiring_quantity: 最大接线量
- current_seat_status_en: 当前状态
```

### 2. 关系映射

- **用户-虚拟用户关系**: `user_virtual_relation`
- **群组-用户关系**: `im_group_user_relation`
- **坐席-分组关系**: `seat_group_mapping`
- **会话分配记录**: `im_conversation_distribute`

## 技术架构详解

### 1. 分层架构

```
Controller层 → Service层 → Manager层 → Repository层 → Mapper层
```

**各层职责：**
- **Controller**: 接口控制，参数校验
- **Service**: 业务逻辑处理
- **Manager**: 第三方服务集成（腾讯IM、Socket等）
- **Repository**: 数据访问封装
- **Mapper**: MyBatis数据映射

### 2. 消息队列架构

**主要Topic：**
- `iop.im.service.general.topic`: 通用消息
- `iop.im.service.delay.topic`: 延时消息
- `wanshifu.ai.topic`: AI回调消息

**核心Consumer：**
- `ImportMsgConsumer`: 历史消息导入
- `ToArtificialMsgConsumer`: 转人工消息
- `WorkFlowExecuteResultMsgCallbackConsumer`: AI回调
- `SendImWebSocketMessageConsumer`: WebSocket消息推送

### 3. 定时任务架构

**XXL-Job集成：**
- 支持动态添加/删除任务
- 超时策略执行
- 会话状态检查

## 开发规范

### 1. 代码结构规范

```
com.wanshifu/
├── controller/          # 控制器
│   ├── inter/          # 内部接口
│   ├── open/           # 开放接口
│   └── backend/        # 后台管理接口
├── service/            # 业务服务
│   ├── impl/          # 实现类
│   └── inner/         # 内部服务
├── manager/            # 管理器层
│   ├── tencentBase/   # 腾讯IM管理
│   ├── socketBase/    # Socket管理
│   └── commonBase/    # 通用管理
├── repository/         # 数据访问
├── mapper/            # MyBatis映射
├── mq/               # 消息队列
│   ├── consumer/     # 消费者
│   └── producer/     # 生产者
├── config/           # 配置类
├── utils/            # 工具类
└── factory/          # 工厂模式实现
```

### 2. 命名规范

**接口命名：**
- Controller: `XxxController`
- Service: `XxxService` / `XxxServiceImpl`
- Repository: `XxxRepository`
- Manager: `XxxManager` / `XxxManagerImpl`

**方法命名：**
- 查询: `get/find/select/query`
- 新增: `add/insert/create`
- 修改: `update/modify`
- 删除: `delete/remove`

### 3. 异常处理规范

```java
// 业务异常
throw new BusException("业务错误信息");

// 参数校验
if (ObjectUtils.isEmpty(param)) {
    throw new BusException("参数不能为空");
}

// 统一异常处理
@ControllerAdvice
public class GlobalExceptionHandler {
    // 异常处理逻辑
}
```

### 4. 事务管理规范

```java
@Transactional(rollbackFor = Exception.class)
public void businessMethod() {
    // 业务逻辑
}
```

## 核心组件使用指南

### 1. 腾讯IM集成

**TencentManager接口：**
```java
// 用户注册
RegisterUserRespBo registerUser(RegisterUserReqBo req);

// 发送消息
SendMsgToPersonalResultBo sendMsgToPersonal(SendMsgPersonalBo req);

// 创建群组
String createGroup(CreateGroupReqBo req);
```

### 2. 消息队列使用

**生产者示例：**
```java
@Resource
private ImportMsgProducer importMsgProducer;

// 发送消息
importMsgProducer.sendMessage(JSON.toJSONString(msgBo));
```

**消费者示例：**
```java
@Tag(value = MqConfig.IMPORT_MSG_TAG, desc = "消息导入")
public Action importMsgAction(@MessageBody ImportMsgTagBo req) {
    // 处理逻辑
    return Action.CommitMessage;
}
```

### 3. 定时任务使用

**添加任务：**
```java
@Resource
private XxlJobTemplate xxlJobTemplate;

AddXxlJob addJob = new AddXxlJob()
    .setJobGroup(1)
    .setJobDesc("任务描述")
    .setExecutorHandler("handlerName")
    .setIntervalSecond(60);

xxlJobTemplate.addJobAndStart(addJob);
```

### 4. 工作流引擎

**流程配置：**
- 支持节点配置
- 条件跳转
- 自动回复
- 系统消息

## 配置管理

### 1. Apollo配置

**核心配置项：**
```properties
# 数据库配置
spring.datasource.url
spring.datasource.username
spring.datasource.password

# Redis配置
spring.redis.host
spring.redis.port

# RocketMQ配置
wanshifu.rocketMQ.iop.im.service.general.topic
wanshifu.rocketMQ.iop.im.service.delay.topic

# 队列配置
wanshifu.globalQueueConfig.length
wanshifu.globalQueueConfig.memberIntervalMinute
```

### 2. 环境配置

**多环境支持：**
- `application-dev.properties`: 开发环境
- `application-test.properties`: 测试环境
- `application-prod.properties`: 生产环境

## 测试指南

### 1. 单元测试

**测试结构：**
```
src/test/java/
├── service/           # 服务层测试
├── repository/        # 数据访问测试
└── integration/       # 集成测试
```

### 2. 集成测试

**数据库测试：**
- 使用H2内存数据库
- 测试SQL脚本: `src/test/resources/sql/createTable.sql`

### 3. API测试

**接口测试工具：**
- Postman集合
- Swagger文档
- 自动化测试脚本

## 部署指南

### 1. 本地开发环境

```bash
# 启动依赖服务
docker-compose up -d mysql redis rocketmq

# 启动应用
mvn spring-boot:run
```

### 2. 生产环境部署

```bash
# 构建镜像
docker build -t iop-im-service:latest .

# 部署服务
kubectl apply -f k8s/deployment.yaml
```

## 监控与运维

### 1. 健康检查

**Actuator端点：**
- `/actuator/health`: 健康状态
- `/actuator/metrics`: 性能指标
- `/actuator/info`: 应用信息

### 2. 日志管理

**日志配置：**
- 使用Logback配置
- 支持日志级别动态调整
- 集成链路追踪

### 3. 性能监控

**监控指标：**
- 接口响应时间
- 消息队列积压
- 数据库连接池
- JVM内存使用

## 常见问题与解决方案

### 1. 消息发送失败

**排查步骤：**
1. 检查腾讯IM配置
2. 验证用户签名有效性
3. 查看错误日志

### 2. 坐席分配异常

**排查步骤：**
1. 检查坐席状态
2. 验证分组规则
3. 查看队列配置

### 3. 消息队列积压

**解决方案：**
1. 增加消费者实例
2. 优化消费逻辑
3. 调整队列配置

## 扩展开发

### 1. 新增业务接口

1. 定义API接口
2. 实现Service逻辑
3. 添加数据访问层
4. 编写单元测试

### 2. 集成第三方服务

1. 创建Manager层接口
2. 实现具体集成逻辑
3. 添加配置项
4. 处理异常情况

### 3. 新增消息类型

1. 定义消息结构
2. 实现消费者逻辑
3. 添加生产者方法
4. 配置路由规则

## 最佳实践

### 1. 性能优化

- 使用连接池管理数据库连接
- 合理使用缓存减少数据库访问
- 异步处理耗时操作
- 批量处理提高效率

### 2. 安全考虑

- 参数校验防止注入攻击
- 敏感信息加密存储
- 接口访问权限控制
- 日志脱敏处理

### 3. 可维护性

- 代码注释清晰完整
- 统一异常处理机制
- 配置外部化管理
- 版本控制规范

---

本开发指南涵盖了IOP IM Service项目的核心架构、业务流程、开发规范和最佳实践，为开发团队提供全面的技术指导。
